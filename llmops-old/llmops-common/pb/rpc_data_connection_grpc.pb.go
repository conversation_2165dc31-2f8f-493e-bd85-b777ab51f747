// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.18.1
// source: proto/rpc_data_connection.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	DataConnectionManager_ListDataConnections_FullMethodName   = "/proto.DataConnectionManager/ListDataConnections"
	DataConnectionManager_GetDataConnection_FullMethodName     = "/proto.DataConnectionManager/GetDataConnection"
	DataConnectionManager_CreateDataConnection_FullMethodName  = "/proto.DataConnectionManager/CreateDataConnection"
	DataConnectionManager_UpdateDataConnection_FullMethodName  = "/proto.DataConnectionManager/UpdateDataConnection"
	DataConnectionManager_DeleteDataConnections_FullMethodName = "/proto.DataConnectionManager/DeleteDataConnections"
	DataConnectionManager_CloneDataConnection_FullMethodName   = "/proto.DataConnectionManager/CloneDataConnection"
)

// DataConnectionManagerClient is the client API for DataConnectionManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DataConnectionManagerClient interface {
	// 获取数据连接列表，需要实时测试连接状态
	ListDataConnections(ctx context.Context, in *ListConnectionsReq, opts ...grpc.CallOption) (*ListConnectionsRsp, error)
	GetDataConnection(ctx context.Context, in *GetConnectionReq, opts ...grpc.CallOption) (*GetConnectionRsp, error)
	CreateDataConnection(ctx context.Context, in *CreateConnectionReq, opts ...grpc.CallOption) (*CreateConnectionRsp, error)
	UpdateDataConnection(ctx context.Context, in *UpdateConnectionReq, opts ...grpc.CallOption) (*UpdateConnectionRsp, error)
	DeleteDataConnections(ctx context.Context, in *DeleteConnectionsReq, opts ...grpc.CallOption) (*DeleteConnectionsRsp, error)
	CloneDataConnection(ctx context.Context, in *CloneConnectionReq, opts ...grpc.CallOption) (*CloneConnectionRsp, error)
}

type dataConnectionManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewDataConnectionManagerClient(cc grpc.ClientConnInterface) DataConnectionManagerClient {
	return &dataConnectionManagerClient{cc}
}

func (c *dataConnectionManagerClient) ListDataConnections(ctx context.Context, in *ListConnectionsReq, opts ...grpc.CallOption) (*ListConnectionsRsp, error) {
	out := new(ListConnectionsRsp)
	err := c.cc.Invoke(ctx, DataConnectionManager_ListDataConnections_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataConnectionManagerClient) GetDataConnection(ctx context.Context, in *GetConnectionReq, opts ...grpc.CallOption) (*GetConnectionRsp, error) {
	out := new(GetConnectionRsp)
	err := c.cc.Invoke(ctx, DataConnectionManager_GetDataConnection_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataConnectionManagerClient) CreateDataConnection(ctx context.Context, in *CreateConnectionReq, opts ...grpc.CallOption) (*CreateConnectionRsp, error) {
	out := new(CreateConnectionRsp)
	err := c.cc.Invoke(ctx, DataConnectionManager_CreateDataConnection_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataConnectionManagerClient) UpdateDataConnection(ctx context.Context, in *UpdateConnectionReq, opts ...grpc.CallOption) (*UpdateConnectionRsp, error) {
	out := new(UpdateConnectionRsp)
	err := c.cc.Invoke(ctx, DataConnectionManager_UpdateDataConnection_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataConnectionManagerClient) DeleteDataConnections(ctx context.Context, in *DeleteConnectionsReq, opts ...grpc.CallOption) (*DeleteConnectionsRsp, error) {
	out := new(DeleteConnectionsRsp)
	err := c.cc.Invoke(ctx, DataConnectionManager_DeleteDataConnections_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataConnectionManagerClient) CloneDataConnection(ctx context.Context, in *CloneConnectionReq, opts ...grpc.CallOption) (*CloneConnectionRsp, error) {
	out := new(CloneConnectionRsp)
	err := c.cc.Invoke(ctx, DataConnectionManager_CloneDataConnection_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DataConnectionManagerServer is the server API for DataConnectionManager service.
// All implementations must embed UnimplementedDataConnectionManagerServer
// for forward compatibility
type DataConnectionManagerServer interface {
	// 获取数据连接列表，需要实时测试连接状态
	ListDataConnections(context.Context, *ListConnectionsReq) (*ListConnectionsRsp, error)
	GetDataConnection(context.Context, *GetConnectionReq) (*GetConnectionRsp, error)
	CreateDataConnection(context.Context, *CreateConnectionReq) (*CreateConnectionRsp, error)
	UpdateDataConnection(context.Context, *UpdateConnectionReq) (*UpdateConnectionRsp, error)
	DeleteDataConnections(context.Context, *DeleteConnectionsReq) (*DeleteConnectionsRsp, error)
	CloneDataConnection(context.Context, *CloneConnectionReq) (*CloneConnectionRsp, error)
	mustEmbedUnimplementedDataConnectionManagerServer()
}

// UnimplementedDataConnectionManagerServer must be embedded to have forward compatible implementations.
type UnimplementedDataConnectionManagerServer struct {
}

func (UnimplementedDataConnectionManagerServer) ListDataConnections(context.Context, *ListConnectionsReq) (*ListConnectionsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDataConnections not implemented")
}
func (UnimplementedDataConnectionManagerServer) GetDataConnection(context.Context, *GetConnectionReq) (*GetConnectionRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDataConnection not implemented")
}
func (UnimplementedDataConnectionManagerServer) CreateDataConnection(context.Context, *CreateConnectionReq) (*CreateConnectionRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDataConnection not implemented")
}
func (UnimplementedDataConnectionManagerServer) UpdateDataConnection(context.Context, *UpdateConnectionReq) (*UpdateConnectionRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDataConnection not implemented")
}
func (UnimplementedDataConnectionManagerServer) DeleteDataConnections(context.Context, *DeleteConnectionsReq) (*DeleteConnectionsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDataConnections not implemented")
}
func (UnimplementedDataConnectionManagerServer) CloneDataConnection(context.Context, *CloneConnectionReq) (*CloneConnectionRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CloneDataConnection not implemented")
}
func (UnimplementedDataConnectionManagerServer) mustEmbedUnimplementedDataConnectionManagerServer() {}

// UnsafeDataConnectionManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DataConnectionManagerServer will
// result in compilation errors.
type UnsafeDataConnectionManagerServer interface {
	mustEmbedUnimplementedDataConnectionManagerServer()
}

func RegisterDataConnectionManagerServer(s grpc.ServiceRegistrar, srv DataConnectionManagerServer) {
	s.RegisterService(&DataConnectionManager_ServiceDesc, srv)
}

func _DataConnectionManager_ListDataConnections_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListConnectionsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataConnectionManagerServer).ListDataConnections(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataConnectionManager_ListDataConnections_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataConnectionManagerServer).ListDataConnections(ctx, req.(*ListConnectionsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataConnectionManager_GetDataConnection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConnectionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataConnectionManagerServer).GetDataConnection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataConnectionManager_GetDataConnection_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataConnectionManagerServer).GetDataConnection(ctx, req.(*GetConnectionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataConnectionManager_CreateDataConnection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateConnectionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataConnectionManagerServer).CreateDataConnection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataConnectionManager_CreateDataConnection_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataConnectionManagerServer).CreateDataConnection(ctx, req.(*CreateConnectionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataConnectionManager_UpdateDataConnection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateConnectionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataConnectionManagerServer).UpdateDataConnection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataConnectionManager_UpdateDataConnection_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataConnectionManagerServer).UpdateDataConnection(ctx, req.(*UpdateConnectionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataConnectionManager_DeleteDataConnections_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteConnectionsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataConnectionManagerServer).DeleteDataConnections(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataConnectionManager_DeleteDataConnections_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataConnectionManagerServer).DeleteDataConnections(ctx, req.(*DeleteConnectionsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataConnectionManager_CloneDataConnection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CloneConnectionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataConnectionManagerServer).CloneDataConnection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataConnectionManager_CloneDataConnection_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataConnectionManagerServer).CloneDataConnection(ctx, req.(*CloneConnectionReq))
	}
	return interceptor(ctx, in, info, handler)
}

// DataConnectionManager_ServiceDesc is the grpc.ServiceDesc for DataConnectionManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DataConnectionManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "proto.DataConnectionManager",
	HandlerType: (*DataConnectionManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListDataConnections",
			Handler:    _DataConnectionManager_ListDataConnections_Handler,
		},
		{
			MethodName: "GetDataConnection",
			Handler:    _DataConnectionManager_GetDataConnection_Handler,
		},
		{
			MethodName: "CreateDataConnection",
			Handler:    _DataConnectionManager_CreateDataConnection_Handler,
		},
		{
			MethodName: "UpdateDataConnection",
			Handler:    _DataConnectionManager_UpdateDataConnection_Handler,
		},
		{
			MethodName: "DeleteDataConnections",
			Handler:    _DataConnectionManager_DeleteDataConnections_Handler,
		},
		{
			MethodName: "CloneDataConnection",
			Handler:    _DataConnectionManager_CloneDataConnection_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/rpc_data_connection.proto",
}
