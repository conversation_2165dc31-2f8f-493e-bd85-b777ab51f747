// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/rpc_pmml_service.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PmmlParseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filepath string `protobuf:"bytes,1,opt,name=filepath,proto3" json:"filepath,omitempty"`
}

func (x *PmmlParseReq) Reset() {
	*x = PmmlParseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_pmml_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PmmlParseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PmmlParseReq) ProtoMessage() {}

func (x *PmmlParseReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_pmml_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PmmlParseReq.ProtoReflect.Descriptor instead.
func (*PmmlParseReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_pmml_service_proto_rawDescGZIP(), []int{0}
}

func (x *PmmlParseReq) GetFilepath() string {
	if x != nil {
		return x.Filepath
	}
	return ""
}

type PmmlParseRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Model *PmmlProtoc `protobuf:"bytes,1,opt,name=model,proto3" json:"model,omitempty"`
}

func (x *PmmlParseRsp) Reset() {
	*x = PmmlParseRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_pmml_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PmmlParseRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PmmlParseRsp) ProtoMessage() {}

func (x *PmmlParseRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_pmml_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PmmlParseRsp.ProtoReflect.Descriptor instead.
func (*PmmlParseRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_pmml_service_proto_rawDescGZIP(), []int{1}
}

func (x *PmmlParseRsp) GetModel() *PmmlProtoc {
	if x != nil {
		return x.Model
	}
	return nil
}

var File_proto_rpc_pmml_service_proto protoreflect.FileDescriptor

var file_proto_rpc_pmml_service_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x70, 0x63, 0x5f, 0x70, 0x6d, 0x6d, 0x6c,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x10, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x6d, 0x6d,
	0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x2a, 0x0a, 0x0c, 0x50, 0x6d, 0x6d, 0x6c, 0x50,
	0x61, 0x72, 0x73, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x70,
	0x61, 0x74, 0x68, 0x22, 0x37, 0x0a, 0x0c, 0x50, 0x6d, 0x6d, 0x6c, 0x50, 0x61, 0x72, 0x73, 0x65,
	0x52, 0x73, 0x70, 0x12, 0x27, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6d, 0x6d, 0x6c, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x32, 0x44, 0x0a, 0x0b,
	0x50, 0x6d, 0x6d, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x35, 0x0a, 0x09, 0x50,
	0x6d, 0x6d, 0x6c, 0x50, 0x61, 0x72, 0x73, 0x65, 0x12, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x50, 0x6d, 0x6d, 0x6c, 0x50, 0x61, 0x72, 0x73, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6d, 0x6d, 0x6c, 0x50, 0x61, 0x72, 0x73, 0x65, 0x52,
	0x73, 0x70, 0x42, 0x26, 0x5a, 0x24, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e,
	0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_proto_rpc_pmml_service_proto_rawDescOnce sync.Once
	file_proto_rpc_pmml_service_proto_rawDescData = file_proto_rpc_pmml_service_proto_rawDesc
)

func file_proto_rpc_pmml_service_proto_rawDescGZIP() []byte {
	file_proto_rpc_pmml_service_proto_rawDescOnce.Do(func() {
		file_proto_rpc_pmml_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_rpc_pmml_service_proto_rawDescData)
	})
	return file_proto_rpc_pmml_service_proto_rawDescData
}

var file_proto_rpc_pmml_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_proto_rpc_pmml_service_proto_goTypes = []interface{}{
	(*PmmlParseReq)(nil), // 0: proto.PmmlParseReq
	(*PmmlParseRsp)(nil), // 1: proto.PmmlParseRsp
	(*PmmlProtoc)(nil),   // 2: proto.PmmlProtoc
}
var file_proto_rpc_pmml_service_proto_depIdxs = []int32{
	2, // 0: proto.PmmlParseRsp.model:type_name -> proto.PmmlProtoc
	0, // 1: proto.PmmlService.PmmlParse:input_type -> proto.PmmlParseReq
	1, // 2: proto.PmmlService.PmmlParse:output_type -> proto.PmmlParseRsp
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_proto_rpc_pmml_service_proto_init() }
func file_proto_rpc_pmml_service_proto_init() {
	if File_proto_rpc_pmml_service_proto != nil {
		return
	}
	file_proto_pmml_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_proto_rpc_pmml_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PmmlParseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_pmml_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PmmlParseRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_rpc_pmml_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_rpc_pmml_service_proto_goTypes,
		DependencyIndexes: file_proto_rpc_pmml_service_proto_depIdxs,
		MessageInfos:      file_proto_rpc_pmml_service_proto_msgTypes,
	}.Build()
	File_proto_rpc_pmml_service_proto = out.File
	file_proto_rpc_pmml_service_proto_rawDesc = nil
	file_proto_rpc_pmml_service_proto_goTypes = nil
	file_proto_rpc_pmml_service_proto_depIdxs = nil
}
