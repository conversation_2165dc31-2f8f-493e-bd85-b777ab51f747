// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/pmml.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PmmlProtoc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version          string             `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	Copyright        string             `protobuf:"bytes,2,opt,name=copyright,proto3" json:"copyright,omitempty"`
	Description      string             `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Application      *Application       `protobuf:"bytes,4,opt,name=application,proto3" json:"application,omitempty"`
	Timestamp        *Timestamp         `protobuf:"bytes,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	InputFields      []*InputFields     `protobuf:"bytes,6,rep,name=input_fields,json=inputFields,proto3" json:"input_fields,omitempty"`
	OutputFields     []*OutputFields    `protobuf:"bytes,7,rep,name=output_fields,json=outputFields,proto3" json:"output_fields,omitempty"`
	MiningMode       bool               `protobuf:"varint,8,opt,name=mining_mode,json=miningMode,proto3" json:"mining_mode,omitempty"`
	ModelDefinitions []*ModelDefinition `protobuf:"bytes,9,rep,name=model_definitions,json=modelDefinitions,proto3" json:"model_definitions,omitempty"`
}

func (x *PmmlProtoc) Reset() {
	*x = PmmlProtoc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pmml_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PmmlProtoc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PmmlProtoc) ProtoMessage() {}

func (x *PmmlProtoc) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pmml_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PmmlProtoc.ProtoReflect.Descriptor instead.
func (*PmmlProtoc) Descriptor() ([]byte, []int) {
	return file_proto_pmml_proto_rawDescGZIP(), []int{0}
}

func (x *PmmlProtoc) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *PmmlProtoc) GetCopyright() string {
	if x != nil {
		return x.Copyright
	}
	return ""
}

func (x *PmmlProtoc) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PmmlProtoc) GetApplication() *Application {
	if x != nil {
		return x.Application
	}
	return nil
}

func (x *PmmlProtoc) GetTimestamp() *Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *PmmlProtoc) GetInputFields() []*InputFields {
	if x != nil {
		return x.InputFields
	}
	return nil
}

func (x *PmmlProtoc) GetOutputFields() []*OutputFields {
	if x != nil {
		return x.OutputFields
	}
	return nil
}

func (x *PmmlProtoc) GetMiningMode() bool {
	if x != nil {
		return x.MiningMode
	}
	return false
}

func (x *PmmlProtoc) GetModelDefinitions() []*ModelDefinition {
	if x != nil {
		return x.ModelDefinitions
	}
	return nil
}

type Application struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *Application) Reset() {
	*x = Application{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pmml_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application) ProtoMessage() {}

func (x *Application) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pmml_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application.ProtoReflect.Descriptor instead.
func (*Application) Descriptor() ([]byte, []int) {
	return file_proto_pmml_proto_rawDescGZIP(), []int{1}
}

func (x *Application) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Application) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type Timestamp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Content []string `protobuf:"bytes,1,rep,name=content,proto3" json:"content,omitempty"`
}

func (x *Timestamp) Reset() {
	*x = Timestamp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pmml_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Timestamp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Timestamp) ProtoMessage() {}

func (x *Timestamp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pmml_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Timestamp.ProtoReflect.Descriptor instead.
func (*Timestamp) Descriptor() ([]byte, []int) {
	return file_proto_pmml_proto_rawDescGZIP(), []int{2}
}

func (x *Timestamp) GetContent() []string {
	if x != nil {
		return x.Content
	}
	return nil
}

type InputFields struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name      string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	UsageType string `protobuf:"bytes,2,opt,name=usage_type,json=usageType,proto3" json:"usage_type,omitempty"`
	DataType  string `protobuf:"bytes,3,opt,name=data_type,json=dataType,proto3" json:"data_type,omitempty"`
	Optype    string `protobuf:"bytes,4,opt,name=optype,proto3" json:"optype,omitempty"`
}

func (x *InputFields) Reset() {
	*x = InputFields{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pmml_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InputFields) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputFields) ProtoMessage() {}

func (x *InputFields) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pmml_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputFields.ProtoReflect.Descriptor instead.
func (*InputFields) Descriptor() ([]byte, []int) {
	return file_proto_pmml_proto_rawDescGZIP(), []int{3}
}

func (x *InputFields) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InputFields) GetUsageType() string {
	if x != nil {
		return x.UsageType
	}
	return ""
}

func (x *InputFields) GetDataType() string {
	if x != nil {
		return x.DataType
	}
	return ""
}

func (x *InputFields) GetOptype() string {
	if x != nil {
		return x.Optype
	}
	return ""
}

type OutputFields struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Optype   string `protobuf:"bytes,2,opt,name=optype,proto3" json:"optype,omitempty"`
	DataType string `protobuf:"bytes,3,opt,name=data_type,json=dataType,proto3" json:"data_type,omitempty"`
	Feature  string `protobuf:"bytes,4,opt,name=feature,proto3" json:"feature,omitempty"`
	Value    string `protobuf:"bytes,5,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *OutputFields) Reset() {
	*x = OutputFields{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pmml_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OutputFields) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutputFields) ProtoMessage() {}

func (x *OutputFields) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pmml_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutputFields.ProtoReflect.Descriptor instead.
func (*OutputFields) Descriptor() ([]byte, []int) {
	return file_proto_pmml_proto_rawDescGZIP(), []int{4}
}

func (x *OutputFields) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *OutputFields) GetOptype() string {
	if x != nil {
		return x.Optype
	}
	return ""
}

func (x *OutputFields) GetDataType() string {
	if x != nil {
		return x.DataType
	}
	return ""
}

func (x *OutputFields) GetFeature() string {
	if x != nil {
		return x.Feature
	}
	return ""
}

func (x *OutputFields) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type ModelDefinition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelName      string `protobuf:"bytes,1,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
	MiningFunction string `protobuf:"bytes,2,opt,name=mining_function,json=miningFunction,proto3" json:"mining_function,omitempty"`
	FunctionName   string `protobuf:"bytes,3,opt,name=function_name,json=functionName,proto3" json:"function_name,omitempty"`
}

func (x *ModelDefinition) Reset() {
	*x = ModelDefinition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_pmml_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelDefinition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelDefinition) ProtoMessage() {}

func (x *ModelDefinition) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pmml_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelDefinition.ProtoReflect.Descriptor instead.
func (*ModelDefinition) Descriptor() ([]byte, []int) {
	return file_proto_pmml_proto_rawDescGZIP(), []int{5}
}

func (x *ModelDefinition) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *ModelDefinition) GetMiningFunction() string {
	if x != nil {
		return x.MiningFunction
	}
	return ""
}

func (x *ModelDefinition) GetFunctionName() string {
	if x != nil {
		return x.FunctionName
	}
	return ""
}

var File_proto_pmml_proto protoreflect.FileDescriptor

var file_proto_pmml_proto_rawDesc = []byte{
	0x0a, 0x10, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x6d, 0x6d, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa3, 0x03, 0x0a, 0x0a, 0x50, 0x6d,
	0x6d, 0x6c, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x35, 0x0a, 0x0c, 0x69, 0x6e, 0x70, 0x75,
	0x74, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x73, 0x52, 0x0b, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12,
	0x38, 0x0a, 0x0d, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4f,
	0x75, 0x74, 0x70, 0x75, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x52, 0x0c, 0x6f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x69, 0x6e,
	0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a,
	0x6d, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x43, 0x0a, 0x11, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x5f, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x10, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22,
	0x3b, 0x0a, 0x0b, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x25, 0x0a, 0x09,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x22, 0x75, 0x0a, 0x0b, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x73, 0x61, 0x67, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x61, 0x67,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x74, 0x79, 0x70, 0x65, 0x22, 0x87, 0x01, 0x0a, 0x0c, 0x4f,
	0x75, 0x74, 0x70, 0x75, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x6f, 0x70, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6f, 0x70, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x22, 0x7e, 0x0a, 0x0f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x6d, 0x69, 0x6e, 0x69, 0x6e, 0x67,
	0x5f, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x6d, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x23, 0x0a, 0x0d, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x42, 0x26, 0x5a, 0x24, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72,
	0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_pmml_proto_rawDescOnce sync.Once
	file_proto_pmml_proto_rawDescData = file_proto_pmml_proto_rawDesc
)

func file_proto_pmml_proto_rawDescGZIP() []byte {
	file_proto_pmml_proto_rawDescOnce.Do(func() {
		file_proto_pmml_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_pmml_proto_rawDescData)
	})
	return file_proto_pmml_proto_rawDescData
}

var file_proto_pmml_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_proto_pmml_proto_goTypes = []interface{}{
	(*PmmlProtoc)(nil),      // 0: proto.PmmlProtoc
	(*Application)(nil),     // 1: proto.Application
	(*Timestamp)(nil),       // 2: proto.Timestamp
	(*InputFields)(nil),     // 3: proto.InputFields
	(*OutputFields)(nil),    // 4: proto.OutputFields
	(*ModelDefinition)(nil), // 5: proto.ModelDefinition
}
var file_proto_pmml_proto_depIdxs = []int32{
	1, // 0: proto.PmmlProtoc.application:type_name -> proto.Application
	2, // 1: proto.PmmlProtoc.timestamp:type_name -> proto.Timestamp
	3, // 2: proto.PmmlProtoc.input_fields:type_name -> proto.InputFields
	4, // 3: proto.PmmlProtoc.output_fields:type_name -> proto.OutputFields
	5, // 4: proto.PmmlProtoc.model_definitions:type_name -> proto.ModelDefinition
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_proto_pmml_proto_init() }
func file_proto_pmml_proto_init() {
	if File_proto_pmml_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_pmml_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PmmlProtoc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pmml_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pmml_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Timestamp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pmml_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InputFields); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pmml_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OutputFields); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_pmml_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelDefinition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_pmml_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_pmml_proto_goTypes,
		DependencyIndexes: file_proto_pmml_proto_depIdxs,
		MessageInfos:      file_proto_pmml_proto_msgTypes,
	}.Build()
	File_proto_pmml_proto = out.File
	file_proto_pmml_proto_rawDesc = nil
	file_proto_pmml_proto_goTypes = nil
	file_proto_pmml_proto_depIdxs = nil
}
