// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/rpc_model_deployment.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReadDeploymentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx        *UserContext   `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Id         string         `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	ModelId    string         `protobuf:"bytes,3,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ReleaseId  string         `protobuf:"bytes,4,opt,name=release_id,json=releaseId,proto3" json:"release_id,omitempty"`
	Type       DeploymentType `protobuf:"varint,5,opt,name=type,proto3,enum=proto.DeploymentType" json:"type,omitempty"` // 为空获取全部类型的模型部署，默认为模型服务启动的部署
	PageReq    *PageReq       `protobuf:"bytes,6,opt,name=page_req,json=pageReq,proto3" json:"page_req,omitempty"`
	WithStatus bool           `protobuf:"varint,7,opt,name=with_status,json=withStatus,proto3" json:"with_status,omitempty"`
	Kind       ModelKind      `protobuf:"varint,8,opt,name=kind,proto3,enum=proto.ModelKind" json:"kind,omitempty"`
	SubKind    ModelSubKind   `protobuf:"varint,9,opt,name=sub_kind,json=subKind,proto3,enum=proto.ModelSubKind" json:"sub_kind,omitempty"`
	State      string         `protobuf:"bytes,10,opt,name=state,proto3" json:"state,omitempty"`
}

func (x *ReadDeploymentReq) Reset() {
	*x = ReadDeploymentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_deployment_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadDeploymentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadDeploymentReq) ProtoMessage() {}

func (x *ReadDeploymentReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_deployment_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadDeploymentReq.ProtoReflect.Descriptor instead.
func (*ReadDeploymentReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_deployment_proto_rawDescGZIP(), []int{0}
}

func (x *ReadDeploymentReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *ReadDeploymentReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ReadDeploymentReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ReadDeploymentReq) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

func (x *ReadDeploymentReq) GetType() DeploymentType {
	if x != nil {
		return x.Type
	}
	return DeploymentType_DEPLOYMENT_TYPE_UNSPECIFIED
}

func (x *ReadDeploymentReq) GetPageReq() *PageReq {
	if x != nil {
		return x.PageReq
	}
	return nil
}

func (x *ReadDeploymentReq) GetWithStatus() bool {
	if x != nil {
		return x.WithStatus
	}
	return false
}

func (x *ReadDeploymentReq) GetKind() ModelKind {
	if x != nil {
		return x.Kind
	}
	return ModelKind_MODEL_KIND_UNSPECIFIED
}

func (x *ReadDeploymentReq) GetSubKind() ModelSubKind {
	if x != nil {
		return x.SubKind
	}
	return ModelSubKind_MODEL_SUB_KIND_UNSPECIFIED
}

func (x *ReadDeploymentReq) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

type ReadDeploymentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total            int32              `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	PageNum          int32              `protobuf:"varint,2,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	PageSize         int32              `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	ModelDeployments []*ModelDeployment `protobuf:"bytes,4,rep,name=model_deployments,json=modelDeployments,proto3" json:"model_deployments,omitempty"`
}

func (x *ReadDeploymentRsp) Reset() {
	*x = ReadDeploymentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_deployment_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadDeploymentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadDeploymentRsp) ProtoMessage() {}

func (x *ReadDeploymentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_deployment_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadDeploymentRsp.ProtoReflect.Descriptor instead.
func (*ReadDeploymentRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_deployment_proto_rawDescGZIP(), []int{1}
}

func (x *ReadDeploymentRsp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ReadDeploymentRsp) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *ReadDeploymentRsp) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ReadDeploymentRsp) GetModelDeployments() []*ModelDeployment {
	if x != nil {
		return x.ModelDeployments
	}
	return nil
}

type CreateDeploymentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx        *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Deployment *Deployment  `protobuf:"bytes,2,opt,name=deployment,proto3" json:"deployment,omitempty"`
}

func (x *CreateDeploymentReq) Reset() {
	*x = CreateDeploymentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_deployment_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDeploymentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDeploymentReq) ProtoMessage() {}

func (x *CreateDeploymentReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_deployment_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDeploymentReq.ProtoReflect.Descriptor instead.
func (*CreateDeploymentReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_deployment_proto_rawDescGZIP(), []int{2}
}

func (x *CreateDeploymentReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *CreateDeploymentReq) GetDeployment() *Deployment {
	if x != nil {
		return x.Deployment
	}
	return nil
}

type CreateDeploymentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Deployment *Deployment `protobuf:"bytes,1,opt,name=deployment,proto3" json:"deployment,omitempty"`
}

func (x *CreateDeploymentRsp) Reset() {
	*x = CreateDeploymentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_deployment_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDeploymentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDeploymentRsp) ProtoMessage() {}

func (x *CreateDeploymentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_deployment_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDeploymentRsp.ProtoReflect.Descriptor instead.
func (*CreateDeploymentRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_deployment_proto_rawDescGZIP(), []int{3}
}

func (x *CreateDeploymentRsp) GetDeployment() *Deployment {
	if x != nil {
		return x.Deployment
	}
	return nil
}

type UpdateDeploymentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx        *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Deployment *Deployment  `protobuf:"bytes,2,opt,name=deployment,proto3" json:"deployment,omitempty"`
}

func (x *UpdateDeploymentReq) Reset() {
	*x = UpdateDeploymentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_deployment_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDeploymentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDeploymentReq) ProtoMessage() {}

func (x *UpdateDeploymentReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_deployment_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDeploymentReq.ProtoReflect.Descriptor instead.
func (*UpdateDeploymentReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_deployment_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateDeploymentReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *UpdateDeploymentReq) GetDeployment() *Deployment {
	if x != nil {
		return x.Deployment
	}
	return nil
}

type UpdateDeploymentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Deployment *Deployment `protobuf:"bytes,1,opt,name=deployment,proto3" json:"deployment,omitempty"`
}

func (x *UpdateDeploymentRsp) Reset() {
	*x = UpdateDeploymentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_deployment_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDeploymentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDeploymentRsp) ProtoMessage() {}

func (x *UpdateDeploymentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_deployment_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDeploymentRsp.ProtoReflect.Descriptor instead.
func (*UpdateDeploymentRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_deployment_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateDeploymentRsp) GetDeployment() *Deployment {
	if x != nil {
		return x.Deployment
	}
	return nil
}

type DeleteDeploymentsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Ids []string     `protobuf:"bytes,2,rep,name=ids,proto3" json:"ids,omitempty"`
}

func (x *DeleteDeploymentsReq) Reset() {
	*x = DeleteDeploymentsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_deployment_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDeploymentsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDeploymentsReq) ProtoMessage() {}

func (x *DeleteDeploymentsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_deployment_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDeploymentsReq.ProtoReflect.Descriptor instead.
func (*DeleteDeploymentsReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_deployment_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteDeploymentsReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *DeleteDeploymentsReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type DeleteDeploymentsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteDeploymentsRsp) Reset() {
	*x = DeleteDeploymentsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_deployment_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDeploymentsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDeploymentsRsp) ProtoMessage() {}

func (x *DeleteDeploymentsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_deployment_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDeploymentsRsp.ProtoReflect.Descriptor instead.
func (*DeleteDeploymentsRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_deployment_proto_rawDescGZIP(), []int{7}
}

type StartDeploymentsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids []string     `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	Ctx *UserContext `protobuf:"bytes,2,opt,name=ctx,proto3" json:"ctx,omitempty"`
}

func (x *StartDeploymentsReq) Reset() {
	*x = StartDeploymentsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_deployment_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartDeploymentsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartDeploymentsReq) ProtoMessage() {}

func (x *StartDeploymentsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_deployment_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartDeploymentsReq.ProtoReflect.Descriptor instead.
func (*StartDeploymentsReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_deployment_proto_rawDescGZIP(), []int{8}
}

func (x *StartDeploymentsReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *StartDeploymentsReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

type StartDeploymentsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StartDeploymentsRsp) Reset() {
	*x = StartDeploymentsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_deployment_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartDeploymentsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartDeploymentsRsp) ProtoMessage() {}

func (x *StartDeploymentsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_deployment_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartDeploymentsRsp.ProtoReflect.Descriptor instead.
func (*StartDeploymentsRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_deployment_proto_rawDescGZIP(), []int{9}
}

type StopDeploymentsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids []string     `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	Ctx *UserContext `protobuf:"bytes,2,opt,name=ctx,proto3" json:"ctx,omitempty"`
}

func (x *StopDeploymentsReq) Reset() {
	*x = StopDeploymentsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_deployment_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopDeploymentsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopDeploymentsReq) ProtoMessage() {}

func (x *StopDeploymentsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_deployment_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopDeploymentsReq.ProtoReflect.Descriptor instead.
func (*StopDeploymentsReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_deployment_proto_rawDescGZIP(), []int{10}
}

func (x *StopDeploymentsReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *StopDeploymentsReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

type StopDeploymentsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StopDeploymentsRsp) Reset() {
	*x = StopDeploymentsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_deployment_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopDeploymentsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopDeploymentsRsp) ProtoMessage() {}

func (x *StopDeploymentsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_deployment_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopDeploymentsRsp.ProtoReflect.Descriptor instead.
func (*StopDeploymentsRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_deployment_proto_rawDescGZIP(), []int{11}
}

type WatchDeploymentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeploySysId   string       `protobuf:"bytes,1,opt,name=deploy_sys_id,json=deploySysId,proto3" json:"deploy_sys_id,omitempty"`
	DeploySysName string       `protobuf:"bytes,2,opt,name=deploy_sys_name,json=deploySysName,proto3" json:"deploy_sys_name,omitempty"`
	Ctx           *UserContext `protobuf:"bytes,3,opt,name=ctx,proto3" json:"ctx,omitempty"`
}

func (x *WatchDeploymentReq) Reset() {
	*x = WatchDeploymentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_deployment_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WatchDeploymentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchDeploymentReq) ProtoMessage() {}

func (x *WatchDeploymentReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_deployment_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchDeploymentReq.ProtoReflect.Descriptor instead.
func (*WatchDeploymentReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_deployment_proto_rawDescGZIP(), []int{12}
}

func (x *WatchDeploymentReq) GetDeploySysId() string {
	if x != nil {
		return x.DeploySysId
	}
	return ""
}

func (x *WatchDeploymentReq) GetDeploySysName() string {
	if x != nil {
		return x.DeploySysName
	}
	return ""
}

func (x *WatchDeploymentReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

type WatchDeploymentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Deployment *Deployment `protobuf:"bytes,1,opt,name=deployment,proto3" json:"deployment,omitempty"`
}

func (x *WatchDeploymentRsp) Reset() {
	*x = WatchDeploymentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_deployment_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WatchDeploymentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchDeploymentRsp) ProtoMessage() {}

func (x *WatchDeploymentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_deployment_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchDeploymentRsp.ProtoReflect.Descriptor instead.
func (*WatchDeploymentRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_deployment_proto_rawDescGZIP(), []int{13}
}

func (x *WatchDeploymentRsp) GetDeployment() *Deployment {
	if x != nil {
		return x.Deployment
	}
	return nil
}

type UpdateDeploymentStateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx       *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Id        string       `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	State     string       `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	Health    string       `protobuf:"bytes,4,opt,name=health,proto3" json:"health,omitempty"`
	HealthMsg string       `protobuf:"bytes,5,opt,name=health_msg,json=healthMsg,proto3" json:"health_msg,omitempty"`
}

func (x *UpdateDeploymentStateReq) Reset() {
	*x = UpdateDeploymentStateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_deployment_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDeploymentStateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDeploymentStateReq) ProtoMessage() {}

func (x *UpdateDeploymentStateReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_deployment_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDeploymentStateReq.ProtoReflect.Descriptor instead.
func (*UpdateDeploymentStateReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_deployment_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateDeploymentStateReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *UpdateDeploymentStateReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateDeploymentStateReq) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *UpdateDeploymentStateReq) GetHealth() string {
	if x != nil {
		return x.Health
	}
	return ""
}

func (x *UpdateDeploymentStateReq) GetHealthMsg() string {
	if x != nil {
		return x.HealthMsg
	}
	return ""
}

type UpdateDeploymentStateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateDeploymentStateRsp) Reset() {
	*x = UpdateDeploymentStateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_deployment_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDeploymentStateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDeploymentStateRsp) ProtoMessage() {}

func (x *UpdateDeploymentStateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_deployment_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDeploymentStateRsp.ProtoReflect.Descriptor instead.
func (*UpdateDeploymentStateRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_deployment_proto_rawDescGZIP(), []int{15}
}

type UpdateReplicaStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx    *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Status *Status      `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateReplicaStatusReq) Reset() {
	*x = UpdateReplicaStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_deployment_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateReplicaStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateReplicaStatusReq) ProtoMessage() {}

func (x *UpdateReplicaStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_deployment_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateReplicaStatusReq.ProtoReflect.Descriptor instead.
func (*UpdateReplicaStatusReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_deployment_proto_rawDescGZIP(), []int{16}
}

func (x *UpdateReplicaStatusReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *UpdateReplicaStatusReq) GetStatus() *Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type UpdateReplicaStatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateReplicaStatusRsp) Reset() {
	*x = UpdateReplicaStatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_deployment_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateReplicaStatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateReplicaStatusRsp) ProtoMessage() {}

func (x *UpdateReplicaStatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_deployment_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateReplicaStatusRsp.ProtoReflect.Descriptor instead.
func (*UpdateReplicaStatusRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_deployment_proto_rawDescGZIP(), []int{17}
}

type StartDeploymentMonitorReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx   *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Id    string       `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Topic string       `protobuf:"bytes,3,opt,name=topic,proto3" json:"topic,omitempty"`
}

func (x *StartDeploymentMonitorReq) Reset() {
	*x = StartDeploymentMonitorReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_deployment_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartDeploymentMonitorReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartDeploymentMonitorReq) ProtoMessage() {}

func (x *StartDeploymentMonitorReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_deployment_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartDeploymentMonitorReq.ProtoReflect.Descriptor instead.
func (*StartDeploymentMonitorReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_deployment_proto_rawDescGZIP(), []int{18}
}

func (x *StartDeploymentMonitorReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *StartDeploymentMonitorReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *StartDeploymentMonitorReq) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

type StartDeploymentMonitorRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StartDeploymentMonitorRsp) Reset() {
	*x = StartDeploymentMonitorRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_deployment_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartDeploymentMonitorRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartDeploymentMonitorRsp) ProtoMessage() {}

func (x *StartDeploymentMonitorRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_deployment_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartDeploymentMonitorRsp.ProtoReflect.Descriptor instead.
func (*StartDeploymentMonitorRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_deployment_proto_rawDescGZIP(), []int{19}
}

type StopDeploymentMonitorReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx   *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Id    string       `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Topic string       `protobuf:"bytes,3,opt,name=topic,proto3" json:"topic,omitempty"`
}

func (x *StopDeploymentMonitorReq) Reset() {
	*x = StopDeploymentMonitorReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_deployment_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopDeploymentMonitorReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopDeploymentMonitorReq) ProtoMessage() {}

func (x *StopDeploymentMonitorReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_deployment_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopDeploymentMonitorReq.ProtoReflect.Descriptor instead.
func (*StopDeploymentMonitorReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_deployment_proto_rawDescGZIP(), []int{20}
}

func (x *StopDeploymentMonitorReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *StopDeploymentMonitorReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *StopDeploymentMonitorReq) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

type StopDeploymentMonitorRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StopDeploymentMonitorRsp) Reset() {
	*x = StopDeploymentMonitorRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_deployment_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopDeploymentMonitorRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopDeploymentMonitorRsp) ProtoMessage() {}

func (x *StopDeploymentMonitorRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_deployment_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopDeploymentMonitorRsp.ProtoReflect.Descriptor instead.
func (*StopDeploymentMonitorRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_deployment_proto_rawDescGZIP(), []int{21}
}

var File_proto_rpc_model_deployment_proto protoreflect.FileDescriptor

var file_proto_rpc_model_deployment_proto_rawDesc = []byte{
	0x0a, 0x20, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x70, 0x63, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x5f, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xe6, 0x02, 0x0a, 0x11, 0x52, 0x65, 0x61, 0x64, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x29, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b,
	0x77, 0x69, 0x74, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0a, 0x77, 0x69, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x24, 0x0a,
	0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x04, 0x6b,
	0x69, 0x6e, 0x64, 0x12, 0x2e, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x07, 0x73, 0x75, 0x62, 0x4b,
	0x69, 0x6e, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0xa6, 0x01, 0x0a, 0x11, 0x52, 0x65,
	0x61, 0x64, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75,
	0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x43, 0x0a,
	0x11, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x10, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x22, 0x6e, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12,
	0x31, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x22, 0x48, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x31, 0x0a, 0x0a, 0x64, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x0a, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x6e, 0x0a, 0x13,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x31, 0x0a, 0x0a, 0x64, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x0a, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x48, 0x0a, 0x13,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x73, 0x70, 0x12, 0x31, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x64, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x4e, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x24,
	0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52,
	0x03, 0x63, 0x74, 0x78, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x16, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x73, 0x70, 0x22, 0x4d,
	0x0a, 0x13, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x22, 0x15, 0x0a,
	0x13, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x52, 0x73, 0x70, 0x22, 0x4c, 0x0a, 0x12, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x24, 0x0a, 0x03,
	0x63, 0x74, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63,
	0x74, 0x78, 0x22, 0x14, 0x0a, 0x12, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x73, 0x70, 0x22, 0x86, 0x01, 0x0a, 0x12, 0x57, 0x61, 0x74,
	0x63, 0x68, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x22, 0x0a, 0x0d, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x5f, 0x73, 0x79, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x53, 0x79,
	0x73, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x5f, 0x73, 0x79,
	0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x65,
	0x70, 0x6c, 0x6f, 0x79, 0x53, 0x79, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x03, 0x63,
	0x74, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74,
	0x78, 0x22, 0x47, 0x0a, 0x12, 0x57, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x31, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a,
	0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x9d, 0x01, 0x0a, 0x18, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x68,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x4d, 0x73, 0x67, 0x22, 0x1a, 0x0a, 0x18, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x22, 0x65, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x25, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x18, 0x0a,
	0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x22, 0x67, 0x0a, 0x19, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x70, 0x69, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63,
	0x22, 0x1b, 0x0a, 0x19, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x73, 0x70, 0x22, 0x66, 0x0a,
	0x18, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x6f, 0x70, 0x69, 0x63, 0x22, 0x1a, 0x0a, 0x18, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x73,
	0x70, 0x32, 0x89, 0x07, 0x0a, 0x11, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x44, 0x0a, 0x0e, 0x52, 0x65, 0x61, 0x64, 0x44,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x61, 0x64,
	0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x4a, 0x0a,
	0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x4a, 0x0a, 0x10, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x4d, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x1b, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x52, 0x73, 0x70, 0x12, 0x4a, 0x0a, 0x10, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x73, 0x70,
	0x12, 0x47, 0x0a, 0x0f, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x12, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x6f, 0x70,
	0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x19,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x73, 0x70, 0x12, 0x49, 0x0a, 0x0f, 0x57, 0x61, 0x74,
	0x63, 0x68, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x19, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x57, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x57, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x73, 0x70, 0x30, 0x01, 0x12, 0x59, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65,
	0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12,
	0x53, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x73, 0x70, 0x12, 0x5c, 0x0a, 0x16, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x20,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71,
	0x1a, 0x20, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x65,
	0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52,
	0x73, 0x70, 0x12, 0x59, 0x0a, 0x15, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x1f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x73, 0x70, 0x42, 0x26, 0x5a,
	0x24, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69,
	0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x70, 0x62, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_rpc_model_deployment_proto_rawDescOnce sync.Once
	file_proto_rpc_model_deployment_proto_rawDescData = file_proto_rpc_model_deployment_proto_rawDesc
)

func file_proto_rpc_model_deployment_proto_rawDescGZIP() []byte {
	file_proto_rpc_model_deployment_proto_rawDescOnce.Do(func() {
		file_proto_rpc_model_deployment_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_rpc_model_deployment_proto_rawDescData)
	})
	return file_proto_rpc_model_deployment_proto_rawDescData
}

var file_proto_rpc_model_deployment_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_proto_rpc_model_deployment_proto_goTypes = []interface{}{
	(*ReadDeploymentReq)(nil),         // 0: proto.ReadDeploymentReq
	(*ReadDeploymentRsp)(nil),         // 1: proto.ReadDeploymentRsp
	(*CreateDeploymentReq)(nil),       // 2: proto.CreateDeploymentReq
	(*CreateDeploymentRsp)(nil),       // 3: proto.CreateDeploymentRsp
	(*UpdateDeploymentReq)(nil),       // 4: proto.UpdateDeploymentReq
	(*UpdateDeploymentRsp)(nil),       // 5: proto.UpdateDeploymentRsp
	(*DeleteDeploymentsReq)(nil),      // 6: proto.DeleteDeploymentsReq
	(*DeleteDeploymentsRsp)(nil),      // 7: proto.DeleteDeploymentsRsp
	(*StartDeploymentsReq)(nil),       // 8: proto.StartDeploymentsReq
	(*StartDeploymentsRsp)(nil),       // 9: proto.StartDeploymentsRsp
	(*StopDeploymentsReq)(nil),        // 10: proto.StopDeploymentsReq
	(*StopDeploymentsRsp)(nil),        // 11: proto.StopDeploymentsRsp
	(*WatchDeploymentReq)(nil),        // 12: proto.WatchDeploymentReq
	(*WatchDeploymentRsp)(nil),        // 13: proto.WatchDeploymentRsp
	(*UpdateDeploymentStateReq)(nil),  // 14: proto.UpdateDeploymentStateReq
	(*UpdateDeploymentStateRsp)(nil),  // 15: proto.UpdateDeploymentStateRsp
	(*UpdateReplicaStatusReq)(nil),    // 16: proto.UpdateReplicaStatusReq
	(*UpdateReplicaStatusRsp)(nil),    // 17: proto.UpdateReplicaStatusRsp
	(*StartDeploymentMonitorReq)(nil), // 18: proto.StartDeploymentMonitorReq
	(*StartDeploymentMonitorRsp)(nil), // 19: proto.StartDeploymentMonitorRsp
	(*StopDeploymentMonitorReq)(nil),  // 20: proto.StopDeploymentMonitorReq
	(*StopDeploymentMonitorRsp)(nil),  // 21: proto.StopDeploymentMonitorRsp
	(*UserContext)(nil),               // 22: proto.UserContext
	(DeploymentType)(0),               // 23: proto.DeploymentType
	(*PageReq)(nil),                   // 24: proto.PageReq
	(ModelKind)(0),                    // 25: proto.ModelKind
	(ModelSubKind)(0),                 // 26: proto.ModelSubKind
	(*ModelDeployment)(nil),           // 27: proto.ModelDeployment
	(*Deployment)(nil),                // 28: proto.Deployment
	(*Status)(nil),                    // 29: proto.Status
}
var file_proto_rpc_model_deployment_proto_depIdxs = []int32{
	22, // 0: proto.ReadDeploymentReq.ctx:type_name -> proto.UserContext
	23, // 1: proto.ReadDeploymentReq.type:type_name -> proto.DeploymentType
	24, // 2: proto.ReadDeploymentReq.page_req:type_name -> proto.PageReq
	25, // 3: proto.ReadDeploymentReq.kind:type_name -> proto.ModelKind
	26, // 4: proto.ReadDeploymentReq.sub_kind:type_name -> proto.ModelSubKind
	27, // 5: proto.ReadDeploymentRsp.model_deployments:type_name -> proto.ModelDeployment
	22, // 6: proto.CreateDeploymentReq.ctx:type_name -> proto.UserContext
	28, // 7: proto.CreateDeploymentReq.deployment:type_name -> proto.Deployment
	28, // 8: proto.CreateDeploymentRsp.deployment:type_name -> proto.Deployment
	22, // 9: proto.UpdateDeploymentReq.ctx:type_name -> proto.UserContext
	28, // 10: proto.UpdateDeploymentReq.deployment:type_name -> proto.Deployment
	28, // 11: proto.UpdateDeploymentRsp.deployment:type_name -> proto.Deployment
	22, // 12: proto.DeleteDeploymentsReq.ctx:type_name -> proto.UserContext
	22, // 13: proto.StartDeploymentsReq.ctx:type_name -> proto.UserContext
	22, // 14: proto.StopDeploymentsReq.ctx:type_name -> proto.UserContext
	22, // 15: proto.WatchDeploymentReq.ctx:type_name -> proto.UserContext
	28, // 16: proto.WatchDeploymentRsp.deployment:type_name -> proto.Deployment
	22, // 17: proto.UpdateDeploymentStateReq.ctx:type_name -> proto.UserContext
	22, // 18: proto.UpdateReplicaStatusReq.ctx:type_name -> proto.UserContext
	29, // 19: proto.UpdateReplicaStatusReq.status:type_name -> proto.Status
	22, // 20: proto.StartDeploymentMonitorReq.ctx:type_name -> proto.UserContext
	22, // 21: proto.StopDeploymentMonitorReq.ctx:type_name -> proto.UserContext
	0,  // 22: proto.DeploymentManager.ReadDeployment:input_type -> proto.ReadDeploymentReq
	2,  // 23: proto.DeploymentManager.CreateDeployment:input_type -> proto.CreateDeploymentReq
	4,  // 24: proto.DeploymentManager.UpdateDeployment:input_type -> proto.UpdateDeploymentReq
	6,  // 25: proto.DeploymentManager.DeleteDeployments:input_type -> proto.DeleteDeploymentsReq
	8,  // 26: proto.DeploymentManager.StartDeployments:input_type -> proto.StartDeploymentsReq
	10, // 27: proto.DeploymentManager.StopDeployments:input_type -> proto.StopDeploymentsReq
	12, // 28: proto.DeploymentManager.WatchDeployment:input_type -> proto.WatchDeploymentReq
	14, // 29: proto.DeploymentManager.UpdateDeploymentState:input_type -> proto.UpdateDeploymentStateReq
	16, // 30: proto.DeploymentManager.UpdateReplicaStatus:input_type -> proto.UpdateReplicaStatusReq
	18, // 31: proto.DeploymentManager.StartDeploymentMonitor:input_type -> proto.StartDeploymentMonitorReq
	20, // 32: proto.DeploymentManager.StopDeploymentMonitor:input_type -> proto.StopDeploymentMonitorReq
	1,  // 33: proto.DeploymentManager.ReadDeployment:output_type -> proto.ReadDeploymentRsp
	3,  // 34: proto.DeploymentManager.CreateDeployment:output_type -> proto.CreateDeploymentRsp
	5,  // 35: proto.DeploymentManager.UpdateDeployment:output_type -> proto.UpdateDeploymentRsp
	7,  // 36: proto.DeploymentManager.DeleteDeployments:output_type -> proto.DeleteDeploymentsRsp
	9,  // 37: proto.DeploymentManager.StartDeployments:output_type -> proto.StartDeploymentsRsp
	11, // 38: proto.DeploymentManager.StopDeployments:output_type -> proto.StopDeploymentsRsp
	13, // 39: proto.DeploymentManager.WatchDeployment:output_type -> proto.WatchDeploymentRsp
	15, // 40: proto.DeploymentManager.UpdateDeploymentState:output_type -> proto.UpdateDeploymentStateRsp
	17, // 41: proto.DeploymentManager.UpdateReplicaStatus:output_type -> proto.UpdateReplicaStatusRsp
	19, // 42: proto.DeploymentManager.StartDeploymentMonitor:output_type -> proto.StartDeploymentMonitorRsp
	21, // 43: proto.DeploymentManager.StopDeploymentMonitor:output_type -> proto.StopDeploymentMonitorRsp
	33, // [33:44] is the sub-list for method output_type
	22, // [22:33] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_proto_rpc_model_deployment_proto_init() }
func file_proto_rpc_model_deployment_proto_init() {
	if File_proto_rpc_model_deployment_proto != nil {
		return
	}
	file_proto_model_proto_init()
	file_proto_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_proto_rpc_model_deployment_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadDeploymentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_deployment_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadDeploymentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_deployment_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDeploymentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_deployment_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDeploymentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_deployment_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDeploymentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_deployment_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDeploymentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_deployment_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteDeploymentsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_deployment_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteDeploymentsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_deployment_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartDeploymentsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_deployment_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartDeploymentsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_deployment_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopDeploymentsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_deployment_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopDeploymentsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_deployment_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WatchDeploymentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_deployment_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WatchDeploymentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_deployment_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDeploymentStateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_deployment_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDeploymentStateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_deployment_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateReplicaStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_deployment_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateReplicaStatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_deployment_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartDeploymentMonitorReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_deployment_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartDeploymentMonitorRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_deployment_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopDeploymentMonitorReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_deployment_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopDeploymentMonitorRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_rpc_model_deployment_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_rpc_model_deployment_proto_goTypes,
		DependencyIndexes: file_proto_rpc_model_deployment_proto_depIdxs,
		MessageInfos:      file_proto_rpc_model_deployment_proto_msgTypes,
	}.Build()
	File_proto_rpc_model_deployment_proto = out.File
	file_proto_rpc_model_deployment_proto_rawDesc = nil
	file_proto_rpc_model_deployment_proto_goTypes = nil
	file_proto_rpc_model_deployment_proto_depIdxs = nil
}
