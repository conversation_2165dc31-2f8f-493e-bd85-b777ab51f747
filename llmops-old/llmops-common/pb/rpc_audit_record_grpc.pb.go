// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.18.1
// source: proto/rpc_audit_record.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	AuditRecordManager_ListAuditRecords_FullMethodName = "/proto.AuditRecordManager/ListAuditRecords"
)

// AuditRecordManagerClient is the client API for AuditRecordManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AuditRecordManagerClient interface {
	ListAuditRecords(ctx context.Context, in *ListAuditRecordsReq, opts ...grpc.CallOption) (*ListAuditRecordsRsp, error)
}

type auditRecordManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewAuditRecordManagerClient(cc grpc.ClientConnInterface) AuditRecordManagerClient {
	return &auditRecordManagerClient{cc}
}

func (c *auditRecordManagerClient) ListAuditRecords(ctx context.Context, in *ListAuditRecordsReq, opts ...grpc.CallOption) (*ListAuditRecordsRsp, error) {
	out := new(ListAuditRecordsRsp)
	err := c.cc.Invoke(ctx, AuditRecordManager_ListAuditRecords_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AuditRecordManagerServer is the server API for AuditRecordManager service.
// All implementations must embed UnimplementedAuditRecordManagerServer
// for forward compatibility
type AuditRecordManagerServer interface {
	ListAuditRecords(context.Context, *ListAuditRecordsReq) (*ListAuditRecordsRsp, error)
	mustEmbedUnimplementedAuditRecordManagerServer()
}

// UnimplementedAuditRecordManagerServer must be embedded to have forward compatible implementations.
type UnimplementedAuditRecordManagerServer struct {
}

func (UnimplementedAuditRecordManagerServer) ListAuditRecords(context.Context, *ListAuditRecordsReq) (*ListAuditRecordsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAuditRecords not implemented")
}
func (UnimplementedAuditRecordManagerServer) mustEmbedUnimplementedAuditRecordManagerServer() {}

// UnsafeAuditRecordManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AuditRecordManagerServer will
// result in compilation errors.
type UnsafeAuditRecordManagerServer interface {
	mustEmbedUnimplementedAuditRecordManagerServer()
}

func RegisterAuditRecordManagerServer(s grpc.ServiceRegistrar, srv AuditRecordManagerServer) {
	s.RegisterService(&AuditRecordManager_ServiceDesc, srv)
}

func _AuditRecordManager_ListAuditRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAuditRecordsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditRecordManagerServer).ListAuditRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuditRecordManager_ListAuditRecords_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditRecordManagerServer).ListAuditRecords(ctx, req.(*ListAuditRecordsReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AuditRecordManager_ServiceDesc is the grpc.ServiceDesc for AuditRecordManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AuditRecordManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "proto.AuditRecordManager",
	HandlerType: (*AuditRecordManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListAuditRecords",
			Handler:    _AuditRecordManager_ListAuditRecords_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/rpc_audit_record.proto",
}
