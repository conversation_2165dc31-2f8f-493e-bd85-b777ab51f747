// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.18.1
// source: proto/rpc_onnx_service.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	OnnxService_OnnxParse_FullMethodName = "/proto.OnnxService/OnnxParse"
)

// OnnxServiceClient is the client API for OnnxService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OnnxServiceClient interface {
	OnnxParse(ctx context.Context, in *OnnxParseReq, opts ...grpc.CallOption) (*OnnxParseRsp, error)
}

type onnxServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOnnxServiceClient(cc grpc.ClientConnInterface) OnnxServiceClient {
	return &onnxServiceClient{cc}
}

func (c *onnxServiceClient) OnnxParse(ctx context.Context, in *OnnxParseReq, opts ...grpc.CallOption) (*OnnxParseRsp, error) {
	out := new(OnnxParseRsp)
	err := c.cc.Invoke(ctx, OnnxService_OnnxParse_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OnnxServiceServer is the server API for OnnxService service.
// All implementations must embed UnimplementedOnnxServiceServer
// for forward compatibility
type OnnxServiceServer interface {
	OnnxParse(context.Context, *OnnxParseReq) (*OnnxParseRsp, error)
	mustEmbedUnimplementedOnnxServiceServer()
}

// UnimplementedOnnxServiceServer must be embedded to have forward compatible implementations.
type UnimplementedOnnxServiceServer struct {
}

func (UnimplementedOnnxServiceServer) OnnxParse(context.Context, *OnnxParseReq) (*OnnxParseRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OnnxParse not implemented")
}
func (UnimplementedOnnxServiceServer) mustEmbedUnimplementedOnnxServiceServer() {}

// UnsafeOnnxServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OnnxServiceServer will
// result in compilation errors.
type UnsafeOnnxServiceServer interface {
	mustEmbedUnimplementedOnnxServiceServer()
}

func RegisterOnnxServiceServer(s grpc.ServiceRegistrar, srv OnnxServiceServer) {
	s.RegisterService(&OnnxService_ServiceDesc, srv)
}

func _OnnxService_OnnxParse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OnnxParseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnnxServiceServer).OnnxParse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OnnxService_OnnxParse_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnnxServiceServer).OnnxParse(ctx, req.(*OnnxParseReq))
	}
	return interceptor(ctx, in, info, handler)
}

// OnnxService_ServiceDesc is the grpc.ServiceDesc for OnnxService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OnnxService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "proto.OnnxService",
	HandlerType: (*OnnxServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "OnnxParse",
			Handler:    _OnnxService_OnnxParse_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/rpc_onnx_service.proto",
}
