// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.18.1
// source: proto/rpc_chat_mgr.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ChatManager_ReadConversations_FullMethodName   = "/proto.ChatManager/ReadConversations"
	ChatManager_DeleteConversations_FullMethodName = "/proto.ChatManager/DeleteConversations"
	ChatManager_RatingConversation_FullMethodName  = "/proto.ChatManager/RatingConversation"
)

// ChatManagerClient is the client API for ChatManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ChatManagerClient interface {
	ReadConversations(ctx context.Context, in *ReadConversationsReq, opts ...grpc.CallOption) (*ReadConversationsRsp, error)
	DeleteConversations(ctx context.Context, in *DeleteConversationsReq, opts ...grpc.CallOption) (*DeleteConversationsRsp, error)
	RatingConversation(ctx context.Context, in *RatingConversationReq, opts ...grpc.CallOption) (*RatingConversationRsp, error)
}

type chatManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewChatManagerClient(cc grpc.ClientConnInterface) ChatManagerClient {
	return &chatManagerClient{cc}
}

func (c *chatManagerClient) ReadConversations(ctx context.Context, in *ReadConversationsReq, opts ...grpc.CallOption) (*ReadConversationsRsp, error) {
	out := new(ReadConversationsRsp)
	err := c.cc.Invoke(ctx, ChatManager_ReadConversations_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatManagerClient) DeleteConversations(ctx context.Context, in *DeleteConversationsReq, opts ...grpc.CallOption) (*DeleteConversationsRsp, error) {
	out := new(DeleteConversationsRsp)
	err := c.cc.Invoke(ctx, ChatManager_DeleteConversations_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatManagerClient) RatingConversation(ctx context.Context, in *RatingConversationReq, opts ...grpc.CallOption) (*RatingConversationRsp, error) {
	out := new(RatingConversationRsp)
	err := c.cc.Invoke(ctx, ChatManager_RatingConversation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChatManagerServer is the server API for ChatManager service.
// All implementations must embed UnimplementedChatManagerServer
// for forward compatibility
type ChatManagerServer interface {
	ReadConversations(context.Context, *ReadConversationsReq) (*ReadConversationsRsp, error)
	DeleteConversations(context.Context, *DeleteConversationsReq) (*DeleteConversationsRsp, error)
	RatingConversation(context.Context, *RatingConversationReq) (*RatingConversationRsp, error)
	mustEmbedUnimplementedChatManagerServer()
}

// UnimplementedChatManagerServer must be embedded to have forward compatible implementations.
type UnimplementedChatManagerServer struct {
}

func (UnimplementedChatManagerServer) ReadConversations(context.Context, *ReadConversationsReq) (*ReadConversationsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReadConversations not implemented")
}
func (UnimplementedChatManagerServer) DeleteConversations(context.Context, *DeleteConversationsReq) (*DeleteConversationsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteConversations not implemented")
}
func (UnimplementedChatManagerServer) RatingConversation(context.Context, *RatingConversationReq) (*RatingConversationRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RatingConversation not implemented")
}
func (UnimplementedChatManagerServer) mustEmbedUnimplementedChatManagerServer() {}

// UnsafeChatManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ChatManagerServer will
// result in compilation errors.
type UnsafeChatManagerServer interface {
	mustEmbedUnimplementedChatManagerServer()
}

func RegisterChatManagerServer(s grpc.ServiceRegistrar, srv ChatManagerServer) {
	s.RegisterService(&ChatManager_ServiceDesc, srv)
}

func _ChatManager_ReadConversations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReadConversationsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatManagerServer).ReadConversations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChatManager_ReadConversations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatManagerServer).ReadConversations(ctx, req.(*ReadConversationsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChatManager_DeleteConversations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteConversationsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatManagerServer).DeleteConversations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChatManager_DeleteConversations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatManagerServer).DeleteConversations(ctx, req.(*DeleteConversationsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChatManager_RatingConversation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RatingConversationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatManagerServer).RatingConversation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChatManager_RatingConversation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatManagerServer).RatingConversation(ctx, req.(*RatingConversationReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ChatManager_ServiceDesc is the grpc.ServiceDesc for ChatManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ChatManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "proto.ChatManager",
	HandlerType: (*ChatManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ReadConversations",
			Handler:    _ChatManager_ReadConversations_Handler,
		},
		{
			MethodName: "DeleteConversations",
			Handler:    _ChatManager_DeleteConversations_Handler,
		},
		{
			MethodName: "RatingConversation",
			Handler:    _ChatManager_RatingConversation_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/rpc_chat_mgr.proto",
}
