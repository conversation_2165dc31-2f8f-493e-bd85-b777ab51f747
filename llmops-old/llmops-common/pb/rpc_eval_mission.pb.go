// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/rpc_eval_mission.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReadEvalMissionsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Id  string       `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *ReadEvalMissionsReq) Reset() {
	*x = ReadEvalMissionsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_eval_mission_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadEvalMissionsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadEvalMissionsReq) ProtoMessage() {}

func (x *ReadEvalMissionsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_eval_mission_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadEvalMissionsReq.ProtoReflect.Descriptor instead.
func (*ReadEvalMissionsReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_eval_mission_proto_rawDescGZIP(), []int{0}
}

func (x *ReadEvalMissionsReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *ReadEvalMissionsReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type ReadEvalMissionsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EvalMissions []*EvalMission `protobuf:"bytes,1,rep,name=eval_missions,json=evalMissions,proto3" json:"eval_missions,omitempty"`
}

func (x *ReadEvalMissionsRsp) Reset() {
	*x = ReadEvalMissionsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_eval_mission_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadEvalMissionsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadEvalMissionsRsp) ProtoMessage() {}

func (x *ReadEvalMissionsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_eval_mission_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadEvalMissionsRsp.ProtoReflect.Descriptor instead.
func (*ReadEvalMissionsRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_eval_mission_proto_rawDescGZIP(), []int{1}
}

func (x *ReadEvalMissionsRsp) GetEvalMissions() []*EvalMission {
	if x != nil {
		return x.EvalMissions
	}
	return nil
}

type CreateEvalMissionsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx         *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	EvalMission *EvalMission `protobuf:"bytes,2,opt,name=eval_mission,json=evalMission,proto3" json:"eval_mission,omitempty"`
}

func (x *CreateEvalMissionsReq) Reset() {
	*x = CreateEvalMissionsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_eval_mission_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEvalMissionsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEvalMissionsReq) ProtoMessage() {}

func (x *CreateEvalMissionsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_eval_mission_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEvalMissionsReq.ProtoReflect.Descriptor instead.
func (*CreateEvalMissionsReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_eval_mission_proto_rawDescGZIP(), []int{2}
}

func (x *CreateEvalMissionsReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *CreateEvalMissionsReq) GetEvalMission() *EvalMission {
	if x != nil {
		return x.EvalMission
	}
	return nil
}

type CreateEvalMissionsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EvalMission *EvalMission `protobuf:"bytes,1,opt,name=eval_mission,json=evalMission,proto3" json:"eval_mission,omitempty"`
}

func (x *CreateEvalMissionsRsp) Reset() {
	*x = CreateEvalMissionsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_eval_mission_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEvalMissionsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEvalMissionsRsp) ProtoMessage() {}

func (x *CreateEvalMissionsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_eval_mission_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEvalMissionsRsp.ProtoReflect.Descriptor instead.
func (*CreateEvalMissionsRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_eval_mission_proto_rawDescGZIP(), []int{3}
}

func (x *CreateEvalMissionsRsp) GetEvalMission() *EvalMission {
	if x != nil {
		return x.EvalMission
	}
	return nil
}

type UpdateEvalMissionsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx         *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	EvalMission *EvalMission `protobuf:"bytes,2,opt,name=eval_mission,json=evalMission,proto3" json:"eval_mission,omitempty"`
}

func (x *UpdateEvalMissionsReq) Reset() {
	*x = UpdateEvalMissionsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_eval_mission_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEvalMissionsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEvalMissionsReq) ProtoMessage() {}

func (x *UpdateEvalMissionsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_eval_mission_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEvalMissionsReq.ProtoReflect.Descriptor instead.
func (*UpdateEvalMissionsReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_eval_mission_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateEvalMissionsReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *UpdateEvalMissionsReq) GetEvalMission() *EvalMission {
	if x != nil {
		return x.EvalMission
	}
	return nil
}

type UpdateEvalMissionsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EvalMission *EvalMission `protobuf:"bytes,1,opt,name=eval_mission,json=evalMission,proto3" json:"eval_mission,omitempty"`
}

func (x *UpdateEvalMissionsRsp) Reset() {
	*x = UpdateEvalMissionsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_eval_mission_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEvalMissionsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEvalMissionsRsp) ProtoMessage() {}

func (x *UpdateEvalMissionsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_eval_mission_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEvalMissionsRsp.ProtoReflect.Descriptor instead.
func (*UpdateEvalMissionsRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_eval_mission_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateEvalMissionsRsp) GetEvalMission() *EvalMission {
	if x != nil {
		return x.EvalMission
	}
	return nil
}

type DeleteEvalMissionsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Ids []string     `protobuf:"bytes,2,rep,name=ids,proto3" json:"ids,omitempty"`
}

func (x *DeleteEvalMissionsReq) Reset() {
	*x = DeleteEvalMissionsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_eval_mission_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteEvalMissionsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteEvalMissionsReq) ProtoMessage() {}

func (x *DeleteEvalMissionsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_eval_mission_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteEvalMissionsReq.ProtoReflect.Descriptor instead.
func (*DeleteEvalMissionsReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_eval_mission_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteEvalMissionsReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *DeleteEvalMissionsReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type DeleteEvalMissionsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteEvalMissionsRsp) Reset() {
	*x = DeleteEvalMissionsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_eval_mission_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteEvalMissionsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteEvalMissionsRsp) ProtoMessage() {}

func (x *DeleteEvalMissionsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_eval_mission_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteEvalMissionsRsp.ProtoReflect.Descriptor instead.
func (*DeleteEvalMissionsRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_eval_mission_proto_rawDescGZIP(), []int{7}
}

type StartEvalMissionsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Ids []string     `protobuf:"bytes,2,rep,name=ids,proto3" json:"ids,omitempty"`
}

func (x *StartEvalMissionsReq) Reset() {
	*x = StartEvalMissionsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_eval_mission_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartEvalMissionsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartEvalMissionsReq) ProtoMessage() {}

func (x *StartEvalMissionsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_eval_mission_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartEvalMissionsReq.ProtoReflect.Descriptor instead.
func (*StartEvalMissionsReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_eval_mission_proto_rawDescGZIP(), []int{8}
}

func (x *StartEvalMissionsReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *StartEvalMissionsReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type StartEvalMissionsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StartEvalMissionsRsp) Reset() {
	*x = StartEvalMissionsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_eval_mission_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartEvalMissionsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartEvalMissionsRsp) ProtoMessage() {}

func (x *StartEvalMissionsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_eval_mission_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartEvalMissionsRsp.ProtoReflect.Descriptor instead.
func (*StartEvalMissionsRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_eval_mission_proto_rawDescGZIP(), []int{9}
}

type StopEvalMissionsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Ids []string     `protobuf:"bytes,2,rep,name=ids,proto3" json:"ids,omitempty"`
}

func (x *StopEvalMissionsReq) Reset() {
	*x = StopEvalMissionsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_eval_mission_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopEvalMissionsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopEvalMissionsReq) ProtoMessage() {}

func (x *StopEvalMissionsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_eval_mission_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopEvalMissionsReq.ProtoReflect.Descriptor instead.
func (*StopEvalMissionsReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_eval_mission_proto_rawDescGZIP(), []int{10}
}

func (x *StopEvalMissionsReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *StopEvalMissionsReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type StopEvalMissionsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StopEvalMissionsRsp) Reset() {
	*x = StopEvalMissionsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_eval_mission_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopEvalMissionsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopEvalMissionsRsp) ProtoMessage() {}

func (x *StopEvalMissionsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_eval_mission_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopEvalMissionsRsp.ProtoReflect.Descriptor instead.
func (*StopEvalMissionsRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_eval_mission_proto_rawDescGZIP(), []int{11}
}

type GetEvalMissionResultReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx  *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Id   string       `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Lang string       `protobuf:"bytes,3,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *GetEvalMissionResultReq) Reset() {
	*x = GetEvalMissionResultReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_eval_mission_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEvalMissionResultReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEvalMissionResultReq) ProtoMessage() {}

func (x *GetEvalMissionResultReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_eval_mission_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEvalMissionResultReq.ProtoReflect.Descriptor instead.
func (*GetEvalMissionResultReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_eval_mission_proto_rawDescGZIP(), []int{12}
}

func (x *GetEvalMissionResultReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *GetEvalMissionResultReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GetEvalMissionResultReq) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type GetEvalMissionResultRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *Evaluation `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *GetEvalMissionResultRsp) Reset() {
	*x = GetEvalMissionResultRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_eval_mission_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEvalMissionResultRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEvalMissionResultRsp) ProtoMessage() {}

func (x *GetEvalMissionResultRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_eval_mission_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEvalMissionResultRsp.ProtoReflect.Descriptor instead.
func (*GetEvalMissionResultRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_eval_mission_proto_rawDescGZIP(), []int{13}
}

func (x *GetEvalMissionResultRsp) GetResult() *Evaluation {
	if x != nil {
		return x.Result
	}
	return nil
}

var File_proto_rpc_eval_mission_proto protoreflect.FileDescriptor

var file_proto_rpc_eval_mission_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x70, 0x63, 0x5f, 0x65, 0x76, 0x61, 0x6c,
	0x5f, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x4b, 0x0a, 0x13, 0x52, 0x65, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x4e,
	0x0a, 0x13, 0x52, 0x65, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x73, 0x70, 0x12, 0x37, 0x0a, 0x0d, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x0c, 0x65, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x74,
	0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x35, 0x0a,
	0x0c, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c,
	0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x22, 0x4e, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76,
	0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x73, 0x70, 0x12, 0x35, 0x0a,
	0x0c, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c,
	0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x22, 0x74, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76,
	0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a,
	0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03,
	0x63, 0x74, 0x78, 0x12, 0x35, 0x0a, 0x0c, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x65,
	0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x4e, 0x0a, 0x15, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x73, 0x70, 0x12, 0x35, 0x0a, 0x0c, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x65,
	0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x4f, 0x0a, 0x15, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x17, 0x0a, 0x15, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x73, 0x70, 0x22, 0x4e, 0x0a, 0x14, 0x53, 0x74, 0x61, 0x72, 0x74, 0x45, 0x76, 0x61,
	0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03,
	0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63,
	0x74, 0x78, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x03, 0x69, 0x64, 0x73, 0x22, 0x16, 0x0a, 0x14, 0x53, 0x74, 0x61, 0x72, 0x74, 0x45, 0x76, 0x61,
	0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x73, 0x70, 0x22, 0x4d, 0x0a, 0x13,
	0x53, 0x74, 0x6f, 0x70, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x15, 0x0a, 0x13, 0x53,
	0x74, 0x6f, 0x70, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x73, 0x70, 0x22, 0x63, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a,
	0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03,
	0x63, 0x74, 0x78, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22, 0x44, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x45, 0x76,
	0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x73, 0x70, 0x12, 0x29, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x32, 0xc3, 0x04,
	0x0a, 0x0e, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x67, 0x72,
	0x12, 0x4a, 0x0a, 0x10, 0x52, 0x65, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x61,
	0x64, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71,
	0x1a, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x45, 0x76, 0x61,
	0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x73, 0x70, 0x12, 0x4f, 0x0a, 0x11,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x1a,
	0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76,
	0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x73, 0x70, 0x12, 0x4f, 0x0a,
	0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71,
	0x1a, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45,
	0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x73, 0x70, 0x12, 0x50,
	0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x73, 0x70,
	0x12, 0x4d, 0x0a, 0x11, 0x53, 0x74, 0x61, 0x72, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x73, 0x70, 0x12,
	0x4a, 0x0a, 0x10, 0x53, 0x74, 0x6f, 0x70, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x6f, 0x70,
	0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x1a,
	0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x45, 0x76, 0x61, 0x6c,
	0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x73, 0x70, 0x12, 0x56, 0x0a, 0x14, 0x47,
	0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x45,
	0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x45,
	0x76, 0x61, 0x6c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x73, 0x70, 0x42, 0x26, 0x5a, 0x24, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70,
	0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_proto_rpc_eval_mission_proto_rawDescOnce sync.Once
	file_proto_rpc_eval_mission_proto_rawDescData = file_proto_rpc_eval_mission_proto_rawDesc
)

func file_proto_rpc_eval_mission_proto_rawDescGZIP() []byte {
	file_proto_rpc_eval_mission_proto_rawDescOnce.Do(func() {
		file_proto_rpc_eval_mission_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_rpc_eval_mission_proto_rawDescData)
	})
	return file_proto_rpc_eval_mission_proto_rawDescData
}

var file_proto_rpc_eval_mission_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_proto_rpc_eval_mission_proto_goTypes = []interface{}{
	(*ReadEvalMissionsReq)(nil),     // 0: proto.ReadEvalMissionsReq
	(*ReadEvalMissionsRsp)(nil),     // 1: proto.ReadEvalMissionsRsp
	(*CreateEvalMissionsReq)(nil),   // 2: proto.CreateEvalMissionsReq
	(*CreateEvalMissionsRsp)(nil),   // 3: proto.CreateEvalMissionsRsp
	(*UpdateEvalMissionsReq)(nil),   // 4: proto.UpdateEvalMissionsReq
	(*UpdateEvalMissionsRsp)(nil),   // 5: proto.UpdateEvalMissionsRsp
	(*DeleteEvalMissionsReq)(nil),   // 6: proto.DeleteEvalMissionsReq
	(*DeleteEvalMissionsRsp)(nil),   // 7: proto.DeleteEvalMissionsRsp
	(*StartEvalMissionsReq)(nil),    // 8: proto.StartEvalMissionsReq
	(*StartEvalMissionsRsp)(nil),    // 9: proto.StartEvalMissionsRsp
	(*StopEvalMissionsReq)(nil),     // 10: proto.StopEvalMissionsReq
	(*StopEvalMissionsRsp)(nil),     // 11: proto.StopEvalMissionsRsp
	(*GetEvalMissionResultReq)(nil), // 12: proto.GetEvalMissionResultReq
	(*GetEvalMissionResultRsp)(nil), // 13: proto.GetEvalMissionResultRsp
	(*UserContext)(nil),             // 14: proto.UserContext
	(*EvalMission)(nil),             // 15: proto.EvalMission
	(*Evaluation)(nil),              // 16: proto.Evaluation
}
var file_proto_rpc_eval_mission_proto_depIdxs = []int32{
	14, // 0: proto.ReadEvalMissionsReq.ctx:type_name -> proto.UserContext
	15, // 1: proto.ReadEvalMissionsRsp.eval_missions:type_name -> proto.EvalMission
	14, // 2: proto.CreateEvalMissionsReq.ctx:type_name -> proto.UserContext
	15, // 3: proto.CreateEvalMissionsReq.eval_mission:type_name -> proto.EvalMission
	15, // 4: proto.CreateEvalMissionsRsp.eval_mission:type_name -> proto.EvalMission
	14, // 5: proto.UpdateEvalMissionsReq.ctx:type_name -> proto.UserContext
	15, // 6: proto.UpdateEvalMissionsReq.eval_mission:type_name -> proto.EvalMission
	15, // 7: proto.UpdateEvalMissionsRsp.eval_mission:type_name -> proto.EvalMission
	14, // 8: proto.DeleteEvalMissionsReq.ctx:type_name -> proto.UserContext
	14, // 9: proto.StartEvalMissionsReq.ctx:type_name -> proto.UserContext
	14, // 10: proto.StopEvalMissionsReq.ctx:type_name -> proto.UserContext
	14, // 11: proto.GetEvalMissionResultReq.ctx:type_name -> proto.UserContext
	16, // 12: proto.GetEvalMissionResultRsp.result:type_name -> proto.Evaluation
	0,  // 13: proto.EvalMissionMgr.ReadEvalMissions:input_type -> proto.ReadEvalMissionsReq
	2,  // 14: proto.EvalMissionMgr.CreateEvalMission:input_type -> proto.CreateEvalMissionsReq
	4,  // 15: proto.EvalMissionMgr.UpdateEvalMission:input_type -> proto.UpdateEvalMissionsReq
	6,  // 16: proto.EvalMissionMgr.DeleteEvalMissions:input_type -> proto.DeleteEvalMissionsReq
	8,  // 17: proto.EvalMissionMgr.StartEvalMissions:input_type -> proto.StartEvalMissionsReq
	10, // 18: proto.EvalMissionMgr.StopEvalMissions:input_type -> proto.StopEvalMissionsReq
	12, // 19: proto.EvalMissionMgr.GetEvalMissionResult:input_type -> proto.GetEvalMissionResultReq
	1,  // 20: proto.EvalMissionMgr.ReadEvalMissions:output_type -> proto.ReadEvalMissionsRsp
	3,  // 21: proto.EvalMissionMgr.CreateEvalMission:output_type -> proto.CreateEvalMissionsRsp
	5,  // 22: proto.EvalMissionMgr.UpdateEvalMission:output_type -> proto.UpdateEvalMissionsRsp
	7,  // 23: proto.EvalMissionMgr.DeleteEvalMissions:output_type -> proto.DeleteEvalMissionsRsp
	9,  // 24: proto.EvalMissionMgr.StartEvalMissions:output_type -> proto.StartEvalMissionsRsp
	11, // 25: proto.EvalMissionMgr.StopEvalMissions:output_type -> proto.StopEvalMissionsRsp
	13, // 26: proto.EvalMissionMgr.GetEvalMissionResult:output_type -> proto.GetEvalMissionResultRsp
	20, // [20:27] is the sub-list for method output_type
	13, // [13:20] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_proto_rpc_eval_mission_proto_init() }
func file_proto_rpc_eval_mission_proto_init() {
	if File_proto_rpc_eval_mission_proto != nil {
		return
	}
	file_proto_common_proto_init()
	file_proto_evaluation_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_proto_rpc_eval_mission_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadEvalMissionsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_eval_mission_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadEvalMissionsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_eval_mission_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEvalMissionsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_eval_mission_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEvalMissionsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_eval_mission_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEvalMissionsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_eval_mission_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEvalMissionsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_eval_mission_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteEvalMissionsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_eval_mission_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteEvalMissionsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_eval_mission_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartEvalMissionsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_eval_mission_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartEvalMissionsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_eval_mission_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopEvalMissionsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_eval_mission_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopEvalMissionsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_eval_mission_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEvalMissionResultReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_eval_mission_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEvalMissionResultRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_rpc_eval_mission_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_rpc_eval_mission_proto_goTypes,
		DependencyIndexes: file_proto_rpc_eval_mission_proto_depIdxs,
		MessageInfos:      file_proto_rpc_eval_mission_proto_msgTypes,
	}.Build()
	File_proto_rpc_eval_mission_proto = out.File
	file_proto_rpc_eval_mission_proto_rawDesc = nil
	file_proto_rpc_eval_mission_proto_goTypes = nil
	file_proto_rpc_eval_mission_proto_depIdxs = nil
}
