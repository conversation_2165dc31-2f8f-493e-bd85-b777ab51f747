// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/rpc_audit_record.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ListAuditRecordsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext *UserContext             `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	Since       int64                    `protobuf:"varint,2,opt,name=since,proto3" json:"since,omitempty"`
	UserId      string                   `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Modules     []AuditRecordModule      `protobuf:"varint,4,rep,packed,name=modules,proto3,enum=proto.AuditRecordModule" json:"modules,omitempty"`
	SubModules  []AuditRecordSubModule   `protobuf:"varint,5,rep,packed,name=sub_modules,json=subModules,proto3,enum=proto.AuditRecordSubModule" json:"sub_modules,omitempty"`
	OpTypes     []AuditRecordOperateType `protobuf:"varint,6,rep,packed,name=op_types,json=opTypes,proto3,enum=proto.AuditRecordOperateType" json:"op_types,omitempty"`
	// @gotags: description:"限制最大返回条数, 小于等于0时不限制"
	Limit int64 `protobuf:"varint,7,opt,name=limit,proto3" json:"limit,omitempty" description:"限制最大返回条数, 小于等于0时不限制"`
}

func (x *ListAuditRecordsReq) Reset() {
	*x = ListAuditRecordsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_audit_record_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAuditRecordsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAuditRecordsReq) ProtoMessage() {}

func (x *ListAuditRecordsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_audit_record_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAuditRecordsReq.ProtoReflect.Descriptor instead.
func (*ListAuditRecordsReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_audit_record_proto_rawDescGZIP(), []int{0}
}

func (x *ListAuditRecordsReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *ListAuditRecordsReq) GetSince() int64 {
	if x != nil {
		return x.Since
	}
	return 0
}

func (x *ListAuditRecordsReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ListAuditRecordsReq) GetModules() []AuditRecordModule {
	if x != nil {
		return x.Modules
	}
	return nil
}

func (x *ListAuditRecordsReq) GetSubModules() []AuditRecordSubModule {
	if x != nil {
		return x.SubModules
	}
	return nil
}

func (x *ListAuditRecordsReq) GetOpTypes() []AuditRecordOperateType {
	if x != nil {
		return x.OpTypes
	}
	return nil
}

func (x *ListAuditRecordsReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type ListAuditRecordsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Records []*AuditRecord `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
}

func (x *ListAuditRecordsRsp) Reset() {
	*x = ListAuditRecordsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_audit_record_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAuditRecordsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAuditRecordsRsp) ProtoMessage() {}

func (x *ListAuditRecordsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_audit_record_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAuditRecordsRsp.ProtoReflect.Descriptor instead.
func (*ListAuditRecordsRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_audit_record_proto_rawDescGZIP(), []int{1}
}

func (x *ListAuditRecordsRsp) GetRecords() []*AuditRecord {
	if x != nil {
		return x.Records
	}
	return nil
}

var File_proto_rpc_audit_record_proto protoreflect.FileDescriptor

var file_proto_rpc_audit_record_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x70, 0x63, 0x5f, 0x61, 0x75, 0x64, 0x69,
	0x74, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x75, 0x64,
	0x69, 0x74, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x12, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xbd, 0x02, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x75, 0x64, 0x69,
	0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x32, 0x0a, 0x07, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x07, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x3c, 0x0a, 0x0b, 0x73, 0x75, 0x62, 0x5f, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x75,
	0x62, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x4d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x73, 0x12, 0x38, 0x0a, 0x08, 0x6f, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x75,
	0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x6f, 0x70, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x14, 0x0a,
	0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x22, 0x43, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x75, 0x64, 0x69, 0x74,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x73, 0x70, 0x12, 0x2c, 0x0a, 0x07, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x32, 0x60, 0x0a, 0x12, 0x41, 0x75, 0x64, 0x69,
	0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x4a,
	0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x73, 0x12, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41,
	0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1a,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x75, 0x64, 0x69, 0x74,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x73, 0x70, 0x42, 0x26, 0x5a, 0x24, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c,
	0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x3b,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_rpc_audit_record_proto_rawDescOnce sync.Once
	file_proto_rpc_audit_record_proto_rawDescData = file_proto_rpc_audit_record_proto_rawDesc
)

func file_proto_rpc_audit_record_proto_rawDescGZIP() []byte {
	file_proto_rpc_audit_record_proto_rawDescOnce.Do(func() {
		file_proto_rpc_audit_record_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_rpc_audit_record_proto_rawDescData)
	})
	return file_proto_rpc_audit_record_proto_rawDescData
}

var file_proto_rpc_audit_record_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_proto_rpc_audit_record_proto_goTypes = []interface{}{
	(*ListAuditRecordsReq)(nil), // 0: proto.ListAuditRecordsReq
	(*ListAuditRecordsRsp)(nil), // 1: proto.ListAuditRecordsRsp
	(*UserContext)(nil),         // 2: proto.UserContext
	(AuditRecordModule)(0),      // 3: proto.AuditRecordModule
	(AuditRecordSubModule)(0),   // 4: proto.AuditRecordSubModule
	(AuditRecordOperateType)(0), // 5: proto.AuditRecordOperateType
	(*AuditRecord)(nil),         // 6: proto.AuditRecord
}
var file_proto_rpc_audit_record_proto_depIdxs = []int32{
	2, // 0: proto.ListAuditRecordsReq.user_context:type_name -> proto.UserContext
	3, // 1: proto.ListAuditRecordsReq.modules:type_name -> proto.AuditRecordModule
	4, // 2: proto.ListAuditRecordsReq.sub_modules:type_name -> proto.AuditRecordSubModule
	5, // 3: proto.ListAuditRecordsReq.op_types:type_name -> proto.AuditRecordOperateType
	6, // 4: proto.ListAuditRecordsRsp.records:type_name -> proto.AuditRecord
	0, // 5: proto.AuditRecordManager.ListAuditRecords:input_type -> proto.ListAuditRecordsReq
	1, // 6: proto.AuditRecordManager.ListAuditRecords:output_type -> proto.ListAuditRecordsRsp
	6, // [6:7] is the sub-list for method output_type
	5, // [5:6] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_proto_rpc_audit_record_proto_init() }
func file_proto_rpc_audit_record_proto_init() {
	if File_proto_rpc_audit_record_proto != nil {
		return
	}
	file_proto_audit_record_proto_init()
	file_proto_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_proto_rpc_audit_record_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAuditRecordsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_audit_record_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAuditRecordsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_rpc_audit_record_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_rpc_audit_record_proto_goTypes,
		DependencyIndexes: file_proto_rpc_audit_record_proto_depIdxs,
		MessageInfos:      file_proto_rpc_audit_record_proto_msgTypes,
	}.Build()
	File_proto_rpc_audit_record_proto = out.File
	file_proto_rpc_audit_record_proto_rawDesc = nil
	file_proto_rpc_audit_record_proto_goTypes = nil
	file_proto_rpc_audit_record_proto_depIdxs = nil
}
