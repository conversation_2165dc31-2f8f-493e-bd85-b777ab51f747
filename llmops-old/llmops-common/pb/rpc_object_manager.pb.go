// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/rpc_object_manager.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ListObjectsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListObjectsReq) Reset() {
	*x = ListObjectsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_object_manager_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListObjectsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListObjectsReq) ProtoMessage() {}

func (x *ListObjectsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_object_manager_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListObjectsReq.ProtoReflect.Descriptor instead.
func (*ListObjectsReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_object_manager_proto_rawDescGZIP(), []int{0}
}

type ListObjectsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListObjectsRsp) Reset() {
	*x = ListObjectsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_object_manager_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListObjectsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListObjectsRsp) ProtoMessage() {}

func (x *ListObjectsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_object_manager_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListObjectsRsp.ProtoReflect.Descriptor instead.
func (*ListObjectsRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_object_manager_proto_rawDescGZIP(), []int{1}
}

type DeleteObjectReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteObjectReq) Reset() {
	*x = DeleteObjectReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_object_manager_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteObjectReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteObjectReq) ProtoMessage() {}

func (x *DeleteObjectReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_object_manager_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteObjectReq.ProtoReflect.Descriptor instead.
func (*DeleteObjectReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_object_manager_proto_rawDescGZIP(), []int{2}
}

type DeleteObjectRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteObjectRsp) Reset() {
	*x = DeleteObjectRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_object_manager_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteObjectRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteObjectRsp) ProtoMessage() {}

func (x *DeleteObjectRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_object_manager_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteObjectRsp.ProtoReflect.Descriptor instead.
func (*DeleteObjectRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_object_manager_proto_rawDescGZIP(), []int{3}
}

type UploadObjectReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId      string `protobuf:"bytes,1,opt,name=modelId,proto3" json:"modelId,omitempty"`
	ReleaseId    string `protobuf:"bytes,2,opt,name=releaseId,proto3" json:"releaseId,omitempty"`
	Filename     string `protobuf:"bytes,3,opt,name=filename,proto3" json:"filename,omitempty"`
	AttachmentId string `protobuf:"bytes,4,opt,name=attachmentId,proto3" json:"attachmentId,omitempty"`
	Encrypt      bool   `protobuf:"varint,5,opt,name=encrypt,proto3" json:"encrypt,omitempty"`
	Data         []byte `protobuf:"bytes,6,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *UploadObjectReq) Reset() {
	*x = UploadObjectReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_object_manager_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadObjectReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadObjectReq) ProtoMessage() {}

func (x *UploadObjectReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_object_manager_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadObjectReq.ProtoReflect.Descriptor instead.
func (*UploadObjectReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_object_manager_proto_rawDescGZIP(), []int{4}
}

func (x *UploadObjectReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *UploadObjectReq) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

func (x *UploadObjectReq) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *UploadObjectReq) GetAttachmentId() string {
	if x != nil {
		return x.AttachmentId
	}
	return ""
}

func (x *UploadObjectReq) GetEncrypt() bool {
	if x != nil {
		return x.Encrypt
	}
	return false
}

func (x *UploadObjectReq) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type UploadObjectRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UploadObjectRsp) Reset() {
	*x = UploadObjectRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_object_manager_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadObjectRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadObjectRsp) ProtoMessage() {}

func (x *UploadObjectRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_object_manager_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadObjectRsp.ProtoReflect.Descriptor instead.
func (*UploadObjectRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_object_manager_proto_rawDescGZIP(), []int{5}
}

type DownloadObjectReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId      string `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ReleaseId    string `protobuf:"bytes,2,opt,name=release_id,json=releaseId,proto3" json:"release_id,omitempty"`
	AttachmentId string `protobuf:"bytes,3,opt,name=attachment_id,json=attachmentId,proto3" json:"attachment_id,omitempty"`
}

func (x *DownloadObjectReq) Reset() {
	*x = DownloadObjectReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_object_manager_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadObjectReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadObjectReq) ProtoMessage() {}

func (x *DownloadObjectReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_object_manager_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadObjectReq.ProtoReflect.Descriptor instead.
func (*DownloadObjectReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_object_manager_proto_rawDescGZIP(), []int{6}
}

func (x *DownloadObjectReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *DownloadObjectReq) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

func (x *DownloadObjectReq) GetAttachmentId() string {
	if x != nil {
		return x.AttachmentId
	}
	return ""
}

type DownloadObjectRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filename string `protobuf:"bytes,1,opt,name=filename,proto3" json:"filename,omitempty"`
	Data     []byte `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *DownloadObjectRsp) Reset() {
	*x = DownloadObjectRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_object_manager_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadObjectRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadObjectRsp) ProtoMessage() {}

func (x *DownloadObjectRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_object_manager_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadObjectRsp.ProtoReflect.Descriptor instead.
func (*DownloadObjectRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_object_manager_proto_rawDescGZIP(), []int{7}
}

func (x *DownloadObjectRsp) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *DownloadObjectRsp) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_proto_rpc_object_manager_proto protoreflect.FileDescriptor

var file_proto_rpc_object_manager_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x70, 0x63, 0x5f, 0x6f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x10, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x4f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x52, 0x65, 0x71, 0x22, 0x10, 0x0a, 0x0e, 0x4c, 0x69, 0x73,
	0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x52, 0x73, 0x70, 0x22, 0x11, 0x0a, 0x0f, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x22, 0x11,
	0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x73,
	0x70, 0x22, 0xb7, 0x01, 0x0a, 0x0f, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12,
	0x1c, 0x0a, 0x09, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x74, 0x74,
	0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x11, 0x0a, 0x0f, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x73, 0x70, 0x22, 0x72,
	0x0a, 0x11, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x22, 0x43, 0x0a, 0x11, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x52, 0x73, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x32, 0x96, 0x02, 0x0a, 0x0d, 0x4f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x3b, 0x0a, 0x0b, 0x4c, 0x69, 0x73,
	0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x52, 0x65, 0x71, 0x1a,
	0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x73, 0x52, 0x73, 0x70, 0x12, 0x3e, 0x0a, 0x0c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x16,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x52, 0x73, 0x70, 0x12, 0x40, 0x0a, 0x0c, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x16,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x52, 0x73, 0x70, 0x28, 0x01, 0x12, 0x46, 0x0a, 0x0e, 0x44, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x18, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x73, 0x70, 0x30, 0x01,
	0x42, 0x26, 0x5a, 0x24, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f,
	0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_rpc_object_manager_proto_rawDescOnce sync.Once
	file_proto_rpc_object_manager_proto_rawDescData = file_proto_rpc_object_manager_proto_rawDesc
)

func file_proto_rpc_object_manager_proto_rawDescGZIP() []byte {
	file_proto_rpc_object_manager_proto_rawDescOnce.Do(func() {
		file_proto_rpc_object_manager_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_rpc_object_manager_proto_rawDescData)
	})
	return file_proto_rpc_object_manager_proto_rawDescData
}

var file_proto_rpc_object_manager_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_proto_rpc_object_manager_proto_goTypes = []interface{}{
	(*ListObjectsReq)(nil),    // 0: proto.ListObjectsReq
	(*ListObjectsRsp)(nil),    // 1: proto.ListObjectsRsp
	(*DeleteObjectReq)(nil),   // 2: proto.DeleteObjectReq
	(*DeleteObjectRsp)(nil),   // 3: proto.DeleteObjectRsp
	(*UploadObjectReq)(nil),   // 4: proto.UploadObjectReq
	(*UploadObjectRsp)(nil),   // 5: proto.UploadObjectRsp
	(*DownloadObjectReq)(nil), // 6: proto.DownloadObjectReq
	(*DownloadObjectRsp)(nil), // 7: proto.DownloadObjectRsp
}
var file_proto_rpc_object_manager_proto_depIdxs = []int32{
	0, // 0: proto.ObjectManager.ListObjects:input_type -> proto.ListObjectsReq
	2, // 1: proto.ObjectManager.DeleteObject:input_type -> proto.DeleteObjectReq
	4, // 2: proto.ObjectManager.UploadObject:input_type -> proto.UploadObjectReq
	6, // 3: proto.ObjectManager.DownloadObject:input_type -> proto.DownloadObjectReq
	1, // 4: proto.ObjectManager.ListObjects:output_type -> proto.ListObjectsRsp
	3, // 5: proto.ObjectManager.DeleteObject:output_type -> proto.DeleteObjectRsp
	5, // 6: proto.ObjectManager.UploadObject:output_type -> proto.UploadObjectRsp
	7, // 7: proto.ObjectManager.DownloadObject:output_type -> proto.DownloadObjectRsp
	4, // [4:8] is the sub-list for method output_type
	0, // [0:4] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proto_rpc_object_manager_proto_init() }
func file_proto_rpc_object_manager_proto_init() {
	if File_proto_rpc_object_manager_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_rpc_object_manager_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListObjectsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_object_manager_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListObjectsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_object_manager_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteObjectReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_object_manager_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteObjectRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_object_manager_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadObjectReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_object_manager_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadObjectRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_object_manager_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadObjectReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_object_manager_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadObjectRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_rpc_object_manager_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_rpc_object_manager_proto_goTypes,
		DependencyIndexes: file_proto_rpc_object_manager_proto_depIdxs,
		MessageInfos:      file_proto_rpc_object_manager_proto_msgTypes,
	}.Build()
	File_proto_rpc_object_manager_proto = out.File
	file_proto_rpc_object_manager_proto_rawDesc = nil
	file_proto_rpc_object_manager_proto_goTypes = nil
	file_proto_rpc_object_manager_proto_depIdxs = nil
}
