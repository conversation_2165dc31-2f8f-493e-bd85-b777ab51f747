// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/data_connection.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ConnectionType int32

const (
	ConnectionType_HIPPO       ConnectionType = 0
	ConnectionType_SCOPE       ConnectionType = 1
	ConnectionType_STELLAR_DB  ConnectionType = 2
	ConnectionType_AWS_S3      ConnectionType = 10 // aws_s3
	ConnectionType_MINIO       ConnectionType = 11 // minio
	ConnectionType_ALI_OSS     ConnectionType = 12 // 阿里云OSS
	ConnectionType_TENCENT_COS ConnectionType = 13 // 腾讯云COS
)

// Enum value maps for ConnectionType.
var (
	ConnectionType_name = map[int32]string{
		0:  "HIPPO",
		1:  "SCOPE",
		2:  "STELLAR_DB",
		10: "AWS_S3",
		11: "MINIO",
		12: "ALI_OSS",
		13: "TENCENT_COS",
	}
	ConnectionType_value = map[string]int32{
		"HIPPO":       0,
		"SCOPE":       1,
		"STELLAR_DB":  2,
		"AWS_S3":      10,
		"MINIO":       11,
		"ALI_OSS":     12,
		"TENCENT_COS": 13,
	}
)

func (x ConnectionType) Enum() *ConnectionType {
	p := new(ConnectionType)
	*p = x
	return p
}

func (x ConnectionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConnectionType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_data_connection_proto_enumTypes[0].Descriptor()
}

func (ConnectionType) Type() protoreflect.EnumType {
	return &file_proto_data_connection_proto_enumTypes[0]
}

func (x ConnectionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConnectionType.Descriptor instead.
func (ConnectionType) EnumDescriptor() ([]byte, []int) {
	return file_proto_data_connection_proto_rawDescGZIP(), []int{0}
}

type ConnectionStatus int32

const (
	ConnectionStatus_NONE      ConnectionStatus = 0
	ConnectionStatus_SUCCEEDED ConnectionStatus = 1
	ConnectionStatus_FAILED    ConnectionStatus = 2
)

// Enum value maps for ConnectionStatus.
var (
	ConnectionStatus_name = map[int32]string{
		0: "NONE",
		1: "SUCCEEDED",
		2: "FAILED",
	}
	ConnectionStatus_value = map[string]int32{
		"NONE":      0,
		"SUCCEEDED": 1,
		"FAILED":    2,
	}
)

func (x ConnectionStatus) Enum() *ConnectionStatus {
	p := new(ConnectionStatus)
	*p = x
	return p
}

func (x ConnectionStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConnectionStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_data_connection_proto_enumTypes[1].Descriptor()
}

func (ConnectionStatus) Type() protoreflect.EnumType {
	return &file_proto_data_connection_proto_enumTypes[1]
}

func (x ConnectionStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConnectionStatus.Descriptor instead.
func (ConnectionStatus) EnumDescriptor() ([]byte, []int) {
	return file_proto_data_connection_proto_rawDescGZIP(), []int{1}
}

type StorageType int32

const (
	StorageType_DATABASE StorageType = 0
	StorageType_OSS      StorageType = 1
)

// Enum value maps for StorageType.
var (
	StorageType_name = map[int32]string{
		0: "DATABASE",
		1: "OSS",
	}
	StorageType_value = map[string]int32{
		"DATABASE": 0,
		"OSS":      1,
	}
)

func (x StorageType) Enum() *StorageType {
	p := new(StorageType)
	*p = x
	return p
}

func (x StorageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StorageType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_data_connection_proto_enumTypes[2].Descriptor()
}

func (StorageType) Type() protoreflect.EnumType {
	return &file_proto_data_connection_proto_enumTypes[2]
}

func (x StorageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StorageType.Descriptor instead.
func (StorageType) EnumDescriptor() ([]byte, []int) {
	return file_proto_data_connection_proto_rawDescGZIP(), []int{2}
}

type DataConnection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"数据连接id"
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" description:"数据连接id"`
	// @gotags: description:"数据连接名称"
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" description:"数据连接名称"`
	// @gotags: description:"数据连接描述"
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty" description:"数据连接描述"`
	// @gotags: description:"数据连接地址"
	Address string `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty" description:"数据连接地址"`
	// @gotags: description:"数据连接端口"
	Port string `protobuf:"bytes,5,opt,name=port,proto3" json:"port,omitempty" description:"数据连接端口"`
	// @gotags: description:"数据连接类型"
	Type ConnectionType `protobuf:"varint,6,opt,name=type,proto3,enum=proto.ConnectionType" json:"type,omitempty" description:"数据连接类型"`
	// @gotags: description:"数据连接状态"
	Status ConnectionStatus `protobuf:"varint,7,opt,name=status,proto3,enum=proto.ConnectionStatus" json:"status,omitempty" description:"数据连接状态"`
	// @gotags: description:"创建用户"
	CreateUser string `protobuf:"bytes,8,opt,name=create_user,json=createUser,proto3" json:"create_user,omitempty" description:"创建用户"`
	// @gotags: description:"创建时间"
	CreateTimeMills int64 `protobuf:"varint,9,opt,name=create_time_mills,json=createTimeMills,proto3" json:"create_time_mills,omitempty" description:"创建时间"`
	// @gotags: description:"更新时间"
	UpdateTimeMills int64 `protobuf:"varint,10,opt,name=update_time_mills,json=updateTimeMills,proto3" json:"update_time_mills,omitempty" description:"更新时间"`
	// @gotags: description:"数据连接用户名"
	Username string `protobuf:"bytes,11,opt,name=username,proto3" json:"username,omitempty" description:"数据连接用户名"`
	// @gotags: description:"数据连接密码"
	Password string `protobuf:"bytes,12,opt,name=password,proto3" json:"password,omitempty" description:"数据连接密码"`
	// @gotags: description:"数据所在数据库"
	Database string `protobuf:"bytes,13,opt,name=database,proto3" json:"database,omitempty" description:"数据所在数据库"`
	// @gotags: description:"额外属性"
	Config string `protobuf:"bytes,14,opt,name=config,proto3" json:"config,omitempty" description:"额外属性"`
	// @gotags: description:"项目id"
	ProjectId string `protobuf:"bytes,15,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty" description:"项目id"`
	// @gotags: description:"存储类型"
	StorageType StorageType `protobuf:"varint,16,opt,name=storage_type,json=storageType,proto3,enum=proto.StorageType" json:"storage_type,omitempty" description:"存储类型"`
	// @gotags: description:"存储配置信息"
	StorageConfig *StorageConfig `protobuf:"bytes,17,opt,name=storage_config,json=storageConfig,proto3" json:"storage_config,omitempty" description:"存储配置信息"`
}

func (x *DataConnection) Reset() {
	*x = DataConnection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_data_connection_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataConnection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataConnection) ProtoMessage() {}

func (x *DataConnection) ProtoReflect() protoreflect.Message {
	mi := &file_proto_data_connection_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataConnection.ProtoReflect.Descriptor instead.
func (*DataConnection) Descriptor() ([]byte, []int) {
	return file_proto_data_connection_proto_rawDescGZIP(), []int{0}
}

func (x *DataConnection) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DataConnection) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DataConnection) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *DataConnection) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *DataConnection) GetPort() string {
	if x != nil {
		return x.Port
	}
	return ""
}

func (x *DataConnection) GetType() ConnectionType {
	if x != nil {
		return x.Type
	}
	return ConnectionType_HIPPO
}

func (x *DataConnection) GetStatus() ConnectionStatus {
	if x != nil {
		return x.Status
	}
	return ConnectionStatus_NONE
}

func (x *DataConnection) GetCreateUser() string {
	if x != nil {
		return x.CreateUser
	}
	return ""
}

func (x *DataConnection) GetCreateTimeMills() int64 {
	if x != nil {
		return x.CreateTimeMills
	}
	return 0
}

func (x *DataConnection) GetUpdateTimeMills() int64 {
	if x != nil {
		return x.UpdateTimeMills
	}
	return 0
}

func (x *DataConnection) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *DataConnection) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *DataConnection) GetDatabase() string {
	if x != nil {
		return x.Database
	}
	return ""
}

func (x *DataConnection) GetConfig() string {
	if x != nil {
		return x.Config
	}
	return ""
}

func (x *DataConnection) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *DataConnection) GetStorageType() StorageType {
	if x != nil {
		return x.StorageType
	}
	return StorageType_DATABASE
}

func (x *DataConnection) GetStorageConfig() *StorageConfig {
	if x != nil {
		return x.StorageConfig
	}
	return nil
}

type StorageConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"s3配置信息"
	S3 *S3Config `protobuf:"bytes,1,opt,name=s3,proto3" json:"s3,omitempty" description:"s3配置信息"`
}

func (x *StorageConfig) Reset() {
	*x = StorageConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_data_connection_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StorageConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StorageConfig) ProtoMessage() {}

func (x *StorageConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_data_connection_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StorageConfig.ProtoReflect.Descriptor instead.
func (*StorageConfig) Descriptor() ([]byte, []int) {
	return file_proto_data_connection_proto_rawDescGZIP(), []int{1}
}

func (x *StorageConfig) GetS3() *S3Config {
	if x != nil {
		return x.S3
	}
	return nil
}

type S3Config struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"s3地址"
	EndPoint string `protobuf:"bytes,1,opt,name=end_point,json=endPoint,proto3" json:"end_point,omitempty" description:"s3地址"`
	// @gotags: description:"区域"
	Region string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty" description:"区域"`
	// @gotags: description:"ak"
	Ak string `protobuf:"bytes,3,opt,name=ak,proto3" json:"ak,omitempty" description:"ak"`
	// @gotags: description:"sk"
	Sk string `protobuf:"bytes,4,opt,name=sk,proto3" json:"sk,omitempty" description:"sk"`
}

func (x *S3Config) Reset() {
	*x = S3Config{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_data_connection_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S3Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S3Config) ProtoMessage() {}

func (x *S3Config) ProtoReflect() protoreflect.Message {
	mi := &file_proto_data_connection_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S3Config.ProtoReflect.Descriptor instead.
func (*S3Config) Descriptor() ([]byte, []int) {
	return file_proto_data_connection_proto_rawDescGZIP(), []int{2}
}

func (x *S3Config) GetEndPoint() string {
	if x != nil {
		return x.EndPoint
	}
	return ""
}

func (x *S3Config) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *S3Config) GetAk() string {
	if x != nil {
		return x.Ak
	}
	return ""
}

func (x *S3Config) GetSk() string {
	if x != nil {
		return x.Sk
	}
	return ""
}

var File_proto_data_connection_proto protoreflect.FileDescriptor

var file_proto_data_connection_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd8, 0x04, 0x0a, 0x0e, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a,
	0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x29, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x69, 0x6c, 0x6c, 0x73, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d,
	0x69, 0x6c, 0x6c, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x69, 0x6c, 0x6c, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x69, 0x6c, 0x6c, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61,
	0x62, 0x61, 0x73, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61,
	0x62, 0x61, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1d, 0x0a, 0x0a,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x0c, 0x73,
	0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x3b, 0x0a, 0x0e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x0d, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22,
	0x30, 0x0a, 0x0d, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x1f, 0x0a, 0x02, 0x73, 0x33, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x33, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x02, 0x73,
	0x33, 0x22, 0x5f, 0x0a, 0x08, 0x53, 0x33, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1b, 0x0a,
	0x09, 0x65, 0x6e, 0x64, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x65, 0x6e, 0x64, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x61, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x61, 0x6b, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x73, 0x6b, 0x2a, 0x6b, 0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x09, 0x0a, 0x05, 0x48, 0x49, 0x50, 0x50, 0x4f, 0x10, 0x00, 0x12,
	0x09, 0x0a, 0x05, 0x53, 0x43, 0x4f, 0x50, 0x45, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x54,
	0x45, 0x4c, 0x4c, 0x41, 0x52, 0x5f, 0x44, 0x42, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x57,
	0x53, 0x5f, 0x53, 0x33, 0x10, 0x0a, 0x12, 0x09, 0x0a, 0x05, 0x4d, 0x49, 0x4e, 0x49, 0x4f, 0x10,
	0x0b, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x4c, 0x49, 0x5f, 0x4f, 0x53, 0x53, 0x10, 0x0c, 0x12, 0x0f,
	0x0a, 0x0b, 0x54, 0x45, 0x4e, 0x43, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x53, 0x10, 0x0d, 0x2a,
	0x37, 0x0a, 0x10, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0d, 0x0a,
	0x09, 0x53, 0x55, 0x43, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x2a, 0x24, 0x0a, 0x0b, 0x53, 0x74, 0x6f, 0x72,
	0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x41, 0x54, 0x41, 0x42,
	0x41, 0x53, 0x45, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x4f, 0x53, 0x53, 0x10, 0x01, 0x42, 0x26,
	0x5a, 0x24, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61,
	0x69, 0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x70, 0x62, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_data_connection_proto_rawDescOnce sync.Once
	file_proto_data_connection_proto_rawDescData = file_proto_data_connection_proto_rawDesc
)

func file_proto_data_connection_proto_rawDescGZIP() []byte {
	file_proto_data_connection_proto_rawDescOnce.Do(func() {
		file_proto_data_connection_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_data_connection_proto_rawDescData)
	})
	return file_proto_data_connection_proto_rawDescData
}

var file_proto_data_connection_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_proto_data_connection_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_proto_data_connection_proto_goTypes = []interface{}{
	(ConnectionType)(0),    // 0: proto.ConnectionType
	(ConnectionStatus)(0),  // 1: proto.ConnectionStatus
	(StorageType)(0),       // 2: proto.StorageType
	(*DataConnection)(nil), // 3: proto.DataConnection
	(*StorageConfig)(nil),  // 4: proto.StorageConfig
	(*S3Config)(nil),       // 5: proto.S3Config
}
var file_proto_data_connection_proto_depIdxs = []int32{
	0, // 0: proto.DataConnection.type:type_name -> proto.ConnectionType
	1, // 1: proto.DataConnection.status:type_name -> proto.ConnectionStatus
	2, // 2: proto.DataConnection.storage_type:type_name -> proto.StorageType
	4, // 3: proto.DataConnection.storage_config:type_name -> proto.StorageConfig
	5, // 4: proto.StorageConfig.s3:type_name -> proto.S3Config
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_proto_data_connection_proto_init() }
func file_proto_data_connection_proto_init() {
	if File_proto_data_connection_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_data_connection_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataConnection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_data_connection_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StorageConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_data_connection_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S3Config); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_data_connection_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_data_connection_proto_goTypes,
		DependencyIndexes: file_proto_data_connection_proto_depIdxs,
		EnumInfos:         file_proto_data_connection_proto_enumTypes,
		MessageInfos:      file_proto_data_connection_proto_msgTypes,
	}.Build()
	File_proto_data_connection_proto = out.File
	file_proto_data_connection_proto_rawDesc = nil
	file_proto_data_connection_proto_goTypes = nil
	file_proto_data_connection_proto_depIdxs = nil
}
