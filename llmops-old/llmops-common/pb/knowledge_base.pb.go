// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/knowledge_base.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	serving "transwarp.io/aip/llmops-common/pb/serving"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type KnowledgeBaseContentType int32

const (
	// 文本知识库
	KnowledgeBaseContentType_TEXT KnowledgeBaseContentType = 0
	// 知识图谱
	KnowledgeBaseContentType_GRAPH KnowledgeBaseContentType = 1
	// 多模态知识库
	KnowledgeBaseContentType_MULTI_MODAL KnowledgeBaseContentType = 2
	// 表格知识库
	KnowledgeBaseContentType_TABLE KnowledgeBaseContentType = 3
)

// Enum value maps for KnowledgeBaseContentType.
var (
	KnowledgeBaseContentType_name = map[int32]string{
		0: "TEXT",
		1: "GRAPH",
		2: "MULTI_MODAL",
		3: "TABLE",
	}
	KnowledgeBaseContentType_value = map[string]int32{
		"TEXT":        0,
		"GRAPH":       1,
		"MULTI_MODAL": 2,
		"TABLE":       3,
	}
)

func (x KnowledgeBaseContentType) Enum() *KnowledgeBaseContentType {
	p := new(KnowledgeBaseContentType)
	*p = x
	return p
}

func (x KnowledgeBaseContentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KnowledgeBaseContentType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_knowledge_base_proto_enumTypes[0].Descriptor()
}

func (KnowledgeBaseContentType) Type() protoreflect.EnumType {
	return &file_proto_knowledge_base_proto_enumTypes[0]
}

func (x KnowledgeBaseContentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KnowledgeBaseContentType.Descriptor instead.
func (KnowledgeBaseContentType) EnumDescriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{0}
}

type KnowledgeBaseSourceType int32

const (
	// 新建知识库
	KnowledgeBaseSourceType_FROM_SCRATCH KnowledgeBaseSourceType = 0
	// 注册已有的知识库
	KnowledgeBaseSourceType_FROM_REGISTRY KnowledgeBaseSourceType = 1
)

// Enum value maps for KnowledgeBaseSourceType.
var (
	KnowledgeBaseSourceType_name = map[int32]string{
		0: "FROM_SCRATCH",
		1: "FROM_REGISTRY",
	}
	KnowledgeBaseSourceType_value = map[string]int32{
		"FROM_SCRATCH":  0,
		"FROM_REGISTRY": 1,
	}
)

func (x KnowledgeBaseSourceType) Enum() *KnowledgeBaseSourceType {
	p := new(KnowledgeBaseSourceType)
	*p = x
	return p
}

func (x KnowledgeBaseSourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KnowledgeBaseSourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_knowledge_base_proto_enumTypes[1].Descriptor()
}

func (KnowledgeBaseSourceType) Type() protoreflect.EnumType {
	return &file_proto_knowledge_base_proto_enumTypes[1]
}

func (x KnowledgeBaseSourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KnowledgeBaseSourceType.Descriptor instead.
func (KnowledgeBaseSourceType) EnumDescriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{1}
}

type KnowledgeBaseRegistryType int32

const (
	KnowledgeBaseRegistryType_NULL                 KnowledgeBaseRegistryType = 0
	KnowledgeBaseRegistryType_FROM_DATA_CONNECTION KnowledgeBaseRegistryType = 1
	KnowledgeBaseRegistryType_FROM_TKH             KnowledgeBaseRegistryType = 2
	KnowledgeBaseRegistryType_FROM_LANGCHAIN       KnowledgeBaseRegistryType = 3
)

// Enum value maps for KnowledgeBaseRegistryType.
var (
	KnowledgeBaseRegistryType_name = map[int32]string{
		0: "NULL",
		1: "FROM_DATA_CONNECTION",
		2: "FROM_TKH",
		3: "FROM_LANGCHAIN",
	}
	KnowledgeBaseRegistryType_value = map[string]int32{
		"NULL":                 0,
		"FROM_DATA_CONNECTION": 1,
		"FROM_TKH":             2,
		"FROM_LANGCHAIN":       3,
	}
)

func (x KnowledgeBaseRegistryType) Enum() *KnowledgeBaseRegistryType {
	p := new(KnowledgeBaseRegistryType)
	*p = x
	return p
}

func (x KnowledgeBaseRegistryType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KnowledgeBaseRegistryType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_knowledge_base_proto_enumTypes[2].Descriptor()
}

func (KnowledgeBaseRegistryType) Type() protoreflect.EnumType {
	return &file_proto_knowledge_base_proto_enumTypes[2]
}

func (x KnowledgeBaseRegistryType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KnowledgeBaseRegistryType.Descriptor instead.
func (KnowledgeBaseRegistryType) EnumDescriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{2}
}

type KnowledgeBaseCreationType int32

const (
	// @gotags: description:"从知识库管理创建"
	KnowledgeBaseCreationType_FROM_MANAGEMENT KnowledgeBaseCreationType = 0
	// @gotags: description:"从Agent配置创建"
	KnowledgeBaseCreationType_FROM_AGENT_CONFIGURATION KnowledgeBaseCreationType = 1
)

// Enum value maps for KnowledgeBaseCreationType.
var (
	KnowledgeBaseCreationType_name = map[int32]string{
		0: "FROM_MANAGEMENT",
		1: "FROM_AGENT_CONFIGURATION",
	}
	KnowledgeBaseCreationType_value = map[string]int32{
		"FROM_MANAGEMENT":          0,
		"FROM_AGENT_CONFIGURATION": 1,
	}
)

func (x KnowledgeBaseCreationType) Enum() *KnowledgeBaseCreationType {
	p := new(KnowledgeBaseCreationType)
	*p = x
	return p
}

func (x KnowledgeBaseCreationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KnowledgeBaseCreationType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_knowledge_base_proto_enumTypes[3].Descriptor()
}

func (KnowledgeBaseCreationType) Type() protoreflect.EnumType {
	return &file_proto_knowledge_base_proto_enumTypes[3]
}

func (x KnowledgeBaseCreationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KnowledgeBaseCreationType.Descriptor instead.
func (KnowledgeBaseCreationType) EnumDescriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{3}
}

type KnowledgeBaseSceneType int32

const (
	// @gotags: description:"问答场景"
	KnowledgeBaseSceneType_SceneType_QA KnowledgeBaseSceneType = 0
	// @gotags: description:"安全场景"
	KnowledgeBaseSceneType_SceneType_SAFETY KnowledgeBaseSceneType = 1
	// @gotags: description:"标准化问答"
	KnowledgeBaseSceneType_SceneType_STANDARD KnowledgeBaseSceneType = 2
	// @gotags: description:"其他场景"
	KnowledgeBaseSceneType_SceneType_OTHERS KnowledgeBaseSceneType = 99
)

// Enum value maps for KnowledgeBaseSceneType.
var (
	KnowledgeBaseSceneType_name = map[int32]string{
		0:  "SceneType_QA",
		1:  "SceneType_SAFETY",
		2:  "SceneType_STANDARD",
		99: "SceneType_OTHERS",
	}
	KnowledgeBaseSceneType_value = map[string]int32{
		"SceneType_QA":       0,
		"SceneType_SAFETY":   1,
		"SceneType_STANDARD": 2,
		"SceneType_OTHERS":   99,
	}
)

func (x KnowledgeBaseSceneType) Enum() *KnowledgeBaseSceneType {
	p := new(KnowledgeBaseSceneType)
	*p = x
	return p
}

func (x KnowledgeBaseSceneType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KnowledgeBaseSceneType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_knowledge_base_proto_enumTypes[4].Descriptor()
}

func (KnowledgeBaseSceneType) Type() protoreflect.EnumType {
	return &file_proto_knowledge_base_proto_enumTypes[4]
}

func (x KnowledgeBaseSceneType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KnowledgeBaseSceneType.Descriptor instead.
func (KnowledgeBaseSceneType) EnumDescriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{4}
}

type DocLoadStrategyType int32

const (
	DocLoadStrategyType_AUTO     DocLoadStrategyType = 0
	DocLoadStrategyType_FAST     DocLoadStrategyType = 1
	DocLoadStrategyType_HI_RES   DocLoadStrategyType = 2
	DocLoadStrategyType_OCR_ONLY DocLoadStrategyType = 3
	DocLoadStrategyType_VL_MODEL DocLoadStrategyType = 4
)

// Enum value maps for DocLoadStrategyType.
var (
	DocLoadStrategyType_name = map[int32]string{
		0: "AUTO",
		1: "FAST",
		2: "HI_RES",
		3: "OCR_ONLY",
		4: "VL_MODEL",
	}
	DocLoadStrategyType_value = map[string]int32{
		"AUTO":     0,
		"FAST":     1,
		"HI_RES":   2,
		"OCR_ONLY": 3,
		"VL_MODEL": 4,
	}
)

func (x DocLoadStrategyType) Enum() *DocLoadStrategyType {
	p := new(DocLoadStrategyType)
	*p = x
	return p
}

func (x DocLoadStrategyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DocLoadStrategyType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_knowledge_base_proto_enumTypes[5].Descriptor()
}

func (DocLoadStrategyType) Type() protoreflect.EnumType {
	return &file_proto_knowledge_base_proto_enumTypes[5]
}

func (x DocLoadStrategyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DocLoadStrategyType.Descriptor instead.
func (DocLoadStrategyType) EnumDescriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{5}
}

type StrategyOrigin int32

const (
	// @gotags: description:"平台预置策略"
	StrategyOrigin_PRESET StrategyOrigin = 0
	// @gotags: description:"自定义策略"
	StrategyOrigin_CUSTOM     StrategyOrigin = 1
	StrategyOrigin_DOC_ENGINE StrategyOrigin = 2
)

// Enum value maps for StrategyOrigin.
var (
	StrategyOrigin_name = map[int32]string{
		0: "PRESET",
		1: "CUSTOM",
		2: "DOC_ENGINE",
	}
	StrategyOrigin_value = map[string]int32{
		"PRESET":     0,
		"CUSTOM":     1,
		"DOC_ENGINE": 2,
	}
)

func (x StrategyOrigin) Enum() *StrategyOrigin {
	p := new(StrategyOrigin)
	*p = x
	return p
}

func (x StrategyOrigin) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StrategyOrigin) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_knowledge_base_proto_enumTypes[6].Descriptor()
}

func (StrategyOrigin) Type() protoreflect.EnumType {
	return &file_proto_knowledge_base_proto_enumTypes[6]
}

func (x StrategyOrigin) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StrategyOrigin.Descriptor instead.
func (StrategyOrigin) EnumDescriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{6}
}

type DocSplitStrategyType int32

const (
	// @gotags: description:"按字符等价切分"
	DocSplitStrategyType_CHARACTER DocSplitStrategyType = 0
	// @gotags: description:"递归切分"
	DocSplitStrategyType_RECURSIVE DocSplitStrategyType = 1
	// @gotags: description:"按页切分"
	DocSplitStrategyType_PAGE DocSplitStrategyType = 2
)

// Enum value maps for DocSplitStrategyType.
var (
	DocSplitStrategyType_name = map[int32]string{
		0: "CHARACTER",
		1: "RECURSIVE",
		2: "PAGE",
	}
	DocSplitStrategyType_value = map[string]int32{
		"CHARACTER": 0,
		"RECURSIVE": 1,
		"PAGE":      2,
	}
)

func (x DocSplitStrategyType) Enum() *DocSplitStrategyType {
	p := new(DocSplitStrategyType)
	*p = x
	return p
}

func (x DocSplitStrategyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DocSplitStrategyType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_knowledge_base_proto_enumTypes[7].Descriptor()
}

func (DocSplitStrategyType) Type() protoreflect.EnumType {
	return &file_proto_knowledge_base_proto_enumTypes[7]
}

func (x DocSplitStrategyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DocSplitStrategyType.Descriptor instead.
func (DocSplitStrategyType) EnumDescriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{7}
}

type KnowledgeBaseRetrieveStrategy int32

const (
	// 基于文本向量相似度的检索
	KnowledgeBaseRetrieveStrategy_VECTOR KnowledgeBaseRetrieveStrategy = 0
	// 全文检索
	KnowledgeBaseRetrieveStrategy_FULL_TEXT KnowledgeBaseRetrieveStrategy = 1
	// 混合检索
	KnowledgeBaseRetrieveStrategy_MIXED               KnowledgeBaseRetrieveStrategy = 2
	KnowledgeBaseRetrieveStrategy_DOC_ENGINE_RETRIEVE KnowledgeBaseRetrieveStrategy = 3
)

// Enum value maps for KnowledgeBaseRetrieveStrategy.
var (
	KnowledgeBaseRetrieveStrategy_name = map[int32]string{
		0: "VECTOR",
		1: "FULL_TEXT",
		2: "MIXED",
		3: "DOC_ENGINE_RETRIEVE",
	}
	KnowledgeBaseRetrieveStrategy_value = map[string]int32{
		"VECTOR":              0,
		"FULL_TEXT":           1,
		"MIXED":               2,
		"DOC_ENGINE_RETRIEVE": 3,
	}
)

func (x KnowledgeBaseRetrieveStrategy) Enum() *KnowledgeBaseRetrieveStrategy {
	p := new(KnowledgeBaseRetrieveStrategy)
	*p = x
	return p
}

func (x KnowledgeBaseRetrieveStrategy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KnowledgeBaseRetrieveStrategy) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_knowledge_base_proto_enumTypes[8].Descriptor()
}

func (KnowledgeBaseRetrieveStrategy) Type() protoreflect.EnumType {
	return &file_proto_knowledge_base_proto_enumTypes[8]
}

func (x KnowledgeBaseRetrieveStrategy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KnowledgeBaseRetrieveStrategy.Descriptor instead.
func (KnowledgeBaseRetrieveStrategy) EnumDescriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{8}
}

type ChunkSourceType int32

const (
	// @gotags: description:"自动生成"
	ChunkSourceType_SOURCE_TYPE_GENERATED ChunkSourceType = 0
	// @gotags: description:"手动创建"
	ChunkSourceType_SOURCE_TYPE_CREATED ChunkSourceType = 1
)

// Enum value maps for ChunkSourceType.
var (
	ChunkSourceType_name = map[int32]string{
		0: "SOURCE_TYPE_GENERATED",
		1: "SOURCE_TYPE_CREATED",
	}
	ChunkSourceType_value = map[string]int32{
		"SOURCE_TYPE_GENERATED": 0,
		"SOURCE_TYPE_CREATED":   1,
	}
)

func (x ChunkSourceType) Enum() *ChunkSourceType {
	p := new(ChunkSourceType)
	*p = x
	return p
}

func (x ChunkSourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChunkSourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_knowledge_base_proto_enumTypes[9].Descriptor()
}

func (ChunkSourceType) Type() protoreflect.EnumType {
	return &file_proto_knowledge_base_proto_enumTypes[9]
}

func (x ChunkSourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChunkSourceType.Descriptor instead.
func (ChunkSourceType) EnumDescriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{9}
}

type OriginalContentType int32

const (
	// @gotags: description:"原文文本"
	OriginalContentType_ORIGINAL_CONTENT_TYPE_TEXT OriginalContentType = 0
	// @gotags: description:"原文表格"
	OriginalContentType_ORIGINAL_CONTENT_TYPE_TABLE OriginalContentType = 1
	// @gotags: description:"表格行数据的json"
	OriginalContentType_ORIGINAL_CONTENT_TYPE_JSON OriginalContentType = 2
	// @gotags: description:"原文中的图像"
	OriginalContentType_ORIGINAL_CONTENT_TYPE_IMAGE OriginalContentType = 3
	// @gotags: description:"按页分割新增，表示切片类型为markdown"
	OriginalContentType_ORIGINAL_CONTENT_TYPE_MARKDOWN OriginalContentType = 4
)

// Enum value maps for OriginalContentType.
var (
	OriginalContentType_name = map[int32]string{
		0: "ORIGINAL_CONTENT_TYPE_TEXT",
		1: "ORIGINAL_CONTENT_TYPE_TABLE",
		2: "ORIGINAL_CONTENT_TYPE_JSON",
		3: "ORIGINAL_CONTENT_TYPE_IMAGE",
		4: "ORIGINAL_CONTENT_TYPE_MARKDOWN",
	}
	OriginalContentType_value = map[string]int32{
		"ORIGINAL_CONTENT_TYPE_TEXT":     0,
		"ORIGINAL_CONTENT_TYPE_TABLE":    1,
		"ORIGINAL_CONTENT_TYPE_JSON":     2,
		"ORIGINAL_CONTENT_TYPE_IMAGE":    3,
		"ORIGINAL_CONTENT_TYPE_MARKDOWN": 4,
	}
)

func (x OriginalContentType) Enum() *OriginalContentType {
	p := new(OriginalContentType)
	*p = x
	return p
}

func (x OriginalContentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OriginalContentType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_knowledge_base_proto_enumTypes[10].Descriptor()
}

func (OriginalContentType) Type() protoreflect.EnumType {
	return &file_proto_knowledge_base_proto_enumTypes[10]
}

func (x OriginalContentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OriginalContentType.Descriptor instead.
func (OriginalContentType) EnumDescriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{10}
}

type AugmentedChunkType int32

const (
	// @gotags: description:"知识增强-摘要"
	AugmentedChunkType_AUGMENTED_CHUNK_TYPE_SUMMARY AugmentedChunkType = 0
	// @gotags: description:"知识增强-问题"
	AugmentedChunkType_AUGMENTED_CHUNK_TYPE_QUESTION AugmentedChunkType = 1
	// @gotags: description:"知识增强-表格描述"
	AugmentedChunkType_AUGMENTED_CHUNK_TYPE_TABLE_DESCRIPTION AugmentedChunkType = 20
	// @gotags: description:"知识增强-表格摘要"
	AugmentedChunkType_AUGMENTED_CHUNK_TYPE_TABLE_SUMMARY AugmentedChunkType = 21
	// @gotags: description:"知识增强-问题改写"
	AugmentedChunkType_AUGMENTED_CHUNK_TYPE_QUESTION_REWRITE AugmentedChunkType = 3
	// @gotags: description:"知识增强-未定义类型，用于手动新建的增强切片"
	AugmentedChunkType_AUGMENTED_CHUNK_TYPE_UNDEFINED AugmentedChunkType = 4
	// @gotags: description:"表格知识库的索引字段作为AgumentedChunk"
	AugmentedChunkType_AUGMENTED_CHUNK_TYPE_INDEX_TABLE_FIELD AugmentedChunkType = 5
	// @gotags: description:"上下文内容(用于图片chunk的增强)"
	AugmentedChunkType_AUGMENTED_CHUNK_TYPE_CONTEXT AugmentedChunkType = 6
	// @gotags: description:"知识增强-图片描述"
	AugmentedChunkType_AUGMENTED_CHUNK_TYPE_IMAGE_DESCRIPTION AugmentedChunkType = 7
	// @gotags: description:"知识增强-自定义增强"
	AugmentedChunkType_AUGMENTED_CHUNK_TYPE_CUSTOM AugmentedChunkType = 8
)

// Enum value maps for AugmentedChunkType.
var (
	AugmentedChunkType_name = map[int32]string{
		0:  "AUGMENTED_CHUNK_TYPE_SUMMARY",
		1:  "AUGMENTED_CHUNK_TYPE_QUESTION",
		20: "AUGMENTED_CHUNK_TYPE_TABLE_DESCRIPTION",
		21: "AUGMENTED_CHUNK_TYPE_TABLE_SUMMARY",
		3:  "AUGMENTED_CHUNK_TYPE_QUESTION_REWRITE",
		4:  "AUGMENTED_CHUNK_TYPE_UNDEFINED",
		5:  "AUGMENTED_CHUNK_TYPE_INDEX_TABLE_FIELD",
		6:  "AUGMENTED_CHUNK_TYPE_CONTEXT",
		7:  "AUGMENTED_CHUNK_TYPE_IMAGE_DESCRIPTION",
		8:  "AUGMENTED_CHUNK_TYPE_CUSTOM",
	}
	AugmentedChunkType_value = map[string]int32{
		"AUGMENTED_CHUNK_TYPE_SUMMARY":           0,
		"AUGMENTED_CHUNK_TYPE_QUESTION":          1,
		"AUGMENTED_CHUNK_TYPE_TABLE_DESCRIPTION": 20,
		"AUGMENTED_CHUNK_TYPE_TABLE_SUMMARY":     21,
		"AUGMENTED_CHUNK_TYPE_QUESTION_REWRITE":  3,
		"AUGMENTED_CHUNK_TYPE_UNDEFINED":         4,
		"AUGMENTED_CHUNK_TYPE_INDEX_TABLE_FIELD": 5,
		"AUGMENTED_CHUNK_TYPE_CONTEXT":           6,
		"AUGMENTED_CHUNK_TYPE_IMAGE_DESCRIPTION": 7,
		"AUGMENTED_CHUNK_TYPE_CUSTOM":            8,
	}
)

func (x AugmentedChunkType) Enum() *AugmentedChunkType {
	p := new(AugmentedChunkType)
	*p = x
	return p
}

func (x AugmentedChunkType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AugmentedChunkType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_knowledge_base_proto_enumTypes[11].Descriptor()
}

func (AugmentedChunkType) Type() protoreflect.EnumType {
	return &file_proto_knowledge_base_proto_enumTypes[11]
}

func (x AugmentedChunkType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AugmentedChunkType.Descriptor instead.
func (AugmentedChunkType) EnumDescriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{11}
}

type DocElementType int32

const (
	DocElementType_DocElementType_FigureCaption     DocElementType = 0
	DocElementType_DocElementType_NarrativeText     DocElementType = 1
	DocElementType_DocElementType_ListItem          DocElementType = 2
	DocElementType_DocElementType_Title             DocElementType = 3
	DocElementType_DocElementType_Address           DocElementType = 4
	DocElementType_DocElementType_Table             DocElementType = 5
	DocElementType_DocElementType_PageBreak         DocElementType = 6
	DocElementType_DocElementType_Header            DocElementType = 7
	DocElementType_DocElementType_Footer            DocElementType = 8
	DocElementType_DocElementType_UncategorizedText DocElementType = 9
	DocElementType_DocElementType_Image             DocElementType = 10
	DocElementType_DocElementType_Formula           DocElementType = 11
)

// Enum value maps for DocElementType.
var (
	DocElementType_name = map[int32]string{
		0:  "DocElementType_FigureCaption",
		1:  "DocElementType_NarrativeText",
		2:  "DocElementType_ListItem",
		3:  "DocElementType_Title",
		4:  "DocElementType_Address",
		5:  "DocElementType_Table",
		6:  "DocElementType_PageBreak",
		7:  "DocElementType_Header",
		8:  "DocElementType_Footer",
		9:  "DocElementType_UncategorizedText",
		10: "DocElementType_Image",
		11: "DocElementType_Formula",
	}
	DocElementType_value = map[string]int32{
		"DocElementType_FigureCaption":     0,
		"DocElementType_NarrativeText":     1,
		"DocElementType_ListItem":          2,
		"DocElementType_Title":             3,
		"DocElementType_Address":           4,
		"DocElementType_Table":             5,
		"DocElementType_PageBreak":         6,
		"DocElementType_Header":            7,
		"DocElementType_Footer":            8,
		"DocElementType_UncategorizedText": 9,
		"DocElementType_Image":             10,
		"DocElementType_Formula":           11,
	}
)

func (x DocElementType) Enum() *DocElementType {
	p := new(DocElementType)
	*p = x
	return p
}

func (x DocElementType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DocElementType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_knowledge_base_proto_enumTypes[12].Descriptor()
}

func (DocElementType) Type() protoreflect.EnumType {
	return &file_proto_knowledge_base_proto_enumTypes[12]
}

func (x DocElementType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DocElementType.Descriptor instead.
func (DocElementType) EnumDescriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{12}
}

type TableFieldDataType int32

const (
	// @gotags: description:"字符串"
	TableFieldDataType_STRING TableFieldDataType = 0
	// @gotags: description:"整数"
	TableFieldDataType_INT TableFieldDataType = 1
	// @gotags: description:"时间"
	TableFieldDataType_TIME TableFieldDataType = 2
	// @gotags: description:"浮点数"
	TableFieldDataType_NUMBER TableFieldDataType = 3
	// @gotags: description:"布尔值"
	TableFieldDataType_BOOL TableFieldDataType = 4
	// @gotags: description:"图像"
	TableFieldDataType_IMAGE TableFieldDataType = 5
)

// Enum value maps for TableFieldDataType.
var (
	TableFieldDataType_name = map[int32]string{
		0: "STRING",
		1: "INT",
		2: "TIME",
		3: "NUMBER",
		4: "BOOL",
		5: "IMAGE",
	}
	TableFieldDataType_value = map[string]int32{
		"STRING": 0,
		"INT":    1,
		"TIME":   2,
		"NUMBER": 3,
		"BOOL":   4,
		"IMAGE":  5,
	}
)

func (x TableFieldDataType) Enum() *TableFieldDataType {
	p := new(TableFieldDataType)
	*p = x
	return p
}

func (x TableFieldDataType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TableFieldDataType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_knowledge_base_proto_enumTypes[13].Descriptor()
}

func (TableFieldDataType) Type() protoreflect.EnumType {
	return &file_proto_knowledge_base_proto_enumTypes[13]
}

func (x TableFieldDataType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TableFieldDataType.Descriptor instead.
func (TableFieldDataType) EnumDescriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{13}
}

type DocumentFileSource int32

const (
	// @gotags: description:"本地上传"
	DocumentFileSource_LOCAL_FILE DocumentFileSource = 0
	// @gotags: description:"语料数据集"
	DocumentFileSource_CORPUS_DATA_SET DocumentFileSource = 1
)

// Enum value maps for DocumentFileSource.
var (
	DocumentFileSource_name = map[int32]string{
		0: "LOCAL_FILE",
		1: "CORPUS_DATA_SET",
	}
	DocumentFileSource_value = map[string]int32{
		"LOCAL_FILE":      0,
		"CORPUS_DATA_SET": 1,
	}
)

func (x DocumentFileSource) Enum() *DocumentFileSource {
	p := new(DocumentFileSource)
	*p = x
	return p
}

func (x DocumentFileSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DocumentFileSource) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_knowledge_base_proto_enumTypes[14].Descriptor()
}

func (DocumentFileSource) Type() protoreflect.EnumType {
	return &file_proto_knowledge_base_proto_enumTypes[14]
}

func (x DocumentFileSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DocumentFileSource.Descriptor instead.
func (DocumentFileSource) EnumDescriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{14}
}

// 从数据连接导入知识库的注册信息
type KnowledgeBaseConnectionRegistry struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"数据连接id"
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" description:"数据连接id"`
	// @gotags: description:"数据连接类型"
	DataConnectionType ConnectionType `protobuf:"varint,2,opt,name=data_connection_type,json=dataConnectionType,proto3,enum=proto.ConnectionType" json:"data_connection_type,omitempty" description:"数据连接类型"`
	// @gotags: description:"数据库表"
	Table string `protobuf:"bytes,3,opt,name=table,proto3" json:"table,omitempty" description:"数据库表"`
	// @gotags: description:"用于排序的字段"
	SortFields []string `protobuf:"bytes,4,rep,name=sort_fields,json=sortFields,proto3" json:"sort_fields,omitempty" description:"用于排序的字段"`
	// @gotags: description:"文本字段, 作为召回内容"
	TextField string `protobuf:"bytes,5,opt,name=text_field,json=textField,proto3" json:"text_field,omitempty" description:"文本字段, 作为召回内容"`
	// @gotags: description:"向量字段"
	VectorField string `protobuf:"bytes,6,opt,name=vector_field,json=vectorField,proto3" json:"vector_field,omitempty" description:"向量字段"`
	// @gotags: description:"展示详情的字段列表，用于召回卡片中展示kv"
	DisplayFields []string `protobuf:"bytes,7,rep,name=display_fields,json=displayFields,proto3" json:"display_fields,omitempty" description:"展示详情的字段列表，用于召回卡片中展示kv"`
	// @gotags: description:"向量模型"
	VectorModel *ModelService `protobuf:"bytes,8,opt,name=vector_model,json=vectorModel,proto3" json:"vector_model,omitempty" description:"向量模型"`
	// @gotags: description:"向量维度"
	VectorDim int32 `protobuf:"varint,9,opt,name=vector_dim,json=vectorDim,proto3" json:"vector_dim,omitempty" description:"向量维度"`
	// @gotags: description:"默认排序方式，是否降序"
	IsDesc bool `protobuf:"varint,10,opt,name=is_desc,json=isDesc,proto3" json:"is_desc,omitempty" description:"默认排序方式，是否降序"`
	// @gotags: description:"文档名称字段，用于标识文本段来源的文档"
	DocField string `protobuf:"bytes,11,opt,name=doc_field,json=docField,proto3" json:"doc_field,omitempty" description:"文档名称字段，用于标识文本段来源的文档"`
	// @gotags: description:"段落/向量的ID字段"
	IdField string `protobuf:"bytes,12,opt,name=id_field,json=idField,proto3" json:"id_field,omitempty" description:"段落/向量的ID字段"`
	// @gotags: description:"向量索引名称"
	VectorIndexName string `protobuf:"bytes,13,opt,name=vector_index_name,json=vectorIndexName,proto3" json:"vector_index_name,omitempty" description:"向量索引名称"`
}

func (x *KnowledgeBaseConnectionRegistry) Reset() {
	*x = KnowledgeBaseConnectionRegistry{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_knowledge_base_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KnowledgeBaseConnectionRegistry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnowledgeBaseConnectionRegistry) ProtoMessage() {}

func (x *KnowledgeBaseConnectionRegistry) ProtoReflect() protoreflect.Message {
	mi := &file_proto_knowledge_base_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnowledgeBaseConnectionRegistry.ProtoReflect.Descriptor instead.
func (*KnowledgeBaseConnectionRegistry) Descriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{0}
}

func (x *KnowledgeBaseConnectionRegistry) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *KnowledgeBaseConnectionRegistry) GetDataConnectionType() ConnectionType {
	if x != nil {
		return x.DataConnectionType
	}
	return ConnectionType_HIPPO
}

func (x *KnowledgeBaseConnectionRegistry) GetTable() string {
	if x != nil {
		return x.Table
	}
	return ""
}

func (x *KnowledgeBaseConnectionRegistry) GetSortFields() []string {
	if x != nil {
		return x.SortFields
	}
	return nil
}

func (x *KnowledgeBaseConnectionRegistry) GetTextField() string {
	if x != nil {
		return x.TextField
	}
	return ""
}

func (x *KnowledgeBaseConnectionRegistry) GetVectorField() string {
	if x != nil {
		return x.VectorField
	}
	return ""
}

func (x *KnowledgeBaseConnectionRegistry) GetDisplayFields() []string {
	if x != nil {
		return x.DisplayFields
	}
	return nil
}

func (x *KnowledgeBaseConnectionRegistry) GetVectorModel() *ModelService {
	if x != nil {
		return x.VectorModel
	}
	return nil
}

func (x *KnowledgeBaseConnectionRegistry) GetVectorDim() int32 {
	if x != nil {
		return x.VectorDim
	}
	return 0
}

func (x *KnowledgeBaseConnectionRegistry) GetIsDesc() bool {
	if x != nil {
		return x.IsDesc
	}
	return false
}

func (x *KnowledgeBaseConnectionRegistry) GetDocField() string {
	if x != nil {
		return x.DocField
	}
	return ""
}

func (x *KnowledgeBaseConnectionRegistry) GetIdField() string {
	if x != nil {
		return x.IdField
	}
	return ""
}

func (x *KnowledgeBaseConnectionRegistry) GetVectorIndexName() string {
	if x != nil {
		return x.VectorIndexName
	}
	return ""
}

// 从TKH导入知识库的注册信息
type KnowledgeBaseTKHRegistry struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"TKH知识库id"
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" description:"TKH知识库id"`
}

func (x *KnowledgeBaseTKHRegistry) Reset() {
	*x = KnowledgeBaseTKHRegistry{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_knowledge_base_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KnowledgeBaseTKHRegistry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnowledgeBaseTKHRegistry) ProtoMessage() {}

func (x *KnowledgeBaseTKHRegistry) ProtoReflect() protoreflect.Message {
	mi := &file_proto_knowledge_base_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnowledgeBaseTKHRegistry.ProtoReflect.Descriptor instead.
func (*KnowledgeBaseTKHRegistry) Descriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{1}
}

func (x *KnowledgeBaseTKHRegistry) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type KnowledgeBasePublishInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"发布的服务id"
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" description:"发布的服务id"`
	// @gotags: description:"发布的服务虚拟svc用于istio route"
	VirtualSvcUrl string `protobuf:"bytes,2,opt,name=virtual_svc_url,json=virtualSvcUrl,proto3" json:"virtual_svc_url,omitempty" description:"发布的服务虚拟svc用于istio route"`
	// @gotags: description:"发布的服务名字"
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty" description:"发布的服务名字"`
	// @gotags: description:"发布的服务的描述"
	Desc string `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty" description:"发布的服务的描述"`
	// @gotags: description:"发布的服务qps速率限制"
	RateLimit *serving.RateLimit `protobuf:"bytes,5,opt,name=rate_limit,json=rateLimit,proto3" json:"rate_limit,omitempty" description:"发布的服务qps速率限制"`
	// @gotags: description:"发布的服务是否开启安全围栏"
	IsSecurity bool `protobuf:"varint,6,opt,name=is_security,json=isSecurity,proto3" json:"is_security,omitempty" description:"发布的服务是否开启安全围栏"`
}

func (x *KnowledgeBasePublishInfo) Reset() {
	*x = KnowledgeBasePublishInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_knowledge_base_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KnowledgeBasePublishInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnowledgeBasePublishInfo) ProtoMessage() {}

func (x *KnowledgeBasePublishInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_knowledge_base_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnowledgeBasePublishInfo.ProtoReflect.Descriptor instead.
func (*KnowledgeBasePublishInfo) Descriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{2}
}

func (x *KnowledgeBasePublishInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *KnowledgeBasePublishInfo) GetVirtualSvcUrl() string {
	if x != nil {
		return x.VirtualSvcUrl
	}
	return ""
}

func (x *KnowledgeBasePublishInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *KnowledgeBasePublishInfo) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *KnowledgeBasePublishInfo) GetRateLimit() *serving.RateLimit {
	if x != nil {
		return x.RateLimit
	}
	return nil
}

func (x *KnowledgeBasePublishInfo) GetIsSecurity() bool {
	if x != nil {
		return x.IsSecurity
	}
	return false
}

type KnowledgeBase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"知识库id"
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" description:"知识库id"`
	// @gotags: description:"知识库名称"
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" description:"知识库名称"`
	// @gotags: description:"知识库描述"
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty" description:"知识库描述"`
	// @gotags: description:"知识库类型"
	ContentType KnowledgeBaseContentType `protobuf:"varint,4,opt,name=content_type,json=contentType,proto3,enum=proto.KnowledgeBaseContentType" json:"content_type,omitempty" description:"知识库类型"`
	// @gotags: description:"知识库来源"
	SourceType KnowledgeBaseSourceType `protobuf:"varint,5,opt,name=source_type,json=sourceType,proto3,enum=proto.KnowledgeBaseSourceType" json:"source_type,omitempty" description:"知识库来源"`
	// @gotags: description:"知识库注册类型"
	RegistryType KnowledgeBaseRegistryType `protobuf:"varint,6,opt,name=registry_type,json=registryType,proto3,enum=proto.KnowledgeBaseRegistryType" json:"registry_type,omitempty" description:"知识库注册类型"`
	// @gotags: description:"知识库图标"
	Icon string `protobuf:"bytes,7,opt,name=icon,proto3" json:"icon,omitempty" description:"知识库图标"`
	// @gotags: description:"数据连接类型的注册信息"
	ConnectionRegistry *KnowledgeBaseConnectionRegistry `protobuf:"bytes,8,opt,name=connection_registry,json=connectionRegistry,proto3" json:"connection_registry,omitempty" description:"数据连接类型的注册信息"`
	// @gotags: description:"TKH类型的注册信息"
	TkhRegistry *KnowledgeBaseTKHRegistry `protobuf:"bytes,9,opt,name=tkh_registry,json=tkhRegistry,proto3" json:"tkh_registry,omitempty" description:"TKH类型的注册信息"`
	// @gotags: description:"创建用户"
	CreateUser string `protobuf:"bytes,10,opt,name=create_user,json=createUser,proto3" json:"create_user,omitempty" description:"创建用户"`
	// @gotags: description:"创建时间"
	CreateTimeMills int64 `protobuf:"varint,11,opt,name=create_time_mills,json=createTimeMills,proto3" json:"create_time_mills,omitempty" description:"创建时间"`
	// @gotags: description:"更新时间"
	UpdateTimeMills int64 `protobuf:"varint,12,opt,name=update_time_mills,json=updateTimeMills,proto3" json:"update_time_mills,omitempty" description:"更新时间"`
	// @gotags: description:"禁用文档id列表"
	DisabledDocs []string `protobuf:"bytes,13,rep,name=disabled_docs,json=disabledDocs,proto3" json:"disabled_docs,omitempty" description:"禁用文档id列表"`
	// @gotags: description:"项目id"
	ProjectId string `protobuf:"bytes,14,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty" description:"项目id"`
	// @gotags: description:"是否可见"
	IsVisible bool `protobuf:"varint,15,opt,name=is_visible,json=isVisible,proto3" json:"is_visible,omitempty" description:"是否可见"`
	// @gotags: description:"是否可检索"
	IsRetrievable bool `protobuf:"varint,16,opt,name=is_retrievable,json=isRetrievable,proto3" json:"is_retrievable,omitempty" description:"是否可检索"`
	// @gotags: description:"创建类型"
	CreationType KnowledgeBaseCreationType `protobuf:"varint,17,opt,name=creation_type,json=creationType,proto3,enum=proto.KnowledgeBaseCreationType" json:"creation_type,omitempty" description:"创建类型"`
	// @gotags: description:"文档加工设置"
	DocProcessingConfig *DocProcessingConfig `protobuf:"bytes,18,opt,name=doc_processing_config,json=docProcessingConfig,proto3" json:"doc_processing_config,omitempty" description:"文档加工设置"`
	// @gotags: description:"检索设置"
	RetrievalConfig *RetrievalConfig `protobuf:"bytes,19,opt,name=retrieval_config,json=retrievalConfig,proto3" json:"retrieval_config,omitempty" description:"检索设置"`
	// @gotags: description:"向量模型"
	VectorModel *ModelService `protobuf:"bytes,20,opt,name=vector_model,json=vectorModel,proto3" json:"vector_model,omitempty" description:"向量模型"`
	// @gotags: description:"是否共享到公共空间"
	IsPublic bool `protobuf:"varint,21,opt,name=is_public,json=isPublic,proto3" json:"is_public,omitempty" description:"是否共享到公共空间"`
	// @gotags: description:"是否已发布"
	IsPublished bool `protobuf:"varint,22,opt,name=is_published,json=isPublished,proto3" json:"is_published,omitempty" description:"是否已发布"`
	// @gotags: description:"业务场景类型"
	SceneType KnowledgeBaseSceneType `protobuf:"varint,23,opt,name=scene_type,json=sceneType,proto3,enum=proto.KnowledgeBaseSceneType" json:"scene_type,omitempty" description:"业务场景类型"`
	// @gotags: description:"记录知识库的使用次数"
	MetricsInfo *MetricsInfo `protobuf:"bytes,24,opt,name=metrics_info,json=metricsInfo,proto3" json:"metrics_info,omitempty" description:"记录知识库的使用次数"`
	// @gotags: description:"发布信息"
	PublishInfo *KnowledgeBasePublishInfo `protobuf:"bytes,25,opt,name=publish_info,json=publishInfo,proto3" json:"publish_info,omitempty" description:"发布信息"`
}

func (x *KnowledgeBase) Reset() {
	*x = KnowledgeBase{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_knowledge_base_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KnowledgeBase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnowledgeBase) ProtoMessage() {}

func (x *KnowledgeBase) ProtoReflect() protoreflect.Message {
	mi := &file_proto_knowledge_base_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnowledgeBase.ProtoReflect.Descriptor instead.
func (*KnowledgeBase) Descriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{3}
}

func (x *KnowledgeBase) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *KnowledgeBase) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *KnowledgeBase) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *KnowledgeBase) GetContentType() KnowledgeBaseContentType {
	if x != nil {
		return x.ContentType
	}
	return KnowledgeBaseContentType_TEXT
}

func (x *KnowledgeBase) GetSourceType() KnowledgeBaseSourceType {
	if x != nil {
		return x.SourceType
	}
	return KnowledgeBaseSourceType_FROM_SCRATCH
}

func (x *KnowledgeBase) GetRegistryType() KnowledgeBaseRegistryType {
	if x != nil {
		return x.RegistryType
	}
	return KnowledgeBaseRegistryType_NULL
}

func (x *KnowledgeBase) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *KnowledgeBase) GetConnectionRegistry() *KnowledgeBaseConnectionRegistry {
	if x != nil {
		return x.ConnectionRegistry
	}
	return nil
}

func (x *KnowledgeBase) GetTkhRegistry() *KnowledgeBaseTKHRegistry {
	if x != nil {
		return x.TkhRegistry
	}
	return nil
}

func (x *KnowledgeBase) GetCreateUser() string {
	if x != nil {
		return x.CreateUser
	}
	return ""
}

func (x *KnowledgeBase) GetCreateTimeMills() int64 {
	if x != nil {
		return x.CreateTimeMills
	}
	return 0
}

func (x *KnowledgeBase) GetUpdateTimeMills() int64 {
	if x != nil {
		return x.UpdateTimeMills
	}
	return 0
}

func (x *KnowledgeBase) GetDisabledDocs() []string {
	if x != nil {
		return x.DisabledDocs
	}
	return nil
}

func (x *KnowledgeBase) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *KnowledgeBase) GetIsVisible() bool {
	if x != nil {
		return x.IsVisible
	}
	return false
}

func (x *KnowledgeBase) GetIsRetrievable() bool {
	if x != nil {
		return x.IsRetrievable
	}
	return false
}

func (x *KnowledgeBase) GetCreationType() KnowledgeBaseCreationType {
	if x != nil {
		return x.CreationType
	}
	return KnowledgeBaseCreationType_FROM_MANAGEMENT
}

func (x *KnowledgeBase) GetDocProcessingConfig() *DocProcessingConfig {
	if x != nil {
		return x.DocProcessingConfig
	}
	return nil
}

func (x *KnowledgeBase) GetRetrievalConfig() *RetrievalConfig {
	if x != nil {
		return x.RetrievalConfig
	}
	return nil
}

func (x *KnowledgeBase) GetVectorModel() *ModelService {
	if x != nil {
		return x.VectorModel
	}
	return nil
}

func (x *KnowledgeBase) GetIsPublic() bool {
	if x != nil {
		return x.IsPublic
	}
	return false
}

func (x *KnowledgeBase) GetIsPublished() bool {
	if x != nil {
		return x.IsPublished
	}
	return false
}

func (x *KnowledgeBase) GetSceneType() KnowledgeBaseSceneType {
	if x != nil {
		return x.SceneType
	}
	return KnowledgeBaseSceneType_SceneType_QA
}

func (x *KnowledgeBase) GetMetricsInfo() *MetricsInfo {
	if x != nil {
		return x.MetricsInfo
	}
	return nil
}

func (x *KnowledgeBase) GetPublishInfo() *KnowledgeBasePublishInfo {
	if x != nil {
		return x.PublishInfo
	}
	return nil
}

type MetricsInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VisitTimes   int64 `protobuf:"varint,1,opt,name=visit_times,json=visitTimes,proto3" json:"visit_times,omitempty"`
	CloneTimes   int64 `protobuf:"varint,2,opt,name=clone_times,json=cloneTimes,proto3" json:"clone_times,omitempty"`
	ExecuteTimes int64 `protobuf:"varint,3,opt,name=execute_times,json=executeTimes,proto3" json:"execute_times,omitempty"`
}

func (x *MetricsInfo) Reset() {
	*x = MetricsInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_knowledge_base_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MetricsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetricsInfo) ProtoMessage() {}

func (x *MetricsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_knowledge_base_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetricsInfo.ProtoReflect.Descriptor instead.
func (*MetricsInfo) Descriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{4}
}

func (x *MetricsInfo) GetVisitTimes() int64 {
	if x != nil {
		return x.VisitTimes
	}
	return 0
}

func (x *MetricsInfo) GetCloneTimes() int64 {
	if x != nil {
		return x.CloneTimes
	}
	return 0
}

func (x *MetricsInfo) GetExecuteTimes() int64 {
	if x != nil {
		return x.ExecuteTimes
	}
	return 0
}

type AppletServiceRef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicationId string `protobuf:"bytes,1,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	ServiceId     string `protobuf:"bytes,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	Name          string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *AppletServiceRef) Reset() {
	*x = AppletServiceRef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_knowledge_base_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppletServiceRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppletServiceRef) ProtoMessage() {}

func (x *AppletServiceRef) ProtoReflect() protoreflect.Message {
	mi := &file_proto_knowledge_base_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppletServiceRef.ProtoReflect.Descriptor instead.
func (*AppletServiceRef) Descriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{5}
}

func (x *AppletServiceRef) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *AppletServiceRef) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *AppletServiceRef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DocProcessingConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"文档加工策略来源"
	DocProcessingStrategyOrigin StrategyOrigin `protobuf:"varint,1,opt,name=doc_processing_strategy_origin,json=docProcessingStrategyOrigin,proto3,enum=proto.StrategyOrigin" json:"doc_processing_strategy_origin,omitempty" description:"文档加工策略来源"`
	// @gotags: description:"文档分割设置"
	DocSplitConfig *DocSplitConfig `protobuf:"bytes,2,opt,name=doc_split_config,json=docSplitConfig,proto3" json:"doc_split_config,omitempty" description:"文档分割设置"`
	// @gotags: description:"分段增强设置"
	ChunkAugmentConfig *ChunkAugmentConfig `protobuf:"bytes,3,opt,name=chunk_augment_config,json=chunkAugmentConfig,proto3" json:"chunk_augment_config,omitempty" description:"分段增强设置"`
	// @gotags: description:"应用链服务；自定义策略时指定"
	AppletService *AppletServiceRef `protobuf:"bytes,4,opt,name=applet_service,json=appletService,proto3" json:"applet_service,omitempty" description:"应用链服务；自定义策略时指定"`
	// @gotags: description:"文档解析设置"
	DocLoadConfig *DocLoadConfig `protobuf:"bytes,5,opt,name=doc_load_config,json=docLoadConfig,proto3" json:"doc_load_config,omitempty" description:"文档解析设置"`
	// @gotags: description:"是否自动配置"
	AutoConfigured bool `protobuf:"varint,6,opt,name=auto_configured,json=autoConfigured,proto3" json:"auto_configured,omitempty" description:"是否自动配置"`
}

func (x *DocProcessingConfig) Reset() {
	*x = DocProcessingConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_knowledge_base_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DocProcessingConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocProcessingConfig) ProtoMessage() {}

func (x *DocProcessingConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_knowledge_base_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocProcessingConfig.ProtoReflect.Descriptor instead.
func (*DocProcessingConfig) Descriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{6}
}

func (x *DocProcessingConfig) GetDocProcessingStrategyOrigin() StrategyOrigin {
	if x != nil {
		return x.DocProcessingStrategyOrigin
	}
	return StrategyOrigin_PRESET
}

func (x *DocProcessingConfig) GetDocSplitConfig() *DocSplitConfig {
	if x != nil {
		return x.DocSplitConfig
	}
	return nil
}

func (x *DocProcessingConfig) GetChunkAugmentConfig() *ChunkAugmentConfig {
	if x != nil {
		return x.ChunkAugmentConfig
	}
	return nil
}

func (x *DocProcessingConfig) GetAppletService() *AppletServiceRef {
	if x != nil {
		return x.AppletService
	}
	return nil
}

func (x *DocProcessingConfig) GetDocLoadConfig() *DocLoadConfig {
	if x != nil {
		return x.DocLoadConfig
	}
	return nil
}

func (x *DocProcessingConfig) GetAutoConfigured() bool {
	if x != nil {
		return x.AutoConfigured
	}
	return false
}

type DocLoadConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文档解析策略
	StrategyType DocLoadStrategyType `protobuf:"varint,1,opt,name=strategy_type,json=strategyType,proto3,enum=proto.DocLoadStrategyType" json:"strategy_type,omitempty"`
	// 保留图片
	RemainFigure bool `protobuf:"varint,20,opt,name=remain_figure,json=remainFigure,proto3" json:"remain_figure,omitempty"`
	// 保留表格
	RemainTable bool `protobuf:"varint,21,opt,name=remain_table,json=remainTable,proto3" json:"remain_table,omitempty"`
	// 识别数学公式
	RecogMathFormula bool `protobuf:"varint,22,opt,name=recog_math_formula,json=recogMathFormula,proto3" json:"recog_math_formula,omitempty"`
	// 识别化学公式
	RecogChemicalFormula bool `protobuf:"varint,23,opt,name=recog_chemical_formula,json=recogChemicalFormula,proto3" json:"recog_chemical_formula,omitempty"`
	// @gotags: description:"doc-engine配置文件"
	EntitySchemaConf string `protobuf:"bytes,24,opt,name=entity_schema_conf,json=entitySchemaConf,proto3" json:"entity_schema_conf,omitempty" description:"doc-engine配置文件"`
	// @gotags: description:"deprecated 文本解析服务,当策略为HI_RES或VL_MODEL时必填 弃用"
	DocService *serving.MLOpsServiceBaseInfo `protobuf:"bytes,12,opt,name=doc_service,json=docService,proto3" json:"doc_service,omitempty" description:"deprecated 文本解析服务,当策略为HI_RES或VL_MODEL时必填 弃用"`
	// @gotags: description:"图像理解模型，当策略为VL_MODEL时必填"
	ImageModel *ModelService `protobuf:"bytes,13,opt,name=image_model,json=imageModel,proto3" json:"image_model,omitempty" description:"图像理解模型，当策略为VL_MODEL时必填"`
	// @gotags: description:"文本解析模型服务,当策略为HI_RES或VL_MODEL时必填"
	DocModel *ModelService `protobuf:"bytes,14,opt,name=doc_model,json=docModel,proto3" json:"doc_model,omitempty" description:"文本解析模型服务,当策略为HI_RES或VL_MODEL时必填"`
}

func (x *DocLoadConfig) Reset() {
	*x = DocLoadConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_knowledge_base_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DocLoadConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocLoadConfig) ProtoMessage() {}

func (x *DocLoadConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_knowledge_base_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocLoadConfig.ProtoReflect.Descriptor instead.
func (*DocLoadConfig) Descriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{7}
}

func (x *DocLoadConfig) GetStrategyType() DocLoadStrategyType {
	if x != nil {
		return x.StrategyType
	}
	return DocLoadStrategyType_AUTO
}

func (x *DocLoadConfig) GetRemainFigure() bool {
	if x != nil {
		return x.RemainFigure
	}
	return false
}

func (x *DocLoadConfig) GetRemainTable() bool {
	if x != nil {
		return x.RemainTable
	}
	return false
}

func (x *DocLoadConfig) GetRecogMathFormula() bool {
	if x != nil {
		return x.RecogMathFormula
	}
	return false
}

func (x *DocLoadConfig) GetRecogChemicalFormula() bool {
	if x != nil {
		return x.RecogChemicalFormula
	}
	return false
}

func (x *DocLoadConfig) GetEntitySchemaConf() string {
	if x != nil {
		return x.EntitySchemaConf
	}
	return ""
}

func (x *DocLoadConfig) GetDocService() *serving.MLOpsServiceBaseInfo {
	if x != nil {
		return x.DocService
	}
	return nil
}

func (x *DocLoadConfig) GetImageModel() *ModelService {
	if x != nil {
		return x.ImageModel
	}
	return nil
}

func (x *DocLoadConfig) GetDocModel() *ModelService {
	if x != nil {
		return x.DocModel
	}
	return nil
}

type RetrievalConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"检索策略来源"
	RetrievalStrategyOrigin StrategyOrigin `protobuf:"varint,1,opt,name=retrieval_strategy_origin,json=retrievalStrategyOrigin,proto3,enum=proto.StrategyOrigin" json:"retrieval_strategy_origin,omitempty" description:"检索策略来源"`
	// @gotags: description:"检索策略"
	Strategy KnowledgeBaseRetrieveStrategy `protobuf:"varint,2,opt,name=strategy,proto3,enum=proto.KnowledgeBaseRetrieveStrategy" json:"strategy,omitempty" description:"检索策略"`
	// @gotags: description:"召回参数"
	RecallParams *RecallParams `protobuf:"bytes,3,opt,name=recall_params,json=recallParams,proto3" json:"recall_params,omitempty" description:"召回参数"`
	// @gotags: description:"重排参数"
	RerankParams *RerankParams `protobuf:"bytes,4,opt,name=rerank_params,json=rerankParams,proto3" json:"rerank_params,omitempty" description:"重排参数"`
	// @gotags: description:"应用链服务；自定义策略时指定" TODO
	ChainService string `protobuf:"bytes,5,opt,name=chain_service,json=chainService,proto3" json:"chain_service,omitempty" description:"应用链服务；自定义策略时指定"`
	// @gotags: description:"是否自动配置"
	AutoConfigured bool `protobuf:"varint,6,opt,name=auto_configured,json=autoConfigured,proto3" json:"auto_configured,omitempty" description:"是否自动配置"`
	// @gotags: description:"切片上下文切片数量"
	ContextNum int32 `protobuf:"varint,7,opt,name=context_num,json=contextNum,proto3" json:"context_num,omitempty" description:"切片上下文切片数量"`
}

func (x *RetrievalConfig) Reset() {
	*x = RetrievalConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_knowledge_base_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrievalConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrievalConfig) ProtoMessage() {}

func (x *RetrievalConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_knowledge_base_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrievalConfig.ProtoReflect.Descriptor instead.
func (*RetrievalConfig) Descriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{8}
}

func (x *RetrievalConfig) GetRetrievalStrategyOrigin() StrategyOrigin {
	if x != nil {
		return x.RetrievalStrategyOrigin
	}
	return StrategyOrigin_PRESET
}

func (x *RetrievalConfig) GetStrategy() KnowledgeBaseRetrieveStrategy {
	if x != nil {
		return x.Strategy
	}
	return KnowledgeBaseRetrieveStrategy_VECTOR
}

func (x *RetrievalConfig) GetRecallParams() *RecallParams {
	if x != nil {
		return x.RecallParams
	}
	return nil
}

func (x *RetrievalConfig) GetRerankParams() *RerankParams {
	if x != nil {
		return x.RerankParams
	}
	return nil
}

func (x *RetrievalConfig) GetChainService() string {
	if x != nil {
		return x.ChainService
	}
	return ""
}

func (x *RetrievalConfig) GetAutoConfigured() bool {
	if x != nil {
		return x.AutoConfigured
	}
	return false
}

func (x *RetrievalConfig) GetContextNum() int32 {
	if x != nil {
		return x.ContextNum
	}
	return 0
}

type DocSplitConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"分段切分标识符"
	Separator string `protobuf:"bytes,1,opt,name=separator,proto3" json:"separator,omitempty" description:"分段切分标识符"`
	// @gotags: description:"分段最大长度"
	ChunkSize int32 `protobuf:"varint,2,opt,name=chunk_size,json=chunkSize,proto3" json:"chunk_size,omitempty" description:"分段最大长度"`
	// @gotags: description:"分段重叠长度"
	ChunkOverlap int32 `protobuf:"varint,3,opt,name=chunk_overlap,json=chunkOverlap,proto3" json:"chunk_overlap,omitempty" description:"分段重叠长度"`
	// @gotags: description:"分段切分标识符列表"
	Separators []string `protobuf:"bytes,4,rep,name=separators,proto3" json:"separators,omitempty" description:"分段切分标识符列表"`
	// @gotags: description:"分段策略"
	SplitStrategyType DocSplitStrategyType `protobuf:"varint,5,opt,name=split_strategy_type,json=splitStrategyType,proto3,enum=proto.DocSplitStrategyType" json:"split_strategy_type,omitempty" description:"分段策略"`
	// @gotags: description:"是否禁用分段合并"
	DisableChunkMerging bool `protobuf:"varint,6,opt,name=disable_chunk_merging,json=disableChunkMerging,proto3" json:"disable_chunk_merging,omitempty" description:"是否禁用分段合并"`
}

func (x *DocSplitConfig) Reset() {
	*x = DocSplitConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_knowledge_base_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DocSplitConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocSplitConfig) ProtoMessage() {}

func (x *DocSplitConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_knowledge_base_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocSplitConfig.ProtoReflect.Descriptor instead.
func (*DocSplitConfig) Descriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{9}
}

func (x *DocSplitConfig) GetSeparator() string {
	if x != nil {
		return x.Separator
	}
	return ""
}

func (x *DocSplitConfig) GetChunkSize() int32 {
	if x != nil {
		return x.ChunkSize
	}
	return 0
}

func (x *DocSplitConfig) GetChunkOverlap() int32 {
	if x != nil {
		return x.ChunkOverlap
	}
	return 0
}

func (x *DocSplitConfig) GetSeparators() []string {
	if x != nil {
		return x.Separators
	}
	return nil
}

func (x *DocSplitConfig) GetSplitStrategyType() DocSplitStrategyType {
	if x != nil {
		return x.SplitStrategyType
	}
	return DocSplitStrategyType_CHARACTER
}

func (x *DocSplitConfig) GetDisableChunkMerging() bool {
	if x != nil {
		return x.DisableChunkMerging
	}
	return false
}

type ChunkAugmentConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"是否启用文本增强"
	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty" description:"是否启用文本增强"`
	// @gotags: description:"问题数量"
	QuestionNum int32 `protobuf:"varint,2,opt,name=question_num,json=questionNum,proto3" json:"question_num,omitempty" description:"问题数量"`
	// @gotags: description:"摘要数量"
	SummaryNum int32 `protobuf:"varint,3,opt,name=summary_num,json=summaryNum,proto3" json:"summary_num,omitempty" description:"摘要数量"`
	// @gotags: description:"文本生成模型"
	TextModel *ModelService `protobuf:"bytes,4,opt,name=text_model,json=textModel,proto3" json:"text_model,omitempty" description:"文本生成模型"`
	// @gotags: description:"表格描述数量"
	TableDescNum int32 `protobuf:"varint,5,opt,name=table_desc_num,json=tableDescNum,proto3" json:"table_desc_num,omitempty" description:"表格描述数量"`
	// @gotags: description:"表格摘要数量"
	TableSummaryNum int32 `protobuf:"varint,6,opt,name=table_summary_num,json=tableSummaryNum,proto3" json:"table_summary_num,omitempty" description:"表格摘要数量"`
	// @gotags: description:"用于生成问题的提示词"
	QuestionPrompt string `protobuf:"bytes,7,opt,name=question_prompt,json=questionPrompt,proto3" json:"question_prompt,omitempty" description:"用于生成问题的提示词"`
	// @gotags: description:"用于生成摘要的提示词"
	SummaryPrompt string `protobuf:"bytes,8,opt,name=summary_prompt,json=summaryPrompt,proto3" json:"summary_prompt,omitempty" description:"用于生成摘要的提示词"`
	// @gotags: description:"用于生成表格描述的提示词"
	TableDescPrompt string `protobuf:"bytes,9,opt,name=table_desc_prompt,json=tableDescPrompt,proto3" json:"table_desc_prompt,omitempty" description:"用于生成表格描述的提示词"`
	// @gotags: description:"用于生成表格摘要的提示词"
	TableSummaryPrompt string `protobuf:"bytes,10,opt,name=table_summary_prompt,json=tableSummaryPrompt,proto3" json:"table_summary_prompt,omitempty" description:"用于生成表格摘要的提示词"`
	// @gotags: description:"是否开启图像增强"
	ImageEnable bool `protobuf:"varint,11,opt,name=image_enable,json=imageEnable,proto3" json:"image_enable,omitempty" description:"是否开启图像增强"`
	// @gotags: description:"图像理解模型"
	ImageModel *ModelService `protobuf:"bytes,13,opt,name=image_model,json=imageModel,proto3" json:"image_model,omitempty" description:"图像理解模型"`
	// @gotags: description:"图像描述数量"
	ImageDescNum int32 `protobuf:"varint,14,opt,name=image_desc_num,json=imageDescNum,proto3" json:"image_desc_num,omitempty" description:"图像描述数量"`
	// @gotags: description:"用于生成图像描述提示词"
	ImageDescPrompt string `protobuf:"bytes,15,opt,name=image_desc_prompt,json=imageDescPrompt,proto3" json:"image_desc_prompt,omitempty" description:"用于生成图像描述提示词"`
}

func (x *ChunkAugmentConfig) Reset() {
	*x = ChunkAugmentConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_knowledge_base_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChunkAugmentConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChunkAugmentConfig) ProtoMessage() {}

func (x *ChunkAugmentConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_knowledge_base_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChunkAugmentConfig.ProtoReflect.Descriptor instead.
func (*ChunkAugmentConfig) Descriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{10}
}

func (x *ChunkAugmentConfig) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *ChunkAugmentConfig) GetQuestionNum() int32 {
	if x != nil {
		return x.QuestionNum
	}
	return 0
}

func (x *ChunkAugmentConfig) GetSummaryNum() int32 {
	if x != nil {
		return x.SummaryNum
	}
	return 0
}

func (x *ChunkAugmentConfig) GetTextModel() *ModelService {
	if x != nil {
		return x.TextModel
	}
	return nil
}

func (x *ChunkAugmentConfig) GetTableDescNum() int32 {
	if x != nil {
		return x.TableDescNum
	}
	return 0
}

func (x *ChunkAugmentConfig) GetTableSummaryNum() int32 {
	if x != nil {
		return x.TableSummaryNum
	}
	return 0
}

func (x *ChunkAugmentConfig) GetQuestionPrompt() string {
	if x != nil {
		return x.QuestionPrompt
	}
	return ""
}

func (x *ChunkAugmentConfig) GetSummaryPrompt() string {
	if x != nil {
		return x.SummaryPrompt
	}
	return ""
}

func (x *ChunkAugmentConfig) GetTableDescPrompt() string {
	if x != nil {
		return x.TableDescPrompt
	}
	return ""
}

func (x *ChunkAugmentConfig) GetTableSummaryPrompt() string {
	if x != nil {
		return x.TableSummaryPrompt
	}
	return ""
}

func (x *ChunkAugmentConfig) GetImageEnable() bool {
	if x != nil {
		return x.ImageEnable
	}
	return false
}

func (x *ChunkAugmentConfig) GetImageModel() *ModelService {
	if x != nil {
		return x.ImageModel
	}
	return nil
}

func (x *ChunkAugmentConfig) GetImageDescNum() int32 {
	if x != nil {
		return x.ImageDescNum
	}
	return 0
}

func (x *ChunkAugmentConfig) GetImageDescPrompt() string {
	if x != nil {
		return x.ImageDescPrompt
	}
	return ""
}

type RecallParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"topk，限制召回个数"
	TopK int32 `protobuf:"varint,1,opt,name=top_k,json=topK,proto3" json:"top_k,omitempty" description:"topk，限制召回个数"`
	// @gotags: description:"分数阈值, 取值范围[0,1)"
	ScoreThreshold float32 `protobuf:"fixed32,2,opt,name=score_threshold,json=scoreThreshold,proto3" json:"score_threshold,omitempty" description:"分数阈值, 取值范围[0,1)"`
	// @gotags: description:"检索策略"
	Strategy KnowledgeBaseRetrieveStrategy `protobuf:"varint,3,opt,name=strategy,proto3,enum=proto.KnowledgeBaseRetrieveStrategy" json:"strategy,omitempty" description:"检索策略"`
}

func (x *RecallParams) Reset() {
	*x = RecallParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_knowledge_base_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecallParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecallParams) ProtoMessage() {}

func (x *RecallParams) ProtoReflect() protoreflect.Message {
	mi := &file_proto_knowledge_base_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecallParams.ProtoReflect.Descriptor instead.
func (*RecallParams) Descriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{11}
}

func (x *RecallParams) GetTopK() int32 {
	if x != nil {
		return x.TopK
	}
	return 0
}

func (x *RecallParams) GetScoreThreshold() float32 {
	if x != nil {
		return x.ScoreThreshold
	}
	return 0
}

func (x *RecallParams) GetStrategy() KnowledgeBaseRetrieveStrategy {
	if x != nil {
		return x.Strategy
	}
	return KnowledgeBaseRetrieveStrategy_VECTOR
}

type RerankParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"topk，限制召回个数"
	TopK int32 `protobuf:"varint,1,opt,name=top_k,json=topK,proto3" json:"top_k,omitempty" description:"topk，限制召回个数"`
	// @gotags: description:"分数阈值, 取值范围[0,1)"
	ScoreThreshold float32 `protobuf:"fixed32,2,opt,name=score_threshold,json=scoreThreshold,proto3" json:"score_threshold,omitempty" description:"分数阈值, 取值范围[0,1)"`
	// @gotags: description:"rerank模型"
	Model *ModelService `protobuf:"bytes,3,opt,name=model,proto3" json:"model,omitempty" description:"rerank模型"`
	// @gotags: description:"是否保留不符合分数阈值的结果"
	RetainUnqualified bool `protobuf:"varint,4,opt,name=retain_unqualified,json=retainUnqualified,proto3" json:"retain_unqualified,omitempty" description:"是否保留不符合分数阈值的结果"`
}

func (x *RerankParams) Reset() {
	*x = RerankParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_knowledge_base_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RerankParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RerankParams) ProtoMessage() {}

func (x *RerankParams) ProtoReflect() protoreflect.Message {
	mi := &file_proto_knowledge_base_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RerankParams.ProtoReflect.Descriptor instead.
func (*RerankParams) Descriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{12}
}

func (x *RerankParams) GetTopK() int32 {
	if x != nil {
		return x.TopK
	}
	return 0
}

func (x *RerankParams) GetScoreThreshold() float32 {
	if x != nil {
		return x.ScoreThreshold
	}
	return 0
}

func (x *RerankParams) GetModel() *ModelService {
	if x != nil {
		return x.Model
	}
	return nil
}

func (x *RerankParams) GetRetainUnqualified() bool {
	if x != nil {
		return x.RetainUnqualified
	}
	return false
}

type Document struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"文档id"
	DocId string `protobuf:"bytes,1,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty" description:"文档id"`
	// @gotags: description:"文档名称"
	DocName string `protobuf:"bytes,2,opt,name=doc_name,json=docName,proto3" json:"doc_name,omitempty" description:"文档名称"`
	// @gotags: description:"文档路径"
	FilePath string `protobuf:"bytes,3,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty" description:"文档路径"`
	// @gotags: description:"文档大小"
	FileSizeBytes int32 `protobuf:"varint,4,opt,name=file_size_bytes,json=fileSizeBytes,proto3" json:"file_size_bytes,omitempty" description:"文档大小"`
	// @gotags: description:"文档格式"
	FileFormat string `protobuf:"bytes,5,opt,name=file_format,json=fileFormat,proto3" json:"file_format,omitempty" description:"文档格式"`
	// @gotags: description:"上传时间"
	UploadTimeMills int64 `protobuf:"varint,6,opt,name=upload_time_mills,json=uploadTimeMills,proto3" json:"upload_time_mills,omitempty" description:"上传时间"`
	// @gotags: description:"字符数"
	NumChars        int32  `protobuf:"varint,7,opt,name=num_chars,json=numChars,proto3" json:"num_chars,omitempty" description:"字符数"`
	FileMd5         string `protobuf:"bytes,8,opt,name=file_md5,json=fileMd5,proto3" json:"file_md5,omitempty"`
	KnowledgeBaseId string `protobuf:"bytes,9,opt,name=knowledge_base_id,json=knowledgeBaseId,proto3" json:"knowledge_base_id,omitempty"`
	// @gotags: description:"表格配置, 仅表格文档时有效"
	TableConfig *TableConfig `protobuf:"bytes,10,opt,name=table_config,json=tableConfig,proto3" json:"table_config,omitempty" description:"表格配置, 仅表格文档时有效"`
	// @gotags: description:"数据来源"
	DocumentFileSource DocumentFileSource `protobuf:"varint,11,opt,name=document_file_source,json=documentFileSource,proto3,enum=proto.DocumentFileSource" json:"document_file_source,omitempty" description:"数据来源"`
	// @gotags: description:"语料来源配置；仅来源为语料时使用"
	CorpusConfig *CorpusConfig `protobuf:"bytes,12,opt,name=corpus_config,json=corpusConfig,proto3" json:"corpus_config,omitempty" description:"语料来源配置；仅来源为语料时使用"`
	// @gotags: description:"文档加工设置"
	DocProcessingConfig *DocProcessingConfig `protobuf:"bytes,13,opt,name=doc_processing_config,json=docProcessingConfig,proto3" json:"doc_processing_config,omitempty" description:"文档加工设置"`
}

func (x *Document) Reset() {
	*x = Document{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_knowledge_base_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Document) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Document) ProtoMessage() {}

func (x *Document) ProtoReflect() protoreflect.Message {
	mi := &file_proto_knowledge_base_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Document.ProtoReflect.Descriptor instead.
func (*Document) Descriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{13}
}

func (x *Document) GetDocId() string {
	if x != nil {
		return x.DocId
	}
	return ""
}

func (x *Document) GetDocName() string {
	if x != nil {
		return x.DocName
	}
	return ""
}

func (x *Document) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *Document) GetFileSizeBytes() int32 {
	if x != nil {
		return x.FileSizeBytes
	}
	return 0
}

func (x *Document) GetFileFormat() string {
	if x != nil {
		return x.FileFormat
	}
	return ""
}

func (x *Document) GetUploadTimeMills() int64 {
	if x != nil {
		return x.UploadTimeMills
	}
	return 0
}

func (x *Document) GetNumChars() int32 {
	if x != nil {
		return x.NumChars
	}
	return 0
}

func (x *Document) GetFileMd5() string {
	if x != nil {
		return x.FileMd5
	}
	return ""
}

func (x *Document) GetKnowledgeBaseId() string {
	if x != nil {
		return x.KnowledgeBaseId
	}
	return ""
}

func (x *Document) GetTableConfig() *TableConfig {
	if x != nil {
		return x.TableConfig
	}
	return nil
}

func (x *Document) GetDocumentFileSource() DocumentFileSource {
	if x != nil {
		return x.DocumentFileSource
	}
	return DocumentFileSource_LOCAL_FILE
}

func (x *Document) GetCorpusConfig() *CorpusConfig {
	if x != nil {
		return x.CorpusConfig
	}
	return nil
}

func (x *Document) GetDocProcessingConfig() *DocProcessingConfig {
	if x != nil {
		return x.DocProcessingConfig
	}
	return nil
}

type DisplayInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *DisplayInfo) Reset() {
	*x = DisplayInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_knowledge_base_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisplayInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisplayInfo) ProtoMessage() {}

func (x *DisplayInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_knowledge_base_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisplayInfo.ProtoReflect.Descriptor instead.
func (*DisplayInfo) Descriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{14}
}

func (x *DisplayInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DisplayInfo) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type Point struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X float32 `protobuf:"fixed32,1,opt,name=x,proto3" json:"x,omitempty"`
	Y float32 `protobuf:"fixed32,2,opt,name=y,proto3" json:"y,omitempty"`
}

func (x *Point) Reset() {
	*x = Point{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_knowledge_base_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Point) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Point) ProtoMessage() {}

func (x *Point) ProtoReflect() protoreflect.Message {
	mi := &file_proto_knowledge_base_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Point.ProtoReflect.Descriptor instead.
func (*Point) Descriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{15}
}

func (x *Point) GetX() float32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *Point) GetY() float32 {
	if x != nil {
		return x.Y
	}
	return 0
}

type Coordinates struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"坐标点，四个点表示一个矩形区域，大于四个点的区域表示一个多边形区域"
	Points []*Point `protobuf:"bytes,1,rep,name=points,proto3" json:"points,omitempty" description:"坐标点，四个点表示一个矩形区域，大于四个点的区域表示一个多边形区域"`
	// @gotags: description:"版面宽度"
	LayoutWidth float32 `protobuf:"fixed32,2,opt,name=layout_width,json=layoutWidth,proto3" json:"layout_width,omitempty" description:"版面宽度"`
	// @gotags: description:"版面宽度"
	LayoutHeight float32 `protobuf:"fixed32,3,opt,name=layout_height,json=layoutHeight,proto3" json:"layout_height,omitempty" description:"版面宽度"`
}

func (x *Coordinates) Reset() {
	*x = Coordinates{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_knowledge_base_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Coordinates) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Coordinates) ProtoMessage() {}

func (x *Coordinates) ProtoReflect() protoreflect.Message {
	mi := &file_proto_knowledge_base_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Coordinates.ProtoReflect.Descriptor instead.
func (*Coordinates) Descriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{16}
}

func (x *Coordinates) GetPoints() []*Point {
	if x != nil {
		return x.Points
	}
	return nil
}

func (x *Coordinates) GetLayoutWidth() float32 {
	if x != nil {
		return x.LayoutWidth
	}
	return 0
}

func (x *Coordinates) GetLayoutHeight() float32 {
	if x != nil {
		return x.LayoutHeight
	}
	return 0
}

type DocElement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"文档元素id"
	ElementId string `protobuf:"bytes,1,opt,name=element_id,json=elementId,proto3" json:"element_id,omitempty" description:"文档元素id"`
	// @gotags: description:"文档元素类型"
	Type DocElementType `protobuf:"varint,2,opt,name=type,proto3,enum=proto.DocElementType" json:"type,omitempty" description:"文档元素类型"`
	// @gotags: description:"文本"
	Text string `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty" description:"文本"`
	// @gotags: description:"元数据"
	Metadata *DocElementMetadata `protobuf:"bytes,4,opt,name=metadata,proto3" json:"metadata,omitempty" description:"元数据"`
	// @gotags: description:"父元素id列表，自顶向底排序"
	ParentIds []string `protobuf:"bytes,5,rep,name=parent_ids,json=parentIds,proto3" json:"parent_ids,omitempty" description:"父元素id列表，自顶向底排序"`
	// @gotags: description:"父元素内容列表，自顶向底排序"
	ParentTexts []string `protobuf:"bytes,6,rep,name=parent_texts,json=parentTexts,proto3" json:"parent_texts,omitempty" description:"父元素内容列表，自顶向底排序"`
	// @gotags: description:"额外属性"
	Extra map[string]string `protobuf:"bytes,7,rep,name=extra,proto3" json:"extra,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"额外属性"`
}

func (x *DocElement) Reset() {
	*x = DocElement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_knowledge_base_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DocElement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocElement) ProtoMessage() {}

func (x *DocElement) ProtoReflect() protoreflect.Message {
	mi := &file_proto_knowledge_base_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocElement.ProtoReflect.Descriptor instead.
func (*DocElement) Descriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{17}
}

func (x *DocElement) GetElementId() string {
	if x != nil {
		return x.ElementId
	}
	return ""
}

func (x *DocElement) GetType() DocElementType {
	if x != nil {
		return x.Type
	}
	return DocElementType_DocElementType_FigureCaption
}

func (x *DocElement) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *DocElement) GetMetadata() *DocElementMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *DocElement) GetParentIds() []string {
	if x != nil {
		return x.ParentIds
	}
	return nil
}

func (x *DocElement) GetParentTexts() []string {
	if x != nil {
		return x.ParentTexts
	}
	return nil
}

func (x *DocElement) GetExtra() map[string]string {
	if x != nil {
		return x.Extra
	}
	return nil
}

type DocElementMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"文档元素所在页码"
	PageNumber int32 `protobuf:"varint,1,opt,name=page_number,json=pageNumber,proto3" json:"page_number,omitempty" description:"文档元素所在页码"`
	// @gotags: description:"文档元素区域坐标"
	Coordinates *Coordinates `protobuf:"bytes,2,opt,name=coordinates,proto3" json:"coordinates,omitempty" description:"文档元素区域坐标"`
	// 检测置信度
	DetectionClassProb float32 `protobuf:"fixed32,3,opt,name=detection_class_prob,json=detectionClassProb,proto3" json:"detection_class_prob,omitempty"`
	// @gotags: description:"如果是表格类型的element，此字段存放html格式的文本"
	TextAsHtml string `protobuf:"bytes,4,opt,name=text_as_html,json=textAsHtml,proto3" json:"text_as_html,omitempty" description:"如果是表格类型的element，此字段存放html格式的文本"`
	ParentId   string `protobuf:"bytes,5,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
}

func (x *DocElementMetadata) Reset() {
	*x = DocElementMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_knowledge_base_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DocElementMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocElementMetadata) ProtoMessage() {}

func (x *DocElementMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_proto_knowledge_base_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocElementMetadata.ProtoReflect.Descriptor instead.
func (*DocElementMetadata) Descriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{18}
}

func (x *DocElementMetadata) GetPageNumber() int32 {
	if x != nil {
		return x.PageNumber
	}
	return 0
}

func (x *DocElementMetadata) GetCoordinates() *Coordinates {
	if x != nil {
		return x.Coordinates
	}
	return nil
}

func (x *DocElementMetadata) GetDetectionClassProb() float32 {
	if x != nil {
		return x.DetectionClassProb
	}
	return 0
}

func (x *DocElementMetadata) GetTextAsHtml() string {
	if x != nil {
		return x.TextAsHtml
	}
	return ""
}

func (x *DocElementMetadata) GetParentId() string {
	if x != nil {
		return x.ParentId
	}
	return ""
}

type Chunk struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"chunk id"
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" description:"chunk id"`
	// @gotags: description:"原文内容"
	Content string `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty" description:"原文内容"`
	// @gotags: description:"chunk对应的文档元素id列表,仅原文类型的chunk需要该属性"
	ElementIds []string `protobuf:"bytes,3,rep,name=element_ids,json=elementIds,proto3" json:"element_ids,omitempty" description:"chunk对应的文档元素id列表,仅原文类型的chunk需要该属性"`
	// @gotags: description:"chunk来源类型"
	SourceType ChunkSourceType `protobuf:"varint,4,opt,name=source_type,json=sourceType,proto3,enum=proto.ChunkSourceType" json:"source_type,omitempty" description:"chunk来源类型"`
	// @gotags: description:"chunk原文内容类型"
	ContentType OriginalContentType `protobuf:"varint,5,opt,name=content_type,json=contentType,proto3,enum=proto.OriginalContentType" json:"content_type,omitempty" description:"chunk原文内容类型"`
	// @gotags: description:"是否禁用向量化索引"
	DisableVectorIndexing bool `protobuf:"varint,6,opt,name=disable_vector_indexing,json=disableVectorIndexing,proto3" json:"disable_vector_indexing,omitempty" description:"是否禁用向量化索引"`
	// @gotags: description:"是否禁用全文索引"
	DisableFullTextIndexing bool `protobuf:"varint,7,opt,name=disable_full_text_indexing,json=disableFullTextIndexing,proto3" json:"disable_full_text_indexing,omitempty" description:"是否禁用全文索引"`
	// @gotags: description:"知识增强的分段列表"
	AugmentedChunks []*AugmentedChunk `protobuf:"bytes,8,rep,name=augmented_chunks,json=augmentedChunks,proto3" json:"augmented_chunks,omitempty" description:"知识增强的分段列表"`
	// @gotags: description:"流转中产生的额外属性，不会进行存储"
	Extra map[string]string `protobuf:"bytes,9,rep,name=extra,proto3" json:"extra,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"流转中产生的额外属性，不会进行存储"`
	// @gotags: description:"是否被修改编辑过"
	Edited bool `protobuf:"varint,10,opt,name=edited,proto3" json:"edited,omitempty" description:"是否被修改编辑过"`
}

func (x *Chunk) Reset() {
	*x = Chunk{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_knowledge_base_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Chunk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Chunk) ProtoMessage() {}

func (x *Chunk) ProtoReflect() protoreflect.Message {
	mi := &file_proto_knowledge_base_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Chunk.ProtoReflect.Descriptor instead.
func (*Chunk) Descriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{19}
}

func (x *Chunk) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Chunk) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *Chunk) GetElementIds() []string {
	if x != nil {
		return x.ElementIds
	}
	return nil
}

func (x *Chunk) GetSourceType() ChunkSourceType {
	if x != nil {
		return x.SourceType
	}
	return ChunkSourceType_SOURCE_TYPE_GENERATED
}

func (x *Chunk) GetContentType() OriginalContentType {
	if x != nil {
		return x.ContentType
	}
	return OriginalContentType_ORIGINAL_CONTENT_TYPE_TEXT
}

func (x *Chunk) GetDisableVectorIndexing() bool {
	if x != nil {
		return x.DisableVectorIndexing
	}
	return false
}

func (x *Chunk) GetDisableFullTextIndexing() bool {
	if x != nil {
		return x.DisableFullTextIndexing
	}
	return false
}

func (x *Chunk) GetAugmentedChunks() []*AugmentedChunk {
	if x != nil {
		return x.AugmentedChunks
	}
	return nil
}

func (x *Chunk) GetExtra() map[string]string {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *Chunk) GetEdited() bool {
	if x != nil {
		return x.Edited
	}
	return false
}

type AugmentedChunk struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"chunk id"
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" description:"chunk id"`
	// @gotags: description:"增强内容"
	Content string `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty" description:"增强内容"`
	// @gotags: description:"chunk来源类型"
	SourceType ChunkSourceType `protobuf:"varint,3,opt,name=source_type,json=sourceType,proto3,enum=proto.ChunkSourceType" json:"source_type,omitempty" description:"chunk来源类型"`
	// @gotags: description:"增强类型"
	AugmentedType AugmentedChunkType `protobuf:"varint,4,opt,name=augmented_type,json=augmentedType,proto3,enum=proto.AugmentedChunkType" json:"augmented_type,omitempty" description:"增强类型"`
	// @gotags: description:"是否禁用向量化索引"
	DisableVectorIndexing bool `protobuf:"varint,5,opt,name=disable_vector_indexing,json=disableVectorIndexing,proto3" json:"disable_vector_indexing,omitempty" description:"是否禁用向量化索引"`
	// @gotags: description:"是否禁用全文索引"
	DisableFullTextIndexing bool `protobuf:"varint,6,opt,name=disable_full_text_indexing,json=disableFullTextIndexing,proto3" json:"disable_full_text_indexing,omitempty" description:"是否禁用全文索引"`
	// @gotags: description:"是否被修改编辑过"
	Edited bool `protobuf:"varint,7,opt,name=edited,proto3" json:"edited,omitempty" description:"是否被修改编辑过"`
}

func (x *AugmentedChunk) Reset() {
	*x = AugmentedChunk{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_knowledge_base_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AugmentedChunk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AugmentedChunk) ProtoMessage() {}

func (x *AugmentedChunk) ProtoReflect() protoreflect.Message {
	mi := &file_proto_knowledge_base_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AugmentedChunk.ProtoReflect.Descriptor instead.
func (*AugmentedChunk) Descriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{20}
}

func (x *AugmentedChunk) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AugmentedChunk) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *AugmentedChunk) GetSourceType() ChunkSourceType {
	if x != nil {
		return x.SourceType
	}
	return ChunkSourceType_SOURCE_TYPE_GENERATED
}

func (x *AugmentedChunk) GetAugmentedType() AugmentedChunkType {
	if x != nil {
		return x.AugmentedType
	}
	return AugmentedChunkType_AUGMENTED_CHUNK_TYPE_SUMMARY
}

func (x *AugmentedChunk) GetDisableVectorIndexing() bool {
	if x != nil {
		return x.DisableVectorIndexing
	}
	return false
}

func (x *AugmentedChunk) GetDisableFullTextIndexing() bool {
	if x != nil {
		return x.DisableFullTextIndexing
	}
	return false
}

func (x *AugmentedChunk) GetEdited() bool {
	if x != nil {
		return x.Edited
	}
	return false
}

// ChunkInfo 基于Chunk扩增了一些相关信息
type ChunkInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"分段"
	Chunk *Chunk `protobuf:"bytes,1,opt,name=chunk,proto3" json:"chunk,omitempty" description:"分段"`
	// @gotags: description:"分段的展示信息"
	DisplayInfos []*DisplayInfo `protobuf:"bytes,2,rep,name=display_infos,json=displayInfos,proto3" json:"display_infos,omitempty" description:"分段的展示信息"`
	// @gotags: description:"文档元素, 预留"
	Elements []*DocElement `protobuf:"bytes,3,rep,name=elements,proto3" json:"elements,omitempty" description:"文档元素, 预留"`
}

func (x *ChunkInfo) Reset() {
	*x = ChunkInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_knowledge_base_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChunkInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChunkInfo) ProtoMessage() {}

func (x *ChunkInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_knowledge_base_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChunkInfo.ProtoReflect.Descriptor instead.
func (*ChunkInfo) Descriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{21}
}

func (x *ChunkInfo) GetChunk() *Chunk {
	if x != nil {
		return x.Chunk
	}
	return nil
}

func (x *ChunkInfo) GetDisplayInfos() []*DisplayInfo {
	if x != nil {
		return x.DisplayInfos
	}
	return nil
}

func (x *ChunkInfo) GetElements() []*DocElement {
	if x != nil {
		return x.Elements
	}
	return nil
}

// 数据库的表信息
type TableInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"表名"
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty" description:"表名"`
	// @gotags: description:"表的行数"
	Rows int64 `protobuf:"varint,2,opt,name=rows,proto3" json:"rows,omitempty" description:"表的行数"`
	// @gotags: description:"表描述信息，包含表schema"
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty" description:"表描述信息，包含表schema"`
}

func (x *TableInfo) Reset() {
	*x = TableInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_knowledge_base_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TableInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TableInfo) ProtoMessage() {}

func (x *TableInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_knowledge_base_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TableInfo.ProtoReflect.Descriptor instead.
func (*TableInfo) Descriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{22}
}

func (x *TableInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TableInfo) GetRows() int64 {
	if x != nil {
		return x.Rows
	}
	return 0
}

func (x *TableInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// 表格知识库的表格配置
type TableConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"sheet名称"
	SheetName string `protobuf:"bytes,1,opt,name=sheet_name,json=sheetName,proto3" json:"sheet_name,omitempty" description:"sheet名称"`
	// @gotags: description:"表头行号"
	HeaderRow int32 `protobuf:"varint,2,opt,name=header_row,json=headerRow,proto3" json:"header_row,omitempty" description:"表头行号"`
	// @gotags: description:"数据起始行号"
	DataStartingRow int32 `protobuf:"varint,3,opt,name=data_starting_row,json=dataStartingRow,proto3" json:"data_starting_row,omitempty" description:"数据起始行号"`
	// @gotags: description:"表格总行数"
	NumRows int32 `protobuf:"varint,4,opt,name=num_rows,json=numRows,proto3" json:"num_rows,omitempty" description:"表格总行数"`
	// @gotags: description:"字段配置"
	FieldsConfig []*TableFieldConfig `protobuf:"bytes,5,rep,name=fields_config,json=fieldsConfig,proto3" json:"fields_config,omitempty" description:"字段配置"`
}

func (x *TableConfig) Reset() {
	*x = TableConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_knowledge_base_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TableConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TableConfig) ProtoMessage() {}

func (x *TableConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_knowledge_base_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TableConfig.ProtoReflect.Descriptor instead.
func (*TableConfig) Descriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{23}
}

func (x *TableConfig) GetSheetName() string {
	if x != nil {
		return x.SheetName
	}
	return ""
}

func (x *TableConfig) GetHeaderRow() int32 {
	if x != nil {
		return x.HeaderRow
	}
	return 0
}

func (x *TableConfig) GetDataStartingRow() int32 {
	if x != nil {
		return x.DataStartingRow
	}
	return 0
}

func (x *TableConfig) GetNumRows() int32 {
	if x != nil {
		return x.NumRows
	}
	return 0
}

func (x *TableConfig) GetFieldsConfig() []*TableFieldConfig {
	if x != nil {
		return x.FieldsConfig
	}
	return nil
}

type CorpusConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SetId     int32  `protobuf:"varint,1,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	VersionId int32  `protobuf:"varint,2,opt,name=version_id,json=versionId,proto3" json:"version_id,omitempty"`
	Dir       string `protobuf:"bytes,3,opt,name=dir,proto3" json:"dir,omitempty"`
}

func (x *CorpusConfig) Reset() {
	*x = CorpusConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_knowledge_base_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CorpusConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CorpusConfig) ProtoMessage() {}

func (x *CorpusConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_knowledge_base_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CorpusConfig.ProtoReflect.Descriptor instead.
func (*CorpusConfig) Descriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{24}
}

func (x *CorpusConfig) GetSetId() int32 {
	if x != nil {
		return x.SetId
	}
	return 0
}

func (x *CorpusConfig) GetVersionId() int32 {
	if x != nil {
		return x.VersionId
	}
	return 0
}

func (x *CorpusConfig) GetDir() string {
	if x != nil {
		return x.Dir
	}
	return ""
}

type TableFieldConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"字段的列号"
	Idx int32 `protobuf:"varint,1,opt,name=idx,proto3" json:"idx,omitempty" description:"字段的列号"`
	// @gotags: description:"字段名称"
	Name string `protobuf:"bytes,20,opt,name=name,proto3" json:"name,omitempty" description:"字段名称"`
	// @gotags: description:"原字段名称"
	OriName string `protobuf:"bytes,21,opt,name=ori_name,json=oriName,proto3" json:"ori_name,omitempty" description:"原字段名称"`
	// @gotags: description:"字段描述"
	Desc string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty" description:"字段描述"`
	// @gotags: description:"字段类型"
	DataType TableFieldDataType `protobuf:"varint,4,opt,name=data_type,json=dataType,proto3,enum=proto.TableFieldDataType" json:"data_type,omitempty" description:"字段类型"`
	// @gotags: description:"是否启用索引"
	EnableIndex bool `protobuf:"varint,5,opt,name=enable_index,json=enableIndex,proto3" json:"enable_index,omitempty" description:"是否启用索引"`
	// @gotags: description:"字段的索引权重, (0,1]"
	Weight float32 `protobuf:"fixed32,6,opt,name=weight,proto3" json:"weight,omitempty" description:"字段的索引权重, (0,1]"`
	// @gotags: description:"是否需要召回内容"
	EnableContentRecall bool `protobuf:"varint,7,opt,name=enable_content_recall,json=enableContentRecall,proto3" json:"enable_content_recall,omitempty" description:"是否需要召回内容"`
	// @gotags: description:"字段示例"
	Example string `protobuf:"bytes,8,opt,name=example,proto3" json:"example,omitempty" description:"字段示例"`
}

func (x *TableFieldConfig) Reset() {
	*x = TableFieldConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_knowledge_base_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TableFieldConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TableFieldConfig) ProtoMessage() {}

func (x *TableFieldConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_knowledge_base_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TableFieldConfig.ProtoReflect.Descriptor instead.
func (*TableFieldConfig) Descriptor() ([]byte, []int) {
	return file_proto_knowledge_base_proto_rawDescGZIP(), []int{25}
}

func (x *TableFieldConfig) GetIdx() int32 {
	if x != nil {
		return x.Idx
	}
	return 0
}

func (x *TableFieldConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TableFieldConfig) GetOriName() string {
	if x != nil {
		return x.OriName
	}
	return ""
}

func (x *TableFieldConfig) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *TableFieldConfig) GetDataType() TableFieldDataType {
	if x != nil {
		return x.DataType
	}
	return TableFieldDataType_STRING
}

func (x *TableFieldConfig) GetEnableIndex() bool {
	if x != nil {
		return x.EnableIndex
	}
	return false
}

func (x *TableFieldConfig) GetWeight() float32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *TableFieldConfig) GetEnableContentRecall() bool {
	if x != nil {
		return x.EnableContentRecall
	}
	return false
}

func (x *TableFieldConfig) GetExample() string {
	if x != nil {
		return x.Example
	}
	return ""
}

var File_proto_knowledge_base_proto protoreflect.FileDescriptor

var file_proto_knowledge_base_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x11, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x6e, 0x67, 0x2f, 0x6d, 0x6c, 0x6f, 0x70, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xee, 0x03, 0x0a, 0x1f, 0x4b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x47, 0x0a, 0x14, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x12, 0x64, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6f, 0x72,
	0x74, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a,
	0x73, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x65,
	0x78, 0x74, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x74, 0x65, 0x78, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x76, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x25, 0x0a, 0x0e,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x73, 0x12, 0x36, 0x0a, 0x0c, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x0b,
	0x76, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x76,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x64, 0x69, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x44, 0x69, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73,
	0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x44,
	0x65, 0x73, 0x63, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x6f, 0x63, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x6f, 0x63, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x69, 0x64, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x69, 0x64, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x76,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x6e,
	0x64, 0x65, 0x78, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x2a, 0x0a, 0x18, 0x4b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x54, 0x4b, 0x48, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x02, 0x69, 0x64, 0x22, 0xce, 0x01, 0x0a, 0x18, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x26, 0x0a, 0x0f, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x73, 0x76, 0x63, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x76, 0x69, 0x72, 0x74, 0x75,
	0x61, 0x6c, 0x53, 0x76, 0x63, 0x55, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x64, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63,
	0x12, 0x31, 0x0a, 0x0a, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x52,
	0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x09, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x53, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x22, 0xe0, 0x09, 0x0a, 0x0d, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x0c,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x3f, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x45, 0x0a, 0x0d, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x72, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x57, 0x0a, 0x13,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x72, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72,
	0x79, 0x52, 0x12, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x72, 0x79, 0x12, 0x42, 0x0a, 0x0c, 0x74, 0x6b, 0x68, 0x5f, 0x72, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x72, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73,
	0x65, 0x54, 0x4b, 0x48, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x74, 0x6b,
	0x68, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x69, 0x6c, 0x6c, 0x73, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x4d, 0x69, 0x6c, 0x6c, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x69, 0x6c, 0x6c, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x69, 0x6c,
	0x6c, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x5f, 0x64,
	0x6f, 0x63, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x76, 0x69, 0x73,
	0x69, 0x62, 0x6c, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x56, 0x69,
	0x73, 0x69, 0x62, 0x6c, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x74, 0x72,
	0x69, 0x65, 0x76, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69,
	0x73, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x45, 0x0a, 0x0d,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x4e, 0x0a, 0x15, 0x64, 0x6f, 0x63, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x63, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x13,
	0x64, 0x6f, 0x63, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x41, 0x0a, 0x10, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x36, 0x0a, 0x0c, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x0b, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1b,
	0x0a, 0x09, 0x69, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x69, 0x73, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x12, 0x21, 0x0a, 0x0c, 0x69,
	0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0b, 0x69, 0x73, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x12, 0x3c,
	0x0a, 0x0a, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x17, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x09, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x35, 0x0a, 0x0c,
	0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x18, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x50,
	0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x74, 0x0a, 0x0b, 0x4d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x69, 0x73, 0x69, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x76, 0x69, 0x73,
	0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6c, 0x6f, 0x6e, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x6c,
	0x6f, 0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0c, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x22, 0x6c, 0x0a,
	0x10, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65,
	0x66, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xa6, 0x03, 0x0a, 0x13,
	0x44, 0x6f, 0x63, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x5a, 0x0a, 0x1e, 0x64, 0x6f, 0x63, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x5f, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x4f, 0x72, 0x69, 0x67,
	0x69, 0x6e, 0x52, 0x1b, 0x64, 0x6f, 0x63, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x12,
	0x3f, 0x0a, 0x10, 0x64, 0x6f, 0x63, 0x5f, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x44, 0x6f, 0x63, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x0e, 0x64, 0x6f, 0x63, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x4b, 0x0a, 0x14, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x61, 0x75, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x41, 0x75, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x12, 0x63, 0x68, 0x75, 0x6e, 0x6b,
	0x41, 0x75, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3e, 0x0a,
	0x0e, 0x61, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x70,
	0x70, 0x6c, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x66, 0x52, 0x0d,
	0x61, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3c, 0x0a,
	0x0f, 0x64, 0x6f, 0x63, 0x5f, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44,
	0x6f, 0x63, 0x4c, 0x6f, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x64, 0x6f,
	0x63, 0x4c, 0x6f, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x27, 0x0a, 0x0f, 0x61,
	0x75, 0x74, 0x6f, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x65, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x61, 0x75, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x75, 0x72, 0x65, 0x64, 0x22, 0xd2, 0x03, 0x0a, 0x0d, 0x44, 0x6f, 0x63, 0x4c, 0x6f, 0x61, 0x64,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3f, 0x0a, 0x0d, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65,
	0x67, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x63, 0x4c, 0x6f, 0x61, 0x64, 0x53, 0x74, 0x72,
	0x61, 0x74, 0x65, 0x67, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x65, 0x67, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x6d, 0x61, 0x69,
	0x6e, 0x5f, 0x66, 0x69, 0x67, 0x75, 0x72, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c,
	0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x46, 0x69, 0x67, 0x75, 0x72, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x15, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0b, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x12,
	0x2c, 0x0a, 0x12, 0x72, 0x65, 0x63, 0x6f, 0x67, 0x5f, 0x6d, 0x61, 0x74, 0x68, 0x5f, 0x66, 0x6f,
	0x72, 0x6d, 0x75, 0x6c, 0x61, 0x18, 0x16, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x72, 0x65, 0x63,
	0x6f, 0x67, 0x4d, 0x61, 0x74, 0x68, 0x46, 0x6f, 0x72, 0x6d, 0x75, 0x6c, 0x61, 0x12, 0x34, 0x0a,
	0x16, 0x72, 0x65, 0x63, 0x6f, 0x67, 0x5f, 0x63, 0x68, 0x65, 0x6d, 0x69, 0x63, 0x61, 0x6c, 0x5f,
	0x66, 0x6f, 0x72, 0x6d, 0x75, 0x6c, 0x61, 0x18, 0x17, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x72,
	0x65, 0x63, 0x6f, 0x67, 0x43, 0x68, 0x65, 0x6d, 0x69, 0x63, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x6d,
	0x75, 0x6c, 0x61, 0x12, 0x2c, 0x0a, 0x12, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x63,
	0x68, 0x65, 0x6d, 0x61, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x43, 0x6f, 0x6e,
	0x66, 0x12, 0x3e, 0x0a, 0x0b, 0x64, 0x6f, 0x63, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67,
	0x2e, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x61, 0x73,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x64, 0x6f, 0x63, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x34, 0x0a, 0x0b, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x0a, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x30, 0x0a, 0x09, 0x64, 0x6f, 0x63, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x08, 0x64, 0x6f, 0x63, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0x89, 0x03, 0x0a, 0x0f, 0x52, 0x65,
	0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x51, 0x0a,
	0x19, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x65, 0x67, 0x79, 0x5f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67,
	0x79, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x52, 0x17, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76,
	0x61, 0x6c, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x12, 0x40, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x24, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65,
	0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x08, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65,
	0x67, 0x79, 0x12, 0x38, 0x0a, 0x0d, 0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x0c,
	0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x38, 0x0a, 0x0d,
	0x72, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x72, 0x61,
	0x6e, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x0c, 0x72, 0x65, 0x72, 0x61, 0x6e, 0x6b,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x61,
	0x75, 0x74, 0x6f, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x65, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x61, 0x75, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x75, 0x72, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f,
	0x6e, 0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x4e, 0x75, 0x6d, 0x22, 0x93, 0x02, 0x0a, 0x0e, 0x44, 0x6f, 0x63, 0x53, 0x70, 0x6c,
	0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x70, 0x61,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x70,
	0x61, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x68, 0x75, 0x6e,
	0x6b, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x6f,
	0x76, 0x65, 0x72, 0x6c, 0x61, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x68,
	0x75, 0x6e, 0x6b, 0x4f, 0x76, 0x65, 0x72, 0x6c, 0x61, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65,
	0x70, 0x61, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a,
	0x73, 0x65, 0x70, 0x61, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x4b, 0x0a, 0x13, 0x73, 0x70,
	0x6c, 0x69, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x44, 0x6f, 0x63, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x11, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x53, 0x74, 0x72, 0x61, 0x74,
	0x65, 0x67, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x64, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x6d, 0x65, 0x72, 0x67, 0x69, 0x6e, 0x67,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x43,
	0x68, 0x75, 0x6e, 0x6b, 0x4d, 0x65, 0x72, 0x67, 0x69, 0x6e, 0x67, 0x22, 0xd1, 0x04, 0x0a, 0x12,
	0x43, 0x68, 0x75, 0x6e, 0x6b, 0x41, 0x75, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x21, 0x0a, 0x0c,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x12,
	0x1f, 0x0a, 0x0b, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x4e, 0x75, 0x6d,
	0x12, 0x32, 0x0a, 0x0a, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x09, 0x74, 0x65, 0x78, 0x74, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x12, 0x24, 0x0a, 0x0e, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x65,
	0x73, 0x63, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x44, 0x65, 0x73, 0x63, 0x4e, 0x75, 0x6d, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x6e, 0x75, 0x6d, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x4e, 0x75, 0x6d, 0x12, 0x27, 0x0a, 0x0f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x12,
	0x25, 0x0a, 0x0e, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x6d, 0x70,
	0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f,
	0x64, 0x65, 0x73, 0x63, 0x5f, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x65, 0x73, 0x63, 0x50, 0x72, 0x6f, 0x6d,
	0x70, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x12, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x50, 0x72,
	0x6f, 0x6d, 0x70, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x34, 0x0a, 0x0b, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x24, 0x0a,
	0x0e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x5f, 0x6e, 0x75, 0x6d, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x44, 0x65, 0x73, 0x63,
	0x4e, 0x75, 0x6d, 0x12, 0x2a, 0x0a, 0x11, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x73,
	0x63, 0x5f, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x44, 0x65, 0x73, 0x63, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x22,
	0x8e, 0x01, 0x0a, 0x0c, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x13, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x5f, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x74, 0x6f, 0x70, 0x4b, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x74,
	0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x40,
	0x0a, 0x08, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x24, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x53, 0x74,
	0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x08, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79,
	0x22, 0xa6, 0x01, 0x0a, 0x0c, 0x52, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x13, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x5f, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x74, 0x6f, 0x70, 0x4b, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f,
	0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0e, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12,
	0x29, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x2d, 0x0a, 0x12, 0x72, 0x65,
	0x74, 0x61, 0x69, 0x6e, 0x5f, 0x75, 0x6e, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x72, 0x65, 0x74, 0x61, 0x69, 0x6e, 0x55, 0x6e,
	0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x65, 0x64, 0x22, 0xc0, 0x04, 0x0a, 0x08, 0x44, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x6f, 0x63, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x6f, 0x63, 0x49, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x64, 0x6f, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x64, 0x6f, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x26, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d,
	0x66, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x2a,
	0x0a, 0x11, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x69,
	0x6c, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x75, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x69, 0x6c, 0x6c, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x75,
	0x6d, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6e,
	0x75, 0x6d, 0x43, 0x68, 0x61, 0x72, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x6d, 0x64, 0x35, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x4d,
	0x64, 0x35, 0x12, 0x2a, 0x0a, 0x11, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f,
	0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x35,
	0x0a, 0x0c, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x61, 0x62,
	0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0b, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4b, 0x0a, 0x14, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x63, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x12,
	0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x12, 0x38, 0x0a, 0x0d, 0x63, 0x6f, 0x72, 0x70, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x43, 0x6f, 0x72, 0x70, 0x75, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c,
	0x63, 0x6f, 0x72, 0x70, 0x75, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4e, 0x0a, 0x15,
	0x64, 0x6f, 0x63, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x63, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x13, 0x64, 0x6f, 0x63, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x37, 0x0a, 0x0b,
	0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x23, 0x0a, 0x05, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x0c,
	0x0a, 0x01, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x01, 0x79, 0x22, 0x7b, 0x0a, 0x0b, 0x43, 0x6f,
	0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x73, 0x12, 0x24, 0x0a, 0x06, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x06, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12,
	0x21, 0x0a, 0x0c, 0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x57, 0x69, 0x64,
	0x74, 0x68, 0x12, 0x23, 0x0a, 0x0d, 0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x5f, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x6c, 0x61, 0x79, 0x6f, 0x75,
	0x74, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0xd1, 0x02, 0x0a, 0x0a, 0x44, 0x6f, 0x63, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x63, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x65, 0x78, 0x74, 0x12, 0x35, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44,
	0x6f, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x54, 0x65, 0x78, 0x74, 0x73, 0x12, 0x32, 0x0a,
	0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x45, 0x78, 0x74, 0x72, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x1a, 0x38, 0x0a, 0x0a, 0x45, 0x78, 0x74, 0x72, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xdc, 0x01, 0x0a, 0x12,
	0x44, 0x6f, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x34, 0x0a, 0x0b, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x43, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x73, 0x52, 0x0b, 0x63, 0x6f,
	0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x64, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x70, 0x72, 0x6f,
	0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x12, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x50, 0x72, 0x6f, 0x62, 0x12, 0x20, 0x0a, 0x0c, 0x74,
	0x65, 0x78, 0x74, 0x5f, 0x61, 0x73, 0x5f, 0x68, 0x74, 0x6d, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x74, 0x65, 0x78, 0x74, 0x41, 0x73, 0x48, 0x74, 0x6d, 0x6c, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x82, 0x04, 0x0a, 0x05, 0x43,
	0x68, 0x75, 0x6e, 0x6b, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12,
	0x37, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x75,
	0x6e, 0x6b, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x64, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x69,
	0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c,
	0x65, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x69, 0x6e, 0x67, 0x12,
	0x3b, 0x0a, 0x1a, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x66, 0x75, 0x6c, 0x6c, 0x5f,
	0x74, 0x65, 0x78, 0x74, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x69, 0x6e, 0x67, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x17, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x75, 0x6c, 0x6c,
	0x54, 0x65, 0x78, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x69, 0x6e, 0x67, 0x12, 0x40, 0x0a, 0x10,
	0x61, 0x75, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x73,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41,
	0x75, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x65, 0x64, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52, 0x0f, 0x61,
	0x75, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x65, 0x64, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x12, 0x2d,
	0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x2e, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x16, 0x0a,
	0x06, 0x65, 0x64, 0x69, 0x74, 0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65,
	0x64, 0x69, 0x74, 0x65, 0x64, 0x1a, 0x38, 0x0a, 0x0a, 0x45, 0x78, 0x74, 0x72, 0x61, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0xc2, 0x02, 0x0a, 0x0e, 0x41, 0x75, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x65, 0x64, 0x43, 0x68, 0x75,
	0x6e, 0x6b, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x0b,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x40, 0x0a, 0x0e, 0x61, 0x75, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x65, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x75, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x65, 0x64, 0x43,
	0x68, 0x75, 0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x61, 0x75, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x64, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x69,
	0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c,
	0x65, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x69, 0x6e, 0x67, 0x12,
	0x3b, 0x0a, 0x1a, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x66, 0x75, 0x6c, 0x6c, 0x5f,
	0x74, 0x65, 0x78, 0x74, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x17, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x75, 0x6c, 0x6c,
	0x54, 0x65, 0x78, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x69, 0x6e, 0x67, 0x12, 0x16, 0x0a, 0x06,
	0x65, 0x64, 0x69, 0x74, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x64,
	0x69, 0x74, 0x65, 0x64, 0x22, 0x97, 0x01, 0x0a, 0x09, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x22, 0x0a, 0x05, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52,
	0x05, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x12, 0x37, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12,
	0x2d, 0x0a, 0x08, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x63, 0x45, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x55,
	0x0a, 0x09, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x72,
	0x6f, 0x77, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xd0, 0x01, 0x0a, 0x0b, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x68, 0x65, 0x65, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x68, 0x65, 0x65, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x72,
	0x6f, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x6f, 0x77, 0x12, 0x2a, 0x0a, 0x11, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x6f, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f,
	0x64, 0x61, 0x74, 0x61, 0x53, 0x74, 0x61, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x6f, 0x77, 0x12,
	0x19, 0x0a, 0x08, 0x6e, 0x75, 0x6d, 0x5f, 0x72, 0x6f, 0x77, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x6e, 0x75, 0x6d, 0x52, 0x6f, 0x77, 0x73, 0x12, 0x3c, 0x0a, 0x0d, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x56, 0x0a, 0x0c, 0x43, 0x6f, 0x72, 0x70,
	0x75, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x10,
	0x0a, 0x03, 0x64, 0x69, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x64, 0x69, 0x72,
	0x22, 0xa8, 0x02, 0x0a, 0x10, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x78, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x03, 0x69, 0x64, 0x78, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6f,
	0x72, 0x69, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f,
	0x72, 0x69, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x36, 0x0a, 0x09, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x32, 0x0a,
	0x15, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f,
	0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x61, 0x6c,
	0x6c, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x2a, 0x4b, 0x0a, 0x18, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x45, 0x58, 0x54, 0x10,
	0x00, 0x12, 0x09, 0x0a, 0x05, 0x47, 0x52, 0x41, 0x50, 0x48, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b,
	0x4d, 0x55, 0x4c, 0x54, 0x49, 0x5f, 0x4d, 0x4f, 0x44, 0x41, 0x4c, 0x10, 0x02, 0x12, 0x09, 0x0a,
	0x05, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x03, 0x2a, 0x3e, 0x0a, 0x17, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x53, 0x43, 0x52, 0x41,
	0x54, 0x43, 0x48, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x52, 0x45,
	0x47, 0x49, 0x53, 0x54, 0x52, 0x59, 0x10, 0x01, 0x2a, 0x61, 0x0a, 0x19, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12,
	0x18, 0x0a, 0x14, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x43, 0x4f, 0x4e,
	0x4e, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x52, 0x4f,
	0x4d, 0x5f, 0x54, 0x4b, 0x48, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x46, 0x52, 0x4f, 0x4d, 0x5f,
	0x4c, 0x41, 0x4e, 0x47, 0x43, 0x48, 0x41, 0x49, 0x4e, 0x10, 0x03, 0x2a, 0x4e, 0x0a, 0x19, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x13, 0x0a, 0x0f, 0x46, 0x52, 0x4f, 0x4d,
	0x5f, 0x4d, 0x41, 0x4e, 0x41, 0x47, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x00, 0x12, 0x1c, 0x0a,
	0x18, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x46,
	0x49, 0x47, 0x55, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x2a, 0x6e, 0x0a, 0x16, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x53, 0x63, 0x65, 0x6e,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x51, 0x41, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x63, 0x65, 0x6e, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x41, 0x46, 0x45, 0x54, 0x59, 0x10, 0x01, 0x12, 0x16, 0x0a,
	0x12, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x54, 0x41, 0x4e, 0x44,
	0x41, 0x52, 0x44, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x53, 0x10, 0x63, 0x2a, 0x51, 0x0a, 0x13, 0x44,
	0x6f, 0x63, 0x4c, 0x6f, 0x61, 0x64, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x41, 0x55, 0x54, 0x4f, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04,
	0x46, 0x41, 0x53, 0x54, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x48, 0x49, 0x5f, 0x52, 0x45, 0x53,
	0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x4f, 0x43, 0x52, 0x5f, 0x4f, 0x4e, 0x4c, 0x59, 0x10, 0x03,
	0x12, 0x0c, 0x0a, 0x08, 0x56, 0x4c, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x10, 0x04, 0x2a, 0x38,
	0x0a, 0x0e, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x12, 0x0a, 0x0a, 0x06, 0x50, 0x52, 0x45, 0x53, 0x45, 0x54, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06,
	0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x4f, 0x43, 0x5f,
	0x45, 0x4e, 0x47, 0x49, 0x4e, 0x45, 0x10, 0x02, 0x2a, 0x3e, 0x0a, 0x14, 0x44, 0x6f, 0x63, 0x53,
	0x70, 0x6c, 0x69, 0x74, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x0d, 0x0a, 0x09, 0x43, 0x48, 0x41, 0x52, 0x41, 0x43, 0x54, 0x45, 0x52, 0x10, 0x00, 0x12,
	0x0d, 0x0a, 0x09, 0x52, 0x45, 0x43, 0x55, 0x52, 0x53, 0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x08,
	0x0a, 0x04, 0x50, 0x41, 0x47, 0x45, 0x10, 0x02, 0x2a, 0x5e, 0x0a, 0x1d, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76,
	0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x0a, 0x0a, 0x06, 0x56, 0x45, 0x43,
	0x54, 0x4f, 0x52, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x46, 0x55, 0x4c, 0x4c, 0x5f, 0x54, 0x45,
	0x58, 0x54, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x4d, 0x49, 0x58, 0x45, 0x44, 0x10, 0x02, 0x12,
	0x17, 0x0a, 0x13, 0x44, 0x4f, 0x43, 0x5f, 0x45, 0x4e, 0x47, 0x49, 0x4e, 0x45, 0x5f, 0x52, 0x45,
	0x54, 0x52, 0x49, 0x45, 0x56, 0x45, 0x10, 0x03, 0x2a, 0x45, 0x0a, 0x0f, 0x43, 0x68, 0x75, 0x6e,
	0x6b, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x53,
	0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52,
	0x41, 0x54, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x2a,
	0xbb, 0x01, 0x0a, 0x13, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x4f, 0x52, 0x49, 0x47, 0x49,
	0x4e, 0x41, 0x4c, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x54, 0x45, 0x58, 0x54, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x4f, 0x52, 0x49, 0x47, 0x49,
	0x4e, 0x41, 0x4c, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x4f, 0x52, 0x49, 0x47,
	0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x4a, 0x53, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x1f, 0x0a, 0x1b, 0x4f, 0x52, 0x49, 0x47,
	0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x10, 0x03, 0x12, 0x22, 0x0a, 0x1e, 0x4f, 0x52, 0x49,
	0x47, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x4d, 0x41, 0x52, 0x4b, 0x44, 0x4f, 0x57, 0x4e, 0x10, 0x04, 0x2a, 0x97, 0x03,
	0x0a, 0x12, 0x41, 0x75, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x65, 0x64, 0x43, 0x68, 0x75, 0x6e, 0x6b,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x41, 0x55, 0x47, 0x4d, 0x45, 0x4e, 0x54, 0x45,
	0x44, 0x5f, 0x43, 0x48, 0x55, 0x4e, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x55, 0x4d,
	0x4d, 0x41, 0x52, 0x59, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x41, 0x55, 0x47, 0x4d, 0x45, 0x4e,
	0x54, 0x45, 0x44, 0x5f, 0x43, 0x48, 0x55, 0x4e, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x2a, 0x0a, 0x26, 0x41, 0x55, 0x47,
	0x4d, 0x45, 0x4e, 0x54, 0x45, 0x44, 0x5f, 0x43, 0x48, 0x55, 0x4e, 0x4b, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0x14, 0x12, 0x26, 0x0a, 0x22, 0x41, 0x55, 0x47, 0x4d, 0x45, 0x4e, 0x54,
	0x45, 0x44, 0x5f, 0x43, 0x48, 0x55, 0x4e, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x41,
	0x42, 0x4c, 0x45, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x10, 0x15, 0x12, 0x29, 0x0a,
	0x25, 0x41, 0x55, 0x47, 0x4d, 0x45, 0x4e, 0x54, 0x45, 0x44, 0x5f, 0x43, 0x48, 0x55, 0x4e, 0x4b,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52,
	0x45, 0x57, 0x52, 0x49, 0x54, 0x45, 0x10, 0x03, 0x12, 0x22, 0x0a, 0x1e, 0x41, 0x55, 0x47, 0x4d,
	0x45, 0x4e, 0x54, 0x45, 0x44, 0x5f, 0x43, 0x48, 0x55, 0x4e, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x44, 0x45, 0x46, 0x49, 0x4e, 0x45, 0x44, 0x10, 0x04, 0x12, 0x2a, 0x0a, 0x26,
	0x41, 0x55, 0x47, 0x4d, 0x45, 0x4e, 0x54, 0x45, 0x44, 0x5f, 0x43, 0x48, 0x55, 0x4e, 0x4b, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x44, 0x45, 0x58, 0x5f, 0x54, 0x41, 0x42, 0x4c, 0x45,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x10, 0x05, 0x12, 0x20, 0x0a, 0x1c, 0x41, 0x55, 0x47, 0x4d,
	0x45, 0x4e, 0x54, 0x45, 0x44, 0x5f, 0x43, 0x48, 0x55, 0x4e, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x58, 0x54, 0x10, 0x06, 0x12, 0x2a, 0x0a, 0x26, 0x41, 0x55,
	0x47, 0x4d, 0x45, 0x4e, 0x54, 0x45, 0x44, 0x5f, 0x43, 0x48, 0x55, 0x4e, 0x4b, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x07, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x55, 0x47, 0x4d, 0x45, 0x4e,
	0x54, 0x45, 0x44, 0x5f, 0x43, 0x48, 0x55, 0x4e, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43,
	0x55, 0x53, 0x54, 0x4f, 0x4d, 0x10, 0x08, 0x2a, 0xf1, 0x02, 0x0a, 0x0e, 0x44, 0x6f, 0x63, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x44, 0x6f,
	0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x46, 0x69, 0x67,
	0x75, 0x72, 0x65, 0x43, 0x61, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c,
	0x44, 0x6f, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e,
	0x61, 0x72, 0x72, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x65, 0x78, 0x74, 0x10, 0x01, 0x12, 0x1b,
	0x0a, 0x17, 0x44, 0x6f, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x44,
	0x6f, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x54, 0x69,
	0x74, 0x6c, 0x65, 0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x44, 0x6f, 0x63, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x10,
	0x04, 0x12, 0x18, 0x0a, 0x14, 0x44, 0x6f, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x10, 0x05, 0x12, 0x1c, 0x0a, 0x18, 0x44,
	0x6f, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x61,
	0x67, 0x65, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x10, 0x06, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x6f, 0x63,
	0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x10, 0x07, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x6f, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x46, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x10, 0x08, 0x12,
	0x24, 0x0a, 0x20, 0x44, 0x6f, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x55, 0x6e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64, 0x54,
	0x65, 0x78, 0x74, 0x10, 0x09, 0x12, 0x18, 0x0a, 0x14, 0x44, 0x6f, 0x63, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x10, 0x0a, 0x12,
	0x1a, 0x0a, 0x16, 0x44, 0x6f, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x46, 0x6f, 0x72, 0x6d, 0x75, 0x6c, 0x61, 0x10, 0x0b, 0x2a, 0x54, 0x0a, 0x12, 0x54,
	0x61, 0x62, 0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x54, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x07, 0x0a,
	0x03, 0x49, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x02,
	0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x03, 0x12, 0x08, 0x0a, 0x04,
	0x42, 0x4f, 0x4f, 0x4c, 0x10, 0x04, 0x12, 0x09, 0x0a, 0x05, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x10,
	0x05, 0x2a, 0x39, 0x0a, 0x12, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c,
	0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x4f, 0x43, 0x41, 0x4c,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x4f, 0x52, 0x50, 0x55,
	0x53, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x53, 0x45, 0x54, 0x10, 0x01, 0x42, 0x26, 0x5a, 0x24,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70,
	0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70,
	0x62, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_knowledge_base_proto_rawDescOnce sync.Once
	file_proto_knowledge_base_proto_rawDescData = file_proto_knowledge_base_proto_rawDesc
)

func file_proto_knowledge_base_proto_rawDescGZIP() []byte {
	file_proto_knowledge_base_proto_rawDescOnce.Do(func() {
		file_proto_knowledge_base_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_knowledge_base_proto_rawDescData)
	})
	return file_proto_knowledge_base_proto_rawDescData
}

var file_proto_knowledge_base_proto_enumTypes = make([]protoimpl.EnumInfo, 15)
var file_proto_knowledge_base_proto_msgTypes = make([]protoimpl.MessageInfo, 28)
var file_proto_knowledge_base_proto_goTypes = []interface{}{
	(KnowledgeBaseContentType)(0),           // 0: proto.KnowledgeBaseContentType
	(KnowledgeBaseSourceType)(0),            // 1: proto.KnowledgeBaseSourceType
	(KnowledgeBaseRegistryType)(0),          // 2: proto.KnowledgeBaseRegistryType
	(KnowledgeBaseCreationType)(0),          // 3: proto.KnowledgeBaseCreationType
	(KnowledgeBaseSceneType)(0),             // 4: proto.KnowledgeBaseSceneType
	(DocLoadStrategyType)(0),                // 5: proto.DocLoadStrategyType
	(StrategyOrigin)(0),                     // 6: proto.StrategyOrigin
	(DocSplitStrategyType)(0),               // 7: proto.DocSplitStrategyType
	(KnowledgeBaseRetrieveStrategy)(0),      // 8: proto.KnowledgeBaseRetrieveStrategy
	(ChunkSourceType)(0),                    // 9: proto.ChunkSourceType
	(OriginalContentType)(0),                // 10: proto.OriginalContentType
	(AugmentedChunkType)(0),                 // 11: proto.AugmentedChunkType
	(DocElementType)(0),                     // 12: proto.DocElementType
	(TableFieldDataType)(0),                 // 13: proto.TableFieldDataType
	(DocumentFileSource)(0),                 // 14: proto.DocumentFileSource
	(*KnowledgeBaseConnectionRegistry)(nil), // 15: proto.KnowledgeBaseConnectionRegistry
	(*KnowledgeBaseTKHRegistry)(nil),        // 16: proto.KnowledgeBaseTKHRegistry
	(*KnowledgeBasePublishInfo)(nil),        // 17: proto.KnowledgeBasePublishInfo
	(*KnowledgeBase)(nil),                   // 18: proto.KnowledgeBase
	(*MetricsInfo)(nil),                     // 19: proto.MetricsInfo
	(*AppletServiceRef)(nil),                // 20: proto.AppletServiceRef
	(*DocProcessingConfig)(nil),             // 21: proto.DocProcessingConfig
	(*DocLoadConfig)(nil),                   // 22: proto.DocLoadConfig
	(*RetrievalConfig)(nil),                 // 23: proto.RetrievalConfig
	(*DocSplitConfig)(nil),                  // 24: proto.DocSplitConfig
	(*ChunkAugmentConfig)(nil),              // 25: proto.ChunkAugmentConfig
	(*RecallParams)(nil),                    // 26: proto.RecallParams
	(*RerankParams)(nil),                    // 27: proto.RerankParams
	(*Document)(nil),                        // 28: proto.Document
	(*DisplayInfo)(nil),                     // 29: proto.DisplayInfo
	(*Point)(nil),                           // 30: proto.Point
	(*Coordinates)(nil),                     // 31: proto.Coordinates
	(*DocElement)(nil),                      // 32: proto.DocElement
	(*DocElementMetadata)(nil),              // 33: proto.DocElementMetadata
	(*Chunk)(nil),                           // 34: proto.Chunk
	(*AugmentedChunk)(nil),                  // 35: proto.AugmentedChunk
	(*ChunkInfo)(nil),                       // 36: proto.ChunkInfo
	(*TableInfo)(nil),                       // 37: proto.TableInfo
	(*TableConfig)(nil),                     // 38: proto.TableConfig
	(*CorpusConfig)(nil),                    // 39: proto.CorpusConfig
	(*TableFieldConfig)(nil),                // 40: proto.TableFieldConfig
	nil,                                     // 41: proto.DocElement.ExtraEntry
	nil,                                     // 42: proto.Chunk.ExtraEntry
	(ConnectionType)(0),                     // 43: proto.ConnectionType
	(*ModelService)(nil),                    // 44: proto.ModelService
	(*serving.RateLimit)(nil),               // 45: serving.RateLimit
	(*serving.MLOpsServiceBaseInfo)(nil),    // 46: serving.MLOpsServiceBaseInfo
}
var file_proto_knowledge_base_proto_depIdxs = []int32{
	43, // 0: proto.KnowledgeBaseConnectionRegistry.data_connection_type:type_name -> proto.ConnectionType
	44, // 1: proto.KnowledgeBaseConnectionRegistry.vector_model:type_name -> proto.ModelService
	45, // 2: proto.KnowledgeBasePublishInfo.rate_limit:type_name -> serving.RateLimit
	0,  // 3: proto.KnowledgeBase.content_type:type_name -> proto.KnowledgeBaseContentType
	1,  // 4: proto.KnowledgeBase.source_type:type_name -> proto.KnowledgeBaseSourceType
	2,  // 5: proto.KnowledgeBase.registry_type:type_name -> proto.KnowledgeBaseRegistryType
	15, // 6: proto.KnowledgeBase.connection_registry:type_name -> proto.KnowledgeBaseConnectionRegistry
	16, // 7: proto.KnowledgeBase.tkh_registry:type_name -> proto.KnowledgeBaseTKHRegistry
	3,  // 8: proto.KnowledgeBase.creation_type:type_name -> proto.KnowledgeBaseCreationType
	21, // 9: proto.KnowledgeBase.doc_processing_config:type_name -> proto.DocProcessingConfig
	23, // 10: proto.KnowledgeBase.retrieval_config:type_name -> proto.RetrievalConfig
	44, // 11: proto.KnowledgeBase.vector_model:type_name -> proto.ModelService
	4,  // 12: proto.KnowledgeBase.scene_type:type_name -> proto.KnowledgeBaseSceneType
	19, // 13: proto.KnowledgeBase.metrics_info:type_name -> proto.MetricsInfo
	17, // 14: proto.KnowledgeBase.publish_info:type_name -> proto.KnowledgeBasePublishInfo
	6,  // 15: proto.DocProcessingConfig.doc_processing_strategy_origin:type_name -> proto.StrategyOrigin
	24, // 16: proto.DocProcessingConfig.doc_split_config:type_name -> proto.DocSplitConfig
	25, // 17: proto.DocProcessingConfig.chunk_augment_config:type_name -> proto.ChunkAugmentConfig
	20, // 18: proto.DocProcessingConfig.applet_service:type_name -> proto.AppletServiceRef
	22, // 19: proto.DocProcessingConfig.doc_load_config:type_name -> proto.DocLoadConfig
	5,  // 20: proto.DocLoadConfig.strategy_type:type_name -> proto.DocLoadStrategyType
	46, // 21: proto.DocLoadConfig.doc_service:type_name -> serving.MLOpsServiceBaseInfo
	44, // 22: proto.DocLoadConfig.image_model:type_name -> proto.ModelService
	44, // 23: proto.DocLoadConfig.doc_model:type_name -> proto.ModelService
	6,  // 24: proto.RetrievalConfig.retrieval_strategy_origin:type_name -> proto.StrategyOrigin
	8,  // 25: proto.RetrievalConfig.strategy:type_name -> proto.KnowledgeBaseRetrieveStrategy
	26, // 26: proto.RetrievalConfig.recall_params:type_name -> proto.RecallParams
	27, // 27: proto.RetrievalConfig.rerank_params:type_name -> proto.RerankParams
	7,  // 28: proto.DocSplitConfig.split_strategy_type:type_name -> proto.DocSplitStrategyType
	44, // 29: proto.ChunkAugmentConfig.text_model:type_name -> proto.ModelService
	44, // 30: proto.ChunkAugmentConfig.image_model:type_name -> proto.ModelService
	8,  // 31: proto.RecallParams.strategy:type_name -> proto.KnowledgeBaseRetrieveStrategy
	44, // 32: proto.RerankParams.model:type_name -> proto.ModelService
	38, // 33: proto.Document.table_config:type_name -> proto.TableConfig
	14, // 34: proto.Document.document_file_source:type_name -> proto.DocumentFileSource
	39, // 35: proto.Document.corpus_config:type_name -> proto.CorpusConfig
	21, // 36: proto.Document.doc_processing_config:type_name -> proto.DocProcessingConfig
	30, // 37: proto.Coordinates.points:type_name -> proto.Point
	12, // 38: proto.DocElement.type:type_name -> proto.DocElementType
	33, // 39: proto.DocElement.metadata:type_name -> proto.DocElementMetadata
	41, // 40: proto.DocElement.extra:type_name -> proto.DocElement.ExtraEntry
	31, // 41: proto.DocElementMetadata.coordinates:type_name -> proto.Coordinates
	9,  // 42: proto.Chunk.source_type:type_name -> proto.ChunkSourceType
	10, // 43: proto.Chunk.content_type:type_name -> proto.OriginalContentType
	35, // 44: proto.Chunk.augmented_chunks:type_name -> proto.AugmentedChunk
	42, // 45: proto.Chunk.extra:type_name -> proto.Chunk.ExtraEntry
	9,  // 46: proto.AugmentedChunk.source_type:type_name -> proto.ChunkSourceType
	11, // 47: proto.AugmentedChunk.augmented_type:type_name -> proto.AugmentedChunkType
	34, // 48: proto.ChunkInfo.chunk:type_name -> proto.Chunk
	29, // 49: proto.ChunkInfo.display_infos:type_name -> proto.DisplayInfo
	32, // 50: proto.ChunkInfo.elements:type_name -> proto.DocElement
	40, // 51: proto.TableConfig.fields_config:type_name -> proto.TableFieldConfig
	13, // 52: proto.TableFieldConfig.data_type:type_name -> proto.TableFieldDataType
	53, // [53:53] is the sub-list for method output_type
	53, // [53:53] is the sub-list for method input_type
	53, // [53:53] is the sub-list for extension type_name
	53, // [53:53] is the sub-list for extension extendee
	0,  // [0:53] is the sub-list for field type_name
}

func init() { file_proto_knowledge_base_proto_init() }
func file_proto_knowledge_base_proto_init() {
	if File_proto_knowledge_base_proto != nil {
		return
	}
	file_proto_data_connection_proto_init()
	file_proto_model_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_proto_knowledge_base_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KnowledgeBaseConnectionRegistry); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_knowledge_base_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KnowledgeBaseTKHRegistry); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_knowledge_base_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KnowledgeBasePublishInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_knowledge_base_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KnowledgeBase); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_knowledge_base_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MetricsInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_knowledge_base_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppletServiceRef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_knowledge_base_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DocProcessingConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_knowledge_base_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DocLoadConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_knowledge_base_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrievalConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_knowledge_base_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DocSplitConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_knowledge_base_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChunkAugmentConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_knowledge_base_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecallParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_knowledge_base_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RerankParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_knowledge_base_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Document); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_knowledge_base_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DisplayInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_knowledge_base_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Point); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_knowledge_base_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Coordinates); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_knowledge_base_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DocElement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_knowledge_base_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DocElementMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_knowledge_base_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Chunk); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_knowledge_base_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AugmentedChunk); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_knowledge_base_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChunkInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_knowledge_base_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TableInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_knowledge_base_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TableConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_knowledge_base_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CorpusConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_knowledge_base_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TableFieldConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_knowledge_base_proto_rawDesc,
			NumEnums:      15,
			NumMessages:   28,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_knowledge_base_proto_goTypes,
		DependencyIndexes: file_proto_knowledge_base_proto_depIdxs,
		EnumInfos:         file_proto_knowledge_base_proto_enumTypes,
		MessageInfos:      file_proto_knowledge_base_proto_msgTypes,
	}.Build()
	File_proto_knowledge_base_proto = out.File
	file_proto_knowledge_base_proto_rawDesc = nil
	file_proto_knowledge_base_proto_goTypes = nil
	file_proto_knowledge_base_proto_depIdxs = nil
}
