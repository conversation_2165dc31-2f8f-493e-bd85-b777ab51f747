// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/dynamic_param.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DynamicParam_DataType int32

const (
	DynamicParam_DATA_TYPE_UNSPECIFIED DynamicParam_DataType = 0
	DynamicParam_DATA_TYPE_INT         DynamicParam_DataType = 1
	DynamicParam_DATA_TYPE_FLOAT       DynamicParam_DataType = 2
	DynamicParam_DATA_TYPE_STRING      DynamicParam_DataType = 3
	DynamicParam_DATA_TYPE_BOOLEAN     DynamicParam_DataType = 4
)

// Enum value maps for DynamicParam_DataType.
var (
	DynamicParam_DataType_name = map[int32]string{
		0: "DATA_TYPE_UNSPECIFIED",
		1: "DATA_TYPE_INT",
		2: "DATA_TYPE_FLOAT",
		3: "DATA_TYPE_STRING",
		4: "DATA_TYPE_BOOLEAN",
	}
	DynamicParam_DataType_value = map[string]int32{
		"DATA_TYPE_UNSPECIFIED": 0,
		"DATA_TYPE_INT":         1,
		"DATA_TYPE_FLOAT":       2,
		"DATA_TYPE_STRING":      3,
		"DATA_TYPE_BOOLEAN":     4,
	}
)

func (x DynamicParam_DataType) Enum() *DynamicParam_DataType {
	p := new(DynamicParam_DataType)
	*p = x
	return p
}

func (x DynamicParam_DataType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DynamicParam_DataType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_dynamic_param_proto_enumTypes[0].Descriptor()
}

func (DynamicParam_DataType) Type() protoreflect.EnumType {
	return &file_proto_dynamic_param_proto_enumTypes[0]
}

func (x DynamicParam_DataType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DynamicParam_DataType.Descriptor instead.
func (DynamicParam_DataType) EnumDescriptor() ([]byte, []int) {
	return file_proto_dynamic_param_proto_rawDescGZIP(), []int{0, 0}
}

type DynamicParam_Type int32

const (
	DynamicParam_TYPE_UNSPECIFIED DynamicParam_Type = 0
	DynamicParam_TYPE_INPUT       DynamicParam_Type = 1 // 单行输入文本框, 支持开启多选
	DynamicParam_TYPE_NUMBER      DynamicParam_Type = 2 // 单行输入数字框, 支持开启多选
	DynamicParam_TYPE_PASSWORD    DynamicParam_Type = 3 // 单行输入密码框，不支持多选
	DynamicParam_TYPE_TEXTAREA    DynamicParam_Type = 4 // 多行输入文本框，不支持多选
	DynamicParam_TYPE_SWITCH      DynamicParam_Type = 5 // 单行开关选择器，不支持多选
	// TYPE_SELECTOR 单选框， 选择范围由datasource给定, 支持开启多选
	// e.g. datasource="true@@是,false@@否"
	// 则下拉框中则展示 ["是","否"]
	// 最终用户选择确认后，传递给后端的值应为名称对应的id
	DynamicParam_TYPE_SELECTOR DynamicParam_Type = 6
	// TYPE_SELECTOR_DYNAMIC 动态选择框，选择范围通过调用datasource中的指定API获取， 支持开启多选
	// e.g. datasource="name,id@/hub/widgets/range/products?category=tick"
	// 则前端调用API(/hub/widgets/range/products?category=tick) 获取响应结果,
	//
	//	[{"id":"id1","name":"name1"},{"id":"id2","name":"name2"}]
	//
	// 则对应的最终下拉框应为： id1@@name1,id2@@name2
	DynamicParam_TYPE_SELECTOR_DYNAMIC DynamicParam_Type = 7
	// TYPE_KVITEM 键值对类型, 用户需要手动成对输入, 支持开启多选
	// 无论是否开启多选，value 中键值对均使用json格式进行, e.g. {"key1":"value1"}， {"key1":"value1","key2":"value2"}
	DynamicParam_TYPE_KVITEM DynamicParam_Type = 8
	// 代码类型
	DynamicParam_TYPE_CODE_PYTHON  DynamicParam_Type = 9
	DynamicParam_TYPE_CODE_JSONNET DynamicParam_Type = 10
	// 应用链类型
	DynamicParam_TYPE_APPLET_CHAIN DynamicParam_Type = 11
	// 智能体算子相关
	DynamicParam_TYPE_AGENT_MODEL_API           DynamicParam_Type = 12
	DynamicParam_TYPE_AGENT_INSTRUCTION         DynamicParam_Type = 13
	DynamicParam_TYPE_AGENT_SKILL_API_TOOLS     DynamicParam_Type = 14
	DynamicParam_TYPE_AGENT_SKILL_KNOW_TOOLS    DynamicParam_Type = 15
	DynamicParam_TYPE_AGENT_SKILL_SERVICE_TOOLS DynamicParam_Type = 16
	// 文本知识库算子
	DynamicParam_TYPE_TEXT_KNOWLEDGE_BASE DynamicParam_Type = 17
	// 滑动条类型
	DynamicParam_TYPE_SLIDER DynamicParam_Type = 18
	// 工具算子调用
	DynamicParam_TYPE_TOOL_CALL DynamicParam_Type = 19
	// 统一模型服务下拉框
	DynamicParam_TYPE_MODEL_SERVICE DynamicParam_Type = 20
	// 输入安全防护
	DynamicParam_TYPE_GUARDRAIL_INPUT DynamicParam_Type = 21
	// 输出安全防护
	DynamicParam_TYPE_GUARDRAIL_OUTPUT DynamicParam_Type = 22
	// 参数提取
	DynamicParam_TYPE_PARAMETER_EXTRACTOR DynamicParam_Type = 23
	// 代码实例id
	DynamicParam_TYPE_CODE_INSTANCE_ID DynamicParam_Type = 24
)

// Enum value maps for DynamicParam_Type.
var (
	DynamicParam_Type_name = map[int32]string{
		0:  "TYPE_UNSPECIFIED",
		1:  "TYPE_INPUT",
		2:  "TYPE_NUMBER",
		3:  "TYPE_PASSWORD",
		4:  "TYPE_TEXTAREA",
		5:  "TYPE_SWITCH",
		6:  "TYPE_SELECTOR",
		7:  "TYPE_SELECTOR_DYNAMIC",
		8:  "TYPE_KVITEM",
		9:  "TYPE_CODE_PYTHON",
		10: "TYPE_CODE_JSONNET",
		11: "TYPE_APPLET_CHAIN",
		12: "TYPE_AGENT_MODEL_API",
		13: "TYPE_AGENT_INSTRUCTION",
		14: "TYPE_AGENT_SKILL_API_TOOLS",
		15: "TYPE_AGENT_SKILL_KNOW_TOOLS",
		16: "TYPE_AGENT_SKILL_SERVICE_TOOLS",
		17: "TYPE_TEXT_KNOWLEDGE_BASE",
		18: "TYPE_SLIDER",
		19: "TYPE_TOOL_CALL",
		20: "TYPE_MODEL_SERVICE",
		21: "TYPE_GUARDRAIL_INPUT",
		22: "TYPE_GUARDRAIL_OUTPUT",
		23: "TYPE_PARAMETER_EXTRACTOR",
		24: "TYPE_CODE_INSTANCE_ID",
	}
	DynamicParam_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED":               0,
		"TYPE_INPUT":                     1,
		"TYPE_NUMBER":                    2,
		"TYPE_PASSWORD":                  3,
		"TYPE_TEXTAREA":                  4,
		"TYPE_SWITCH":                    5,
		"TYPE_SELECTOR":                  6,
		"TYPE_SELECTOR_DYNAMIC":          7,
		"TYPE_KVITEM":                    8,
		"TYPE_CODE_PYTHON":               9,
		"TYPE_CODE_JSONNET":              10,
		"TYPE_APPLET_CHAIN":              11,
		"TYPE_AGENT_MODEL_API":           12,
		"TYPE_AGENT_INSTRUCTION":         13,
		"TYPE_AGENT_SKILL_API_TOOLS":     14,
		"TYPE_AGENT_SKILL_KNOW_TOOLS":    15,
		"TYPE_AGENT_SKILL_SERVICE_TOOLS": 16,
		"TYPE_TEXT_KNOWLEDGE_BASE":       17,
		"TYPE_SLIDER":                    18,
		"TYPE_TOOL_CALL":                 19,
		"TYPE_MODEL_SERVICE":             20,
		"TYPE_GUARDRAIL_INPUT":           21,
		"TYPE_GUARDRAIL_OUTPUT":          22,
		"TYPE_PARAMETER_EXTRACTOR":       23,
		"TYPE_CODE_INSTANCE_ID":          24,
	}
)

func (x DynamicParam_Type) Enum() *DynamicParam_Type {
	p := new(DynamicParam_Type)
	*p = x
	return p
}

func (x DynamicParam_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DynamicParam_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_dynamic_param_proto_enumTypes[1].Descriptor()
}

func (DynamicParam_Type) Type() protoreflect.EnumType {
	return &file_proto_dynamic_param_proto_enumTypes[1]
}

func (x DynamicParam_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DynamicParam_Type.Descriptor instead.
func (DynamicParam_Type) EnumDescriptor() ([]byte, []int) {
	return file_proto_dynamic_param_proto_rawDescGZIP(), []int{0, 1}
}

type DynamicParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string                `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                               // id 为最终存储该属性值时的Key
	Name         string                `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                                           // name 为属性的展示名称
	Desc         string                `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`                                                           // desc 为属性的描述, 通常以名称旁的?形式进行展示
	Type         DynamicParam_Type     `protobuf:"varint,4,opt,name=type,proto3,enum=proto.DynamicParam_Type" json:"type,omitempty"`                             // type 为该属性在前端的展示样式类型
	DataType     DynamicParam_DataType `protobuf:"varint,5,opt,name=data_type,json=dataType,proto3,enum=proto.DynamicParam_DataType" json:"data_type,omitempty"` // data_type 为该属性的数据类型（仅用作语法校验，前端最终传输values时，全部以string类型传输）
	NumberRange  *NumberRange          `protobuf:"bytes,6,opt,name=number_range,json=numberRange,proto3" json:"number_range,omitempty"`                          // number_range 当data_type为 int 或 float 时, 数值的取值范围
	DefaultValue string                `protobuf:"bytes,7,opt,name=default_value,json=defaultValue,proto3" json:"default_value,omitempty"`                       // default_value 该属性默认的属性值
	// 当 PARAM_TYPE_SELECTOR_DYNAMIC 时， 格式为 "<name_field>,<id_field>@<api_path>"
	//   - 接口使用GET方式进行调用， 并返回json格式结果
	//   - 返回值最外层是一个数组, e.g.  [{"id":"id1","name":"name1"},{"id":"id2","name":"name2"}]
	//   - <name_field> 和 <id_field> 表明数组中每个item如何提取其 id与name
	//   - 完成 name, id 提取后，处理逻辑同 PARAM_TYPE_SELECTOR
	Datasource   string `protobuf:"bytes,8,opt,name=datasource,proto3" json:"datasource,omitempty"`
	Precondition string `protobuf:"bytes,9,opt,name=precondition,proto3" json:"precondition,omitempty"` // precondition 为当前属性展示的前置条件, 用来实现简单的动态依赖功能
	Required     bool   `protobuf:"varint,10,opt,name=required,proto3" json:"required,omitempty"`       // required 表示该属性是否为必填项
	Multiple     bool   `protobuf:"varint,11,opt,name=multiple,proto3" json:"multiple,omitempty"`       // multiple 表示是否支持多选(下拉框), 列表(输入), Map(K,V)
	// @deprecated
	//
	//	bool readonly = 12;         // readonly 表示该动态参数是否只读
	Maxlen      int64  `protobuf:"varint,13,opt,name=maxlen,proto3" json:"maxlen,omitempty"`          // maxlen 表示当Multiple为true时, 可选择的最大数量
	Placeholder string `protobuf:"bytes,14,opt,name=placeholder,proto3" json:"placeholder,omitempty"` // placeholder 输入框在用户填写前的提示信息
	// @deprecated
	//
	//	bool show = 15;             // show 是否在前端进行展示
	Advanced  bool  `protobuf:"varint,16,opt,name=advanced,proto3" json:"advanced,omitempty"`   // advanced 是否仅在额外的弹窗中进行编辑， e.g. 一个高级配置界面，通过配置按钮触发
	Multiline bool  `protobuf:"varint,17,opt,name=multiline,proto3" json:"multiline,omitempty"` // 是否点击后弹出多行编辑框
	Hidden    bool  `protobuf:"varint,18,opt,name=hidden,proto3" json:"hidden,omitempty"`       // hidden 是否编排页面隐藏
	Precision int32 `protobuf:"varint,19,opt,name=precision,proto3" json:"precision,omitempty"` // 精度 表示小数点后几位 e.g. 1表示小数点后一位
	Disabled  bool  `protobuf:"varint,20,opt,name=disabled,proto3" json:"disabled,omitempty"`   // 是否可编辑，替换原先的readonly属性
	// comp_props 用于控制前端的组件支持的属性, 可灵活进行配置, 格式为json string
	// 具体每种类型的表单组件支持的具体可配置属性,可以参考前端框架文档,或者咨询相关同事.
	// 以文本输入框(TYPE_INPUT)为例: 可参考 https://ant-design.antgroup.com/components/input-cn#input
	// 部分可配参数如下:
	//
	//	参数	          说明	                      类型	        默认值
	//	allowClear	  可以点击清除图标删除内容	    boolean   	false
	//	showCount	    是否展示字数	              boolean     false
	//	disabled	    是否禁用状态，默认为 false	  boolean	    false
	//	maxLength	    最大长度	                  number	    -
	//
	// e.g.
	// - 控制输入框的最大文本长度, 展示字数统计, 并且允许清空 -> "{\"maxLength\": 1024, \"showCount\":true, \"allowClear\": true }"
	CompProps string `protobuf:"bytes,50,opt,name=comp_props,json=compProps,proto3" json:"comp_props,omitempty"`
}

func (x *DynamicParam) Reset() {
	*x = DynamicParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_dynamic_param_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DynamicParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DynamicParam) ProtoMessage() {}

func (x *DynamicParam) ProtoReflect() protoreflect.Message {
	mi := &file_proto_dynamic_param_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DynamicParam.ProtoReflect.Descriptor instead.
func (*DynamicParam) Descriptor() ([]byte, []int) {
	return file_proto_dynamic_param_proto_rawDescGZIP(), []int{0}
}

func (x *DynamicParam) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DynamicParam) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DynamicParam) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *DynamicParam) GetType() DynamicParam_Type {
	if x != nil {
		return x.Type
	}
	return DynamicParam_TYPE_UNSPECIFIED
}

func (x *DynamicParam) GetDataType() DynamicParam_DataType {
	if x != nil {
		return x.DataType
	}
	return DynamicParam_DATA_TYPE_UNSPECIFIED
}

func (x *DynamicParam) GetNumberRange() *NumberRange {
	if x != nil {
		return x.NumberRange
	}
	return nil
}

func (x *DynamicParam) GetDefaultValue() string {
	if x != nil {
		return x.DefaultValue
	}
	return ""
}

func (x *DynamicParam) GetDatasource() string {
	if x != nil {
		return x.Datasource
	}
	return ""
}

func (x *DynamicParam) GetPrecondition() string {
	if x != nil {
		return x.Precondition
	}
	return ""
}

func (x *DynamicParam) GetRequired() bool {
	if x != nil {
		return x.Required
	}
	return false
}

func (x *DynamicParam) GetMultiple() bool {
	if x != nil {
		return x.Multiple
	}
	return false
}

func (x *DynamicParam) GetMaxlen() int64 {
	if x != nil {
		return x.Maxlen
	}
	return 0
}

func (x *DynamicParam) GetPlaceholder() string {
	if x != nil {
		return x.Placeholder
	}
	return ""
}

func (x *DynamicParam) GetAdvanced() bool {
	if x != nil {
		return x.Advanced
	}
	return false
}

func (x *DynamicParam) GetMultiline() bool {
	if x != nil {
		return x.Multiline
	}
	return false
}

func (x *DynamicParam) GetHidden() bool {
	if x != nil {
		return x.Hidden
	}
	return false
}

func (x *DynamicParam) GetPrecision() int32 {
	if x != nil {
		return x.Precision
	}
	return 0
}

func (x *DynamicParam) GetDisabled() bool {
	if x != nil {
		return x.Disabled
	}
	return false
}

func (x *DynamicParam) GetCompProps() string {
	if x != nil {
		return x.CompProps
	}
	return ""
}

// NumberRange 数字的取值范围
type NumberRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Min float32 `protobuf:"fixed32,1,opt,name=min,proto3" json:"min,omitempty"`
	Max float32 `protobuf:"fixed32,2,opt,name=max,proto3" json:"max,omitempty"`
	// step 表示数值类型参数每次修改的调整步长
	// 即当 data_type 为 number 时, 前端组件每次 +- 的数值变动量
	// 默认为 1.0, 当 data_type 类型为Int时，step将四舍五入忽略小数部分
	Step float32 `protobuf:"fixed32,3,opt,name=step,proto3" json:"step,omitempty"`
}

func (x *NumberRange) Reset() {
	*x = NumberRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_dynamic_param_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NumberRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NumberRange) ProtoMessage() {}

func (x *NumberRange) ProtoReflect() protoreflect.Message {
	mi := &file_proto_dynamic_param_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NumberRange.ProtoReflect.Descriptor instead.
func (*NumberRange) Descriptor() ([]byte, []int) {
	return file_proto_dynamic_param_proto_rawDescGZIP(), []int{1}
}

func (x *NumberRange) GetMin() float32 {
	if x != nil {
		return x.Min
	}
	return 0
}

func (x *NumberRange) GetMax() float32 {
	if x != nil {
		return x.Max
	}
	return 0
}

func (x *NumberRange) GetStep() float32 {
	if x != nil {
		return x.Step
	}
	return 0
}

// DynamicParamValues 动态参数值
type DynamicParamValues struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// values 定义了所有填写的动态参数的 id -> value 的映射关系
	// 其中:
	// * id     即为对应的 DynamicParam.id
	// * value  即为最终获取到的动态参数值，支持多种参数类型:
	//   - 数值     "3.14"
	//   - 字符串   "abcd"
	//   - 数组     '["item1","item2","item3"]'
	//   - Map     '{"key1":"value1","key2":"value2"}'
	Values map[string]string `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *DynamicParamValues) Reset() {
	*x = DynamicParamValues{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_dynamic_param_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DynamicParamValues) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DynamicParamValues) ProtoMessage() {}

func (x *DynamicParamValues) ProtoReflect() protoreflect.Message {
	mi := &file_proto_dynamic_param_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DynamicParamValues.ProtoReflect.Descriptor instead.
func (*DynamicParamValues) Descriptor() ([]byte, []int) {
	return file_proto_dynamic_param_proto_rawDescGZIP(), []int{2}
}

func (x *DynamicParamValues) GetValues() map[string]string {
	if x != nil {
		return x.Values
	}
	return nil
}

// DynamicSelectRange 动态选择范围，支持多级菜单
// 最终用户选择的多级的值之间使用 @@ 进行分割， e.g. 三级菜单最终设置的值为： l1_value@@l2_value@@l3_value
type DynamicSelectRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Layer int64                `protobuf:"varint,1,opt,name=layer,proto3" json:"layer,omitempty"` // 该选项对应的多级菜单的层级数量
	Items []*DynamicSelectItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`  // 第一层菜单的子选项范围
}

func (x *DynamicSelectRange) Reset() {
	*x = DynamicSelectRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_dynamic_param_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DynamicSelectRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DynamicSelectRange) ProtoMessage() {}

func (x *DynamicSelectRange) ProtoReflect() protoreflect.Message {
	mi := &file_proto_dynamic_param_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DynamicSelectRange.ProtoReflect.Descriptor instead.
func (*DynamicSelectRange) Descriptor() ([]byte, []int) {
	return file_proto_dynamic_param_proto_rawDescGZIP(), []int{3}
}

func (x *DynamicSelectRange) GetLayer() int64 {
	if x != nil {
		return x.Layer
	}
	return 0
}

func (x *DynamicSelectRange) GetItems() []*DynamicSelectItem {
	if x != nil {
		return x.Items
	}
	return nil
}

// DynamicSelectItem 动态选择的可选项，可包括下一级的子选项
type DynamicSelectItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string               `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                             // 可选项的ID, 用于标识用户最终参数选择的值
	Name     string               `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                         // 可选项的名称, 用于展示相对用户友好的名称
	Desc     string               `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`                         // 可选项的描述, 用于Hover时展示额外的一些描述信息
	SubItems []*DynamicSelectItem `protobuf:"bytes,4,rep,name=sub_items,json=subItems,proto3" json:"sub_items,omitempty"` // 该选项的下一级子选项的可选范围
}

func (x *DynamicSelectItem) Reset() {
	*x = DynamicSelectItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_dynamic_param_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DynamicSelectItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DynamicSelectItem) ProtoMessage() {}

func (x *DynamicSelectItem) ProtoReflect() protoreflect.Message {
	mi := &file_proto_dynamic_param_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DynamicSelectItem.ProtoReflect.Descriptor instead.
func (*DynamicSelectItem) Descriptor() ([]byte, []int) {
	return file_proto_dynamic_param_proto_rawDescGZIP(), []int{4}
}

func (x *DynamicSelectItem) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DynamicSelectItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DynamicSelectItem) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *DynamicSelectItem) GetSubItems() []*DynamicSelectItem {
	if x != nil {
		return x.SubItems
	}
	return nil
}

var File_proto_dynamic_param_proto protoreflect.FileDescriptor

var file_proto_dynamic_param_proto_rawDesc = []byte{
	0x0a, 0x19, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xc6, 0x0a, 0x0a, 0x0c, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x2c, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x2e, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x09, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x35, 0x0a, 0x0c, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x72,
	0x61, 0x6e, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0b,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x12, 0x22, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x08, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x6d, 0x61, 0x78, 0x6c, 0x65, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6d, 0x61,
	0x78, 0x6c, 0x65, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c,
	0x64, 0x65, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x6c, 0x61, 0x63, 0x65,
	0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x64, 0x76, 0x61, 0x6e, 0x63,
	0x65, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x61, 0x64, 0x76, 0x61, 0x6e, 0x63,
	0x65, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x6c, 0x69, 0x6e, 0x65, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x6c, 0x69, 0x6e, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x68, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x68, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x65, 0x63,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72, 0x65,
	0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x73,
	0x18, 0x32, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x50, 0x72, 0x6f, 0x70,
	0x73, 0x22, 0x7a, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a,
	0x15, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x44, 0x41, 0x54, 0x41,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x44,
	0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x4c, 0x4f, 0x41, 0x54, 0x10, 0x02,
	0x12, 0x14, 0x0a, 0x10, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x54,
	0x52, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4c, 0x45, 0x41, 0x4e, 0x10, 0x04, 0x22, 0xdb, 0x04,
	0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x50, 0x55, 0x54, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x02, 0x12, 0x11, 0x0a,
	0x0d, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x57, 0x4f, 0x52, 0x44, 0x10, 0x03,
	0x12, 0x11, 0x0a, 0x0d, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x41, 0x52, 0x45,
	0x41, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x57, 0x49, 0x54,
	0x43, 0x48, 0x10, 0x05, 0x12, 0x11, 0x0a, 0x0d, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x4c,
	0x45, 0x43, 0x54, 0x4f, 0x52, 0x10, 0x06, 0x12, 0x19, 0x0a, 0x15, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x44, 0x59, 0x4e, 0x41, 0x4d, 0x49, 0x43,
	0x10, 0x07, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4b, 0x56, 0x49, 0x54, 0x45,
	0x4d, 0x10, 0x08, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45,
	0x5f, 0x50, 0x59, 0x54, 0x48, 0x4f, 0x4e, 0x10, 0x09, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4a, 0x53, 0x4f, 0x4e, 0x4e, 0x45, 0x54, 0x10, 0x0a,
	0x12, 0x15, 0x0a, 0x11, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x45, 0x54, 0x5f,
	0x43, 0x48, 0x41, 0x49, 0x4e, 0x10, 0x0b, 0x12, 0x18, 0x0a, 0x14, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x41, 0x50, 0x49, 0x10,
	0x0c, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f,
	0x49, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0d, 0x12, 0x1e, 0x0a,
	0x1a, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x4b, 0x49, 0x4c,
	0x4c, 0x5f, 0x41, 0x50, 0x49, 0x5f, 0x54, 0x4f, 0x4f, 0x4c, 0x53, 0x10, 0x0e, 0x12, 0x1f, 0x0a,
	0x1b, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x4b, 0x49, 0x4c,
	0x4c, 0x5f, 0x4b, 0x4e, 0x4f, 0x57, 0x5f, 0x54, 0x4f, 0x4f, 0x4c, 0x53, 0x10, 0x0f, 0x12, 0x22,
	0x0a, 0x1e, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x4b, 0x49,
	0x4c, 0x4c, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x54, 0x4f, 0x4f, 0x4c, 0x53,
	0x10, 0x10, 0x12, 0x1c, 0x0a, 0x18, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x5f,
	0x4b, 0x4e, 0x4f, 0x57, 0x4c, 0x45, 0x44, 0x47, 0x45, 0x5f, 0x42, 0x41, 0x53, 0x45, 0x10, 0x11,
	0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x4c, 0x49, 0x44, 0x45, 0x52, 0x10,
	0x12, 0x12, 0x12, 0x0a, 0x0e, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x4f, 0x4f, 0x4c, 0x5f, 0x43,
	0x41, 0x4c, 0x4c, 0x10, 0x13, 0x12, 0x16, 0x0a, 0x12, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x4f,
	0x44, 0x45, 0x4c, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x14, 0x12, 0x18, 0x0a,
	0x14, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x55, 0x41, 0x52, 0x44, 0x52, 0x41, 0x49, 0x4c, 0x5f,
	0x49, 0x4e, 0x50, 0x55, 0x54, 0x10, 0x15, 0x12, 0x19, 0x0a, 0x15, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x47, 0x55, 0x41, 0x52, 0x44, 0x52, 0x41, 0x49, 0x4c, 0x5f, 0x4f, 0x55, 0x54, 0x50, 0x55, 0x54,
	0x10, 0x16, 0x12, 0x1c, 0x0a, 0x18, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d,
	0x45, 0x54, 0x45, 0x52, 0x5f, 0x45, 0x58, 0x54, 0x52, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x10, 0x17,
	0x12, 0x19, 0x0a, 0x15, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e,
	0x53, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x49, 0x44, 0x10, 0x18, 0x22, 0x45, 0x0a, 0x0b, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x69,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x6d, 0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x61, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x6d, 0x61, 0x78, 0x12, 0x12,
	0x0a, 0x04, 0x73, 0x74, 0x65, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x73, 0x74,
	0x65, 0x70, 0x22, 0x8e, 0x01, 0x0a, 0x12, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x3d, 0x0a, 0x06, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x73, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x5a, 0x0a, 0x12, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x53, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x12,
	0x2e, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x53, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22,
	0x82, 0x01, 0x0a, 0x11, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x53, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73,
	0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x35, 0x0a,
	0x09, 0x73, 0x75, 0x62, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63,
	0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x08, 0x73, 0x75, 0x62, 0x49,
	0x74, 0x65, 0x6d, 0x73, 0x42, 0x26, 0x5a, 0x24, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72,
	0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_dynamic_param_proto_rawDescOnce sync.Once
	file_proto_dynamic_param_proto_rawDescData = file_proto_dynamic_param_proto_rawDesc
)

func file_proto_dynamic_param_proto_rawDescGZIP() []byte {
	file_proto_dynamic_param_proto_rawDescOnce.Do(func() {
		file_proto_dynamic_param_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_dynamic_param_proto_rawDescData)
	})
	return file_proto_dynamic_param_proto_rawDescData
}

var file_proto_dynamic_param_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_proto_dynamic_param_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_proto_dynamic_param_proto_goTypes = []interface{}{
	(DynamicParam_DataType)(0), // 0: proto.DynamicParam.DataType
	(DynamicParam_Type)(0),     // 1: proto.DynamicParam.Type
	(*DynamicParam)(nil),       // 2: proto.DynamicParam
	(*NumberRange)(nil),        // 3: proto.NumberRange
	(*DynamicParamValues)(nil), // 4: proto.DynamicParamValues
	(*DynamicSelectRange)(nil), // 5: proto.DynamicSelectRange
	(*DynamicSelectItem)(nil),  // 6: proto.DynamicSelectItem
	nil,                        // 7: proto.DynamicParamValues.ValuesEntry
}
var file_proto_dynamic_param_proto_depIdxs = []int32{
	1, // 0: proto.DynamicParam.type:type_name -> proto.DynamicParam.Type
	0, // 1: proto.DynamicParam.data_type:type_name -> proto.DynamicParam.DataType
	3, // 2: proto.DynamicParam.number_range:type_name -> proto.NumberRange
	7, // 3: proto.DynamicParamValues.values:type_name -> proto.DynamicParamValues.ValuesEntry
	6, // 4: proto.DynamicSelectRange.items:type_name -> proto.DynamicSelectItem
	6, // 5: proto.DynamicSelectItem.sub_items:type_name -> proto.DynamicSelectItem
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_proto_dynamic_param_proto_init() }
func file_proto_dynamic_param_proto_init() {
	if File_proto_dynamic_param_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_dynamic_param_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DynamicParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_dynamic_param_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NumberRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_dynamic_param_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DynamicParamValues); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_dynamic_param_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DynamicSelectRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_dynamic_param_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DynamicSelectItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_dynamic_param_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_dynamic_param_proto_goTypes,
		DependencyIndexes: file_proto_dynamic_param_proto_depIdxs,
		EnumInfos:         file_proto_dynamic_param_proto_enumTypes,
		MessageInfos:      file_proto_dynamic_param_proto_msgTypes,
	}.Build()
	File_proto_dynamic_param_proto = out.File
	file_proto_dynamic_param_proto_rawDesc = nil
	file_proto_dynamic_param_proto_goTypes = nil
	file_proto_dynamic_param_proto_depIdxs = nil
}
