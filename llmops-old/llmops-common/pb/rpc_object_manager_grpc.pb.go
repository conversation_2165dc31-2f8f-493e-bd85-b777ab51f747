// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.18.1
// source: proto/rpc_object_manager.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ObjectManager_ListObjects_FullMethodName    = "/proto.ObjectManager/ListObjects"
	ObjectManager_DeleteObject_FullMethodName   = "/proto.ObjectManager/DeleteObject"
	ObjectManager_UploadObject_FullMethodName   = "/proto.ObjectManager/UploadObject"
	ObjectManager_DownloadObject_FullMethodName = "/proto.ObjectManager/DownloadObject"
)

// ObjectManagerClient is the client API for ObjectManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ObjectManagerClient interface {
	ListObjects(ctx context.Context, in *ListObjectsReq, opts ...grpc.CallOption) (*ListObjectsRsp, error)
	DeleteObject(ctx context.Context, in *DeleteObjectReq, opts ...grpc.CallOption) (*DeleteObjectRsp, error)
	UploadObject(ctx context.Context, opts ...grpc.CallOption) (ObjectManager_UploadObjectClient, error)
	DownloadObject(ctx context.Context, in *DownloadObjectReq, opts ...grpc.CallOption) (ObjectManager_DownloadObjectClient, error)
}

type objectManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewObjectManagerClient(cc grpc.ClientConnInterface) ObjectManagerClient {
	return &objectManagerClient{cc}
}

func (c *objectManagerClient) ListObjects(ctx context.Context, in *ListObjectsReq, opts ...grpc.CallOption) (*ListObjectsRsp, error) {
	out := new(ListObjectsRsp)
	err := c.cc.Invoke(ctx, ObjectManager_ListObjects_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *objectManagerClient) DeleteObject(ctx context.Context, in *DeleteObjectReq, opts ...grpc.CallOption) (*DeleteObjectRsp, error) {
	out := new(DeleteObjectRsp)
	err := c.cc.Invoke(ctx, ObjectManager_DeleteObject_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *objectManagerClient) UploadObject(ctx context.Context, opts ...grpc.CallOption) (ObjectManager_UploadObjectClient, error) {
	stream, err := c.cc.NewStream(ctx, &ObjectManager_ServiceDesc.Streams[0], ObjectManager_UploadObject_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &objectManagerUploadObjectClient{stream}
	return x, nil
}

type ObjectManager_UploadObjectClient interface {
	Send(*UploadObjectReq) error
	CloseAndRecv() (*UploadObjectRsp, error)
	grpc.ClientStream
}

type objectManagerUploadObjectClient struct {
	grpc.ClientStream
}

func (x *objectManagerUploadObjectClient) Send(m *UploadObjectReq) error {
	return x.ClientStream.SendMsg(m)
}

func (x *objectManagerUploadObjectClient) CloseAndRecv() (*UploadObjectRsp, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(UploadObjectRsp)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *objectManagerClient) DownloadObject(ctx context.Context, in *DownloadObjectReq, opts ...grpc.CallOption) (ObjectManager_DownloadObjectClient, error) {
	stream, err := c.cc.NewStream(ctx, &ObjectManager_ServiceDesc.Streams[1], ObjectManager_DownloadObject_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &objectManagerDownloadObjectClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type ObjectManager_DownloadObjectClient interface {
	Recv() (*DownloadObjectRsp, error)
	grpc.ClientStream
}

type objectManagerDownloadObjectClient struct {
	grpc.ClientStream
}

func (x *objectManagerDownloadObjectClient) Recv() (*DownloadObjectRsp, error) {
	m := new(DownloadObjectRsp)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// ObjectManagerServer is the server API for ObjectManager service.
// All implementations must embed UnimplementedObjectManagerServer
// for forward compatibility
type ObjectManagerServer interface {
	ListObjects(context.Context, *ListObjectsReq) (*ListObjectsRsp, error)
	DeleteObject(context.Context, *DeleteObjectReq) (*DeleteObjectRsp, error)
	UploadObject(ObjectManager_UploadObjectServer) error
	DownloadObject(*DownloadObjectReq, ObjectManager_DownloadObjectServer) error
	mustEmbedUnimplementedObjectManagerServer()
}

// UnimplementedObjectManagerServer must be embedded to have forward compatible implementations.
type UnimplementedObjectManagerServer struct {
}

func (UnimplementedObjectManagerServer) ListObjects(context.Context, *ListObjectsReq) (*ListObjectsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListObjects not implemented")
}
func (UnimplementedObjectManagerServer) DeleteObject(context.Context, *DeleteObjectReq) (*DeleteObjectRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteObject not implemented")
}
func (UnimplementedObjectManagerServer) UploadObject(ObjectManager_UploadObjectServer) error {
	return status.Errorf(codes.Unimplemented, "method UploadObject not implemented")
}
func (UnimplementedObjectManagerServer) DownloadObject(*DownloadObjectReq, ObjectManager_DownloadObjectServer) error {
	return status.Errorf(codes.Unimplemented, "method DownloadObject not implemented")
}
func (UnimplementedObjectManagerServer) mustEmbedUnimplementedObjectManagerServer() {}

// UnsafeObjectManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ObjectManagerServer will
// result in compilation errors.
type UnsafeObjectManagerServer interface {
	mustEmbedUnimplementedObjectManagerServer()
}

func RegisterObjectManagerServer(s grpc.ServiceRegistrar, srv ObjectManagerServer) {
	s.RegisterService(&ObjectManager_ServiceDesc, srv)
}

func _ObjectManager_ListObjects_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListObjectsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ObjectManagerServer).ListObjects(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ObjectManager_ListObjects_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ObjectManagerServer).ListObjects(ctx, req.(*ListObjectsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ObjectManager_DeleteObject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteObjectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ObjectManagerServer).DeleteObject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ObjectManager_DeleteObject_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ObjectManagerServer).DeleteObject(ctx, req.(*DeleteObjectReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ObjectManager_UploadObject_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(ObjectManagerServer).UploadObject(&objectManagerUploadObjectServer{stream})
}

type ObjectManager_UploadObjectServer interface {
	SendAndClose(*UploadObjectRsp) error
	Recv() (*UploadObjectReq, error)
	grpc.ServerStream
}

type objectManagerUploadObjectServer struct {
	grpc.ServerStream
}

func (x *objectManagerUploadObjectServer) SendAndClose(m *UploadObjectRsp) error {
	return x.ServerStream.SendMsg(m)
}

func (x *objectManagerUploadObjectServer) Recv() (*UploadObjectReq, error) {
	m := new(UploadObjectReq)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _ObjectManager_DownloadObject_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(DownloadObjectReq)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ObjectManagerServer).DownloadObject(m, &objectManagerDownloadObjectServer{stream})
}

type ObjectManager_DownloadObjectServer interface {
	Send(*DownloadObjectRsp) error
	grpc.ServerStream
}

type objectManagerDownloadObjectServer struct {
	grpc.ServerStream
}

func (x *objectManagerDownloadObjectServer) Send(m *DownloadObjectRsp) error {
	return x.ServerStream.SendMsg(m)
}

// ObjectManager_ServiceDesc is the grpc.ServiceDesc for ObjectManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ObjectManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "proto.ObjectManager",
	HandlerType: (*ObjectManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListObjects",
			Handler:    _ObjectManager_ListObjects_Handler,
		},
		{
			MethodName: "DeleteObject",
			Handler:    _ObjectManager_DeleteObject_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "UploadObject",
			Handler:       _ObjectManager_UploadObject_Handler,
			ClientStreams: true,
		},
		{
			StreamName:    "DownloadObject",
			Handler:       _ObjectManager_DownloadObject_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "proto/rpc_object_manager.proto",
}
