// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/rpc_model.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CheckModelReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx       *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	ModelId   string       `protobuf:"bytes,2,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ModelName string       `protobuf:"bytes,3,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
}

func (x *CheckModelReq) Reset() {
	*x = CheckModelReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckModelReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckModelReq) ProtoMessage() {}

func (x *CheckModelReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckModelReq.ProtoReflect.Descriptor instead.
func (*CheckModelReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{0}
}

func (x *CheckModelReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *CheckModelReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *CheckModelReq) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

type CheckModelReleaseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx         *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	ReleaseId   string       `protobuf:"bytes,2,opt,name=release_id,json=releaseId,proto3" json:"release_id,omitempty"`
	ReleaseName string       `protobuf:"bytes,3,opt,name=release_name,json=releaseName,proto3" json:"release_name,omitempty"`
}

func (x *CheckModelReleaseReq) Reset() {
	*x = CheckModelReleaseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckModelReleaseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckModelReleaseReq) ProtoMessage() {}

func (x *CheckModelReleaseReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckModelReleaseReq.ProtoReflect.Descriptor instead.
func (*CheckModelReleaseReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{1}
}

func (x *CheckModelReleaseReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *CheckModelReleaseReq) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

func (x *CheckModelReleaseReq) GetReleaseName() string {
	if x != nil {
		return x.ReleaseName
	}
	return ""
}

type ListModelsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx       *UserContext    `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	ModelId   string          `protobuf:"bytes,2,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ModelName string          `protobuf:"bytes,3,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
	Domain    *DomainSelector `protobuf:"bytes,6,opt,name=domain,proto3" json:"domain,omitempty"`
	Labels    *LabelSelector  `protobuf:"bytes,7,opt,name=labels,proto3" json:"labels,omitempty"`
	PageReq   *PageReq        `protobuf:"bytes,8,opt,name=page_req,json=pageReq,proto3" json:"page_req,omitempty"`
	SortedBy  string          `protobuf:"bytes,9,opt,name=sorted_by,json=sortedBy,proto3" json:"sorted_by,omitempty"`
}

func (x *ListModelsReq) Reset() {
	*x = ListModelsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListModelsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListModelsReq) ProtoMessage() {}

func (x *ListModelsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListModelsReq.ProtoReflect.Descriptor instead.
func (*ListModelsReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{2}
}

func (x *ListModelsReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *ListModelsReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ListModelsReq) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *ListModelsReq) GetDomain() *DomainSelector {
	if x != nil {
		return x.Domain
	}
	return nil
}

func (x *ListModelsReq) GetLabels() *LabelSelector {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ListModelsReq) GetPageReq() *PageReq {
	if x != nil {
		return x.PageReq
	}
	return nil
}

func (x *ListModelsReq) GetSortedBy() string {
	if x != nil {
		return x.SortedBy
	}
	return ""
}

type ListModelsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total    int32    `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	PageNum  int32    `protobuf:"varint,2,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	PageSize int32    `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Models   []*Model `protobuf:"bytes,4,rep,name=models,proto3" json:"models,omitempty"`
}

func (x *ListModelsRsp) Reset() {
	*x = ListModelsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListModelsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListModelsRsp) ProtoMessage() {}

func (x *ListModelsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListModelsRsp.ProtoReflect.Descriptor instead.
func (*ListModelsRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{3}
}

func (x *ListModelsRsp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListModelsRsp) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *ListModelsRsp) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListModelsRsp) GetModels() []*Model {
	if x != nil {
		return x.Models
	}
	return nil
}

type CreateModelReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx   *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Model *Model       `protobuf:"bytes,2,opt,name=model,proto3" json:"model,omitempty"`
}

func (x *CreateModelReq) Reset() {
	*x = CreateModelReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateModelReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateModelReq) ProtoMessage() {}

func (x *CreateModelReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateModelReq.ProtoReflect.Descriptor instead.
func (*CreateModelReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{4}
}

func (x *CreateModelReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *CreateModelReq) GetModel() *Model {
	if x != nil {
		return x.Model
	}
	return nil
}

type CreateModelRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Model *Model `protobuf:"bytes,1,opt,name=model,proto3" json:"model,omitempty"`
}

func (x *CreateModelRsp) Reset() {
	*x = CreateModelRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateModelRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateModelRsp) ProtoMessage() {}

func (x *CreateModelRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateModelRsp.ProtoReflect.Descriptor instead.
func (*CreateModelRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{5}
}

func (x *CreateModelRsp) GetModel() *Model {
	if x != nil {
		return x.Model
	}
	return nil
}

type UpdateModelReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx   *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Model *Model       `protobuf:"bytes,2,opt,name=model,proto3" json:"model,omitempty"`
}

func (x *UpdateModelReq) Reset() {
	*x = UpdateModelReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateModelReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateModelReq) ProtoMessage() {}

func (x *UpdateModelReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateModelReq.ProtoReflect.Descriptor instead.
func (*UpdateModelReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateModelReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *UpdateModelReq) GetModel() *Model {
	if x != nil {
		return x.Model
	}
	return nil
}

type UpdateModelRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Model *Model `protobuf:"bytes,1,opt,name=model,proto3" json:"model,omitempty"`
}

func (x *UpdateModelRsp) Reset() {
	*x = UpdateModelRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateModelRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateModelRsp) ProtoMessage() {}

func (x *UpdateModelRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateModelRsp.ProtoReflect.Descriptor instead.
func (*UpdateModelRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateModelRsp) GetModel() *Model {
	if x != nil {
		return x.Model
	}
	return nil
}

type DeleteModelReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx     *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	ModelId string       `protobuf:"bytes,2,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
}

func (x *DeleteModelReq) Reset() {
	*x = DeleteModelReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteModelReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteModelReq) ProtoMessage() {}

func (x *DeleteModelReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteModelReq.ProtoReflect.Descriptor instead.
func (*DeleteModelReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteModelReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *DeleteModelReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

type DeleteModelRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteModelRsp) Reset() {
	*x = DeleteModelRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteModelRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteModelRsp) ProtoMessage() {}

func (x *DeleteModelRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteModelRsp.ProtoReflect.Descriptor instead.
func (*DeleteModelRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{9}
}

type ListModelReleasesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx         *UserContext    `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	ModelId     string          `protobuf:"bytes,2,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ReleaseId   string          `protobuf:"bytes,3,opt,name=release_id,json=releaseId,proto3" json:"release_id,omitempty"`
	ReleaseName string          `protobuf:"bytes,4,opt,name=release_name,json=releaseName,proto3" json:"release_name,omitempty"`
	Domain      *DomainSelector `protobuf:"bytes,6,opt,name=domain,proto3" json:"domain,omitempty"`
	Labels      *LabelSelector  `protobuf:"bytes,7,opt,name=labels,proto3" json:"labels,omitempty"`
	Hardware    *HardwareRange  `protobuf:"bytes,8,opt,name=hardware,proto3" json:"hardware,omitempty"`
	PageReq     *PageReq        `protobuf:"bytes,13,opt,name=page_req,json=pageReq,proto3" json:"page_req,omitempty"`
}

func (x *ListModelReleasesReq) Reset() {
	*x = ListModelReleasesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListModelReleasesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListModelReleasesReq) ProtoMessage() {}

func (x *ListModelReleasesReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListModelReleasesReq.ProtoReflect.Descriptor instead.
func (*ListModelReleasesReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{10}
}

func (x *ListModelReleasesReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *ListModelReleasesReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ListModelReleasesReq) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

func (x *ListModelReleasesReq) GetReleaseName() string {
	if x != nil {
		return x.ReleaseName
	}
	return ""
}

func (x *ListModelReleasesReq) GetDomain() *DomainSelector {
	if x != nil {
		return x.Domain
	}
	return nil
}

func (x *ListModelReleasesReq) GetLabels() *LabelSelector {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ListModelReleasesReq) GetHardware() *HardwareRange {
	if x != nil {
		return x.Hardware
	}
	return nil
}

func (x *ListModelReleasesReq) GetPageReq() *PageReq {
	if x != nil {
		return x.PageReq
	}
	return nil
}

type ListModelReleasesRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total    int32           `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	PageNum  int32           `protobuf:"varint,2,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	PageSize int32           `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Releases []*ModelRelease `protobuf:"bytes,4,rep,name=releases,proto3" json:"releases,omitempty"`
}

func (x *ListModelReleasesRsp) Reset() {
	*x = ListModelReleasesRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListModelReleasesRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListModelReleasesRsp) ProtoMessage() {}

func (x *ListModelReleasesRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListModelReleasesRsp.ProtoReflect.Descriptor instead.
func (*ListModelReleasesRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{11}
}

func (x *ListModelReleasesRsp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListModelReleasesRsp) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *ListModelReleasesRsp) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListModelReleasesRsp) GetReleases() []*ModelRelease {
	if x != nil {
		return x.Releases
	}
	return nil
}

type CreateModelReleaseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx     *UserContext  `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Release *ModelRelease `protobuf:"bytes,2,opt,name=release,proto3" json:"release,omitempty"`
}

func (x *CreateModelReleaseReq) Reset() {
	*x = CreateModelReleaseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateModelReleaseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateModelReleaseReq) ProtoMessage() {}

func (x *CreateModelReleaseReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateModelReleaseReq.ProtoReflect.Descriptor instead.
func (*CreateModelReleaseReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{12}
}

func (x *CreateModelReleaseReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *CreateModelReleaseReq) GetRelease() *ModelRelease {
	if x != nil {
		return x.Release
	}
	return nil
}

type CreateModelReleaseRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Release *ModelRelease `protobuf:"bytes,1,opt,name=release,proto3" json:"release,omitempty"`
}

func (x *CreateModelReleaseRsp) Reset() {
	*x = CreateModelReleaseRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateModelReleaseRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateModelReleaseRsp) ProtoMessage() {}

func (x *CreateModelReleaseRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateModelReleaseRsp.ProtoReflect.Descriptor instead.
func (*CreateModelReleaseRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{13}
}

func (x *CreateModelReleaseRsp) GetRelease() *ModelRelease {
	if x != nil {
		return x.Release
	}
	return nil
}

type UpdateModelReleaseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx         *UserContext  `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Release     *ModelRelease `protobuf:"bytes,2,opt,name=release,proto3" json:"release,omitempty"`
	ForceUpdate bool          `protobuf:"varint,3,opt,name=force_update,json=forceUpdate,proto3" json:"force_update,omitempty"` // 更新系统内置模型的后门
}

func (x *UpdateModelReleaseReq) Reset() {
	*x = UpdateModelReleaseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateModelReleaseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateModelReleaseReq) ProtoMessage() {}

func (x *UpdateModelReleaseReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateModelReleaseReq.ProtoReflect.Descriptor instead.
func (*UpdateModelReleaseReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateModelReleaseReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *UpdateModelReleaseReq) GetRelease() *ModelRelease {
	if x != nil {
		return x.Release
	}
	return nil
}

func (x *UpdateModelReleaseReq) GetForceUpdate() bool {
	if x != nil {
		return x.ForceUpdate
	}
	return false
}

type UpdateModelReleaseRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Release *ModelRelease `protobuf:"bytes,1,opt,name=release,proto3" json:"release,omitempty"`
}

func (x *UpdateModelReleaseRsp) Reset() {
	*x = UpdateModelReleaseRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateModelReleaseRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateModelReleaseRsp) ProtoMessage() {}

func (x *UpdateModelReleaseRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateModelReleaseRsp.ProtoReflect.Descriptor instead.
func (*UpdateModelReleaseRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateModelReleaseRsp) GetRelease() *ModelRelease {
	if x != nil {
		return x.Release
	}
	return nil
}

type DeleteModelReleaseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx       *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	ModelId   string       `protobuf:"bytes,2,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ReleaseId string       `protobuf:"bytes,3,opt,name=release_id,json=releaseId,proto3" json:"release_id,omitempty"`
}

func (x *DeleteModelReleaseReq) Reset() {
	*x = DeleteModelReleaseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteModelReleaseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteModelReleaseReq) ProtoMessage() {}

func (x *DeleteModelReleaseReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteModelReleaseReq.ProtoReflect.Descriptor instead.
func (*DeleteModelReleaseReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{16}
}

func (x *DeleteModelReleaseReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *DeleteModelReleaseReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *DeleteModelReleaseReq) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

type DeleteModelReleaseRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteModelReleaseRsp) Reset() {
	*x = DeleteModelReleaseRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteModelReleaseRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteModelReleaseRsp) ProtoMessage() {}

func (x *DeleteModelReleaseRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteModelReleaseRsp.ProtoReflect.Descriptor instead.
func (*DeleteModelReleaseRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{17}
}

type GetModelReleaseRelationGraphReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx       *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	ModelId   string       `protobuf:"bytes,2,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ReleaseId string       `protobuf:"bytes,3,opt,name=release_id,json=releaseId,proto3" json:"release_id,omitempty"`
}

func (x *GetModelReleaseRelationGraphReq) Reset() {
	*x = GetModelReleaseRelationGraphReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModelReleaseRelationGraphReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModelReleaseRelationGraphReq) ProtoMessage() {}

func (x *GetModelReleaseRelationGraphReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModelReleaseRelationGraphReq.ProtoReflect.Descriptor instead.
func (*GetModelReleaseRelationGraphReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{18}
}

func (x *GetModelReleaseRelationGraphReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *GetModelReleaseRelationGraphReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *GetModelReleaseRelationGraphReq) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

type GetModelReleaseRelationGraphRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RelationGraph *RelationGraph `protobuf:"bytes,1,opt,name=relation_graph,json=relationGraph,proto3" json:"relation_graph,omitempty"`
}

func (x *GetModelReleaseRelationGraphRsp) Reset() {
	*x = GetModelReleaseRelationGraphRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModelReleaseRelationGraphRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModelReleaseRelationGraphRsp) ProtoMessage() {}

func (x *GetModelReleaseRelationGraphRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModelReleaseRelationGraphRsp.ProtoReflect.Descriptor instead.
func (*GetModelReleaseRelationGraphRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{19}
}

func (x *GetModelReleaseRelationGraphRsp) GetRelationGraph() *RelationGraph {
	if x != nil {
		return x.RelationGraph
	}
	return nil
}

type CheckModelsPackageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx         *UserContext   `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Manifest    *ModelManifest `protobuf:"bytes,2,opt,name=manifest,proto3" json:"manifest,omitempty"`
	PackagePath string         `protobuf:"bytes,3,opt,name=package_path,json=packagePath,proto3" json:"package_path,omitempty"`
}

func (x *CheckModelsPackageReq) Reset() {
	*x = CheckModelsPackageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckModelsPackageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckModelsPackageReq) ProtoMessage() {}

func (x *CheckModelsPackageReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckModelsPackageReq.ProtoReflect.Descriptor instead.
func (*CheckModelsPackageReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{20}
}

func (x *CheckModelsPackageReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *CheckModelsPackageReq) GetManifest() *ModelManifest {
	if x != nil {
		return x.Manifest
	}
	return nil
}

func (x *CheckModelsPackageReq) GetPackagePath() string {
	if x != nil {
		return x.PackagePath
	}
	return ""
}

type CheckModelsPackageRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Models []*ModelCheckResult `protobuf:"bytes,1,rep,name=models,proto3" json:"models,omitempty"`
}

func (x *CheckModelsPackageRsp) Reset() {
	*x = CheckModelsPackageRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckModelsPackageRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckModelsPackageRsp) ProtoMessage() {}

func (x *CheckModelsPackageRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckModelsPackageRsp.ProtoReflect.Descriptor instead.
func (*CheckModelsPackageRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{21}
}

func (x *CheckModelsPackageRsp) GetModels() []*ModelCheckResult {
	if x != nil {
		return x.Models
	}
	return nil
}

type ModelCheckResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId           string                     `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ModelName         string                     `protobuf:"bytes,2,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
	ModelType         ModelType                  `protobuf:"varint,3,opt,name=model_type,json=modelType,proto3,enum=proto.ModelType" json:"model_type,omitempty"`
	Releases          []*ModelReleaseCheckResult `protobuf:"bytes,4,rep,name=releases,proto3" json:"releases,omitempty"`
	Existed           bool                       `protobuf:"varint,5,opt,name=existed,proto3" json:"existed,omitempty"`
	ConflictWithModel string                     `protobuf:"bytes,6,opt,name=conflict_with_model,json=conflictWithModel,proto3" json:"conflict_with_model,omitempty"`
	Overwrite         bool                       `protobuf:"varint,7,opt,name=overwrite,proto3" json:"overwrite,omitempty"`
}

func (x *ModelCheckResult) Reset() {
	*x = ModelCheckResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelCheckResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelCheckResult) ProtoMessage() {}

func (x *ModelCheckResult) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelCheckResult.ProtoReflect.Descriptor instead.
func (*ModelCheckResult) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{22}
}

func (x *ModelCheckResult) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ModelCheckResult) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *ModelCheckResult) GetModelType() ModelType {
	if x != nil {
		return x.ModelType
	}
	return ModelType_MODEL_TYPE_UNSPECIFIED
}

func (x *ModelCheckResult) GetReleases() []*ModelReleaseCheckResult {
	if x != nil {
		return x.Releases
	}
	return nil
}

func (x *ModelCheckResult) GetExisted() bool {
	if x != nil {
		return x.Existed
	}
	return false
}

func (x *ModelCheckResult) GetConflictWithModel() string {
	if x != nil {
		return x.ConflictWithModel
	}
	return ""
}

func (x *ModelCheckResult) GetOverwrite() bool {
	if x != nil {
		return x.Overwrite
	}
	return false
}

type ModelReleaseCheckResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReleaseId           string `protobuf:"bytes,1,opt,name=release_id,json=releaseId,proto3" json:"release_id,omitempty"`
	ReleaseName         string `protobuf:"bytes,2,opt,name=release_name,json=releaseName,proto3" json:"release_name,omitempty"`
	ModelId             string `protobuf:"bytes,3,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ReleaseVersion      string `protobuf:"bytes,4,opt,name=release_version,json=releaseVersion,proto3" json:"release_version,omitempty"`
	Existed             bool   `protobuf:"varint,5,opt,name=existed,proto3" json:"existed,omitempty"`
	ConflictWithRelease string `protobuf:"bytes,6,opt,name=conflict_with_release,json=conflictWithRelease,proto3" json:"conflict_with_release,omitempty"`
	Overwrite           bool   `protobuf:"varint,7,opt,name=overwrite,proto3" json:"overwrite,omitempty"`
}

func (x *ModelReleaseCheckResult) Reset() {
	*x = ModelReleaseCheckResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelReleaseCheckResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelReleaseCheckResult) ProtoMessage() {}

func (x *ModelReleaseCheckResult) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelReleaseCheckResult.ProtoReflect.Descriptor instead.
func (*ModelReleaseCheckResult) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{23}
}

func (x *ModelReleaseCheckResult) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

func (x *ModelReleaseCheckResult) GetReleaseName() string {
	if x != nil {
		return x.ReleaseName
	}
	return ""
}

func (x *ModelReleaseCheckResult) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ModelReleaseCheckResult) GetReleaseVersion() string {
	if x != nil {
		return x.ReleaseVersion
	}
	return ""
}

func (x *ModelReleaseCheckResult) GetExisted() bool {
	if x != nil {
		return x.Existed
	}
	return false
}

func (x *ModelReleaseCheckResult) GetConflictWithRelease() string {
	if x != nil {
		return x.ConflictWithRelease
	}
	return ""
}

func (x *ModelReleaseCheckResult) GetOverwrite() bool {
	if x != nil {
		return x.Overwrite
	}
	return false
}

// TODO gRPC 将 BatchUploadObjectXXX 替换为 ImportModelsPackageXXX
type ImportModelsPackageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx         *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	PackagePath string       `protobuf:"bytes,2,opt,name=package_path,json=packagePath,proto3" json:"package_path,omitempty"`
	// Deprecated 兼容性保留，之后批量导入每个模型都会选择是否覆盖
	IsOverwrite  bool                `protobuf:"varint,3,opt,name=is_overwrite,json=isOverwrite,proto3" json:"is_overwrite,omitempty"`
	CheckResults []*ModelCheckResult `protobuf:"bytes,5,rep,name=check_results,json=checkResults,proto3" json:"check_results,omitempty"`
	IsSystemInit bool                `protobuf:"varint,4,opt,name=is_system_init,json=isSystemInit,proto3" json:"is_system_init,omitempty"`
}

func (x *ImportModelsPackageReq) Reset() {
	*x = ImportModelsPackageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportModelsPackageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportModelsPackageReq) ProtoMessage() {}

func (x *ImportModelsPackageReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportModelsPackageReq.ProtoReflect.Descriptor instead.
func (*ImportModelsPackageReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{24}
}

func (x *ImportModelsPackageReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *ImportModelsPackageReq) GetPackagePath() string {
	if x != nil {
		return x.PackagePath
	}
	return ""
}

func (x *ImportModelsPackageReq) GetIsOverwrite() bool {
	if x != nil {
		return x.IsOverwrite
	}
	return false
}

func (x *ImportModelsPackageReq) GetCheckResults() []*ModelCheckResult {
	if x != nil {
		return x.CheckResults
	}
	return nil
}

func (x *ImportModelsPackageReq) GetIsSystemInit() bool {
	if x != nil {
		return x.IsSystemInit
	}
	return false
}

type ImportModelsPackageRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Models        map[string]*ModelImportResult `protobuf:"bytes,1,rep,name=models,proto3" json:"models,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	ImportSuccess bool                          `protobuf:"varint,2,opt,name=import_success,json=importSuccess,proto3" json:"import_success,omitempty"`
	Reason        string                        `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	RawError      string                        `protobuf:"bytes,4,opt,name=raw_error,json=rawError,proto3" json:"raw_error,omitempty"`
}

func (x *ImportModelsPackageRsp) Reset() {
	*x = ImportModelsPackageRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportModelsPackageRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportModelsPackageRsp) ProtoMessage() {}

func (x *ImportModelsPackageRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportModelsPackageRsp.ProtoReflect.Descriptor instead.
func (*ImportModelsPackageRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{25}
}

func (x *ImportModelsPackageRsp) GetModels() map[string]*ModelImportResult {
	if x != nil {
		return x.Models
	}
	return nil
}

func (x *ImportModelsPackageRsp) GetImportSuccess() bool {
	if x != nil {
		return x.ImportSuccess
	}
	return false
}

func (x *ImportModelsPackageRsp) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *ImportModelsPackageRsp) GetRawError() string {
	if x != nil {
		return x.RawError
	}
	return ""
}

type ModelImportResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId   string                               `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ModelName string                               `protobuf:"bytes,2,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
	Releases  map[string]*ModelReleaseImportResult `protobuf:"bytes,3,rep,name=releases,proto3" json:"releases,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ModelImportResult) Reset() {
	*x = ModelImportResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelImportResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelImportResult) ProtoMessage() {}

func (x *ModelImportResult) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelImportResult.ProtoReflect.Descriptor instead.
func (*ModelImportResult) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{26}
}

func (x *ModelImportResult) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ModelImportResult) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *ModelImportResult) GetReleases() map[string]*ModelReleaseImportResult {
	if x != nil {
		return x.Releases
	}
	return nil
}

type ModelReleaseImportResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReleaseId   string `protobuf:"bytes,1,opt,name=release_id,json=releaseId,proto3" json:"release_id,omitempty"`
	ReleaseName string `protobuf:"bytes,2,opt,name=release_name,json=releaseName,proto3" json:"release_name,omitempty"`
	ModelId     string `protobuf:"bytes,3,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ModelName   string `protobuf:"bytes,4,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
}

func (x *ModelReleaseImportResult) Reset() {
	*x = ModelReleaseImportResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelReleaseImportResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelReleaseImportResult) ProtoMessage() {}

func (x *ModelReleaseImportResult) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelReleaseImportResult.ProtoReflect.Descriptor instead.
func (*ModelReleaseImportResult) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{27}
}

func (x *ModelReleaseImportResult) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

func (x *ModelReleaseImportResult) GetReleaseName() string {
	if x != nil {
		return x.ReleaseName
	}
	return ""
}

func (x *ModelReleaseImportResult) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ModelReleaseImportResult) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

type ExportModelsPackageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx    *UserContext              `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Models map[string]*ModelManifest `protobuf:"bytes,2,rep,name=models,proto3" json:"models,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // Model.id -> ModelManifest
}

func (x *ExportModelsPackageReq) Reset() {
	*x = ExportModelsPackageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportModelsPackageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportModelsPackageReq) ProtoMessage() {}

func (x *ExportModelsPackageReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportModelsPackageReq.ProtoReflect.Descriptor instead.
func (*ExportModelsPackageReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{28}
}

func (x *ExportModelsPackageReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *ExportModelsPackageReq) GetModels() map[string]*ModelManifest {
	if x != nil {
		return x.Models
	}
	return nil
}

type ExportModelsPackageRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PackagePath string `protobuf:"bytes,1,opt,name=package_path,json=packagePath,proto3" json:"package_path,omitempty"`
}

func (x *ExportModelsPackageRsp) Reset() {
	*x = ExportModelsPackageRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportModelsPackageRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportModelsPackageRsp) ProtoMessage() {}

func (x *ExportModelsPackageRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportModelsPackageRsp.ProtoReflect.Descriptor instead.
func (*ExportModelsPackageRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{29}
}

func (x *ExportModelsPackageRsp) GetPackagePath() string {
	if x != nil {
		return x.PackagePath
	}
	return ""
}

type CheckResourceExistenceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Exist bool   `protobuf:"varint,1,opt,name=exist,proto3" json:"exist,omitempty"`
	Msg   string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *CheckResourceExistenceRsp) Reset() {
	*x = CheckResourceExistenceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckResourceExistenceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckResourceExistenceRsp) ProtoMessage() {}

func (x *CheckResourceExistenceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckResourceExistenceRsp.ProtoReflect.Descriptor instead.
func (*CheckResourceExistenceRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{30}
}

func (x *CheckResourceExistenceRsp) GetExist() bool {
	if x != nil {
		return x.Exist
	}
	return false
}

func (x *CheckResourceExistenceRsp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type MigrateModelsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx      *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Releases []string     `protobuf:"bytes,2,rep,name=releases,proto3" json:"releases,omitempty"`
	Users    []string     `protobuf:"bytes,3,rep,name=users,proto3" json:"users,omitempty"`
}

func (x *MigrateModelsReq) Reset() {
	*x = MigrateModelsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MigrateModelsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MigrateModelsReq) ProtoMessage() {}

func (x *MigrateModelsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MigrateModelsReq.ProtoReflect.Descriptor instead.
func (*MigrateModelsReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{31}
}

func (x *MigrateModelsReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *MigrateModelsReq) GetReleases() []string {
	if x != nil {
		return x.Releases
	}
	return nil
}

func (x *MigrateModelsReq) GetUsers() []string {
	if x != nil {
		return x.Users
	}
	return nil
}

type MigrateModelsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Models   []*ModelMigrateResult        `protobuf:"bytes,1,rep,name=models,proto3" json:"models,omitempty"`
	Releases []*ModelReleaseMigrateResult `protobuf:"bytes,2,rep,name=releases,proto3" json:"releases,omitempty"`
}

func (x *MigrateModelsRsp) Reset() {
	*x = MigrateModelsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MigrateModelsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MigrateModelsRsp) ProtoMessage() {}

func (x *MigrateModelsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MigrateModelsRsp.ProtoReflect.Descriptor instead.
func (*MigrateModelsRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{32}
}

func (x *MigrateModelsRsp) GetModels() []*ModelMigrateResult {
	if x != nil {
		return x.Models
	}
	return nil
}

func (x *MigrateModelsRsp) GetReleases() []*ModelReleaseMigrateResult {
	if x != nil {
		return x.Releases
	}
	return nil
}

type ModelMigrateResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId   string `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ModelName string `protobuf:"bytes,2,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
}

func (x *ModelMigrateResult) Reset() {
	*x = ModelMigrateResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelMigrateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelMigrateResult) ProtoMessage() {}

func (x *ModelMigrateResult) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelMigrateResult.ProtoReflect.Descriptor instead.
func (*ModelMigrateResult) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{33}
}

func (x *ModelMigrateResult) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ModelMigrateResult) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

type ModelReleaseMigrateResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReleaseId   string `protobuf:"bytes,1,opt,name=release_id,json=releaseId,proto3" json:"release_id,omitempty"`
	ReleaseName string `protobuf:"bytes,2,opt,name=release_name,json=releaseName,proto3" json:"release_name,omitempty"`
	ModelId     string `protobuf:"bytes,3,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
}

func (x *ModelReleaseMigrateResult) Reset() {
	*x = ModelReleaseMigrateResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelReleaseMigrateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelReleaseMigrateResult) ProtoMessage() {}

func (x *ModelReleaseMigrateResult) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelReleaseMigrateResult.ProtoReflect.Descriptor instead.
func (*ModelReleaseMigrateResult) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{34}
}

func (x *ModelReleaseMigrateResult) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

func (x *ModelReleaseMigrateResult) GetReleaseName() string {
	if x != nil {
		return x.ReleaseName
	}
	return ""
}

func (x *ModelReleaseMigrateResult) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

type ModelManifests struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Models map[string]*ModelManifest `protobuf:"bytes,1,rep,name=models,proto3" json:"models,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // Model.name -> ModelManifest
}

func (x *ModelManifests) Reset() {
	*x = ModelManifests{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelManifests) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelManifests) ProtoMessage() {}

func (x *ModelManifests) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelManifests.ProtoReflect.Descriptor instead.
func (*ModelManifests) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{35}
}

func (x *ModelManifests) GetModels() map[string]*ModelManifest {
	if x != nil {
		return x.Models
	}
	return nil
}

type ModelManifest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Models      map[string]*Model        `protobuf:"bytes,1,rep,name=models,proto3" json:"models,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Releases    map[string]*ModelRelease `protobuf:"bytes,2,rep,name=releases,proto3" json:"releases,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`       // Release.name -> ModelRelease
	Attachments map[string]*Attachment   `protobuf:"bytes,3,rep,name=attachments,proto3" json:"attachments,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // Attachment.id -> Attachment
	Version     string                   `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *ModelManifest) Reset() {
	*x = ModelManifest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelManifest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelManifest) ProtoMessage() {}

func (x *ModelManifest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelManifest.ProtoReflect.Descriptor instead.
func (*ModelManifest) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{36}
}

func (x *ModelManifest) GetModels() map[string]*Model {
	if x != nil {
		return x.Models
	}
	return nil
}

func (x *ModelManifest) GetReleases() map[string]*ModelRelease {
	if x != nil {
		return x.Releases
	}
	return nil
}

func (x *ModelManifest) GetAttachments() map[string]*Attachment {
	if x != nil {
		return x.Attachments
	}
	return nil
}

func (x *ModelManifest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type ParseFileModelReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelSubType ModelSubType `protobuf:"varint,1,opt,name=model_sub_type,json=modelSubType,proto3,enum=proto.ModelSubType" json:"model_sub_type,omitempty"`
	ModelSubKind ModelSubKind `protobuf:"varint,2,opt,name=model_sub_kind,json=modelSubKind,proto3,enum=proto.ModelSubKind" json:"model_sub_kind,omitempty"`
	FilePath     string       `protobuf:"bytes,3,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
}

func (x *ParseFileModelReq) Reset() {
	*x = ParseFileModelReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ParseFileModelReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseFileModelReq) ProtoMessage() {}

func (x *ParseFileModelReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseFileModelReq.ProtoReflect.Descriptor instead.
func (*ParseFileModelReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{37}
}

func (x *ParseFileModelReq) GetModelSubType() ModelSubType {
	if x != nil {
		return x.ModelSubType
	}
	return ModelSubType_MODEL_SUB_TYPE_UNSPECIFIED
}

func (x *ParseFileModelReq) GetModelSubKind() ModelSubKind {
	if x != nil {
		return x.ModelSubKind
	}
	return ModelSubKind_MODEL_SUB_KIND_UNSPECIFIED
}

func (x *ParseFileModelReq) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

type ParseFileModelRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelApis []*ModelApi `protobuf:"bytes,1,rep,name=model_apis,json=modelApis,proto3" json:"model_apis,omitempty"`
}

func (x *ParseFileModelRsp) Reset() {
	*x = ParseFileModelRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ParseFileModelRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseFileModelRsp) ProtoMessage() {}

func (x *ParseFileModelRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseFileModelRsp.ProtoReflect.Descriptor instead.
func (*ParseFileModelRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{38}
}

func (x *ParseFileModelRsp) GetModelApis() []*ModelApi {
	if x != nil {
		return x.ModelApis
	}
	return nil
}

type CreateModelFileDownloadJobsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx       *UserContext     `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	ModelId   string           `protobuf:"bytes,2,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ReleaseId string           `protobuf:"bytes,3,opt,name=release_id,json=releaseId,proto3" json:"release_id,omitempty"`
	Protocol  RepoAddrProtocol `protobuf:"varint,4,opt,name=protocol,proto3,enum=proto.RepoAddrProtocol" json:"protocol,omitempty"`
	Repo      string           `protobuf:"bytes,5,opt,name=repo,proto3" json:"repo,omitempty"`
}

func (x *CreateModelFileDownloadJobsReq) Reset() {
	*x = CreateModelFileDownloadJobsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateModelFileDownloadJobsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateModelFileDownloadJobsReq) ProtoMessage() {}

func (x *CreateModelFileDownloadJobsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateModelFileDownloadJobsReq.ProtoReflect.Descriptor instead.
func (*CreateModelFileDownloadJobsReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{39}
}

func (x *CreateModelFileDownloadJobsReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *CreateModelFileDownloadJobsReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *CreateModelFileDownloadJobsReq) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

func (x *CreateModelFileDownloadJobsReq) GetProtocol() RepoAddrProtocol {
	if x != nil {
		return x.Protocol
	}
	return RepoAddrProtocol_REPO_ADDR_PROTOCOL_UNSPECIFIED
}

func (x *CreateModelFileDownloadJobsReq) GetRepo() string {
	if x != nil {
		return x.Repo
	}
	return ""
}

type CreateModelFileDownloadJobsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CreateModelFileDownloadJobsRsp) Reset() {
	*x = CreateModelFileDownloadJobsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateModelFileDownloadJobsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateModelFileDownloadJobsRsp) ProtoMessage() {}

func (x *CreateModelFileDownloadJobsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateModelFileDownloadJobsRsp.ProtoReflect.Descriptor instead.
func (*CreateModelFileDownloadJobsRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_proto_rawDescGZIP(), []int{40}
}

func (x *CreateModelFileDownloadJobsRsp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

var File_proto_rpc_model_proto protoreflect.FileDescriptor

var file_proto_rpc_model_proto_rawDesc = []byte{
	0x0a, 0x15, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x70, 0x63, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x11, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x6f, 0x0a, 0x0d, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03,
	0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63,
	0x74, 0x78, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x7e, 0x0a, 0x14,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x94, 0x02, 0x0a,
	0x0d, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x12, 0x24,
	0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52,
	0x03, 0x63, 0x74, 0x78, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2d,
	0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x2c, 0x0a,
	0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x53, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x29, 0x0a, 0x08, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x52, 0x07, 0x70,
	0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f, 0x72, 0x74, 0x65, 0x64,
	0x5f, 0x62, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6f, 0x72, 0x74, 0x65,
	0x64, 0x42, 0x79, 0x22, 0x83, 0x01, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x52, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70,
	0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x12, 0x24, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x22, 0x5a, 0x0a, 0x0e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63,
	0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74,
	0x78, 0x12, 0x22, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0x34, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x22, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0x5a, 0x0a, 0x0e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a,
	0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03,
	0x63, 0x74, 0x78, 0x12, 0x22, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0x34, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x22, 0x0a, 0x05, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0x51, 0x0a,
	0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x12,
	0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64,
	0x22, 0x10, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x73, 0x70, 0x22, 0xd3, 0x02, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63,
	0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74,
	0x78, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2d,
	0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x2c, 0x0a,
	0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x53, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x30, 0x0a, 0x08, 0x68,
	0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x08, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x12, 0x29, 0x0a,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x52,
	0x07, 0x70, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x22, 0x95, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73,
	0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x73, 0x52, 0x73,
	0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e,
	0x75, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x2f, 0x0a, 0x08, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x08, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x73,
	0x22, 0x6c, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12,
	0x2d, 0x0a, 0x07, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x07, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x22, 0x46,
	0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x52, 0x73, 0x70, 0x12, 0x2d, 0x0a, 0x07, 0x72, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x07, 0x72,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x22, 0x8f, 0x01, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71,
	0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x2d, 0x0a, 0x07, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x07, 0x72, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x5f, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x66, 0x6f, 0x72,
	0x63, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x22, 0x46, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x73,
	0x70, 0x12, 0x2d, 0x0a, 0x07, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x07, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x22, 0x77, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12,
	0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x64, 0x22, 0x17, 0x0a, 0x15, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52,
	0x73, 0x70, 0x22, 0x81, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72,
	0x61, 0x70, 0x68, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x19, 0x0a, 0x08,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x49, 0x64, 0x22, 0x5e, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x52, 0x73, 0x70, 0x12, 0x3b, 0x0a, 0x0e, 0x72, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x52, 0x0d, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x22, 0x92, 0x01, 0x0a, 0x15, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x30, 0x0a, 0x08, 0x6d, 0x61, 0x6e, 0x69, 0x66, 0x65,
	0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x52, 0x08,
	0x6d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x50, 0x61, 0x74, 0x68, 0x22, 0x48, 0x0a, 0x15, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x52, 0x73, 0x70, 0x12, 0x2f, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x22, 0xa1, 0x02, 0x0a, 0x10, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3a, 0x0a, 0x08, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x08, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x73, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x78, 0x69, 0x73, 0x74, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x65, 0x78, 0x69, 0x73, 0x74, 0x65, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x63,
	0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x63, 0x6f, 0x6e, 0x66, 0x6c, 0x69,
	0x63, 0x74, 0x57, 0x69, 0x74, 0x68, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x6f,
	0x76, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09,
	0x6f, 0x76, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x22, 0x8b, 0x02, 0x0a, 0x17, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x65,
	0x78, 0x69, 0x73, 0x74, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x78,
	0x69, 0x73, 0x74, 0x65, 0x64, 0x12, 0x32, 0x0a, 0x15, 0x63, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63,
	0x74, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x63, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74, 0x57, 0x69,
	0x74, 0x68, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6f, 0x76, 0x65,
	0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x6f, 0x76,
	0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x22, 0xe8, 0x01, 0x0a, 0x16, 0x49, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x69,
	0x73, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0b, 0x69, 0x73, 0x4f, 0x76, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x12, 0x3c,
	0x0a, 0x0d, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0c,
	0x63, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x12, 0x24, 0x0a, 0x0e,
	0x69, 0x73, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x6e, 0x69, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x49, 0x6e,
	0x69, 0x74, 0x22, 0x8c, 0x02, 0x0a, 0x16, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x41, 0x0a,
	0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x73, 0x70, 0x2e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x12, 0x25, 0x0a, 0x0e, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12,
	0x1b, 0x0a, 0x09, 0x72, 0x61, 0x77, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x72, 0x61, 0x77, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x1a, 0x53, 0x0a, 0x0b,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2e, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0xef, 0x01, 0x0a, 0x11, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x42, 0x0a, 0x08, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x52, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x72, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x73, 0x1a, 0x5c, 0x0a, 0x0d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x35, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x96, 0x01, 0x0a, 0x18, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12,
	0x21, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xd2, 0x01, 0x0a,
	0x16, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x50, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x41, 0x0a,
	0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x2e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x1a, 0x4f, 0x0a, 0x0b, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x2a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x61,
	0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x3b, 0x0a, 0x16, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x70,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x50, 0x61, 0x74, 0x68, 0x22, 0x43,
	0x0a, 0x19, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45,
	0x78, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x65,
	0x78, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x65, 0x78, 0x69, 0x73,
	0x74, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x22, 0x6a, 0x0a, 0x10, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x1a, 0x0a,
	0x08, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x08, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x75, 0x73, 0x65,
	0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x22,
	0x83, 0x01, 0x0a, 0x10, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x52, 0x73, 0x70, 0x12, 0x31, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x12, 0x3c, 0x0a, 0x08, 0x72, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4d, 0x69,
	0x67, 0x72, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x08, 0x72, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x73, 0x22, 0x4e, 0x0a, 0x12, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x69,
	0x67, 0x72, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x78, 0x0a, 0x19, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49,
	0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x22,
	0x9c, 0x01, 0x0a, 0x0e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73,
	0x74, 0x73, 0x12, 0x39, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x4d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x73, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x1a, 0x4f, 0x0a,
	0x0b, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2a,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x61, 0x6e, 0x69, 0x66,
	0x65, 0x73, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xda,
	0x03, 0x0a, 0x0d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74,
	0x12, 0x38, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x61,
	0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x12, 0x3e, 0x0a, 0x08, 0x72, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x61, 0x6e, 0x69, 0x66, 0x65,
	0x73, 0x74, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x08, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x73, 0x12, 0x47, 0x0a, 0x0b, 0x61, 0x74,
	0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x61, 0x6e,
	0x69, 0x66, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0x47, 0x0a,
	0x0b, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x22,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x50, 0x0a, 0x0d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x29, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x51, 0x0a, 0x10, 0x41, 0x74, 0x74, 0x61,
	0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x27,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xa6, 0x01, 0x0a, 0x11,
	0x50, 0x61, 0x72, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65,
	0x71, 0x12, 0x39, 0x0a, 0x0e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x53, 0x75, 0x62, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x53, 0x75, 0x62, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x50, 0x61, 0x74, 0x68, 0x22, 0x43, 0x0a, 0x11, 0x50, 0x61, 0x72, 0x73, 0x65, 0x46, 0x69, 0x6c,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x2e, 0x0a, 0x0a, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x5f, 0x61, 0x70, 0x69, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x70, 0x69, 0x52, 0x09,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x70, 0x69, 0x73, 0x22, 0xc9, 0x01, 0x0a, 0x1e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03,
	0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63,
	0x74, 0x78, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x08,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x41, 0x64, 0x64, 0x72, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x65, 0x70, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x72, 0x65, 0x70, 0x6f, 0x22, 0x30, 0x0a, 0x1e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64,
	0x4a, 0x6f, 0x62, 0x73, 0x52, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x32, 0xb2, 0x09, 0x0a, 0x0c, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x0a, 0x4c, 0x69, 0x73, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x12, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x52,
	0x73, 0x70, 0x12, 0x3b, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x12, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x73, 0x70, 0x12,
	0x3b, 0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x15,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x3b, 0x0a, 0x0b,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x15, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x65, 0x71, 0x1a, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x4d, 0x0a, 0x13, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x78, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x63, 0x65,
	0x12, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x78, 0x69, 0x73,
	0x74, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0x50, 0x0a, 0x12, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x1c,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x53, 0x0a, 0x13, 0x49, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x12, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x1a, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12,
	0x53, 0x0a, 0x13, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x52, 0x73, 0x70, 0x12, 0x4d, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x73, 0x12, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x73,
	0x52, 0x73, 0x70, 0x12, 0x50, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x12, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x52, 0x73, 0x70, 0x12, 0x50, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x12, 0x1c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x52, 0x73, 0x70, 0x12, 0x50, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x12, 0x1c, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x73, 0x70, 0x12, 0x6e, 0x0a, 0x1c, 0x47, 0x65, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x12, 0x26, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x52, 0x65,
	0x71, 0x1a, 0x26, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x52, 0x73, 0x70, 0x12, 0x44, 0x0a, 0x0e, 0x50, 0x61, 0x72,
	0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x18, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x61,
	0x72, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x73, 0x70, 0x12,
	0x6b, 0x0a, 0x1b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x46, 0x69,
	0x6c, 0x65, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4a, 0x6f, 0x62, 0x73, 0x12, 0x25,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4a, 0x6f,
	0x62, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x73, 0x70, 0x42, 0x26, 0x5a, 0x24,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70,
	0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70,
	0x62, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_rpc_model_proto_rawDescOnce sync.Once
	file_proto_rpc_model_proto_rawDescData = file_proto_rpc_model_proto_rawDesc
)

func file_proto_rpc_model_proto_rawDescGZIP() []byte {
	file_proto_rpc_model_proto_rawDescOnce.Do(func() {
		file_proto_rpc_model_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_rpc_model_proto_rawDescData)
	})
	return file_proto_rpc_model_proto_rawDescData
}

var file_proto_rpc_model_proto_msgTypes = make([]protoimpl.MessageInfo, 48)
var file_proto_rpc_model_proto_goTypes = []interface{}{
	(*CheckModelReq)(nil),                   // 0: proto.CheckModelReq
	(*CheckModelReleaseReq)(nil),            // 1: proto.CheckModelReleaseReq
	(*ListModelsReq)(nil),                   // 2: proto.ListModelsReq
	(*ListModelsRsp)(nil),                   // 3: proto.ListModelsRsp
	(*CreateModelReq)(nil),                  // 4: proto.CreateModelReq
	(*CreateModelRsp)(nil),                  // 5: proto.CreateModelRsp
	(*UpdateModelReq)(nil),                  // 6: proto.UpdateModelReq
	(*UpdateModelRsp)(nil),                  // 7: proto.UpdateModelRsp
	(*DeleteModelReq)(nil),                  // 8: proto.DeleteModelReq
	(*DeleteModelRsp)(nil),                  // 9: proto.DeleteModelRsp
	(*ListModelReleasesReq)(nil),            // 10: proto.ListModelReleasesReq
	(*ListModelReleasesRsp)(nil),            // 11: proto.ListModelReleasesRsp
	(*CreateModelReleaseReq)(nil),           // 12: proto.CreateModelReleaseReq
	(*CreateModelReleaseRsp)(nil),           // 13: proto.CreateModelReleaseRsp
	(*UpdateModelReleaseReq)(nil),           // 14: proto.UpdateModelReleaseReq
	(*UpdateModelReleaseRsp)(nil),           // 15: proto.UpdateModelReleaseRsp
	(*DeleteModelReleaseReq)(nil),           // 16: proto.DeleteModelReleaseReq
	(*DeleteModelReleaseRsp)(nil),           // 17: proto.DeleteModelReleaseRsp
	(*GetModelReleaseRelationGraphReq)(nil), // 18: proto.GetModelReleaseRelationGraphReq
	(*GetModelReleaseRelationGraphRsp)(nil), // 19: proto.GetModelReleaseRelationGraphRsp
	(*CheckModelsPackageReq)(nil),           // 20: proto.CheckModelsPackageReq
	(*CheckModelsPackageRsp)(nil),           // 21: proto.CheckModelsPackageRsp
	(*ModelCheckResult)(nil),                // 22: proto.ModelCheckResult
	(*ModelReleaseCheckResult)(nil),         // 23: proto.ModelReleaseCheckResult
	(*ImportModelsPackageReq)(nil),          // 24: proto.ImportModelsPackageReq
	(*ImportModelsPackageRsp)(nil),          // 25: proto.ImportModelsPackageRsp
	(*ModelImportResult)(nil),               // 26: proto.ModelImportResult
	(*ModelReleaseImportResult)(nil),        // 27: proto.ModelReleaseImportResult
	(*ExportModelsPackageReq)(nil),          // 28: proto.ExportModelsPackageReq
	(*ExportModelsPackageRsp)(nil),          // 29: proto.ExportModelsPackageRsp
	(*CheckResourceExistenceRsp)(nil),       // 30: proto.CheckResourceExistenceRsp
	(*MigrateModelsReq)(nil),                // 31: proto.MigrateModelsReq
	(*MigrateModelsRsp)(nil),                // 32: proto.MigrateModelsRsp
	(*ModelMigrateResult)(nil),              // 33: proto.ModelMigrateResult
	(*ModelReleaseMigrateResult)(nil),       // 34: proto.ModelReleaseMigrateResult
	(*ModelManifests)(nil),                  // 35: proto.ModelManifests
	(*ModelManifest)(nil),                   // 36: proto.ModelManifest
	(*ParseFileModelReq)(nil),               // 37: proto.ParseFileModelReq
	(*ParseFileModelRsp)(nil),               // 38: proto.ParseFileModelRsp
	(*CreateModelFileDownloadJobsReq)(nil),  // 39: proto.CreateModelFileDownloadJobsReq
	(*CreateModelFileDownloadJobsRsp)(nil),  // 40: proto.CreateModelFileDownloadJobsRsp
	nil,                                     // 41: proto.ImportModelsPackageRsp.ModelsEntry
	nil,                                     // 42: proto.ModelImportResult.ReleasesEntry
	nil,                                     // 43: proto.ExportModelsPackageReq.ModelsEntry
	nil,                                     // 44: proto.ModelManifests.ModelsEntry
	nil,                                     // 45: proto.ModelManifest.ModelsEntry
	nil,                                     // 46: proto.ModelManifest.ReleasesEntry
	nil,                                     // 47: proto.ModelManifest.AttachmentsEntry
	(*UserContext)(nil),                     // 48: proto.UserContext
	(*DomainSelector)(nil),                  // 49: proto.DomainSelector
	(*LabelSelector)(nil),                   // 50: proto.LabelSelector
	(*PageReq)(nil),                         // 51: proto.PageReq
	(*Model)(nil),                           // 52: proto.Model
	(*HardwareRange)(nil),                   // 53: proto.HardwareRange
	(*ModelRelease)(nil),                    // 54: proto.ModelRelease
	(*RelationGraph)(nil),                   // 55: proto.RelationGraph
	(ModelType)(0),                          // 56: proto.ModelType
	(ModelSubType)(0),                       // 57: proto.ModelSubType
	(ModelSubKind)(0),                       // 58: proto.ModelSubKind
	(*ModelApi)(nil),                        // 59: proto.ModelApi
	(RepoAddrProtocol)(0),                   // 60: proto.RepoAddrProtocol
	(*Attachment)(nil),                      // 61: proto.Attachment
}
var file_proto_rpc_model_proto_depIdxs = []int32{
	48, // 0: proto.CheckModelReq.ctx:type_name -> proto.UserContext
	48, // 1: proto.CheckModelReleaseReq.ctx:type_name -> proto.UserContext
	48, // 2: proto.ListModelsReq.ctx:type_name -> proto.UserContext
	49, // 3: proto.ListModelsReq.domain:type_name -> proto.DomainSelector
	50, // 4: proto.ListModelsReq.labels:type_name -> proto.LabelSelector
	51, // 5: proto.ListModelsReq.page_req:type_name -> proto.PageReq
	52, // 6: proto.ListModelsRsp.models:type_name -> proto.Model
	48, // 7: proto.CreateModelReq.ctx:type_name -> proto.UserContext
	52, // 8: proto.CreateModelReq.model:type_name -> proto.Model
	52, // 9: proto.CreateModelRsp.model:type_name -> proto.Model
	48, // 10: proto.UpdateModelReq.ctx:type_name -> proto.UserContext
	52, // 11: proto.UpdateModelReq.model:type_name -> proto.Model
	52, // 12: proto.UpdateModelRsp.model:type_name -> proto.Model
	48, // 13: proto.DeleteModelReq.ctx:type_name -> proto.UserContext
	48, // 14: proto.ListModelReleasesReq.ctx:type_name -> proto.UserContext
	49, // 15: proto.ListModelReleasesReq.domain:type_name -> proto.DomainSelector
	50, // 16: proto.ListModelReleasesReq.labels:type_name -> proto.LabelSelector
	53, // 17: proto.ListModelReleasesReq.hardware:type_name -> proto.HardwareRange
	51, // 18: proto.ListModelReleasesReq.page_req:type_name -> proto.PageReq
	54, // 19: proto.ListModelReleasesRsp.releases:type_name -> proto.ModelRelease
	48, // 20: proto.CreateModelReleaseReq.ctx:type_name -> proto.UserContext
	54, // 21: proto.CreateModelReleaseReq.release:type_name -> proto.ModelRelease
	54, // 22: proto.CreateModelReleaseRsp.release:type_name -> proto.ModelRelease
	48, // 23: proto.UpdateModelReleaseReq.ctx:type_name -> proto.UserContext
	54, // 24: proto.UpdateModelReleaseReq.release:type_name -> proto.ModelRelease
	54, // 25: proto.UpdateModelReleaseRsp.release:type_name -> proto.ModelRelease
	48, // 26: proto.DeleteModelReleaseReq.ctx:type_name -> proto.UserContext
	48, // 27: proto.GetModelReleaseRelationGraphReq.ctx:type_name -> proto.UserContext
	55, // 28: proto.GetModelReleaseRelationGraphRsp.relation_graph:type_name -> proto.RelationGraph
	48, // 29: proto.CheckModelsPackageReq.ctx:type_name -> proto.UserContext
	36, // 30: proto.CheckModelsPackageReq.manifest:type_name -> proto.ModelManifest
	22, // 31: proto.CheckModelsPackageRsp.models:type_name -> proto.ModelCheckResult
	56, // 32: proto.ModelCheckResult.model_type:type_name -> proto.ModelType
	23, // 33: proto.ModelCheckResult.releases:type_name -> proto.ModelReleaseCheckResult
	48, // 34: proto.ImportModelsPackageReq.ctx:type_name -> proto.UserContext
	22, // 35: proto.ImportModelsPackageReq.check_results:type_name -> proto.ModelCheckResult
	41, // 36: proto.ImportModelsPackageRsp.models:type_name -> proto.ImportModelsPackageRsp.ModelsEntry
	42, // 37: proto.ModelImportResult.releases:type_name -> proto.ModelImportResult.ReleasesEntry
	48, // 38: proto.ExportModelsPackageReq.ctx:type_name -> proto.UserContext
	43, // 39: proto.ExportModelsPackageReq.models:type_name -> proto.ExportModelsPackageReq.ModelsEntry
	48, // 40: proto.MigrateModelsReq.ctx:type_name -> proto.UserContext
	33, // 41: proto.MigrateModelsRsp.models:type_name -> proto.ModelMigrateResult
	34, // 42: proto.MigrateModelsRsp.releases:type_name -> proto.ModelReleaseMigrateResult
	44, // 43: proto.ModelManifests.models:type_name -> proto.ModelManifests.ModelsEntry
	45, // 44: proto.ModelManifest.models:type_name -> proto.ModelManifest.ModelsEntry
	46, // 45: proto.ModelManifest.releases:type_name -> proto.ModelManifest.ReleasesEntry
	47, // 46: proto.ModelManifest.attachments:type_name -> proto.ModelManifest.AttachmentsEntry
	57, // 47: proto.ParseFileModelReq.model_sub_type:type_name -> proto.ModelSubType
	58, // 48: proto.ParseFileModelReq.model_sub_kind:type_name -> proto.ModelSubKind
	59, // 49: proto.ParseFileModelRsp.model_apis:type_name -> proto.ModelApi
	48, // 50: proto.CreateModelFileDownloadJobsReq.ctx:type_name -> proto.UserContext
	60, // 51: proto.CreateModelFileDownloadJobsReq.protocol:type_name -> proto.RepoAddrProtocol
	26, // 52: proto.ImportModelsPackageRsp.ModelsEntry.value:type_name -> proto.ModelImportResult
	27, // 53: proto.ModelImportResult.ReleasesEntry.value:type_name -> proto.ModelReleaseImportResult
	36, // 54: proto.ExportModelsPackageReq.ModelsEntry.value:type_name -> proto.ModelManifest
	36, // 55: proto.ModelManifests.ModelsEntry.value:type_name -> proto.ModelManifest
	52, // 56: proto.ModelManifest.ModelsEntry.value:type_name -> proto.Model
	54, // 57: proto.ModelManifest.ReleasesEntry.value:type_name -> proto.ModelRelease
	61, // 58: proto.ModelManifest.AttachmentsEntry.value:type_name -> proto.Attachment
	2,  // 59: proto.ModelManager.ListModels:input_type -> proto.ListModelsReq
	4,  // 60: proto.ModelManager.CreateModel:input_type -> proto.CreateModelReq
	6,  // 61: proto.ModelManager.UpdateModel:input_type -> proto.UpdateModelReq
	8,  // 62: proto.ModelManager.DeleteModel:input_type -> proto.DeleteModelReq
	0,  // 63: proto.ModelManager.CheckModelExistence:input_type -> proto.CheckModelReq
	20, // 64: proto.ModelManager.CheckModelsPackage:input_type -> proto.CheckModelsPackageReq
	24, // 65: proto.ModelManager.ImportModelsPackage:input_type -> proto.ImportModelsPackageReq
	28, // 66: proto.ModelManager.ExportModelsPackage:input_type -> proto.ExportModelsPackageReq
	10, // 67: proto.ModelManager.ListModelReleases:input_type -> proto.ListModelReleasesReq
	12, // 68: proto.ModelManager.CreateModelRelease:input_type -> proto.CreateModelReleaseReq
	14, // 69: proto.ModelManager.UpdateModelRelease:input_type -> proto.UpdateModelReleaseReq
	16, // 70: proto.ModelManager.DeleteModelRelease:input_type -> proto.DeleteModelReleaseReq
	18, // 71: proto.ModelManager.GetModelReleaseRelationGraph:input_type -> proto.GetModelReleaseRelationGraphReq
	37, // 72: proto.ModelManager.ParseFileModel:input_type -> proto.ParseFileModelReq
	39, // 73: proto.ModelManager.CreateModelFileDownloadJobs:input_type -> proto.CreateModelFileDownloadJobsReq
	3,  // 74: proto.ModelManager.ListModels:output_type -> proto.ListModelsRsp
	5,  // 75: proto.ModelManager.CreateModel:output_type -> proto.CreateModelRsp
	7,  // 76: proto.ModelManager.UpdateModel:output_type -> proto.UpdateModelRsp
	9,  // 77: proto.ModelManager.DeleteModel:output_type -> proto.DeleteModelRsp
	30, // 78: proto.ModelManager.CheckModelExistence:output_type -> proto.CheckResourceExistenceRsp
	21, // 79: proto.ModelManager.CheckModelsPackage:output_type -> proto.CheckModelsPackageRsp
	25, // 80: proto.ModelManager.ImportModelsPackage:output_type -> proto.ImportModelsPackageRsp
	29, // 81: proto.ModelManager.ExportModelsPackage:output_type -> proto.ExportModelsPackageRsp
	11, // 82: proto.ModelManager.ListModelReleases:output_type -> proto.ListModelReleasesRsp
	13, // 83: proto.ModelManager.CreateModelRelease:output_type -> proto.CreateModelReleaseRsp
	15, // 84: proto.ModelManager.UpdateModelRelease:output_type -> proto.UpdateModelReleaseRsp
	17, // 85: proto.ModelManager.DeleteModelRelease:output_type -> proto.DeleteModelReleaseRsp
	19, // 86: proto.ModelManager.GetModelReleaseRelationGraph:output_type -> proto.GetModelReleaseRelationGraphRsp
	38, // 87: proto.ModelManager.ParseFileModel:output_type -> proto.ParseFileModelRsp
	40, // 88: proto.ModelManager.CreateModelFileDownloadJobs:output_type -> proto.CreateModelFileDownloadJobsRsp
	74, // [74:89] is the sub-list for method output_type
	59, // [59:74] is the sub-list for method input_type
	59, // [59:59] is the sub-list for extension type_name
	59, // [59:59] is the sub-list for extension extendee
	0,  // [0:59] is the sub-list for field type_name
}

func init() { file_proto_rpc_model_proto_init() }
func file_proto_rpc_model_proto_init() {
	if File_proto_rpc_model_proto != nil {
		return
	}
	file_proto_common_proto_init()
	file_proto_model_proto_init()
	file_proto_relation_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_proto_rpc_model_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckModelReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckModelReleaseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListModelsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListModelsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateModelReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateModelRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateModelReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateModelRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteModelReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteModelRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListModelReleasesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListModelReleasesRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateModelReleaseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateModelReleaseRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateModelReleaseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateModelReleaseRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteModelReleaseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteModelReleaseRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetModelReleaseRelationGraphReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetModelReleaseRelationGraphRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckModelsPackageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckModelsPackageRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelCheckResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelReleaseCheckResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportModelsPackageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportModelsPackageRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelImportResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelReleaseImportResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportModelsPackageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportModelsPackageRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckResourceExistenceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MigrateModelsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MigrateModelsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelMigrateResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelReleaseMigrateResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelManifests); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelManifest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ParseFileModelReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ParseFileModelRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateModelFileDownloadJobsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateModelFileDownloadJobsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_rpc_model_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   48,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_rpc_model_proto_goTypes,
		DependencyIndexes: file_proto_rpc_model_proto_depIdxs,
		MessageInfos:      file_proto_rpc_model_proto_msgTypes,
	}.Build()
	File_proto_rpc_model_proto = out.File
	file_proto_rpc_model_proto_rawDesc = nil
	file_proto_rpc_model_proto_goTypes = nil
	file_proto_rpc_model_proto_depIdxs = nil
}
