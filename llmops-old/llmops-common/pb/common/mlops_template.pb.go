// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/common/mlops_template.proto

package common

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MLOpsPortType int32

const (
	// @gotags: description:"http"
	MLOpsPortType_MLOPS_PORT_TYPE_HTTP MLOpsPortType = 0
	// @gotags: description:"grpc"
	MLOpsPortType_MLOPS_PORT_TYPE_GRPC MLOpsPortType = 1
	// @gotags: description:"未知"
	MLOpsPortType_MLOPS_PORT_TYPE_UNKNOW MLOpsPortType = 11
)

// Enum value maps for MLOpsPortType.
var (
	MLOpsPortType_name = map[int32]string{
		0:  "MLOPS_PORT_TYPE_HTTP",
		1:  "MLOPS_PORT_TYPE_GRPC",
		11: "MLOPS_PORT_TYPE_UNKNOW",
	}
	MLOpsPortType_value = map[string]int32{
		"MLOPS_PORT_TYPE_HTTP":   0,
		"MLOPS_PORT_TYPE_GRPC":   1,
		"MLOPS_PORT_TYPE_UNKNOW": 11,
	}
)

func (x MLOpsPortType) Enum() *MLOpsPortType {
	p := new(MLOpsPortType)
	*p = x
	return p
}

func (x MLOpsPortType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MLOpsPortType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_common_mlops_template_proto_enumTypes[0].Descriptor()
}

func (MLOpsPortType) Type() protoreflect.EnumType {
	return &file_proto_common_mlops_template_proto_enumTypes[0]
}

func (x MLOpsPortType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MLOpsPortType.Descriptor instead.
func (MLOpsPortType) EnumDescriptor() ([]byte, []int) {
	return file_proto_common_mlops_template_proto_rawDescGZIP(), []int{0}
}

type SourceType int32

const (
	// @gotags: description:"未知"
	SourceType_SOURCE_TYPE_UNKNOW SourceType = 0
	// @gotags: description:"模型仓库"
	SourceType_SOURCE_TYPE_MODEL_CUBE SourceType = 1
	// @gotags: description:"应用仓库"
	SourceType_SOURCE_TYPE_APP_CUBE SourceType = 2
	// @gotags: description:"vlab"
	SourceType_SOURCE_TYPE_VLAB SourceType = 3
	// @gotags: description:"自定义镜像"
	SourceType_SOURCE_TYPE_CUSTOM SourceType = 11
)

// Enum value maps for SourceType.
var (
	SourceType_name = map[int32]string{
		0:  "SOURCE_TYPE_UNKNOW",
		1:  "SOURCE_TYPE_MODEL_CUBE",
		2:  "SOURCE_TYPE_APP_CUBE",
		3:  "SOURCE_TYPE_VLAB",
		11: "SOURCE_TYPE_CUSTOM",
	}
	SourceType_value = map[string]int32{
		"SOURCE_TYPE_UNKNOW":     0,
		"SOURCE_TYPE_MODEL_CUBE": 1,
		"SOURCE_TYPE_APP_CUBE":   2,
		"SOURCE_TYPE_VLAB":       3,
		"SOURCE_TYPE_CUSTOM":     11,
	}
)

func (x SourceType) Enum() *SourceType {
	p := new(SourceType)
	*p = x
	return p
}

func (x SourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_common_mlops_template_proto_enumTypes[1].Descriptor()
}

func (SourceType) Type() protoreflect.EnumType {
	return &file_proto_common_mlops_template_proto_enumTypes[1]
}

func (x SourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SourceType.Descriptor instead.
func (SourceType) EnumDescriptor() ([]byte, []int) {
	return file_proto_common_mlops_template_proto_rawDescGZIP(), []int{1}
}

type MLOpsServiceTemplate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	SourceType SourceType `protobuf:"varint,1,opt,name=source_type,json=sourceType,proto3,enum=common.SourceType" json:"source_type" description:"来源类型：0未知、1模型仓库、2应用仓库、3vlab、11自定义镜像"`
	 
	SourceId string `protobuf:"bytes,2,opt,name=source_id,json=sourceId,proto3" json:"source_id" description:"来源ID，如应用链ID，模型版本ID"`
	 
	Name *string `protobuf:"bytes,3,opt,name=name,proto3,oneof" json:"name" description:"名字"`
	 
	Desc *string `protobuf:"bytes,4,opt,name=desc,proto3,oneof" json:"desc" description:"描述"`
	 
	Containers []*Container `protobuf:"bytes,5,rep,name=containers,proto3" json:"containers" description:"容器,至少有一个"`
	 
	Apis []*API `protobuf:"bytes,6,rep,name=apis,proto3" json:"apis" description:"api列表"`
	 
	VolumeCfg *VolumeCfg `protobuf:"bytes,7,opt,name=volume_cfg,json=volumeCfg,proto3" json:"volume_cfg" description:"持久卷配置"`
}

func (x *MLOpsServiceTemplate) Reset() {
	*x = MLOpsServiceTemplate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_mlops_template_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MLOpsServiceTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MLOpsServiceTemplate) ProtoMessage() {}

func (x *MLOpsServiceTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_mlops_template_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MLOpsServiceTemplate.ProtoReflect.Descriptor instead.
func (*MLOpsServiceTemplate) Descriptor() ([]byte, []int) {
	return file_proto_common_mlops_template_proto_rawDescGZIP(), []int{0}
}

func (x *MLOpsServiceTemplate) GetSourceType() SourceType {
	if x != nil {
		return x.SourceType
	}
	return SourceType_SOURCE_TYPE_UNKNOW
}

func (x *MLOpsServiceTemplate) GetSourceId() string {
	if x != nil {
		return x.SourceId
	}
	return ""
}

func (x *MLOpsServiceTemplate) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *MLOpsServiceTemplate) GetDesc() string {
	if x != nil && x.Desc != nil {
		return *x.Desc
	}
	return ""
}

func (x *MLOpsServiceTemplate) GetContainers() []*Container {
	if x != nil {
		return x.Containers
	}
	return nil
}

func (x *MLOpsServiceTemplate) GetApis() []*API {
	if x != nil {
		return x.Apis
	}
	return nil
}

func (x *MLOpsServiceTemplate) GetVolumeCfg() *VolumeCfg {
	if x != nil {
		return x.VolumeCfg
	}
	return nil
}

type API struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Port uint32 `protobuf:"varint,1,opt,name=port,proto3" json:"port" description:"端口"`
	 
	Type MLOpsPortType `protobuf:"varint,2,opt,name=type,proto3,enum=common.MLOpsPortType" json:"type" description:"API类型，http，grpc等"`
	 
	Url []string `protobuf:"bytes,3,rep,name=url,proto3" json:"url" description:"端口对应的API"`
}

func (x *API) Reset() {
	*x = API{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_mlops_template_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *API) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*API) ProtoMessage() {}

func (x *API) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_mlops_template_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use API.ProtoReflect.Descriptor instead.
func (*API) Descriptor() ([]byte, []int) {
	return file_proto_common_mlops_template_proto_rawDescGZIP(), []int{1}
}

func (x *API) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *API) GetType() MLOpsPortType {
	if x != nil {
		return x.Type
	}
	return MLOpsPortType_MLOPS_PORT_TYPE_HTTP
}

func (x *API) GetUrl() []string {
	if x != nil {
		return x.Url
	}
	return nil
}

// DeployTemplate 服务部署临时使用
type DeployTemplate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Image              string              `protobuf:"bytes,1,opt,name=image,proto3" json:"image"`
	Envs               map[string]string   `protobuf:"bytes,2,rep,name=envs,proto3" json:"envs" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Endpoints          []*Endpoint         `protobuf:"bytes,3,rep,name=endpoints,proto3" json:"endpoints"`
	Annotations        map[string]string   `protobuf:"bytes,4,rep,name=annotations,proto3" json:"annotations" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Args               []string            `protobuf:"bytes,5,rep,name=args,proto3" json:"args"`
	Mounts             []*Mount            `protobuf:"bytes,6,rep,name=mounts,proto3" json:"mounts"`
	HttpReadinessProbe *HTTPReadinessProbe `protobuf:"bytes,7,opt,name=http_readiness_probe,json=httpReadinessProbe,proto3" json:"http_readiness_probe"`
	SourceMetas        map[string]string   `protobuf:"bytes,8,rep,name=source_metas,json=sourceMetas,proto3" json:"source_metas" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *DeployTemplate) Reset() {
	*x = DeployTemplate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_mlops_template_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeployTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeployTemplate) ProtoMessage() {}

func (x *DeployTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_mlops_template_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeployTemplate.ProtoReflect.Descriptor instead.
func (*DeployTemplate) Descriptor() ([]byte, []int) {
	return file_proto_common_mlops_template_proto_rawDescGZIP(), []int{2}
}

func (x *DeployTemplate) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *DeployTemplate) GetEnvs() map[string]string {
	if x != nil {
		return x.Envs
	}
	return nil
}

func (x *DeployTemplate) GetEndpoints() []*Endpoint {
	if x != nil {
		return x.Endpoints
	}
	return nil
}

func (x *DeployTemplate) GetAnnotations() map[string]string {
	if x != nil {
		return x.Annotations
	}
	return nil
}

func (x *DeployTemplate) GetArgs() []string {
	if x != nil {
		return x.Args
	}
	return nil
}

func (x *DeployTemplate) GetMounts() []*Mount {
	if x != nil {
		return x.Mounts
	}
	return nil
}

func (x *DeployTemplate) GetHttpReadinessProbe() *HTTPReadinessProbe {
	if x != nil {
		return x.HttpReadinessProbe
	}
	return nil
}

func (x *DeployTemplate) GetSourceMetas() map[string]string {
	if x != nil {
		return x.SourceMetas
	}
	return nil
}

// Mount TODO之后跟ImageTemplate Mount做一个统一
type Mount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Volume    *VolumeCfg `protobuf:"bytes,1,opt,name=volume,proto3" json:"volume"`
	MountPath *MountPath `protobuf:"bytes,2,opt,name=mount_path,json=mountPath,proto3" json:"mount_path"`
}

func (x *Mount) Reset() {
	*x = Mount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_mlops_template_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Mount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Mount) ProtoMessage() {}

func (x *Mount) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_mlops_template_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Mount.ProtoReflect.Descriptor instead.
func (*Mount) Descriptor() ([]byte, []int) {
	return file_proto_common_mlops_template_proto_rawDescGZIP(), []int{3}
}

func (x *Mount) GetVolume() *VolumeCfg {
	if x != nil {
		return x.Volume
	}
	return nil
}

func (x *Mount) GetMountPath() *MountPath {
	if x != nil {
		return x.MountPath
	}
	return nil
}

// 其他参数使用默认值
// failureThreshold: 3
// httpGet:
//
//	path: /v2/health/ready
//	port: 8000
//	scheme: HTTP
//
// initialDelaySeconds: 10
// periodSeconds: 10
// successThreshold: 1
// timeoutSeconds: 10
type HTTPReadinessProbe struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path                string `protobuf:"bytes,1,opt,name=path,proto3" json:"path"`
	Port                int32  `protobuf:"varint,2,opt,name=port,proto3" json:"port"`
	InitialDelaySeconds int32  `protobuf:"varint,3,opt,name=initial_delay_seconds,json=initialDelaySeconds,proto3" json:"initial_delay_seconds"`
	TimeoutSeconds      int32  `protobuf:"varint,4,opt,name=timeout_seconds,json=timeoutSeconds,proto3" json:"timeout_seconds"`
	PeriodSeconds       int32  `protobuf:"varint,5,opt,name=period_seconds,json=periodSeconds,proto3" json:"period_seconds"`
	SuccessThreshold    int32  `protobuf:"varint,6,opt,name=success_threshold,json=successThreshold,proto3" json:"success_threshold"`
	FailureThreshold    int32  `protobuf:"varint,7,opt,name=failure_threshold,json=failureThreshold,proto3" json:"failure_threshold"`
}

func (x *HTTPReadinessProbe) Reset() {
	*x = HTTPReadinessProbe{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_mlops_template_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HTTPReadinessProbe) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HTTPReadinessProbe) ProtoMessage() {}

func (x *HTTPReadinessProbe) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_mlops_template_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HTTPReadinessProbe.ProtoReflect.Descriptor instead.
func (*HTTPReadinessProbe) Descriptor() ([]byte, []int) {
	return file_proto_common_mlops_template_proto_rawDescGZIP(), []int{4}
}

func (x *HTTPReadinessProbe) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *HTTPReadinessProbe) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *HTTPReadinessProbe) GetInitialDelaySeconds() int32 {
	if x != nil {
		return x.InitialDelaySeconds
	}
	return 0
}

func (x *HTTPReadinessProbe) GetTimeoutSeconds() int32 {
	if x != nil {
		return x.TimeoutSeconds
	}
	return 0
}

func (x *HTTPReadinessProbe) GetPeriodSeconds() int32 {
	if x != nil {
		return x.PeriodSeconds
	}
	return 0
}

func (x *HTTPReadinessProbe) GetSuccessThreshold() int32 {
	if x != nil {
		return x.SuccessThreshold
	}
	return 0
}

func (x *HTTPReadinessProbe) GetFailureThreshold() int32 {
	if x != nil {
		return x.FailureThreshold
	}
	return 0
}

var File_proto_common_mlops_template_proto protoreflect.FileDescriptor

var file_proto_common_mlops_template_proto_rawDesc = []byte{
	0x0a, 0x21, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6d,
	0x6c, 0x6f, 0x70, 0x73, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x1a, 0x1f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6b, 0x38, 0x73, 0x5f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb2, 0x02, 0x0a, 0x14, 0x4d, 0x4c, 0x4f,
	0x70, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x12, 0x33, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04,
	0x64, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x04, 0x64, 0x65,
	0x73, 0x63, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x0a, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x1f, 0x0a, 0x04, 0x61, 0x70, 0x69, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x41, 0x50, 0x49, 0x52, 0x04, 0x61, 0x70, 0x69, 0x73, 0x12, 0x30, 0x0a, 0x0a, 0x76, 0x6f, 0x6c,
	0x75, 0x6d, 0x65, 0x5f, 0x63, 0x66, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x66, 0x67,
	0x52, 0x09, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x66, 0x67, 0x42, 0x07, 0x0a, 0x05, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x22, 0x56, 0x0a,
	0x03, 0x41, 0x50, 0x49, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x29, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x50, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0xe6, 0x04, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x34,
	0x0a, 0x04, 0x65, 0x6e, 0x76, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x2e, 0x45, 0x6e, 0x76, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04,
	0x65, 0x6e, 0x76, 0x73, 0x12, 0x2f, 0x0a, 0x09, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x73, 0x2e, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x09, 0x65, 0x6e, 0x64, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x49, 0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x12, 0x0a, 0x04, 0x61, 0x72, 0x67, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04,
	0x61, 0x72, 0x67, 0x73, 0x12, 0x25, 0x0a, 0x06, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x06, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x4c, 0x0a, 0x14, 0x68,
	0x74, 0x74, 0x70, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x72,
	0x6f, 0x62, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x52, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x50, 0x72, 0x6f, 0x62, 0x65, 0x52, 0x12, 0x68, 0x74, 0x74, 0x70, 0x52, 0x65, 0x61, 0x64, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x12, 0x4a, 0x0a, 0x0c, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65,
	0x74, 0x61, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x73, 0x1a, 0x37, 0x0a, 0x09, 0x45, 0x6e, 0x76, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3e,
	0x0a, 0x10, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3e,
	0x0a, 0x10, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x64,
	0x0a, 0x05, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x29, 0x0a, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x66, 0x67, 0x52, 0x06, 0x76, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x12, 0x30, 0x0a, 0x0a, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x74, 0x68, 0x52, 0x09, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x50, 0x61, 0x74, 0x68, 0x22, 0x9a, 0x02, 0x0a, 0x12, 0x48, 0x54, 0x54, 0x50, 0x52, 0x65, 0x61,
	0x64, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70,
	0x6f, 0x72, 0x74, 0x12, 0x32, 0x0a, 0x15, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x64,
	0x65, 0x6c, 0x61, 0x79, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x13, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x44, 0x65, 0x6c, 0x61, 0x79,
	0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0e, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73,
	0x12, 0x25, 0x0a, 0x0e, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e,
	0x64, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64,
	0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x10, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x68, 0x72, 0x65, 0x73,
	0x68, 0x6f, 0x6c, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f,
	0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x10, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c,
	0x64, 0x2a, 0x5f, 0x0a, 0x0d, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x50, 0x6f, 0x72, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x4d, 0x4c, 0x4f, 0x50, 0x53, 0x5f, 0x50, 0x4f, 0x52, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x48, 0x54, 0x54, 0x50, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14,
	0x4d, 0x4c, 0x4f, 0x50, 0x53, 0x5f, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x47, 0x52, 0x50, 0x43, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x4d, 0x4c, 0x4f, 0x50, 0x53, 0x5f,
	0x50, 0x4f, 0x52, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x10, 0x0b, 0x2a, 0x88, 0x01, 0x0a, 0x0a, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x4f, 0x55,
	0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x43,
	0x55, 0x42, 0x45, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x43, 0x55, 0x42, 0x45, 0x10, 0x02, 0x12,
	0x14, 0x0a, 0x10, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x56,
	0x4c, 0x41, 0x42, 0x10, 0x03, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x10, 0x0b, 0x42, 0x31, 0x5a,
	0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69,
	0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x70, 0x62, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_common_mlops_template_proto_rawDescOnce sync.Once
	file_proto_common_mlops_template_proto_rawDescData = file_proto_common_mlops_template_proto_rawDesc
)

func file_proto_common_mlops_template_proto_rawDescGZIP() []byte {
	file_proto_common_mlops_template_proto_rawDescOnce.Do(func() {
		file_proto_common_mlops_template_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_common_mlops_template_proto_rawDescData)
	})
	return file_proto_common_mlops_template_proto_rawDescData
}

var file_proto_common_mlops_template_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_proto_common_mlops_template_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_proto_common_mlops_template_proto_goTypes = []interface{}{
	(MLOpsPortType)(0),           // 0: common.MLOpsPortType
	(SourceType)(0),              // 1: common.SourceType
	(*MLOpsServiceTemplate)(nil), // 2: common.MLOpsServiceTemplate
	(*API)(nil),                  // 3: common.API
	(*DeployTemplate)(nil),       // 4: common.DeployTemplate
	(*Mount)(nil),                // 5: common.Mount
	(*HTTPReadinessProbe)(nil),   // 6: common.HTTPReadinessProbe
	nil,                          // 7: common.DeployTemplate.EnvsEntry
	nil,                          // 8: common.DeployTemplate.AnnotationsEntry
	nil,                          // 9: common.DeployTemplate.SourceMetasEntry
	(*Container)(nil),            // 10: common.Container
	(*VolumeCfg)(nil),            // 11: common.VolumeCfg
	(*Endpoint)(nil),             // 12: commons.Endpoint
	(*MountPath)(nil),            // 13: common.MountPath
}
var file_proto_common_mlops_template_proto_depIdxs = []int32{
	1,  // 0: common.MLOpsServiceTemplate.source_type:type_name -> common.SourceType
	10, // 1: common.MLOpsServiceTemplate.containers:type_name -> common.Container
	3,  // 2: common.MLOpsServiceTemplate.apis:type_name -> common.API
	11, // 3: common.MLOpsServiceTemplate.volume_cfg:type_name -> common.VolumeCfg
	0,  // 4: common.API.type:type_name -> common.MLOpsPortType
	7,  // 5: common.DeployTemplate.envs:type_name -> common.DeployTemplate.EnvsEntry
	12, // 6: common.DeployTemplate.endpoints:type_name -> commons.Endpoint
	8,  // 7: common.DeployTemplate.annotations:type_name -> common.DeployTemplate.AnnotationsEntry
	5,  // 8: common.DeployTemplate.mounts:type_name -> common.Mount
	6,  // 9: common.DeployTemplate.http_readiness_probe:type_name -> common.HTTPReadinessProbe
	9,  // 10: common.DeployTemplate.source_metas:type_name -> common.DeployTemplate.SourceMetasEntry
	11, // 11: common.Mount.volume:type_name -> common.VolumeCfg
	13, // 12: common.Mount.mount_path:type_name -> common.MountPath
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_proto_common_mlops_template_proto_init() }
func file_proto_common_mlops_template_proto_init() {
	if File_proto_common_mlops_template_proto != nil {
		return
	}
	file_proto_common_k8s_resource_proto_init()
	file_proto_common_commons_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_proto_common_mlops_template_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MLOpsServiceTemplate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_mlops_template_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*API); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_mlops_template_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeployTemplate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_mlops_template_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Mount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_mlops_template_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HTTPReadinessProbe); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_proto_common_mlops_template_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_common_mlops_template_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_common_mlops_template_proto_goTypes,
		DependencyIndexes: file_proto_common_mlops_template_proto_depIdxs,
		EnumInfos:         file_proto_common_mlops_template_proto_enumTypes,
		MessageInfos:      file_proto_common_mlops_template_proto_msgTypes,
	}.Build()
	File_proto_common_mlops_template_proto = out.File
	file_proto_common_mlops_template_proto_rawDesc = nil
	file_proto_common_mlops_template_proto_goTypes = nil
	file_proto_common_mlops_template_proto_depIdxs = nil
}
