// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/common/commons.proto

package common

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HttpMethod int32

const (
	HttpMethod_HTTP_METHOD_POST HttpMethod = 0
	HttpMethod_HTTP_METHOD_GET  HttpMethod = 1
)

// Enum value maps for HttpMethod.
var (
	HttpMethod_name = map[int32]string{
		0: "HTTP_METHOD_POST",
		1: "HTTP_METHOD_GET",
	}
	HttpMethod_value = map[string]int32{
		"HTTP_METHOD_POST": 0,
		"HTTP_METHOD_GET":  1,
	}
)

func (x HttpMethod) Enum() *HttpMethod {
	p := new(HttpMethod)
	*p = x
	return p
}

func (x HttpMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HttpMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_common_commons_proto_enumTypes[0].Descriptor()
}

func (HttpMethod) Type() protoreflect.EnumType {
	return &file_proto_common_commons_proto_enumTypes[0]
}

func (x HttpMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HttpMethod.Descriptor instead.
func (HttpMethod) EnumDescriptor() ([]byte, []int) {
	return file_proto_common_commons_proto_rawDescGZIP(), []int{0}
}

type EndpointType int32

const (
	EndpointType_ENDPOINT_TYPE_HTTP  EndpointType = 0
	EndpointType_ENDPOINT_TYPE_GRPC  EndpointType = 1
	EndpointType_ENDPOINT_TYPE_HTTPS EndpointType = 2
	EndpointType_ENDPOINT_TYPE_TCP   EndpointType = 3
	EndpointType_ENDPOINT_TYPE_UDP   EndpointType = 4
)

// Enum value maps for EndpointType.
var (
	EndpointType_name = map[int32]string{
		0: "ENDPOINT_TYPE_HTTP",
		1: "ENDPOINT_TYPE_GRPC",
		2: "ENDPOINT_TYPE_HTTPS",
		3: "ENDPOINT_TYPE_TCP",
		4: "ENDPOINT_TYPE_UDP",
	}
	EndpointType_value = map[string]int32{
		"ENDPOINT_TYPE_HTTP":  0,
		"ENDPOINT_TYPE_GRPC":  1,
		"ENDPOINT_TYPE_HTTPS": 2,
		"ENDPOINT_TYPE_TCP":   3,
		"ENDPOINT_TYPE_UDP":   4,
	}
)

func (x EndpointType) Enum() *EndpointType {
	p := new(EndpointType)
	*p = x
	return p
}

func (x EndpointType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EndpointType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_common_commons_proto_enumTypes[1].Descriptor()
}

func (EndpointType) Type() protoreflect.EnumType {
	return &file_proto_common_commons_proto_enumTypes[1]
}

func (x EndpointType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EndpointType.Descriptor instead.
func (EndpointType) EnumDescriptor() ([]byte, []int) {
	return file_proto_common_commons_proto_rawDescGZIP(), []int{1}
}

type APIType int32

const (
	APIType_API_TYPE_OTHERS             APIType = 0
	APIType_API_TYPE_MODEL_INFER        APIType = 1
	APIType_API_TYPE_PROMETHEUS_METRICS APIType = 2
	APIType_API_TYPE_WEB_APP            APIType = 3
	APIType_API_TYPE_HEALTH_CHECK       APIType = 4
)

// Enum value maps for APIType.
var (
	APIType_name = map[int32]string{
		0: "API_TYPE_OTHERS",
		1: "API_TYPE_MODEL_INFER",
		2: "API_TYPE_PROMETHEUS_METRICS",
		3: "API_TYPE_WEB_APP",
		4: "API_TYPE_HEALTH_CHECK",
	}
	APIType_value = map[string]int32{
		"API_TYPE_OTHERS":             0,
		"API_TYPE_MODEL_INFER":        1,
		"API_TYPE_PROMETHEUS_METRICS": 2,
		"API_TYPE_WEB_APP":            3,
		"API_TYPE_HEALTH_CHECK":       4,
	}
)

func (x APIType) Enum() *APIType {
	p := new(APIType)
	*p = x
	return p
}

func (x APIType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (APIType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_common_commons_proto_enumTypes[2].Descriptor()
}

func (APIType) Type() protoreflect.EnumType {
	return &file_proto_common_commons_proto_enumTypes[2]
}

func (x APIType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use APIType.Descriptor instead.
func (APIType) EnumDescriptor() ([]byte, []int) {
	return file_proto_common_commons_proto_rawDescGZIP(), []int{2}
}

type APISpec int32

const (
	APISpec_API_SPEC_OTHERS    APISpec = 0
	APISpec_API_SPEC_OPENAI    APISpec = 1
	APISpec_API_SPEC_TRANSWARP APISpec = 3
)

// Enum value maps for APISpec.
var (
	APISpec_name = map[int32]string{
		0: "API_SPEC_OTHERS",
		1: "API_SPEC_OPENAI",
		3: "API_SPEC_TRANSWARP",
	}
	APISpec_value = map[string]int32{
		"API_SPEC_OTHERS":    0,
		"API_SPEC_OPENAI":    1,
		"API_SPEC_TRANSWARP": 3,
	}
)

func (x APISpec) Enum() *APISpec {
	p := new(APISpec)
	*p = x
	return p
}

func (x APISpec) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (APISpec) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_common_commons_proto_enumTypes[3].Descriptor()
}

func (APISpec) Type() protoreflect.EnumType {
	return &file_proto_common_commons_proto_enumTypes[3]
}

func (x APISpec) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use APISpec.Descriptor instead.
func (APISpec) EnumDescriptor() ([]byte, []int) {
	return file_proto_common_commons_proto_rawDescGZIP(), []int{3}
}

type BillingConfig_Type int32

const (
	BillingConfig_NIL        BillingConfig_Type = 0
	BillingConfig_BY_TOKEN   BillingConfig_Type = 1
	BillingConfig_BY_REQUEST BillingConfig_Type = 2
)

// Enum value maps for BillingConfig_Type.
var (
	BillingConfig_Type_name = map[int32]string{
		0: "NIL",
		1: "BY_TOKEN",
		2: "BY_REQUEST",
	}
	BillingConfig_Type_value = map[string]int32{
		"NIL":        0,
		"BY_TOKEN":   1,
		"BY_REQUEST": 2,
	}
)

func (x BillingConfig_Type) Enum() *BillingConfig_Type {
	p := new(BillingConfig_Type)
	*p = x
	return p
}

func (x BillingConfig_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BillingConfig_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_common_commons_proto_enumTypes[4].Descriptor()
}

func (BillingConfig_Type) Type() protoreflect.EnumType {
	return &file_proto_common_commons_proto_enumTypes[4]
}

func (x BillingConfig_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BillingConfig_Type.Descriptor instead.
func (BillingConfig_Type) EnumDescriptor() ([]byte, []int) {
	return file_proto_common_commons_proto_rawDescGZIP(), []int{7, 0}
}

type DeleteRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteRsp) Reset() {
	*x = DeleteRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_commons_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRsp) ProtoMessage() {}

func (x *DeleteRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_commons_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRsp.ProtoReflect.Descriptor instead.
func (*DeleteRsp) Descriptor() ([]byte, []int) {
	return file_proto_common_commons_proto_rawDescGZIP(), []int{0}
}

type EmptyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyReq) Reset() {
	*x = EmptyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_commons_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyReq) ProtoMessage() {}

func (x *EmptyReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_commons_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyReq.ProtoReflect.Descriptor instead.
func (*EmptyReq) Descriptor() ([]byte, []int) {
	return file_proto_common_commons_proto_rawDescGZIP(), []int{1}
}

type EmptyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyRsp) Reset() {
	*x = EmptyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_commons_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyRsp) ProtoMessage() {}

func (x *EmptyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_commons_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyRsp.ProtoReflect.Descriptor instead.
func (*EmptyRsp) Descriptor() ([]byte, []int) {
	return file_proto_common_commons_proto_rawDescGZIP(), []int{2}
}

type StringList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []string `protobuf:"bytes,1,rep,name=items,proto3" json:"items"`
}

func (x *StringList) Reset() {
	*x = StringList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_commons_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringList) ProtoMessage() {}

func (x *StringList) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_commons_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringList.ProtoReflect.Descriptor instead.
func (*StringList) Descriptor() ([]byte, []int) {
	return file_proto_common_commons_proto_rawDescGZIP(), []int{3}
}

func (x *StringList) GetItems() []string {
	if x != nil {
		return x.Items
	}
	return nil
}

type PageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageSize int32  `protobuf:"varint,1,opt,name=pageSize,proto3" json:"pageSize"`
	Page     int32  `protobuf:"varint,2,opt,name=page,proto3" json:"page"`
	OrderBy  string `protobuf:"bytes,3,opt,name=orderBy,proto3" json:"orderBy"`
	IsDesc   bool   `protobuf:"varint,4,opt,name=isDesc,proto3" json:"isDesc"`
}

func (x *PageReq) Reset() {
	*x = PageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_commons_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageReq) ProtoMessage() {}

func (x *PageReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_commons_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageReq.ProtoReflect.Descriptor instead.
func (*PageReq) Descriptor() ([]byte, []int) {
	return file_proto_common_commons_proto_rawDescGZIP(), []int{4}
}

func (x *PageReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PageReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *PageReq) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *PageReq) GetIsDesc() bool {
	if x != nil {
		return x.IsDesc
	}
	return false
}

type ResultMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result        string `protobuf:"bytes,1,opt,name=result,proto3" json:"result"`
	ErrMsg        string `protobuf:"bytes,2,opt,name=errMsg,proto3" json:"errMsg"`
	State         int32  `protobuf:"varint,3,opt,name=state,proto3" json:"state"`
	CallbackUrl   string `protobuf:"bytes,4,opt,name=callbackUrl,proto3" json:"callbackUrl"`
	CallbackParam string `protobuf:"bytes,5,opt,name=callbackParam,proto3" json:"callbackParam"`
}

func (x *ResultMsg) Reset() {
	*x = ResultMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_commons_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResultMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResultMsg) ProtoMessage() {}

func (x *ResultMsg) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_commons_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResultMsg.ProtoReflect.Descriptor instead.
func (*ResultMsg) Descriptor() ([]byte, []int) {
	return file_proto_common_commons_proto_rawDescGZIP(), []int{5}
}

func (x *ResultMsg) GetResult() string {
	if x != nil {
		return x.Result
	}
	return ""
}

func (x *ResultMsg) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *ResultMsg) GetState() int32 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *ResultMsg) GetCallbackUrl() string {
	if x != nil {
		return x.CallbackUrl
	}
	return ""
}

func (x *ResultMsg) GetCallbackParam() string {
	if x != nil {
		return x.CallbackParam
	}
	return ""
}

type IdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	ModelId string `protobuf:"bytes,2,opt,name=modelId,proto3" json:"modelId"`
}

func (x *IdReq) Reset() {
	*x = IdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_commons_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IdReq) ProtoMessage() {}

func (x *IdReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_commons_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IdReq.ProtoReflect.Descriptor instead.
func (*IdReq) Descriptor() ([]byte, []int) {
	return file_proto_common_commons_proto_rawDescGZIP(), []int{6}
}

func (x *IdReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *IdReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

type BillingConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PricePerThousandTokens float32 `protobuf:"fixed32,1,opt,name=price_per_thousand_tokens,json=pricePerThousandTokens,proto3" json:"price_per_thousand_tokens" description:"计费标准，单位元每千tokens"`

	PricePerRequest float32 `protobuf:"fixed32,2,opt,name=price_per_request,json=pricePerRequest,proto3" json:"price_per_request" description:"计费标准，单位元每次"`

	Type BillingConfig_Type `protobuf:"varint,3,opt,name=type,proto3,enum=commons.BillingConfig_Type" json:"type" description:"计费类型 1: 按token计费 2: 按次计费"`
}

func (x *BillingConfig) Reset() {
	*x = BillingConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_commons_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BillingConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillingConfig) ProtoMessage() {}

func (x *BillingConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_commons_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillingConfig.ProtoReflect.Descriptor instead.
func (*BillingConfig) Descriptor() ([]byte, []int) {
	return file_proto_common_commons_proto_rawDescGZIP(), []int{7}
}

func (x *BillingConfig) GetPricePerThousandTokens() float32 {
	if x != nil {
		return x.PricePerThousandTokens
	}
	return 0
}

func (x *BillingConfig) GetPricePerRequest() float32 {
	if x != nil {
		return x.PricePerRequest
	}
	return 0
}

func (x *BillingConfig) GetType() BillingConfig_Type {
	if x != nil {
		return x.Type
	}
	return BillingConfig_NIL
}

type DashboardChart struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Frames map[string]*DashboardChart_Frame `protobuf:"bytes,1,rep,name=frames,proto3" json:"frames" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"点线图数据"`
}

func (x *DashboardChart) Reset() {
	*x = DashboardChart{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_commons_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardChart) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardChart) ProtoMessage() {}

func (x *DashboardChart) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_commons_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardChart.ProtoReflect.Descriptor instead.
func (*DashboardChart) Descriptor() ([]byte, []int) {
	return file_proto_common_commons_proto_rawDescGZIP(), []int{8}
}

func (x *DashboardChart) GetFrames() map[string]*DashboardChart_Frame {
	if x != nil {
		return x.Frames
	}
	return nil
}

type Endpoint struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Port uint32 `protobuf:"varint,1,opt,name=port,proto3" json:"port" description:"端口"`

	Type EndpointType `protobuf:"varint,2,opt,name=type,proto3,enum=commons.EndpointType" json:"type" description:"类型，http，grpc等"`

	ApiAttrs []*APIAttr `protobuf:"bytes,3,rep,name=api_attrs,json=apiAttrs,proto3" json:"api_attrs" description:"api与相关属性, api_path -> 相关属性"`

	IsDefault bool `protobuf:"varint,4,opt,name=is_default,json=isDefault,proto3" json:"is_default" description:"默认的endpoint"`
}

func (x *Endpoint) Reset() {
	*x = Endpoint{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_commons_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Endpoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Endpoint) ProtoMessage() {}

func (x *Endpoint) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_commons_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Endpoint.ProtoReflect.Descriptor instead.
func (*Endpoint) Descriptor() ([]byte, []int) {
	return file_proto_common_commons_proto_rawDescGZIP(), []int{9}
}

func (x *Endpoint) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *Endpoint) GetType() EndpointType {
	if x != nil {
		return x.Type
	}
	return EndpointType_ENDPOINT_TYPE_HTTP
}

func (x *Endpoint) GetApiAttrs() []*APIAttr {
	if x != nil {
		return x.ApiAttrs
	}
	return nil
}

func (x *Endpoint) GetIsDefault() bool {
	if x != nil {
		return x.IsDefault
	}
	return false
}

type APIAttr struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApiPath string `protobuf:"bytes,1,opt,name=api_path,json=apiPath,proto3" json:"api_path" description:"端口对应的API"`

	ReqExample string `protobuf:"bytes,2,opt,name=req_example,json=reqExample,proto3" json:"req_example" description:"url对应的请求示例"`

	Method HttpMethod `protobuf:"varint,3,opt,name=method,proto3,enum=commons.HttpMethod" json:"method" description:"url对应method，支持GET/POST，默认POST"`

	Headers map[string]string `protobuf:"bytes,7,rep,name=headers,proto3" json:"headers" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"header请求头"`

	ApiType APIType `protobuf:"varint,8,opt,name=api_type,json=apiType,proto3,enum=commons.APIType" json:"api_type" description:"api类型:模型推理 指标 健康检查 网页应用"`

	InferSpec *ModelInferSpec `protobuf:"bytes,9,opt,name=infer_spec,json=inferSpec,proto3" json:"infer_spec" description:"推理接口的说明"`
}

func (x *APIAttr) Reset() {
	*x = APIAttr{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_commons_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *APIAttr) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*APIAttr) ProtoMessage() {}

func (x *APIAttr) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_commons_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use APIAttr.ProtoReflect.Descriptor instead.
func (*APIAttr) Descriptor() ([]byte, []int) {
	return file_proto_common_commons_proto_rawDescGZIP(), []int{10}
}

func (x *APIAttr) GetApiPath() string {
	if x != nil {
		return x.ApiPath
	}
	return ""
}

func (x *APIAttr) GetReqExample() string {
	if x != nil {
		return x.ReqExample
	}
	return ""
}

func (x *APIAttr) GetMethod() HttpMethod {
	if x != nil {
		return x.Method
	}
	return HttpMethod_HTTP_METHOD_POST
}

func (x *APIAttr) GetHeaders() map[string]string {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *APIAttr) GetApiType() APIType {
	if x != nil {
		return x.ApiType
	}
	return APIType_API_TYPE_OTHERS
}

func (x *APIAttr) GetInferSpec() *ModelInferSpec {
	if x != nil {
		return x.InferSpec
	}
	return nil
}

type ModelInferSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Kind string `protobuf:"bytes,5,opt,name=kind,proto3" json:"kind"`

	SubKind string `protobuf:"bytes,6,opt,name=sub_kind,json=subKind,proto3" json:"sub_kind"`

	Spec APISpec `protobuf:"varint,4,opt,name=spec,proto3,enum=commons.APISpec" json:"spec" description:"api所符合的接口规范说明"`
}

func (x *ModelInferSpec) Reset() {
	*x = ModelInferSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_commons_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelInferSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelInferSpec) ProtoMessage() {}

func (x *ModelInferSpec) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_commons_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelInferSpec.ProtoReflect.Descriptor instead.
func (*ModelInferSpec) Descriptor() ([]byte, []int) {
	return file_proto_common_commons_proto_rawDescGZIP(), []int{11}
}

func (x *ModelInferSpec) GetKind() string {
	if x != nil {
		return x.Kind
	}
	return ""
}

func (x *ModelInferSpec) GetSubKind() string {
	if x != nil {
		return x.SubKind
	}
	return ""
}

func (x *ModelInferSpec) GetSpec() APISpec {
	if x != nil {
		return x.Spec
	}
	return APISpec_API_SPEC_OTHERS
}

type NameUniqueReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name" description:"判断名字是否唯一：名字"`
}

func (x *NameUniqueReq) Reset() {
	*x = NameUniqueReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_commons_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NameUniqueReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NameUniqueReq) ProtoMessage() {}

func (x *NameUniqueReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_commons_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NameUniqueReq.ProtoReflect.Descriptor instead.
func (*NameUniqueReq) Descriptor() ([]byte, []int) {
	return file_proto_common_commons_proto_rawDescGZIP(), []int{12}
}

func (x *NameUniqueReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type NameUniqueRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsUnique bool `protobuf:"varint,1,opt,name=isUnique,proto3" json:"isUnique" description:"判断名字是否唯一"`
}

func (x *NameUniqueRes) Reset() {
	*x = NameUniqueRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_commons_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NameUniqueRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NameUniqueRes) ProtoMessage() {}

func (x *NameUniqueRes) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_commons_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NameUniqueRes.ProtoReflect.Descriptor instead.
func (*NameUniqueRes) Descriptor() ([]byte, []int) {
	return file_proto_common_commons_proto_rawDescGZIP(), []int{13}
}

func (x *NameUniqueRes) GetIsUnique() bool {
	if x != nil {
		return x.IsUnique
	}
	return false
}

type DashboardChart_Frame struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Points []*DashboardChart_Point `protobuf:"bytes,1,rep,name=points,proto3" json:"points" description:"点数据"`
}

func (x *DashboardChart_Frame) Reset() {
	*x = DashboardChart_Frame{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_commons_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardChart_Frame) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardChart_Frame) ProtoMessage() {}

func (x *DashboardChart_Frame) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_commons_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardChart_Frame.ProtoReflect.Descriptor instead.
func (*DashboardChart_Frame) Descriptor() ([]byte, []int) {
	return file_proto_common_commons_proto_rawDescGZIP(), []int{8, 0}
}

func (x *DashboardChart_Frame) GetPoints() []*DashboardChart_Point {
	if x != nil {
		return x.Points
	}
	return nil
}

type DashboardChart_Point struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Time string `protobuf:"bytes,1,opt,name=time,proto3" json:"time" description:"时间"`

	Value float32 `protobuf:"fixed32,2,opt,name=value,proto3" json:"value" description:"值"`
}

func (x *DashboardChart_Point) Reset() {
	*x = DashboardChart_Point{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_commons_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardChart_Point) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardChart_Point) ProtoMessage() {}

func (x *DashboardChart_Point) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_commons_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardChart_Point.ProtoReflect.Descriptor instead.
func (*DashboardChart_Point) Descriptor() ([]byte, []int) {
	return file_proto_common_commons_proto_rawDescGZIP(), []int{8, 1}
}

func (x *DashboardChart_Point) GetTime() string {
	if x != nil {
		return x.Time
	}
	return ""
}

func (x *DashboardChart_Point) GetValue() float32 {
	if x != nil {
		return x.Value
	}
	return 0
}

var File_proto_common_commons_proto protoreflect.FileDescriptor

var file_proto_common_commons_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x22, 0x0b, 0x0a, 0x09, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52,
	0x73, 0x70, 0x22, 0x0a, 0x0a, 0x08, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x22, 0x0a,
	0x0a, 0x08, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x73, 0x70, 0x22, 0x22, 0x0a, 0x0a, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x6b,
	0x0a, 0x07, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x42, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x42, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x73, 0x44, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x44, 0x65, 0x73, 0x63, 0x22, 0x99, 0x01, 0x0a, 0x09,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x4d, 0x73, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x55, 0x72,
	0x6c, 0x12, 0x24, 0x0a, 0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x22, 0x31, 0x0a, 0x05, 0x49, 0x64, 0x52, 0x65, 0x71,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x22, 0xd6, 0x01, 0x0a, 0x0d, 0x42,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x39, 0x0a, 0x19,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x74, 0x68, 0x6f, 0x75, 0x73, 0x61,
	0x6e, 0x64, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x16, 0x70, 0x72, 0x69, 0x63, 0x65, 0x50, 0x65, 0x72, 0x54, 0x68, 0x6f, 0x75, 0x73, 0x61, 0x6e,
	0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x5f, 0x70, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x0f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x50, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x42, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x22, 0x2d, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x07, 0x0a, 0x03,
	0x4e, 0x49, 0x4c, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x42, 0x59, 0x5f, 0x54, 0x4f, 0x4b, 0x45,
	0x4e, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x42, 0x59, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x10, 0x02, 0x22, 0x9a, 0x02, 0x0a, 0x0e, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x43, 0x68, 0x61, 0x72, 0x74, 0x12, 0x3b, 0x0a, 0x06, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73,
	0x2e, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x43, 0x68, 0x61, 0x72, 0x74, 0x2e,
	0x46, 0x72, 0x61, 0x6d, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x66, 0x72, 0x61,
	0x6d, 0x65, 0x73, 0x1a, 0x3e, 0x0a, 0x05, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x06,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x43, 0x68, 0x61, 0x72, 0x74, 0x2e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x06, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x73, 0x1a, 0x31, 0x0a, 0x05, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x1a, 0x58, 0x0a, 0x0b, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x33, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73,
	0x2e, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x43, 0x68, 0x61, 0x72, 0x74, 0x2e,
	0x46, 0x72, 0x61, 0x6d, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x97, 0x01, 0x0a, 0x08, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6f, 0x72,
	0x74, 0x12, 0x29, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x09,
	0x61, 0x70, 0x69, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x41, 0x50, 0x49, 0x41, 0x74, 0x74,
	0x72, 0x52, 0x08, 0x61, 0x70, 0x69, 0x41, 0x74, 0x74, 0x72, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x69,
	0x73, 0x5f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x69, 0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x22, 0xcc, 0x02, 0x0a, 0x07, 0x41,
	0x50, 0x49, 0x41, 0x74, 0x74, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x69, 0x5f, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x69, 0x50, 0x61, 0x74,
	0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x71, 0x5f, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x71, 0x45, 0x78, 0x61, 0x6d, 0x70,
	0x6c, 0x65, 0x12, 0x2b, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x48, 0x74, 0x74,
	0x70, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12,
	0x37, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x41, 0x50, 0x49, 0x41, 0x74,
	0x74, 0x72, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x2b, 0x0a, 0x08, 0x61, 0x70, 0x69, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x73, 0x2e, 0x41, 0x50, 0x49, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x61, 0x70,
	0x69, 0x54, 0x79, 0x70, 0x65, 0x12, 0x36, 0x0a, 0x0a, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x5f, 0x73,
	0x70, 0x65, 0x63, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x73, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x53, 0x70,
	0x65, 0x63, 0x52, 0x09, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x53, 0x70, 0x65, 0x63, 0x1a, 0x3a, 0x0a,
	0x0c, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x65, 0x0a, 0x0e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x53, 0x70, 0x65, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x6b,
	0x69, 0x6e, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x24, 0x0a, 0x04, 0x73, 0x70,
	0x65, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x73, 0x2e, 0x41, 0x50, 0x49, 0x53, 0x70, 0x65, 0x63, 0x52, 0x04, 0x73, 0x70, 0x65, 0x63,
	0x22, 0x23, 0x0a, 0x0d, 0x4e, 0x61, 0x6d, 0x65, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x52, 0x65,
	0x71, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x2b, 0x0a, 0x0d, 0x4e, 0x61, 0x6d, 0x65, 0x55, 0x6e, 0x69,
	0x71, 0x75, 0x65, 0x52, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x73, 0x55, 0x6e, 0x69, 0x71,
	0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x55, 0x6e, 0x69, 0x71,
	0x75, 0x65, 0x2a, 0x37, 0x0a, 0x0a, 0x48, 0x74, 0x74, 0x70, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x12, 0x14, 0x0a, 0x10, 0x48, 0x54, 0x54, 0x50, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f,
	0x50, 0x4f, 0x53, 0x54, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x48, 0x54, 0x54, 0x50, 0x5f, 0x4d,
	0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x47, 0x45, 0x54, 0x10, 0x01, 0x2a, 0x85, 0x01, 0x0a, 0x0c,
	0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x12,
	0x45, 0x4e, 0x44, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x48, 0x54,
	0x54, 0x50, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x45, 0x4e, 0x44, 0x50, 0x4f, 0x49, 0x4e, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x52, 0x50, 0x43, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13,
	0x45, 0x4e, 0x44, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x48, 0x54,
	0x54, 0x50, 0x53, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x45, 0x4e, 0x44, 0x50, 0x4f, 0x49, 0x4e,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x43, 0x50, 0x10, 0x03, 0x12, 0x15, 0x0a, 0x11,
	0x45, 0x4e, 0x44, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x44,
	0x50, 0x10, 0x04, 0x2a, 0x8a, 0x01, 0x0a, 0x07, 0x41, 0x50, 0x49, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x13, 0x0a, 0x0f, 0x41, 0x50, 0x49, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x54, 0x48, 0x45,
	0x52, 0x53, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x50, 0x49, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x49, 0x4e, 0x46, 0x45, 0x52, 0x10, 0x01, 0x12, 0x1f,
	0x0a, 0x1b, 0x41, 0x50, 0x49, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x4d, 0x45,
	0x54, 0x48, 0x45, 0x55, 0x53, 0x5f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x53, 0x10, 0x02, 0x12,
	0x14, 0x0a, 0x10, 0x41, 0x50, 0x49, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x57, 0x45, 0x42, 0x5f,
	0x41, 0x50, 0x50, 0x10, 0x03, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x50, 0x49, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x48, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x04,
	0x2a, 0x4b, 0x0a, 0x07, 0x41, 0x50, 0x49, 0x53, 0x70, 0x65, 0x63, 0x12, 0x13, 0x0a, 0x0f, 0x41,
	0x50, 0x49, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x53, 0x10, 0x00,
	0x12, 0x13, 0x0a, 0x0f, 0x41, 0x50, 0x49, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x5f, 0x4f, 0x50, 0x45,
	0x4e, 0x41, 0x49, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x50, 0x49, 0x5f, 0x53, 0x50, 0x45,
	0x43, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x57, 0x41, 0x52, 0x50, 0x10, 0x03, 0x42, 0x31, 0x5a,
	0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69,
	0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x70, 0x62, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_common_commons_proto_rawDescOnce sync.Once
	file_proto_common_commons_proto_rawDescData = file_proto_common_commons_proto_rawDesc
)

func file_proto_common_commons_proto_rawDescGZIP() []byte {
	file_proto_common_commons_proto_rawDescOnce.Do(func() {
		file_proto_common_commons_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_common_commons_proto_rawDescData)
	})
	return file_proto_common_commons_proto_rawDescData
}

var file_proto_common_commons_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_proto_common_commons_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_proto_common_commons_proto_goTypes = []interface{}{
	(HttpMethod)(0),              // 0: commons.HttpMethod
	(EndpointType)(0),            // 1: commons.EndpointType
	(APIType)(0),                 // 2: commons.APIType
	(APISpec)(0),                 // 3: commons.APISpec
	(BillingConfig_Type)(0),      // 4: commons.BillingConfig.Type
	(*DeleteRsp)(nil),            // 5: commons.DeleteRsp
	(*EmptyReq)(nil),             // 6: commons.EmptyReq
	(*EmptyRsp)(nil),             // 7: commons.EmptyRsp
	(*StringList)(nil),           // 8: commons.StringList
	(*PageReq)(nil),              // 9: commons.PageReq
	(*ResultMsg)(nil),            // 10: commons.ResultMsg
	(*IdReq)(nil),                // 11: commons.IdReq
	(*BillingConfig)(nil),        // 12: commons.BillingConfig
	(*DashboardChart)(nil),       // 13: commons.DashboardChart
	(*Endpoint)(nil),             // 14: commons.Endpoint
	(*APIAttr)(nil),              // 15: commons.APIAttr
	(*ModelInferSpec)(nil),       // 16: commons.ModelInferSpec
	(*NameUniqueReq)(nil),        // 17: commons.NameUniqueReq
	(*NameUniqueRes)(nil),        // 18: commons.NameUniqueRes
	(*DashboardChart_Frame)(nil), // 19: commons.DashboardChart.Frame
	(*DashboardChart_Point)(nil), // 20: commons.DashboardChart.Point
	nil,                          // 21: commons.DashboardChart.FramesEntry
	nil,                          // 22: commons.APIAttr.HeadersEntry
}
var file_proto_common_commons_proto_depIdxs = []int32{
	4,  // 0: commons.BillingConfig.type:type_name -> commons.BillingConfig.Type
	21, // 1: commons.DashboardChart.frames:type_name -> commons.DashboardChart.FramesEntry
	1,  // 2: commons.Endpoint.type:type_name -> commons.EndpointType
	15, // 3: commons.Endpoint.api_attrs:type_name -> commons.APIAttr
	0,  // 4: commons.APIAttr.method:type_name -> commons.HttpMethod
	22, // 5: commons.APIAttr.headers:type_name -> commons.APIAttr.HeadersEntry
	2,  // 6: commons.APIAttr.api_type:type_name -> commons.APIType
	16, // 7: commons.APIAttr.infer_spec:type_name -> commons.ModelInferSpec
	3,  // 8: commons.ModelInferSpec.spec:type_name -> commons.APISpec
	20, // 9: commons.DashboardChart.Frame.points:type_name -> commons.DashboardChart.Point
	19, // 10: commons.DashboardChart.FramesEntry.value:type_name -> commons.DashboardChart.Frame
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_proto_common_commons_proto_init() }
func file_proto_common_commons_proto_init() {
	if File_proto_common_commons_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_common_commons_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_commons_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmptyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_commons_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmptyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_commons_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StringList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_commons_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_commons_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResultMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_commons_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_commons_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BillingConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_commons_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardChart); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_commons_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Endpoint); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_commons_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*APIAttr); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_commons_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelInferSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_commons_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NameUniqueReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_commons_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NameUniqueRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_commons_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardChart_Frame); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_commons_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardChart_Point); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_common_commons_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_common_commons_proto_goTypes,
		DependencyIndexes: file_proto_common_commons_proto_depIdxs,
		EnumInfos:         file_proto_common_commons_proto_enumTypes,
		MessageInfos:      file_proto_common_commons_proto_msgTypes,
	}.Build()
	File_proto_common_commons_proto = out.File
	file_proto_common_commons_proto_rawDesc = nil
	file_proto_common_commons_proto_goTypes = nil
	file_proto_common_commons_proto_depIdxs = nil
}
