// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/common/rpc_actuator_service.proto

package common

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CheckHealthReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CheckHealthReq) Reset() {
	*x = CheckHealthReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_rpc_actuator_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckHealthReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckHealthReq) ProtoMessage() {}

func (x *CheckHealthReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_rpc_actuator_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckHealthReq.ProtoReflect.Descriptor instead.
func (*CheckHealthReq) Descriptor() ([]byte, []int) {
	return file_proto_common_rpc_actuator_service_proto_rawDescGZIP(), []int{0}
}

type HealthRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    string            `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	Status  string            `protobuf:"bytes,2,opt,name=status,proto3" json:"status"`
	Details map[string]string `protobuf:"bytes,3,rep,name=details,proto3" json:"details" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *HealthRsp) Reset() {
	*x = HealthRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_rpc_actuator_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HealthRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthRsp) ProtoMessage() {}

func (x *HealthRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_rpc_actuator_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthRsp.ProtoReflect.Descriptor instead.
func (*HealthRsp) Descriptor() ([]byte, []int) {
	return file_proto_common_rpc_actuator_service_proto_rawDescGZIP(), []int{1}
}

func (x *HealthRsp) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *HealthRsp) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *HealthRsp) GetDetails() map[string]string {
	if x != nil {
		return x.Details
	}
	return nil
}

var File_proto_common_rpc_actuator_service_proto protoreflect.FileDescriptor

var file_proto_common_rpc_actuator_service_proto_rawDesc = []byte{
	0x0a, 0x27, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x72,
	0x70, 0x63, 0x5f, 0x61, 0x63, 0x74, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x22, 0x10, 0x0a, 0x0e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x52, 0x65, 0x71, 0x22, 0xad, 0x01, 0x0a, 0x09, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x52, 0x73,
	0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x38, 0x0a,
	0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x52, 0x73,
	0x70, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0x3a, 0x0a, 0x0c, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x32, 0x4b, 0x0a, 0x0f, 0x41, 0x63, 0x74, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x38, 0x0a, 0x0b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x48,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x12, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x52, 0x73, 0x70,
	0x42, 0x31, 0x5a, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f,
	0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x3b, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_common_rpc_actuator_service_proto_rawDescOnce sync.Once
	file_proto_common_rpc_actuator_service_proto_rawDescData = file_proto_common_rpc_actuator_service_proto_rawDesc
)

func file_proto_common_rpc_actuator_service_proto_rawDescGZIP() []byte {
	file_proto_common_rpc_actuator_service_proto_rawDescOnce.Do(func() {
		file_proto_common_rpc_actuator_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_common_rpc_actuator_service_proto_rawDescData)
	})
	return file_proto_common_rpc_actuator_service_proto_rawDescData
}

var file_proto_common_rpc_actuator_service_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_proto_common_rpc_actuator_service_proto_goTypes = []interface{}{
	(*CheckHealthReq)(nil), // 0: common.CheckHealthReq
	(*HealthRsp)(nil),      // 1: common.HealthRsp
	nil,                    // 2: common.HealthRsp.DetailsEntry
}
var file_proto_common_rpc_actuator_service_proto_depIdxs = []int32{
	2, // 0: common.HealthRsp.details:type_name -> common.HealthRsp.DetailsEntry
	0, // 1: common.ActuatorService.CheckHealth:input_type -> common.CheckHealthReq
	1, // 2: common.ActuatorService.CheckHealth:output_type -> common.HealthRsp
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_proto_common_rpc_actuator_service_proto_init() }
func file_proto_common_rpc_actuator_service_proto_init() {
	if File_proto_common_rpc_actuator_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_common_rpc_actuator_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckHealthReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_rpc_actuator_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HealthRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_common_rpc_actuator_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_common_rpc_actuator_service_proto_goTypes,
		DependencyIndexes: file_proto_common_rpc_actuator_service_proto_depIdxs,
		MessageInfos:      file_proto_common_rpc_actuator_service_proto_msgTypes,
	}.Build()
	File_proto_common_rpc_actuator_service_proto = out.File
	file_proto_common_rpc_actuator_service_proto_rawDesc = nil
	file_proto_common_rpc_actuator_service_proto_goTypes = nil
	file_proto_common_rpc_actuator_service_proto_depIdxs = nil
}
