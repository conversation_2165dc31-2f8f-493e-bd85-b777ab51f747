// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/common/k8s_resource.proto

package common

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type URIScheme int32

const (
	URIScheme_HTTP  URIScheme = 0
	URIScheme_HTTPS URIScheme = 1
)

// Enum value maps for URIScheme.
var (
	URIScheme_name = map[int32]string{
		0: "HTTP",
		1: "HTTPS",
	}
	URIScheme_value = map[string]int32{
		"HTTP":  0,
		"HTTPS": 1,
	}
)

func (x URIScheme) Enum() *URIScheme {
	p := new(URIScheme)
	*p = x
	return p
}

func (x URIScheme) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (URIScheme) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_common_k8s_resource_proto_enumTypes[0].Descriptor()
}

func (URIScheme) Type() protoreflect.EnumType {
	return &file_proto_common_k8s_resource_proto_enumTypes[0]
}

func (x URIScheme) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use URIScheme.Descriptor instead.
func (URIScheme) EnumDescriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{0}
}

type ContainerType int32

const (
	// @gotags: description:"主容器"
	ContainerType_CONTAINER_TYPE_MAIN ContainerType = 0
	// @gotags: description:"sidecar"
	ContainerType_CONTAINER_TYPE_SIDECAR ContainerType = 1
)

// Enum value maps for ContainerType.
var (
	ContainerType_name = map[int32]string{
		0: "CONTAINER_TYPE_MAIN",
		1: "CONTAINER_TYPE_SIDECAR",
	}
	ContainerType_value = map[string]int32{
		"CONTAINER_TYPE_MAIN":    0,
		"CONTAINER_TYPE_SIDECAR": 1,
	}
)

func (x ContainerType) Enum() *ContainerType {
	p := new(ContainerType)
	*p = x
	return p
}

func (x ContainerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ContainerType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_common_k8s_resource_proto_enumTypes[1].Descriptor()
}

func (ContainerType) Type() protoreflect.EnumType {
	return &file_proto_common_k8s_resource_proto_enumTypes[1]
}

func (x ContainerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ContainerType.Descriptor instead.
func (ContainerType) EnumDescriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{1}
}

type MLOpsVolumeType int32

const (
	// @gotags: description:"hostpath"
	MLOpsVolumeType_MLOPS_File_TYPE_HOST_PATH MLOpsVolumeType = 0
	// @gotags: description:"文件系统（底层是pvc）"
	MLOpsVolumeType_MLOPS_File_TYPE_FILE_SYSTEM MLOpsVolumeType = 1
	// @gotags: description:"模型仓库（底层是pvc）"
	MLOpsVolumeType_MLOPS_File_TYPE_MODEL_CUBE MLOpsVolumeType = 2
	// @gotags: description:"样本仓库（底层是pvc）"
	MLOpsVolumeType_MLOPS_File_TYPE_SAMPLE_CUBE MLOpsVolumeType = 3
	// @gotags: description:"内存挂载"
	MLOpsVolumeType_MLOPS_File_TYPE_MEMORY MLOpsVolumeType = 4
	// @gotags: description:"sfs(底层是sfs-pvc)"
	MLOpsVolumeType_MLOPS_File_TYPE_SFS MLOpsVolumeType = 5
	// @gotags: description:"pvc"
	MLOpsVolumeType_MLOPS_File_TYPE_PVC MLOpsVolumeType = 6
	// @gotags: description:"config map"
	MLOpsVolumeType_MLOPS_File_TYPE_CONFIG_MAP MLOpsVolumeType = 7
	// @gotags: description:"empty dir"
	MLOpsVolumeType_MLOPS_File_TYPE_EMPTYDIR MLOpsVolumeType = 8
)

// Enum value maps for MLOpsVolumeType.
var (
	MLOpsVolumeType_name = map[int32]string{
		0: "MLOPS_File_TYPE_HOST_PATH",
		1: "MLOPS_File_TYPE_FILE_SYSTEM",
		2: "MLOPS_File_TYPE_MODEL_CUBE",
		3: "MLOPS_File_TYPE_SAMPLE_CUBE",
		4: "MLOPS_File_TYPE_MEMORY",
		5: "MLOPS_File_TYPE_SFS",
		6: "MLOPS_File_TYPE_PVC",
		7: "MLOPS_File_TYPE_CONFIG_MAP",
		8: "MLOPS_File_TYPE_EMPTYDIR",
	}
	MLOpsVolumeType_value = map[string]int32{
		"MLOPS_File_TYPE_HOST_PATH":   0,
		"MLOPS_File_TYPE_FILE_SYSTEM": 1,
		"MLOPS_File_TYPE_MODEL_CUBE":  2,
		"MLOPS_File_TYPE_SAMPLE_CUBE": 3,
		"MLOPS_File_TYPE_MEMORY":      4,
		"MLOPS_File_TYPE_SFS":         5,
		"MLOPS_File_TYPE_PVC":         6,
		"MLOPS_File_TYPE_CONFIG_MAP":  7,
		"MLOPS_File_TYPE_EMPTYDIR":    8,
	}
)

func (x MLOpsVolumeType) Enum() *MLOpsVolumeType {
	p := new(MLOpsVolumeType)
	*p = x
	return p
}

func (x MLOpsVolumeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MLOpsVolumeType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_common_k8s_resource_proto_enumTypes[2].Descriptor()
}

func (MLOpsVolumeType) Type() protoreflect.EnumType {
	return &file_proto_common_k8s_resource_proto_enumTypes[2]
}

func (x MLOpsVolumeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MLOpsVolumeType.Descriptor instead.
func (MLOpsVolumeType) EnumDescriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{2}
}

type Container_ImagePullPolicy int32

const (
	Container_PullAlways       Container_ImagePullPolicy = 0
	Container_PullNever        Container_ImagePullPolicy = 1
	Container_PullIfNotPresent Container_ImagePullPolicy = 2
)

// Enum value maps for Container_ImagePullPolicy.
var (
	Container_ImagePullPolicy_name = map[int32]string{
		0: "PullAlways",
		1: "PullNever",
		2: "PullIfNotPresent",
	}
	Container_ImagePullPolicy_value = map[string]int32{
		"PullAlways":       0,
		"PullNever":        1,
		"PullIfNotPresent": 2,
	}
)

func (x Container_ImagePullPolicy) Enum() *Container_ImagePullPolicy {
	p := new(Container_ImagePullPolicy)
	*p = x
	return p
}

func (x Container_ImagePullPolicy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Container_ImagePullPolicy) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_common_k8s_resource_proto_enumTypes[3].Descriptor()
}

func (Container_ImagePullPolicy) Type() protoreflect.EnumType {
	return &file_proto_common_k8s_resource_proto_enumTypes[3]
}

func (x Container_ImagePullPolicy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Container_ImagePullPolicy.Descriptor instead.
func (Container_ImagePullPolicy) EnumDescriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{1, 0}
}

type IntOrString_Type int32

const (
	IntOrString_int    IntOrString_Type = 0
	IntOrString_string IntOrString_Type = 1
)

// Enum value maps for IntOrString_Type.
var (
	IntOrString_Type_name = map[int32]string{
		0: "int",
		1: "string",
	}
	IntOrString_Type_value = map[string]int32{
		"int":    0,
		"string": 1,
	}
)

func (x IntOrString_Type) Enum() *IntOrString_Type {
	p := new(IntOrString_Type)
	*p = x
	return p
}

func (x IntOrString_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IntOrString_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_common_k8s_resource_proto_enumTypes[4].Descriptor()
}

func (IntOrString_Type) Type() protoreflect.EnumType {
	return &file_proto_common_k8s_resource_proto_enumTypes[4]
}

func (x IntOrString_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IntOrString_Type.Descriptor instead.
func (IntOrString_Type) EnumDescriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{8, 0}
}

type Pod struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Containers []*Container `protobuf:"bytes,1,rep,name=containers,proto3" json:"containers" description:"容器"`
	 
	Volume []*VolumeCfg `protobuf:"bytes,2,rep,name=volume,proto3" json:"volume" description:"持久卷配置"`
}

func (x *Pod) Reset() {
	*x = Pod{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_k8s_resource_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pod) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pod) ProtoMessage() {}

func (x *Pod) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_k8s_resource_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pod.ProtoReflect.Descriptor instead.
func (*Pod) Descriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{0}
}

func (x *Pod) GetContainers() []*Container {
	if x != nil {
		return x.Containers
	}
	return nil
}

func (x *Pod) GetVolume() []*VolumeCfg {
	if x != nil {
		return x.Volume
	}
	return nil
}

type Container struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Image string `protobuf:"bytes,1,opt,name=image,proto3" json:"image" description:"镜像地址"`
	 
	ContainerType ContainerType `protobuf:"varint,2,opt,name=container_type,json=containerType,proto3,enum=common.ContainerType" json:"container_type" description:"容器类型，0主容器，1sidecar容器"`
	 
	ResourceRequirements *ResourceRequirements `protobuf:"bytes,3,opt,name=resource_requirements,json=resourceRequirements,proto3" json:"resource_requirements" description:"资源限制, Deprecated: 在 V2 接口不再使用"`
	 
	Envs map[string]string `protobuf:"bytes,5,rep,name=envs,proto3" json:"envs" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"环境变量"`
	 
	Cmds []string `protobuf:"bytes,6,rep,name=cmds,proto3" json:"cmds" description:"启动命令，需要按照空格拆分成数组，注意引号包裹的空格无需拆分"`
	 
	MountPaths []*MountPath `protobuf:"bytes,7,rep,name=mount_paths,json=mountPaths,proto3" json:"mount_paths" description:"挂载配置"`
	 
	Ports []int32 `protobuf:"varint,8,rep,packed,name=ports,proto3" json:"ports" description:"暴露端口，如[80,81]，同时开启80与81的tcp/udp端口"`
	 
	Args []string `protobuf:"bytes,9,rep,name=args,proto3" json:"args" description:"容器启动参数，需要按照空格拆分成数组，注意引号包裹的空格无需拆分"`
	 
	ImagePullPolicy Container_ImagePullPolicy `protobuf:"varint,10,opt,name=image_pull_policy,json=imagePullPolicy,proto3,enum=common.Container_ImagePullPolicy" json:"image_pull_policy"`
	 
	SecurityContext *SecurityContext `protobuf:"bytes,11,opt,name=security_context,json=securityContext,proto3" json:"security_context" description:"容器安全策略"`
	 
	Name string `protobuf:"bytes,12,opt,name=name,proto3" json:"name" description:"容器名称，非必填"`
	 
	Lifecycle *Lifecycle `protobuf:"bytes,13,opt,name=lifecycle,proto3" json:"lifecycle" description:"describes actions that the management system should take in response to container lifecycle events."`
	 
	ResourceId     *int32 `protobuf:"varint,14,opt,name=resource_id,json=resourceId,proto3,oneof" json:"resource_id" description:"实例规格id"`
	LivenessProbe  *Probe `protobuf:"bytes,15,opt,name=livenessProbe,proto3" json:"livenessProbe"`
	ReadinessProbe *Probe `protobuf:"bytes,16,opt,name=readinessProbe,proto3" json:"readinessProbe"`
}

func (x *Container) Reset() {
	*x = Container{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_k8s_resource_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Container) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Container) ProtoMessage() {}

func (x *Container) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_k8s_resource_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Container.ProtoReflect.Descriptor instead.
func (*Container) Descriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{1}
}

func (x *Container) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *Container) GetContainerType() ContainerType {
	if x != nil {
		return x.ContainerType
	}
	return ContainerType_CONTAINER_TYPE_MAIN
}

func (x *Container) GetResourceRequirements() *ResourceRequirements {
	if x != nil {
		return x.ResourceRequirements
	}
	return nil
}

func (x *Container) GetEnvs() map[string]string {
	if x != nil {
		return x.Envs
	}
	return nil
}

func (x *Container) GetCmds() []string {
	if x != nil {
		return x.Cmds
	}
	return nil
}

func (x *Container) GetMountPaths() []*MountPath {
	if x != nil {
		return x.MountPaths
	}
	return nil
}

func (x *Container) GetPorts() []int32 {
	if x != nil {
		return x.Ports
	}
	return nil
}

func (x *Container) GetArgs() []string {
	if x != nil {
		return x.Args
	}
	return nil
}

func (x *Container) GetImagePullPolicy() Container_ImagePullPolicy {
	if x != nil {
		return x.ImagePullPolicy
	}
	return Container_PullAlways
}

func (x *Container) GetSecurityContext() *SecurityContext {
	if x != nil {
		return x.SecurityContext
	}
	return nil
}

func (x *Container) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Container) GetLifecycle() *Lifecycle {
	if x != nil {
		return x.Lifecycle
	}
	return nil
}

func (x *Container) GetResourceId() int32 {
	if x != nil && x.ResourceId != nil {
		return *x.ResourceId
	}
	return 0
}

func (x *Container) GetLivenessProbe() *Probe {
	if x != nil {
		return x.LivenessProbe
	}
	return nil
}

func (x *Container) GetReadinessProbe() *Probe {
	if x != nil {
		return x.ReadinessProbe
	}
	return nil
}

type Probe struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Handler             *Handler `protobuf:"bytes,1,opt,name=handler,proto3" json:"handler"`
	InitialDelaySeconds int32    `protobuf:"varint,2,opt,name=initialDelaySeconds,proto3" json:"initialDelaySeconds"`
	TimeoutSeconds      int32    `protobuf:"varint,3,opt,name=TimeoutSeconds,proto3" json:"TimeoutSeconds"`
	PeriodSeconds       int32    `protobuf:"varint,4,opt,name=PeriodSeconds,proto3" json:"PeriodSeconds"`
	SuccessThreshold    int32    `protobuf:"varint,5,opt,name=SuccessThreshold,proto3" json:"SuccessThreshold"`
	FailureThreshold    int32    `protobuf:"varint,6,opt,name=FailureThreshold,proto3" json:"FailureThreshold"`
}

func (x *Probe) Reset() {
	*x = Probe{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_k8s_resource_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Probe) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Probe) ProtoMessage() {}

func (x *Probe) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_k8s_resource_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Probe.ProtoReflect.Descriptor instead.
func (*Probe) Descriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{2}
}

func (x *Probe) GetHandler() *Handler {
	if x != nil {
		return x.Handler
	}
	return nil
}

func (x *Probe) GetInitialDelaySeconds() int32 {
	if x != nil {
		return x.InitialDelaySeconds
	}
	return 0
}

func (x *Probe) GetTimeoutSeconds() int32 {
	if x != nil {
		return x.TimeoutSeconds
	}
	return 0
}

func (x *Probe) GetPeriodSeconds() int32 {
	if x != nil {
		return x.PeriodSeconds
	}
	return 0
}

func (x *Probe) GetSuccessThreshold() int32 {
	if x != nil {
		return x.SuccessThreshold
	}
	return 0
}

func (x *Probe) GetFailureThreshold() int32 {
	if x != nil {
		return x.FailureThreshold
	}
	return 0
}

type Lifecycle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	PostStart *Handler `protobuf:"bytes,1,opt,name=post_start,json=postStart,proto3" json:"post_start" description:"describes actions that the management system should take in response to container lifecycle events."`
	 
	PreStop *Handler `protobuf:"bytes,2,opt,name=pre_stop,json=preStop,proto3" json:"pre_stop" description:"PreStop is called immediately before a container is terminated due to an API request or management event such as liveness/startup probe failure"`
}

func (x *Lifecycle) Reset() {
	*x = Lifecycle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_k8s_resource_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Lifecycle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Lifecycle) ProtoMessage() {}

func (x *Lifecycle) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_k8s_resource_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Lifecycle.ProtoReflect.Descriptor instead.
func (*Lifecycle) Descriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{3}
}

func (x *Lifecycle) GetPostStart() *Handler {
	if x != nil {
		return x.PostStart
	}
	return nil
}

func (x *Lifecycle) GetPreStop() *Handler {
	if x != nil {
		return x.PreStop
	}
	return nil
}

// One and only one of the following should be specified.
type Handler struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Exec *ExecAction `protobuf:"bytes,1,opt,name=exec,proto3" json:"exec" description:"Exec specifies the action to take."`
	 
	HttpGet *HTTPGetAction `protobuf:"bytes,2,opt,name=http_get,json=httpGet,proto3" json:"http_get" description:"HTTPGet specifies the http request to perform."`
	 
	TcpSocket *TCPSocketAction `protobuf:"bytes,3,opt,name=tcp_socket,json=tcpSocket,proto3" json:"tcp_socket" description:"TCPSocket specifies an action involving a TCP port."`
}

func (x *Handler) Reset() {
	*x = Handler{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_k8s_resource_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Handler) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Handler) ProtoMessage() {}

func (x *Handler) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_k8s_resource_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Handler.ProtoReflect.Descriptor instead.
func (*Handler) Descriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{4}
}

func (x *Handler) GetExec() *ExecAction {
	if x != nil {
		return x.Exec
	}
	return nil
}

func (x *Handler) GetHttpGet() *HTTPGetAction {
	if x != nil {
		return x.HttpGet
	}
	return nil
}

func (x *Handler) GetTcpSocket() *TCPSocketAction {
	if x != nil {
		return x.TcpSocket
	}
	return nil
}

type ExecAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Command []string `protobuf:"bytes,1,rep,name=command,proto3" json:"command"`
}

func (x *ExecAction) Reset() {
	*x = ExecAction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_k8s_resource_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecAction) ProtoMessage() {}

func (x *ExecAction) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_k8s_resource_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecAction.ProtoReflect.Descriptor instead.
func (*ExecAction) Descriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{5}
}

func (x *ExecAction) GetCommand() []string {
	if x != nil {
		return x.Command
	}
	return nil
}

type HTTPGetAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Path string `protobuf:"bytes,1,opt,name=path,proto3" json:"path" description:"Path to access on the HTTP server."`
	 
	Port *IntOrString `protobuf:"bytes,2,opt,name=port,proto3" json:"port" description:"Name or number of the port to access on the container."`
	 
	Host string `protobuf:"bytes,3,opt,name=host,proto3" json:"host" description:"Host name to connect to, defaults to the pod IP. You probably want to set"`
	 
	Scheme URIScheme `protobuf:"varint,4,opt,name=scheme,proto3,enum=common.URIScheme" json:"scheme" description:"Scheme to use for connecting to the host."`
	 
	HttpHeaders []*HTTPHeader `protobuf:"bytes,5,rep,name=http_headers,json=httpHeaders,proto3" json:"http_headers" description:"Custom headers to set in the request. HTTP allows repeated headers."`
}

func (x *HTTPGetAction) Reset() {
	*x = HTTPGetAction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_k8s_resource_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HTTPGetAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HTTPGetAction) ProtoMessage() {}

func (x *HTTPGetAction) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_k8s_resource_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HTTPGetAction.ProtoReflect.Descriptor instead.
func (*HTTPGetAction) Descriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{6}
}

func (x *HTTPGetAction) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *HTTPGetAction) GetPort() *IntOrString {
	if x != nil {
		return x.Port
	}
	return nil
}

func (x *HTTPGetAction) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *HTTPGetAction) GetScheme() URIScheme {
	if x != nil {
		return x.Scheme
	}
	return URIScheme_HTTP
}

func (x *HTTPGetAction) GetHttpHeaders() []*HTTPHeader {
	if x != nil {
		return x.HttpHeaders
	}
	return nil
}

type HTTPHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name" description:"The header field name"`
	 
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value" description:"The header field value"`
}

func (x *HTTPHeader) Reset() {
	*x = HTTPHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_k8s_resource_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HTTPHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HTTPHeader) ProtoMessage() {}

func (x *HTTPHeader) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_k8s_resource_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HTTPHeader.ProtoReflect.Descriptor instead.
func (*HTTPHeader) Descriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{7}
}

func (x *HTTPHeader) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *HTTPHeader) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type IntOrString struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type   IntOrString_Type `protobuf:"varint,1,opt,name=type,proto3,enum=common.IntOrString_Type" json:"type"`
	IntVal int32            `protobuf:"varint,2,opt,name=int_val,json=intVal,proto3" json:"int_val"`
	StrVal string           `protobuf:"bytes,3,opt,name=str_val,json=strVal,proto3" json:"str_val"`
}

func (x *IntOrString) Reset() {
	*x = IntOrString{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_k8s_resource_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IntOrString) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IntOrString) ProtoMessage() {}

func (x *IntOrString) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_k8s_resource_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IntOrString.ProtoReflect.Descriptor instead.
func (*IntOrString) Descriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{8}
}

func (x *IntOrString) GetType() IntOrString_Type {
	if x != nil {
		return x.Type
	}
	return IntOrString_int
}

func (x *IntOrString) GetIntVal() int32 {
	if x != nil {
		return x.IntVal
	}
	return 0
}

func (x *IntOrString) GetStrVal() string {
	if x != nil {
		return x.StrVal
	}
	return ""
}

type TCPSocketAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Port *IntOrString `protobuf:"bytes,1,opt,name=port,proto3" json:"port" description:"Number or name of the port to access on the container."`
	 
	Host string `protobuf:"bytes,2,opt,name=host,proto3" json:"host" description:"Optional: Host name to connect to, defaults to the pod IP."`
}

func (x *TCPSocketAction) Reset() {
	*x = TCPSocketAction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_k8s_resource_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TCPSocketAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TCPSocketAction) ProtoMessage() {}

func (x *TCPSocketAction) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_k8s_resource_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TCPSocketAction.ProtoReflect.Descriptor instead.
func (*TCPSocketAction) Descriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{9}
}

func (x *TCPSocketAction) GetPort() *IntOrString {
	if x != nil {
		return x.Port
	}
	return nil
}

func (x *TCPSocketAction) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

type ResourceRequirements struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	ResourceLimit *Resource `protobuf:"bytes,3,opt,name=resource_limit,json=resourceLimit,proto3" json:"resource_limit" description:"资源限制"`
	 
	ResourceRequest *Resource `protobuf:"bytes,4,opt,name=resource_request,json=resourceRequest,proto3" json:"resource_request" description:"资源限制"`
	 
	GpuType string `protobuf:"bytes,16,opt,name=gpuType,proto3" json:"gpuType" description:"算力卡，可选 Ascend/Nvidia"`
	 
	GpuCards []string `protobuf:"bytes,17,rep,name=gpuCards,proto3" json:"gpuCards" description:"指定的gpu卡号"`
	 
	AscendConfig *AscendResourceCfg `protobuf:"bytes,11,opt,name=ascendConfig,proto3" json:"ascendConfig" description:"gpuType=Ascend时使用: ascend配置"`
}

func (x *ResourceRequirements) Reset() {
	*x = ResourceRequirements{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_k8s_resource_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceRequirements) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceRequirements) ProtoMessage() {}

func (x *ResourceRequirements) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_k8s_resource_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceRequirements.ProtoReflect.Descriptor instead.
func (*ResourceRequirements) Descriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{10}
}

func (x *ResourceRequirements) GetResourceLimit() *Resource {
	if x != nil {
		return x.ResourceLimit
	}
	return nil
}

func (x *ResourceRequirements) GetResourceRequest() *Resource {
	if x != nil {
		return x.ResourceRequest
	}
	return nil
}

func (x *ResourceRequirements) GetGpuType() string {
	if x != nil {
		return x.GpuType
	}
	return ""
}

func (x *ResourceRequirements) GetGpuCards() []string {
	if x != nil {
		return x.GpuCards
	}
	return nil
}

func (x *ResourceRequirements) GetAscendConfig() *AscendResourceCfg {
	if x != nil {
		return x.AscendConfig
	}
	return nil
}

type AscendResourceCfg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	NpuName string `protobuf:"bytes,1,opt,name=npu_name,json=npuName,proto3" json:"npu_name" description:"npu_name名字"`
	 
	TemplateName string `protobuf:"bytes,2,opt,name=template_name,json=templateName,proto3" json:"template_name" description:"ascend模板名字"`
	 
	Cnt int64 `protobuf:"varint,3,opt,name=cnt,proto3" json:"cnt" description:"数量，当template为exclusive的时候，需要填，默认1"`
}

func (x *AscendResourceCfg) Reset() {
	*x = AscendResourceCfg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_k8s_resource_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AscendResourceCfg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AscendResourceCfg) ProtoMessage() {}

func (x *AscendResourceCfg) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_k8s_resource_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AscendResourceCfg.ProtoReflect.Descriptor instead.
func (*AscendResourceCfg) Descriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{11}
}

func (x *AscendResourceCfg) GetNpuName() string {
	if x != nil {
		return x.NpuName
	}
	return ""
}

func (x *AscendResourceCfg) GetTemplateName() string {
	if x != nil {
		return x.TemplateName
	}
	return ""
}

func (x *AscendResourceCfg) GetCnt() int64 {
	if x != nil {
		return x.Cnt
	}
	return 0
}

type SecurityContext struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Privileged bool `protobuf:"varint,1,opt,name=privileged,proto3" json:"privileged" description:"开启privileged，默认false"`
	 
	RunAsUser int64 `protobuf:"varint,2,opt,name=run_as_user,json=runAsUser,proto3" json:"run_as_user" description:"terminal用户 id"`
}

func (x *SecurityContext) Reset() {
	*x = SecurityContext{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_k8s_resource_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecurityContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecurityContext) ProtoMessage() {}

func (x *SecurityContext) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_k8s_resource_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecurityContext.ProtoReflect.Descriptor instead.
func (*SecurityContext) Descriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{12}
}

func (x *SecurityContext) GetPrivileged() bool {
	if x != nil {
		return x.Privileged
	}
	return false
}

func (x *SecurityContext) GetRunAsUser() int64 {
	if x != nil {
		return x.RunAsUser
	}
	return 0
}

type Resource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Cpu int32 `protobuf:"varint,1,opt,name=cpu,proto3" json:"cpu" description:"cpu限制，单位为millicore（毫核）"`
	 
	Memory int32 `protobuf:"varint,2,opt,name=memory,proto3" json:"memory" description:"内存限制， 单位为MiB（Mebibyte，兆字节）"`
	 
	GpuCore int32 `protobuf:"varint,3,opt,name=gpu_core,json=gpuCore,proto3" json:"gpu_core" description:"gpu算力，单位 1%核"`
	 
	GpuMemory int32 `protobuf:"varint,4,opt,name=gpu_memory,json=gpuMemory,proto3" json:"gpu_memory" description:"gpu内存， 单位为MiB（Mebibyte，兆字节）"`
	 
	GpuCount int32 `protobuf:"varint,5,opt,name=gpu_count,json=gpuCount,proto3" json:"gpu_count" description:"gpu数量"`
}

func (x *Resource) Reset() {
	*x = Resource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_k8s_resource_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Resource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Resource) ProtoMessage() {}

func (x *Resource) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_k8s_resource_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Resource.ProtoReflect.Descriptor instead.
func (*Resource) Descriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{13}
}

func (x *Resource) GetCpu() int32 {
	if x != nil {
		return x.Cpu
	}
	return 0
}

func (x *Resource) GetMemory() int32 {
	if x != nil {
		return x.Memory
	}
	return 0
}

func (x *Resource) GetGpuCore() int32 {
	if x != nil {
		return x.GpuCore
	}
	return 0
}

func (x *Resource) GetGpuMemory() int32 {
	if x != nil {
		return x.GpuMemory
	}
	return 0
}

func (x *Resource) GetGpuCount() int32 {
	if x != nil {
		return x.GpuCount
	}
	return 0
}

type PvcVolumeCfg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	PvcName string `protobuf:"bytes,1,opt,name=pvcName,proto3" json:"pvcName" description:"pvc名字"`
}

func (x *PvcVolumeCfg) Reset() {
	*x = PvcVolumeCfg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_k8s_resource_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PvcVolumeCfg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PvcVolumeCfg) ProtoMessage() {}

func (x *PvcVolumeCfg) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_k8s_resource_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PvcVolumeCfg.ProtoReflect.Descriptor instead.
func (*PvcVolumeCfg) Descriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{14}
}

func (x *PvcVolumeCfg) GetPvcName() string {
	if x != nil {
		return x.PvcName
	}
	return ""
}

type HostPathVolumeCfg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Path string `protobuf:"bytes,1,opt,name=path,proto3" json:"path" description:"hostpath路径"`
}

func (x *HostPathVolumeCfg) Reset() {
	*x = HostPathVolumeCfg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_k8s_resource_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HostPathVolumeCfg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HostPathVolumeCfg) ProtoMessage() {}

func (x *HostPathVolumeCfg) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_k8s_resource_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HostPathVolumeCfg.ProtoReflect.Descriptor instead.
func (*HostPathVolumeCfg) Descriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{15}
}

func (x *HostPathVolumeCfg) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type KeyPathCfg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key  string `protobuf:"bytes,1,opt,name=key,proto3" json:"key"`
	Path string `protobuf:"bytes,2,opt,name=path,proto3" json:"path"`
}

func (x *KeyPathCfg) Reset() {
	*x = KeyPathCfg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_k8s_resource_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KeyPathCfg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KeyPathCfg) ProtoMessage() {}

func (x *KeyPathCfg) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_k8s_resource_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KeyPathCfg.ProtoReflect.Descriptor instead.
func (*KeyPathCfg) Descriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{16}
}

func (x *KeyPathCfg) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *KeyPathCfg) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type EmptyDirVolumeCfg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Medium string `protobuf:"bytes,1,opt,name=medium,proto3" json:"medium" description:"emptydir的medium:如Memory"`
}

func (x *EmptyDirVolumeCfg) Reset() {
	*x = EmptyDirVolumeCfg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_k8s_resource_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyDirVolumeCfg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyDirVolumeCfg) ProtoMessage() {}

func (x *EmptyDirVolumeCfg) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_k8s_resource_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyDirVolumeCfg.ProtoReflect.Descriptor instead.
func (*EmptyDirVolumeCfg) Descriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{17}
}

func (x *EmptyDirVolumeCfg) GetMedium() string {
	if x != nil {
		return x.Medium
	}
	return ""
}

type ConfigMapVolumeCfg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name" description:"cm名字"`
	 
	KeyPaths []*KeyPathCfg `protobuf:"bytes,2,rep,name=keyPaths,proto3" json:"keyPaths" description:"key path配置"`
	 
	DefaultMode *int32 `protobuf:"varint,3,opt,name=defaultMode,proto3,oneof" json:"defaultMode" description:"默认权限，如0644，非必填"`
	 
	DefaultContentValue *string `protobuf:"bytes,4,opt,name=default_content_value,json=defaultContentValue,proto3,oneof" json:"default_content_value" description:"config map的默认值"`
}

func (x *ConfigMapVolumeCfg) Reset() {
	*x = ConfigMapVolumeCfg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_k8s_resource_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigMapVolumeCfg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigMapVolumeCfg) ProtoMessage() {}

func (x *ConfigMapVolumeCfg) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_k8s_resource_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigMapVolumeCfg.ProtoReflect.Descriptor instead.
func (*ConfigMapVolumeCfg) Descriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{18}
}

func (x *ConfigMapVolumeCfg) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ConfigMapVolumeCfg) GetKeyPaths() []*KeyPathCfg {
	if x != nil {
		return x.KeyPaths
	}
	return nil
}

func (x *ConfigMapVolumeCfg) GetDefaultMode() int32 {
	if x != nil && x.DefaultMode != nil {
		return *x.DefaultMode
	}
	return 0
}

func (x *ConfigMapVolumeCfg) GetDefaultContentValue() string {
	if x != nil && x.DefaultContentValue != nil {
		return *x.DefaultContentValue
	}
	return ""
}

type VolumeCfg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name" description:"持久卷名称"`
	 
	VolumeType MLOpsVolumeType `protobuf:"varint,2,opt,name=volume_type,json=volumeType,proto3,enum=common.MLOpsVolumeType" json:"volume_type" description:"持久卷类型,0hostpath,1sfs，2pvc"`
	// @gotags: description:"sfs配置(底层是sfs-pvc)"
	//
	//	optional SfsVolumeCfg sfs_cfg = 3;
	//
	 
	PvcCfg *PvcVolumeCfg `protobuf:"bytes,4,opt,name=pvc_cfg,json=pvcCfg,proto3,oneof" json:"pvc_cfg" description:"pvc配置"`
	 
	EmptyDirCfg *EmptyDirVolumeCfg `protobuf:"bytes,5,opt,name=empty_dir_cfg,json=emptyDirCfg,proto3,oneof" json:"empty_dir_cfg" description:"empty dir"`
	 
	ConfigMapCfg *ConfigMapVolumeCfg `protobuf:"bytes,6,opt,name=config_map_cfg,json=configMapCfg,proto3,oneof" json:"config_map_cfg" description:"config map配置"`
	 
	HostpathCfg *HostPathVolumeCfg `protobuf:"bytes,7,opt,name=hostpath_cfg,json=hostpathCfg,proto3,oneof" json:"hostpath_cfg" description:"host path配置"`
}

func (x *VolumeCfg) Reset() {
	*x = VolumeCfg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_k8s_resource_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VolumeCfg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VolumeCfg) ProtoMessage() {}

func (x *VolumeCfg) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_k8s_resource_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VolumeCfg.ProtoReflect.Descriptor instead.
func (*VolumeCfg) Descriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{19}
}

func (x *VolumeCfg) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VolumeCfg) GetVolumeType() MLOpsVolumeType {
	if x != nil {
		return x.VolumeType
	}
	return MLOpsVolumeType_MLOPS_File_TYPE_HOST_PATH
}

func (x *VolumeCfg) GetPvcCfg() *PvcVolumeCfg {
	if x != nil {
		return x.PvcCfg
	}
	return nil
}

func (x *VolumeCfg) GetEmptyDirCfg() *EmptyDirVolumeCfg {
	if x != nil {
		return x.EmptyDirCfg
	}
	return nil
}

func (x *VolumeCfg) GetConfigMapCfg() *ConfigMapVolumeCfg {
	if x != nil {
		return x.ConfigMapCfg
	}
	return nil
}

func (x *VolumeCfg) GetHostpathCfg() *HostPathVolumeCfg {
	if x != nil {
		return x.HostpathCfg
	}
	return nil
}

type MountPath struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	 
	VolumeName string `protobuf:"bytes,1,opt,name=volume_name,json=volumeName,proto3" json:"volume_name" description:"卷名字"`
	 
	MountPath string `protobuf:"bytes,2,opt,name=mount_path,json=mountPath,proto3" json:"mount_path" description:"挂载路径"`
	 
	SubPath string `protobuf:"bytes,3,opt,name=sub_path,json=subPath,proto3" json:"sub_path" description:"挂载路径"`
	 
	ReadOnly bool `protobuf:"varint,4,opt,name=read_only,json=readOnly,proto3" json:"read_only" description:"是否只读"`
}

func (x *MountPath) Reset() {
	*x = MountPath{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_k8s_resource_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MountPath) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MountPath) ProtoMessage() {}

func (x *MountPath) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_k8s_resource_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MountPath.ProtoReflect.Descriptor instead.
func (*MountPath) Descriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{20}
}

func (x *MountPath) GetVolumeName() string {
	if x != nil {
		return x.VolumeName
	}
	return ""
}

func (x *MountPath) GetMountPath() string {
	if x != nil {
		return x.MountPath
	}
	return ""
}

func (x *MountPath) GetSubPath() string {
	if x != nil {
		return x.SubPath
	}
	return ""
}

func (x *MountPath) GetReadOnly() bool {
	if x != nil {
		return x.ReadOnly
	}
	return false
}

type PodEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type           string `protobuf:"bytes,1,opt,name=type,proto3" json:"type"`
	Reason         string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason"`
	FirstTimestamp int64  `protobuf:"varint,3,opt,name=first_timestamp,json=firstTimestamp,proto3" json:"first_timestamp"`
	LastTimestamp  int64  `protobuf:"varint,4,opt,name=last_timestamp,json=lastTimestamp,proto3" json:"last_timestamp"`
	Count          int32  `protobuf:"varint,5,opt,name=count,proto3" json:"count"`
	Message        string `protobuf:"bytes,6,opt,name=message,proto3" json:"message"`
	From           string `protobuf:"bytes,7,opt,name=from,proto3" json:"from"`
}

func (x *PodEvent) Reset() {
	*x = PodEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_common_k8s_resource_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PodEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PodEvent) ProtoMessage() {}

func (x *PodEvent) ProtoReflect() protoreflect.Message {
	mi := &file_proto_common_k8s_resource_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PodEvent.ProtoReflect.Descriptor instead.
func (*PodEvent) Descriptor() ([]byte, []int) {
	return file_proto_common_k8s_resource_proto_rawDescGZIP(), []int{21}
}

func (x *PodEvent) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *PodEvent) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *PodEvent) GetFirstTimestamp() int64 {
	if x != nil {
		return x.FirstTimestamp
	}
	return 0
}

func (x *PodEvent) GetLastTimestamp() int64 {
	if x != nil {
		return x.LastTimestamp
	}
	return 0
}

func (x *PodEvent) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *PodEvent) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PodEvent) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

var File_proto_common_k8s_resource_proto protoreflect.FileDescriptor

var file_proto_common_k8s_resource_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6b,
	0x38, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x22, 0x63, 0x0a, 0x03, 0x50, 0x6f, 0x64,
	0x12, 0x31, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x6f,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x73, 0x12, 0x29, 0x0a, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x6f, 0x6c,
	0x75, 0x6d, 0x65, 0x43, 0x66, 0x67, 0x52, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x22, 0xd0,
	0x06, 0x0a, 0x09, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x12, 0x3c, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x51, 0x0a, 0x15, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x14, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x12, 0x2f, 0x0a, 0x04, 0x65, 0x6e, 0x76, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x2e, 0x45, 0x6e, 0x76, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04,
	0x65, 0x6e, 0x76, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6d, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x04, 0x63, 0x6d, 0x64, 0x73, 0x12, 0x32, 0x0a, 0x0b, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x74, 0x68,
	0x52, 0x0a, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x74, 0x68, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x05, 0x52, 0x05, 0x70, 0x6f, 0x72,
	0x74, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x72, 0x67, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x04, 0x61, 0x72, 0x67, 0x73, 0x12, 0x4d, 0x0a, 0x11, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f,
	0x70, 0x75, 0x6c, 0x6c, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x75, 0x6c, 0x6c, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x52, 0x0f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x75, 0x6c, 0x6c, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x42, 0x0a, 0x10, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x0f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a,
	0x09, 0x6c, 0x69, 0x66, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4c, 0x69, 0x66, 0x65, 0x63, 0x79,
	0x63, 0x6c, 0x65, 0x52, 0x09, 0x6c, 0x69, 0x66, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x24,
	0x0a, 0x0b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x33, 0x0a, 0x0d, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x50, 0x72, 0x6f, 0x62, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x52, 0x0d, 0x6c, 0x69, 0x76, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x12, 0x35, 0x0a, 0x0e, 0x72, 0x65, 0x61,
	0x64, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x62, 0x65,
	0x52, 0x0e, 0x72, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x72, 0x6f, 0x62, 0x65,
	0x1a, 0x37, 0x0a, 0x09, 0x45, 0x6e, 0x76, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x46, 0x0a, 0x0f, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x50, 0x75, 0x6c, 0x6c, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x0e, 0x0a, 0x0a,
	0x50, 0x75, 0x6c, 0x6c, 0x41, 0x6c, 0x77, 0x61, 0x79, 0x73, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09,
	0x50, 0x75, 0x6c, 0x6c, 0x4e, 0x65, 0x76, 0x65, 0x72, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x50,
	0x75, 0x6c, 0x6c, 0x49, 0x66, 0x4e, 0x6f, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x10,
	0x02, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x22, 0x8a, 0x02, 0x0a, 0x05, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x12, 0x29, 0x0a, 0x07, 0x68,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x52, 0x07, 0x68,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x12, 0x30, 0x0a, 0x13, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61,
	0x6c, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x13, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x44, 0x65, 0x6c, 0x61,
	0x79, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x54, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0e, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73,
	0x12, 0x24, 0x0a, 0x0d, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x53,
	0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x2a, 0x0a, 0x10, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x10, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f,
	0x6c, 0x64, 0x12, 0x2a, 0x0a, 0x10, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x54, 0x68, 0x72,
	0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x46, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x22, 0x67,
	0x0a, 0x09, 0x4c, 0x69, 0x66, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x2e, 0x0a, 0x0a, 0x70,
	0x6f, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0f, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72,
	0x52, 0x09, 0x70, 0x6f, 0x73, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x2a, 0x0a, 0x08, 0x70,
	0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x52, 0x07,
	0x70, 0x72, 0x65, 0x53, 0x74, 0x6f, 0x70, 0x22, 0x9b, 0x01, 0x0a, 0x07, 0x48, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x04, 0x65, 0x78, 0x65, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x65, 0x78, 0x65, 0x63, 0x12, 0x30, 0x0a, 0x08, 0x68,
	0x74, 0x74, 0x70, 0x5f, 0x67, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x47, 0x65, 0x74, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x68, 0x74, 0x74, 0x70, 0x47, 0x65, 0x74, 0x12, 0x36, 0x0a,
	0x0a, 0x74, 0x63, 0x70, 0x5f, 0x73, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x43, 0x50, 0x53, 0x6f,
	0x63, 0x6b, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x74, 0x63, 0x70, 0x53,
	0x6f, 0x63, 0x6b, 0x65, 0x74, 0x22, 0x26, 0x0a, 0x0a, 0x45, 0x78, 0x65, 0x63, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x22, 0xc2, 0x01,
	0x0a, 0x0d, 0x48, 0x54, 0x54, 0x50, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70,
	0x61, 0x74, 0x68, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x4f, 0x72,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x68, 0x6f, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74,
	0x12, 0x29, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x55, 0x52, 0x49, 0x53, 0x63, 0x68,
	0x65, 0x6d, 0x65, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x0c, 0x68,
	0x74, 0x74, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0b, 0x68, 0x74, 0x74, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x73, 0x22, 0x36, 0x0a, 0x0a, 0x48, 0x54, 0x54, 0x50, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x8a, 0x01, 0x0a, 0x0b, 0x49,
	0x6e, 0x74, 0x4f, 0x72, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x2c, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x4f, 0x72, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x6e, 0x74, 0x5f,
	0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x6e, 0x74, 0x56, 0x61,
	0x6c, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x74, 0x72, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x72, 0x56, 0x61, 0x6c, 0x22, 0x1b, 0x0a, 0x04, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x07, 0x0a, 0x03, 0x69, 0x6e, 0x74, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x73,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x10, 0x01, 0x22, 0x4e, 0x0a, 0x0f, 0x54, 0x43, 0x50, 0x53, 0x6f,
	0x63, 0x6b, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x6f,
	0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x4f, 0x72, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x04, 0x70,
	0x6f, 0x72, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x22, 0x81, 0x02, 0x0a, 0x14, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x12, 0x37, 0x0a, 0x0e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0d, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x3b, 0x0a, 0x10, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x70, 0x75, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x70, 0x75, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x67, 0x70, 0x75, 0x43, 0x61, 0x72, 0x64, 0x73, 0x18, 0x11, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x08, 0x67, 0x70, 0x75, 0x43, 0x61, 0x72, 0x64, 0x73, 0x12, 0x3d, 0x0a, 0x0c,
	0x61, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x73, 0x63, 0x65,
	0x6e, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x66, 0x67, 0x52, 0x0c, 0x61,
	0x73, 0x63, 0x65, 0x6e, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x65, 0x0a, 0x11, 0x41,
	0x73, 0x63, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x66, 0x67,
	0x12, 0x19, 0x0a, 0x08, 0x6e, 0x70, 0x75, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6e, 0x70, 0x75, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x63, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x63,
	0x6e, 0x74, 0x22, 0x51, 0x0a, 0x0f, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x76, 0x69, 0x6c, 0x65,
	0x67, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x70, 0x72, 0x69, 0x76, 0x69,
	0x6c, 0x65, 0x67, 0x65, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x72, 0x75, 0x6e, 0x5f, 0x61, 0x73, 0x5f,
	0x75, 0x73, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x72, 0x75, 0x6e, 0x41,
	0x73, 0x55, 0x73, 0x65, 0x72, 0x22, 0x8b, 0x01, 0x0a, 0x08, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x70, 0x75, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x03, 0x63, 0x70, 0x75, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x19, 0x0a, 0x08,
	0x67, 0x70, 0x75, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x67, 0x70, 0x75, 0x43, 0x6f, 0x72, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x70, 0x75, 0x5f, 0x6d,
	0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x67, 0x70, 0x75,
	0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x70, 0x75, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x67, 0x70, 0x75, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0x28, 0x0a, 0x0c, 0x50, 0x76, 0x63, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x43, 0x66, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x76, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x76, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x27, 0x0a,
	0x11, 0x48, 0x6f, 0x73, 0x74, 0x50, 0x61, 0x74, 0x68, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43,
	0x66, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x22, 0x32, 0x0a, 0x0a, 0x4b, 0x65, 0x79, 0x50, 0x61, 0x74,
	0x68, 0x43, 0x66, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x22, 0x2b, 0x0a, 0x11, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x44, 0x69, 0x72, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x66, 0x67, 0x12,
	0x16, 0x0a, 0x06, 0x6d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x22, 0xe2, 0x01, 0x0a, 0x12, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x4d, 0x61, 0x70, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x66, 0x67, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x50, 0x61, 0x74, 0x68, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4b, 0x65,
	0x79, 0x50, 0x61, 0x74, 0x68, 0x43, 0x66, 0x67, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x50, 0x61, 0x74,
	0x68, 0x73, 0x12, 0x25, 0x0a, 0x0b, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x0b, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x37, 0x0a, 0x15, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x13, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x88,
	0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x9d, 0x03, 0x0a,
	0x09, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x66, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x38,
	0x0a, 0x0b, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x4c, 0x4f,
	0x70, 0x73, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x76, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x32, 0x0a, 0x07, 0x70, 0x76, 0x63, 0x5f,
	0x63, 0x66, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x76, 0x63, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x66, 0x67, 0x48,
	0x00, 0x52, 0x06, 0x70, 0x76, 0x63, 0x43, 0x66, 0x67, 0x88, 0x01, 0x01, 0x12, 0x42, 0x0a, 0x0d,
	0x65, 0x6d, 0x70, 0x74, 0x79, 0x5f, 0x64, 0x69, 0x72, 0x5f, 0x63, 0x66, 0x67, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x44, 0x69, 0x72, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x66, 0x67, 0x48, 0x01,
	0x52, 0x0b, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x44, 0x69, 0x72, 0x43, 0x66, 0x67, 0x88, 0x01, 0x01,
	0x12, 0x45, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x6d, 0x61, 0x70, 0x5f, 0x63,
	0x66, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x56, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x43, 0x66, 0x67, 0x48, 0x02, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61,
	0x70, 0x43, 0x66, 0x67, 0x88, 0x01, 0x01, 0x12, 0x41, 0x0a, 0x0c, 0x68, 0x6f, 0x73, 0x74, 0x70,
	0x61, 0x74, 0x68, 0x5f, 0x63, 0x66, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x50, 0x61, 0x74, 0x68, 0x56,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x66, 0x67, 0x48, 0x03, 0x52, 0x0b, 0x68, 0x6f, 0x73, 0x74,
	0x70, 0x61, 0x74, 0x68, 0x43, 0x66, 0x67, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x70,
	0x76, 0x63, 0x5f, 0x63, 0x66, 0x67, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x65, 0x6d, 0x70, 0x74, 0x79,
	0x5f, 0x64, 0x69, 0x72, 0x5f, 0x63, 0x66, 0x67, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x6d, 0x61, 0x70, 0x5f, 0x63, 0x66, 0x67, 0x42, 0x0f, 0x0a, 0x0d, 0x5f,
	0x68, 0x6f, 0x73, 0x74, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x63, 0x66, 0x67, 0x22, 0x83, 0x01, 0x0a,
	0x09, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75,
	0x62, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75,
	0x62, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x6f, 0x6e,
	0x6c, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x61, 0x64, 0x4f, 0x6e,
	0x6c, 0x79, 0x22, 0xca, 0x01, 0x0a, 0x08, 0x50, 0x6f, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x66,
	0x69, 0x72, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x69, 0x72, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6c, 0x61,
	0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x66,
	0x72, 0x6f, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x2a,
	0x20, 0x0a, 0x09, 0x55, 0x52, 0x49, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x08, 0x0a, 0x04,
	0x48, 0x54, 0x54, 0x50, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x48, 0x54, 0x54, 0x50, 0x53, 0x10,
	0x01, 0x2a, 0x44, 0x0a, 0x0d, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x45, 0x52, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x41, 0x49, 0x4e, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x43,
	0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x49,
	0x44, 0x45, 0x43, 0x41, 0x52, 0x10, 0x01, 0x2a, 0x9e, 0x02, 0x0a, 0x0f, 0x4d, 0x4c, 0x4f, 0x70,
	0x73, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x4d,
	0x4c, 0x4f, 0x50, 0x53, 0x5f, 0x46, 0x69, 0x6c, 0x65, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x48,
	0x4f, 0x53, 0x54, 0x5f, 0x50, 0x41, 0x54, 0x48, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x4d, 0x4c,
	0x4f, 0x50, 0x53, 0x5f, 0x46, 0x69, 0x6c, 0x65, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x49,
	0x4c, 0x45, 0x5f, 0x53, 0x59, 0x53, 0x54, 0x45, 0x4d, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x4d,
	0x4c, 0x4f, 0x50, 0x53, 0x5f, 0x46, 0x69, 0x6c, 0x65, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d,
	0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x43, 0x55, 0x42, 0x45, 0x10, 0x02, 0x12, 0x1f, 0x0a, 0x1b, 0x4d,
	0x4c, 0x4f, 0x50, 0x53, 0x5f, 0x46, 0x69, 0x6c, 0x65, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53,
	0x41, 0x4d, 0x50, 0x4c, 0x45, 0x5f, 0x43, 0x55, 0x42, 0x45, 0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16,
	0x4d, 0x4c, 0x4f, 0x50, 0x53, 0x5f, 0x46, 0x69, 0x6c, 0x65, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x4d, 0x45, 0x4d, 0x4f, 0x52, 0x59, 0x10, 0x04, 0x12, 0x17, 0x0a, 0x13, 0x4d, 0x4c, 0x4f, 0x50,
	0x53, 0x5f, 0x46, 0x69, 0x6c, 0x65, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x46, 0x53, 0x10,
	0x05, 0x12, 0x17, 0x0a, 0x13, 0x4d, 0x4c, 0x4f, 0x50, 0x53, 0x5f, 0x46, 0x69, 0x6c, 0x65, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x56, 0x43, 0x10, 0x06, 0x12, 0x1e, 0x0a, 0x1a, 0x4d, 0x4c,
	0x4f, 0x50, 0x53, 0x5f, 0x46, 0x69, 0x6c, 0x65, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4f,
	0x4e, 0x46, 0x49, 0x47, 0x5f, 0x4d, 0x41, 0x50, 0x10, 0x07, 0x12, 0x1c, 0x0a, 0x18, 0x4d, 0x4c,
	0x4f, 0x50, 0x53, 0x5f, 0x46, 0x69, 0x6c, 0x65, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x4d,
	0x50, 0x54, 0x59, 0x44, 0x49, 0x52, 0x10, 0x08, 0x42, 0x31, 0x5a, 0x2f, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c, 0x6d,
	0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_proto_common_k8s_resource_proto_rawDescOnce sync.Once
	file_proto_common_k8s_resource_proto_rawDescData = file_proto_common_k8s_resource_proto_rawDesc
)

func file_proto_common_k8s_resource_proto_rawDescGZIP() []byte {
	file_proto_common_k8s_resource_proto_rawDescOnce.Do(func() {
		file_proto_common_k8s_resource_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_common_k8s_resource_proto_rawDescData)
	})
	return file_proto_common_k8s_resource_proto_rawDescData
}

var file_proto_common_k8s_resource_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_proto_common_k8s_resource_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_proto_common_k8s_resource_proto_goTypes = []interface{}{
	(URIScheme)(0),                 // 0: common.URIScheme
	(ContainerType)(0),             // 1: common.ContainerType
	(MLOpsVolumeType)(0),           // 2: common.MLOpsVolumeType
	(Container_ImagePullPolicy)(0), // 3: common.Container.ImagePullPolicy
	(IntOrString_Type)(0),          // 4: common.IntOrString.Type
	(*Pod)(nil),                    // 5: common.Pod
	(*Container)(nil),              // 6: common.Container
	(*Probe)(nil),                  // 7: common.Probe
	(*Lifecycle)(nil),              // 8: common.Lifecycle
	(*Handler)(nil),                // 9: common.Handler
	(*ExecAction)(nil),             // 10: common.ExecAction
	(*HTTPGetAction)(nil),          // 11: common.HTTPGetAction
	(*HTTPHeader)(nil),             // 12: common.HTTPHeader
	(*IntOrString)(nil),            // 13: common.IntOrString
	(*TCPSocketAction)(nil),        // 14: common.TCPSocketAction
	(*ResourceRequirements)(nil),   // 15: common.ResourceRequirements
	(*AscendResourceCfg)(nil),      // 16: common.AscendResourceCfg
	(*SecurityContext)(nil),        // 17: common.SecurityContext
	(*Resource)(nil),               // 18: common.Resource
	(*PvcVolumeCfg)(nil),           // 19: common.PvcVolumeCfg
	(*HostPathVolumeCfg)(nil),      // 20: common.HostPathVolumeCfg
	(*KeyPathCfg)(nil),             // 21: common.KeyPathCfg
	(*EmptyDirVolumeCfg)(nil),      // 22: common.EmptyDirVolumeCfg
	(*ConfigMapVolumeCfg)(nil),     // 23: common.ConfigMapVolumeCfg
	(*VolumeCfg)(nil),              // 24: common.VolumeCfg
	(*MountPath)(nil),              // 25: common.MountPath
	(*PodEvent)(nil),               // 26: common.PodEvent
	nil,                            // 27: common.Container.EnvsEntry
}
var file_proto_common_k8s_resource_proto_depIdxs = []int32{
	6,  // 0: common.Pod.containers:type_name -> common.Container
	24, // 1: common.Pod.volume:type_name -> common.VolumeCfg
	1,  // 2: common.Container.container_type:type_name -> common.ContainerType
	15, // 3: common.Container.resource_requirements:type_name -> common.ResourceRequirements
	27, // 4: common.Container.envs:type_name -> common.Container.EnvsEntry
	25, // 5: common.Container.mount_paths:type_name -> common.MountPath
	3,  // 6: common.Container.image_pull_policy:type_name -> common.Container.ImagePullPolicy
	17, // 7: common.Container.security_context:type_name -> common.SecurityContext
	8,  // 8: common.Container.lifecycle:type_name -> common.Lifecycle
	7,  // 9: common.Container.livenessProbe:type_name -> common.Probe
	7,  // 10: common.Container.readinessProbe:type_name -> common.Probe
	9,  // 11: common.Probe.handler:type_name -> common.Handler
	9,  // 12: common.Lifecycle.post_start:type_name -> common.Handler
	9,  // 13: common.Lifecycle.pre_stop:type_name -> common.Handler
	10, // 14: common.Handler.exec:type_name -> common.ExecAction
	11, // 15: common.Handler.http_get:type_name -> common.HTTPGetAction
	14, // 16: common.Handler.tcp_socket:type_name -> common.TCPSocketAction
	13, // 17: common.HTTPGetAction.port:type_name -> common.IntOrString
	0,  // 18: common.HTTPGetAction.scheme:type_name -> common.URIScheme
	12, // 19: common.HTTPGetAction.http_headers:type_name -> common.HTTPHeader
	4,  // 20: common.IntOrString.type:type_name -> common.IntOrString.Type
	13, // 21: common.TCPSocketAction.port:type_name -> common.IntOrString
	18, // 22: common.ResourceRequirements.resource_limit:type_name -> common.Resource
	18, // 23: common.ResourceRequirements.resource_request:type_name -> common.Resource
	16, // 24: common.ResourceRequirements.ascendConfig:type_name -> common.AscendResourceCfg
	21, // 25: common.ConfigMapVolumeCfg.keyPaths:type_name -> common.KeyPathCfg
	2,  // 26: common.VolumeCfg.volume_type:type_name -> common.MLOpsVolumeType
	19, // 27: common.VolumeCfg.pvc_cfg:type_name -> common.PvcVolumeCfg
	22, // 28: common.VolumeCfg.empty_dir_cfg:type_name -> common.EmptyDirVolumeCfg
	23, // 29: common.VolumeCfg.config_map_cfg:type_name -> common.ConfigMapVolumeCfg
	20, // 30: common.VolumeCfg.hostpath_cfg:type_name -> common.HostPathVolumeCfg
	31, // [31:31] is the sub-list for method output_type
	31, // [31:31] is the sub-list for method input_type
	31, // [31:31] is the sub-list for extension type_name
	31, // [31:31] is the sub-list for extension extendee
	0,  // [0:31] is the sub-list for field type_name
}

func init() { file_proto_common_k8s_resource_proto_init() }
func file_proto_common_k8s_resource_proto_init() {
	if File_proto_common_k8s_resource_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_common_k8s_resource_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Pod); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_k8s_resource_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Container); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_k8s_resource_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Probe); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_k8s_resource_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Lifecycle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_k8s_resource_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Handler); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_k8s_resource_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecAction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_k8s_resource_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HTTPGetAction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_k8s_resource_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HTTPHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_k8s_resource_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IntOrString); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_k8s_resource_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TCPSocketAction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_k8s_resource_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceRequirements); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_k8s_resource_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AscendResourceCfg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_k8s_resource_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SecurityContext); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_k8s_resource_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Resource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_k8s_resource_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PvcVolumeCfg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_k8s_resource_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HostPathVolumeCfg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_k8s_resource_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KeyPathCfg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_k8s_resource_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmptyDirVolumeCfg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_k8s_resource_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigMapVolumeCfg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_k8s_resource_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VolumeCfg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_k8s_resource_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MountPath); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_common_k8s_resource_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PodEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_proto_common_k8s_resource_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_proto_common_k8s_resource_proto_msgTypes[18].OneofWrappers = []interface{}{}
	file_proto_common_k8s_resource_proto_msgTypes[19].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_common_k8s_resource_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_common_k8s_resource_proto_goTypes,
		DependencyIndexes: file_proto_common_k8s_resource_proto_depIdxs,
		EnumInfos:         file_proto_common_k8s_resource_proto_enumTypes,
		MessageInfos:      file_proto_common_k8s_resource_proto_msgTypes,
	}.Build()
	File_proto_common_k8s_resource_proto = out.File
	file_proto_common_k8s_resource_proto_rawDesc = nil
	file_proto_common_k8s_resource_proto_goTypes = nil
	file_proto_common_k8s_resource_proto_depIdxs = nil
}
