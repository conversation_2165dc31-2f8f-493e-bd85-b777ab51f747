// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/quantization.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	serving "transwarp.io/aip/llmops-common/pb/serving"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type QuantStrategy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name   string          `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Desc   string          `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	Params []*DynamicParam `protobuf:"bytes,3,rep,name=params,proto3" json:"params,omitempty"`
	Effect string          `protobuf:"bytes,4,opt,name=effect,proto3" json:"effect,omitempty"`
}

func (x *QuantStrategy) Reset() {
	*x = QuantStrategy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_quantization_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuantStrategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuantStrategy) ProtoMessage() {}

func (x *QuantStrategy) ProtoReflect() protoreflect.Message {
	mi := &file_proto_quantization_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuantStrategy.ProtoReflect.Descriptor instead.
func (*QuantStrategy) Descriptor() ([]byte, []int) {
	return file_proto_quantization_proto_rawDescGZIP(), []int{0}
}

func (x *QuantStrategy) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *QuantStrategy) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *QuantStrategy) GetParams() []*DynamicParam {
	if x != nil {
		return x.Params
	}
	return nil
}

func (x *QuantStrategy) GetEffect() string {
	if x != nil {
		return x.Effect
	}
	return ""
}

type ReadQuantStrategiesRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Strategies []*QuantStrategy `protobuf:"bytes,1,rep,name=strategies,proto3" json:"strategies,omitempty"`
}

func (x *ReadQuantStrategiesRsp) Reset() {
	*x = ReadQuantStrategiesRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_quantization_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadQuantStrategiesRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadQuantStrategiesRsp) ProtoMessage() {}

func (x *ReadQuantStrategiesRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_quantization_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadQuantStrategiesRsp.ProtoReflect.Descriptor instead.
func (*ReadQuantStrategiesRsp) Descriptor() ([]byte, []int) {
	return file_proto_quantization_proto_rawDescGZIP(), []int{1}
}

func (x *ReadQuantStrategiesRsp) GetStrategies() []*QuantStrategy {
	if x != nil {
		return x.Strategies
	}
	return nil
}

type QuantizationConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId             string                 `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ReleaseId           string                 `protobuf:"bytes,2,opt,name=release_id,json=releaseId,proto3" json:"release_id,omitempty"`
	ModelName           string                 `protobuf:"bytes,3,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
	ReleaseName         string                 `protobuf:"bytes,4,opt,name=release_name,json=releaseName,proto3" json:"release_name,omitempty"`
	ModelSize           int64                  `protobuf:"varint,5,opt,name=model_size,json=modelSize,proto3" json:"model_size,omitempty"`
	Mode                string                 `protobuf:"bytes,6,opt,name=mode,proto3" json:"mode,omitempty"`
	Strategy            string                 `protobuf:"bytes,7,opt,name=strategy,proto3" json:"strategy,omitempty"`
	Resource            *DeployResource        `protobuf:"bytes,8,opt,name=resource,proto3" json:"resource,omitempty"`
	Params              []*DynamicParam        `protobuf:"bytes,9,rep,name=params,proto3" json:"params,omitempty"`
	TemplateId          string                 `protobuf:"bytes,10,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	ModelRepo           string                 `protobuf:"bytes,11,opt,name=model_repo,json=modelRepo,proto3" json:"model_repo,omitempty"`
	ResultPath          string                 `protobuf:"bytes,12,opt,name=result_path,json=resultPath,proto3" json:"result_path,omitempty"`
	UnifyResourceConfig *serving.UnifyResource `protobuf:"bytes,13,opt,name=unify_resource_config,json=unifyResourceConfig,proto3" json:"unify_resource_config,omitempty"`
}

func (x *QuantizationConfig) Reset() {
	*x = QuantizationConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_quantization_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuantizationConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuantizationConfig) ProtoMessage() {}

func (x *QuantizationConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_quantization_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuantizationConfig.ProtoReflect.Descriptor instead.
func (*QuantizationConfig) Descriptor() ([]byte, []int) {
	return file_proto_quantization_proto_rawDescGZIP(), []int{2}
}

func (x *QuantizationConfig) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *QuantizationConfig) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

func (x *QuantizationConfig) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *QuantizationConfig) GetReleaseName() string {
	if x != nil {
		return x.ReleaseName
	}
	return ""
}

func (x *QuantizationConfig) GetModelSize() int64 {
	if x != nil {
		return x.ModelSize
	}
	return 0
}

func (x *QuantizationConfig) GetMode() string {
	if x != nil {
		return x.Mode
	}
	return ""
}

func (x *QuantizationConfig) GetStrategy() string {
	if x != nil {
		return x.Strategy
	}
	return ""
}

func (x *QuantizationConfig) GetResource() *DeployResource {
	if x != nil {
		return x.Resource
	}
	return nil
}

func (x *QuantizationConfig) GetParams() []*DynamicParam {
	if x != nil {
		return x.Params
	}
	return nil
}

func (x *QuantizationConfig) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *QuantizationConfig) GetModelRepo() string {
	if x != nil {
		return x.ModelRepo
	}
	return ""
}

func (x *QuantizationConfig) GetResultPath() string {
	if x != nil {
		return x.ResultPath
	}
	return ""
}

func (x *QuantizationConfig) GetUnifyResourceConfig() *serving.UnifyResource {
	if x != nil {
		return x.UnifyResourceConfig
	}
	return nil
}

type QuantizationResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResultPath string `protobuf:"bytes,1,opt,name=result_path,json=resultPath,proto3" json:"result_path,omitempty"`
	ResultSize int64  `protobuf:"varint,2,opt,name=result_size,json=resultSize,proto3" json:"result_size,omitempty"`
}

func (x *QuantizationResult) Reset() {
	*x = QuantizationResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_quantization_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuantizationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuantizationResult) ProtoMessage() {}

func (x *QuantizationResult) ProtoReflect() protoreflect.Message {
	mi := &file_proto_quantization_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuantizationResult.ProtoReflect.Descriptor instead.
func (*QuantizationResult) Descriptor() ([]byte, []int) {
	return file_proto_quantization_proto_rawDescGZIP(), []int{3}
}

func (x *QuantizationResult) GetResultPath() string {
	if x != nil {
		return x.ResultPath
	}
	return ""
}

func (x *QuantizationResult) GetResultSize() int64 {
	if x != nil {
		return x.ResultSize
	}
	return 0
}

type Quantization struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string              `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name        string              `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Config      *QuantizationConfig `protobuf:"bytes,3,opt,name=config,proto3" json:"config,omitempty"`
	RunId       string              `protobuf:"bytes,4,opt,name=run_id,json=runId,proto3" json:"run_id,omitempty"`
	Status      string              `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	Result      *QuantizationResult `protobuf:"bytes,6,opt,name=result,proto3" json:"result,omitempty"`
	ProjectId   string              `protobuf:"bytes,7,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	CreatedAt   int64               `protobuf:"varint,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt   int64               `protobuf:"varint,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	CreateUser  string              `protobuf:"bytes,10,opt,name=create_user,json=createUser,proto3" json:"create_user,omitempty"`
	StartTimeMs int64               `protobuf:"varint,11,opt,name=start_time_ms,json=startTimeMs,proto3" json:"start_time_ms,omitempty"`
	StopTimeMs  int64               `protobuf:"varint,12,opt,name=stop_time_ms,json=stopTimeMs,proto3" json:"stop_time_ms,omitempty"`
	Desc        string              `protobuf:"bytes,13,opt,name=desc,proto3" json:"desc,omitempty"`
	NodeId      string              `protobuf:"bytes,14,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
}

func (x *Quantization) Reset() {
	*x = Quantization{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_quantization_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Quantization) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Quantization) ProtoMessage() {}

func (x *Quantization) ProtoReflect() protoreflect.Message {
	mi := &file_proto_quantization_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Quantization.ProtoReflect.Descriptor instead.
func (*Quantization) Descriptor() ([]byte, []int) {
	return file_proto_quantization_proto_rawDescGZIP(), []int{4}
}

func (x *Quantization) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Quantization) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Quantization) GetConfig() *QuantizationConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *Quantization) GetRunId() string {
	if x != nil {
		return x.RunId
	}
	return ""
}

func (x *Quantization) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Quantization) GetResult() *QuantizationResult {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *Quantization) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *Quantization) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *Quantization) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *Quantization) GetCreateUser() string {
	if x != nil {
		return x.CreateUser
	}
	return ""
}

func (x *Quantization) GetStartTimeMs() int64 {
	if x != nil {
		return x.StartTimeMs
	}
	return 0
}

func (x *Quantization) GetStopTimeMs() int64 {
	if x != nil {
		return x.StopTimeMs
	}
	return 0
}

func (x *Quantization) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *Quantization) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

type ReadQuantizationsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Quantizations []*Quantization `protobuf:"bytes,1,rep,name=quantizations,proto3" json:"quantizations,omitempty"`
}

func (x *ReadQuantizationsRsp) Reset() {
	*x = ReadQuantizationsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_quantization_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadQuantizationsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadQuantizationsRsp) ProtoMessage() {}

func (x *ReadQuantizationsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_quantization_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadQuantizationsRsp.ProtoReflect.Descriptor instead.
func (*ReadQuantizationsRsp) Descriptor() ([]byte, []int) {
	return file_proto_quantization_proto_rawDescGZIP(), []int{5}
}

func (x *ReadQuantizationsRsp) GetQuantizations() []*Quantization {
	if x != nil {
		return x.Quantizations
	}
	return nil
}

var File_proto_quantization_proto protoreflect.FileDescriptor

var file_proto_quantization_proto_rawDesc = []byte{
	0x0a, 0x18, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x11, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x64, 0x79, 0x6e, 0x61,
	0x6d, 0x69, 0x63, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x21, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2f, 0x6d,
	0x6c, 0x6f, 0x70, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x7c, 0x0a, 0x0d, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x53, 0x74, 0x72, 0x61, 0x74,
	0x65, 0x67, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x2b, 0x0a, 0x06, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x66, 0x66, 0x65,
	0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74,
	0x22, 0x4e, 0x0a, 0x16, 0x52, 0x65, 0x61, 0x64, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x53, 0x74, 0x72,
	0x61, 0x74, 0x65, 0x67, 0x69, 0x65, 0x73, 0x52, 0x73, 0x70, 0x12, 0x34, 0x0a, 0x0a, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x65, 0x67, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x53, 0x74, 0x72, 0x61,
	0x74, 0x65, 0x67, 0x79, 0x52, 0x0a, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x69, 0x65, 0x73,
	0x22, 0xec, 0x03, 0x0a, 0x12, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65,
	0x67, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65,
	0x67, 0x79, 0x12, 0x31, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x08, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2b, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18,
	0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x79,
	0x6e, 0x61, 0x6d, 0x69, 0x63, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x70,
	0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65,
	0x70, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x50,
	0x61, 0x74, 0x68, 0x12, 0x4a, 0x0a, 0x15, 0x75, 0x6e, 0x69, 0x66, 0x79, 0x5f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x6e, 0x69,
	0x66, 0x79, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x13, 0x75, 0x6e, 0x69, 0x66,
	0x79, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22,
	0x56, 0x0a, 0x12, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xb8, 0x03, 0x0a, 0x0c, 0x51, 0x75, 0x61, 0x6e,
	0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x31, 0x0a, 0x06,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x15, 0x0a, 0x06, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x72, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x31,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12,
	0x22, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x4d, 0x73, 0x12, 0x20, 0x0a, 0x0c, 0x73, 0x74, 0x6f, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x6d, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x74, 0x6f, 0x70, 0x54,
	0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x6f, 0x64,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65,
	0x49, 0x64, 0x22, 0x51, 0x0a, 0x14, 0x52, 0x65, 0x61, 0x64, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x73, 0x70, 0x12, 0x39, 0x0a, 0x0d, 0x71, 0x75,
	0x61, 0x6e, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x26, 0x5a, 0x24, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61,
	0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73,
	0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_quantization_proto_rawDescOnce sync.Once
	file_proto_quantization_proto_rawDescData = file_proto_quantization_proto_rawDesc
)

func file_proto_quantization_proto_rawDescGZIP() []byte {
	file_proto_quantization_proto_rawDescOnce.Do(func() {
		file_proto_quantization_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_quantization_proto_rawDescData)
	})
	return file_proto_quantization_proto_rawDescData
}

var file_proto_quantization_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_proto_quantization_proto_goTypes = []interface{}{
	(*QuantStrategy)(nil),          // 0: proto.QuantStrategy
	(*ReadQuantStrategiesRsp)(nil), // 1: proto.ReadQuantStrategiesRsp
	(*QuantizationConfig)(nil),     // 2: proto.QuantizationConfig
	(*QuantizationResult)(nil),     // 3: proto.QuantizationResult
	(*Quantization)(nil),           // 4: proto.Quantization
	(*ReadQuantizationsRsp)(nil),   // 5: proto.ReadQuantizationsRsp
	(*DynamicParam)(nil),           // 6: proto.DynamicParam
	(*DeployResource)(nil),         // 7: proto.DeployResource
	(*serving.UnifyResource)(nil),  // 8: serving.UnifyResource
}
var file_proto_quantization_proto_depIdxs = []int32{
	6, // 0: proto.QuantStrategy.params:type_name -> proto.DynamicParam
	0, // 1: proto.ReadQuantStrategiesRsp.strategies:type_name -> proto.QuantStrategy
	7, // 2: proto.QuantizationConfig.resource:type_name -> proto.DeployResource
	6, // 3: proto.QuantizationConfig.params:type_name -> proto.DynamicParam
	8, // 4: proto.QuantizationConfig.unify_resource_config:type_name -> serving.UnifyResource
	2, // 5: proto.Quantization.config:type_name -> proto.QuantizationConfig
	3, // 6: proto.Quantization.result:type_name -> proto.QuantizationResult
	4, // 7: proto.ReadQuantizationsRsp.quantizations:type_name -> proto.Quantization
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_proto_quantization_proto_init() }
func file_proto_quantization_proto_init() {
	if File_proto_quantization_proto != nil {
		return
	}
	file_proto_model_proto_init()
	file_proto_dynamic_param_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_proto_quantization_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuantStrategy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_quantization_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadQuantStrategiesRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_quantization_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuantizationConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_quantization_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuantizationResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_quantization_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Quantization); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_quantization_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadQuantizationsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_quantization_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_quantization_proto_goTypes,
		DependencyIndexes: file_proto_quantization_proto_depIdxs,
		MessageInfos:      file_proto_quantization_proto_msgTypes,
	}.Build()
	File_proto_quantization_proto = out.File
	file_proto_quantization_proto_rawDesc = nil
	file_proto_quantization_proto_goTypes = nil
	file_proto_quantization_proto_depIdxs = nil
}
