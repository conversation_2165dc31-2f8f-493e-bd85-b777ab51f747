// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/rpc_knowledge_base.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	serving "transwarp.io/aip/llmops-common/pb/serving"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 文件上传或删除等操作来源
type FileOperationSource int32

const (
	// agent配置页面-本地文件上传
	FileOperationSource_AGENT_CONFIG FileOperationSource = 0
	// 知识库管理
	FileOperationSource_KB_MANAGE FileOperationSource = 1
)

// Enum value maps for FileOperationSource.
var (
	FileOperationSource_name = map[int32]string{
		0: "AGENT_CONFIG",
		1: "KB_MANAGE",
	}
	FileOperationSource_value = map[string]int32{
		"AGENT_CONFIG": 0,
		"KB_MANAGE":    1,
	}
)

func (x FileOperationSource) Enum() *FileOperationSource {
	p := new(FileOperationSource)
	*p = x
	return p
}

func (x FileOperationSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileOperationSource) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_rpc_knowledge_base_proto_enumTypes[0].Descriptor()
}

func (FileOperationSource) Type() protoreflect.EnumType {
	return &file_proto_rpc_knowledge_base_proto_enumTypes[0]
}

func (x FileOperationSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileOperationSource.Descriptor instead.
func (FileOperationSource) EnumDescriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{0}
}

type DocumentNodeCategory int32

const (
	// 目录
	DocumentNodeCategory_DIR DocumentNodeCategory = 0
	// 文件
	DocumentNodeCategory_FILE DocumentNodeCategory = 1
)

// Enum value maps for DocumentNodeCategory.
var (
	DocumentNodeCategory_name = map[int32]string{
		0: "DIR",
		1: "FILE",
	}
	DocumentNodeCategory_value = map[string]int32{
		"DIR":  0,
		"FILE": 1,
	}
)

func (x DocumentNodeCategory) Enum() *DocumentNodeCategory {
	p := new(DocumentNodeCategory)
	*p = x
	return p
}

func (x DocumentNodeCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DocumentNodeCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_rpc_knowledge_base_proto_enumTypes[1].Descriptor()
}

func (DocumentNodeCategory) Type() protoreflect.EnumType {
	return &file_proto_rpc_knowledge_base_proto_enumTypes[1]
}

func (x DocumentNodeCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DocumentNodeCategory.Descriptor instead.
func (DocumentNodeCategory) EnumDescriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{1}
}

type DocumentTaskStage int32

const (
	// @gotags: description:"排队中"
	DocumentTaskStage_PENDING DocumentTaskStage = 0
	// @gotags: description:"文档解析分段"
	DocumentTaskStage_LOAD_CHUNK DocumentTaskStage = 11
	// @gotags: description:"知识增强"
	DocumentTaskStage_KNOWLEDGE_AUGMENT DocumentTaskStage = 12
	// @gotags: description:"数据存储"
	DocumentTaskStage_DATA_STORE DocumentTaskStage = 2
	// @gotags: description:"索引构建"
	DocumentTaskStage_INDEX_BUILD DocumentTaskStage = 3
	// @gotags: description:"完成"
	DocumentTaskStage_DONE DocumentTaskStage = 4
)

// Enum value maps for DocumentTaskStage.
var (
	DocumentTaskStage_name = map[int32]string{
		0:  "PENDING",
		11: "LOAD_CHUNK",
		12: "KNOWLEDGE_AUGMENT",
		2:  "DATA_STORE",
		3:  "INDEX_BUILD",
		4:  "DONE",
	}
	DocumentTaskStage_value = map[string]int32{
		"PENDING":           0,
		"LOAD_CHUNK":        11,
		"KNOWLEDGE_AUGMENT": 12,
		"DATA_STORE":        2,
		"INDEX_BUILD":       3,
		"DONE":              4,
	}
)

func (x DocumentTaskStage) Enum() *DocumentTaskStage {
	p := new(DocumentTaskStage)
	*p = x
	return p
}

func (x DocumentTaskStage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DocumentTaskStage) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_rpc_knowledge_base_proto_enumTypes[2].Descriptor()
}

func (DocumentTaskStage) Type() protoreflect.EnumType {
	return &file_proto_rpc_knowledge_base_proto_enumTypes[2]
}

func (x DocumentTaskStage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DocumentTaskStage.Descriptor instead.
func (DocumentTaskStage) EnumDescriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{2}
}

type AppRelation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId   string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	AppName string `protobuf:"bytes,2,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
}

func (x *AppRelation) Reset() {
	*x = AppRelation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppRelation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppRelation) ProtoMessage() {}

func (x *AppRelation) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppRelation.ProtoReflect.Descriptor instead.
func (*AppRelation) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{0}
}

func (x *AppRelation) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *AppRelation) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

type HealthStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Healthy bool   `protobuf:"varint,1,opt,name=healthy,proto3" json:"healthy,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *HealthStatus) Reset() {
	*x = HealthStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HealthStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthStatus) ProtoMessage() {}

func (x *HealthStatus) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthStatus.ProtoReflect.Descriptor instead.
func (*HealthStatus) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{1}
}

func (x *HealthStatus) GetHealthy() bool {
	if x != nil {
		return x.Healthy
	}
	return false
}

func (x *HealthStatus) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 知识库的各项健康状态
type KnowledgeBaseHealthOverview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Healthy              bool          `protobuf:"varint,1,opt,name=healthy,proto3" json:"healthy,omitempty"`
	DocSvcHealth         *HealthStatus `protobuf:"bytes,2,opt,name=doc_svc_health,json=docSvcHealth,proto3" json:"doc_svc_health,omitempty"`
	HippoHealth          *HealthStatus `protobuf:"bytes,3,opt,name=hippo_health,json=hippoHealth,proto3" json:"hippo_health,omitempty"`
	ScopeHealth          *HealthStatus `protobuf:"bytes,4,opt,name=scope_health,json=scopeHealth,proto3" json:"scope_health,omitempty"`
	EmbeddingHealth      *HealthStatus `protobuf:"bytes,5,opt,name=embedding_health,json=embeddingHealth,proto3" json:"embedding_health,omitempty"`
	RerankHealth         *HealthStatus `protobuf:"bytes,6,opt,name=rerank_health,json=rerankHealth,proto3" json:"rerank_health,omitempty"`
	AugmentHealth        *HealthStatus `protobuf:"bytes,7,opt,name=augment_health,json=augmentHealth,proto3" json:"augment_health,omitempty"`
	CustomStrategyHealth *HealthStatus `protobuf:"bytes,8,opt,name=custom_strategy_health,json=customStrategyHealth,proto3" json:"custom_strategy_health,omitempty"`
	ImageAugmentHealth   *HealthStatus `protobuf:"bytes,9,opt,name=image_augment_health,json=imageAugmentHealth,proto3" json:"image_augment_health,omitempty"`
	OcrLayoutHealth      *HealthStatus `protobuf:"bytes,10,opt,name=ocr_layout_health,json=ocrLayoutHealth,proto3" json:"ocr_layout_health,omitempty"`
	VlHealth             *HealthStatus `protobuf:"bytes,11,opt,name=vl_health,json=vlHealth,proto3" json:"vl_health,omitempty"`
}

func (x *KnowledgeBaseHealthOverview) Reset() {
	*x = KnowledgeBaseHealthOverview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KnowledgeBaseHealthOverview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnowledgeBaseHealthOverview) ProtoMessage() {}

func (x *KnowledgeBaseHealthOverview) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnowledgeBaseHealthOverview.ProtoReflect.Descriptor instead.
func (*KnowledgeBaseHealthOverview) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{2}
}

func (x *KnowledgeBaseHealthOverview) GetHealthy() bool {
	if x != nil {
		return x.Healthy
	}
	return false
}

func (x *KnowledgeBaseHealthOverview) GetDocSvcHealth() *HealthStatus {
	if x != nil {
		return x.DocSvcHealth
	}
	return nil
}

func (x *KnowledgeBaseHealthOverview) GetHippoHealth() *HealthStatus {
	if x != nil {
		return x.HippoHealth
	}
	return nil
}

func (x *KnowledgeBaseHealthOverview) GetScopeHealth() *HealthStatus {
	if x != nil {
		return x.ScopeHealth
	}
	return nil
}

func (x *KnowledgeBaseHealthOverview) GetEmbeddingHealth() *HealthStatus {
	if x != nil {
		return x.EmbeddingHealth
	}
	return nil
}

func (x *KnowledgeBaseHealthOverview) GetRerankHealth() *HealthStatus {
	if x != nil {
		return x.RerankHealth
	}
	return nil
}

func (x *KnowledgeBaseHealthOverview) GetAugmentHealth() *HealthStatus {
	if x != nil {
		return x.AugmentHealth
	}
	return nil
}

func (x *KnowledgeBaseHealthOverview) GetCustomStrategyHealth() *HealthStatus {
	if x != nil {
		return x.CustomStrategyHealth
	}
	return nil
}

func (x *KnowledgeBaseHealthOverview) GetImageAugmentHealth() *HealthStatus {
	if x != nil {
		return x.ImageAugmentHealth
	}
	return nil
}

func (x *KnowledgeBaseHealthOverview) GetOcrLayoutHealth() *HealthStatus {
	if x != nil {
		return x.OcrLayoutHealth
	}
	return nil
}

func (x *KnowledgeBaseHealthOverview) GetVlHealth() *HealthStatus {
	if x != nil {
		return x.VlHealth
	}
	return nil
}

type KnowledgeBaseInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	KnowledgeBase               *KnowledgeBase                  `protobuf:"bytes,1,opt,name=knowledge_base,json=knowledgeBase,proto3" json:"knowledge_base,omitempty"`
	NumDocs                     int32                           `protobuf:"varint,2,opt,name=num_docs,json=numDocs,proto3" json:"num_docs,omitempty"`
	NumApps                     int32                           `protobuf:"varint,3,opt,name=num_apps,json=numApps,proto3" json:"num_apps,omitempty"`
	AppRelations                []*AppRelation                  `protobuf:"bytes,4,rep,name=app_relations,json=appRelations,proto3" json:"app_relations,omitempty"`
	SupportedRetrieveStrategies []KnowledgeBaseRetrieveStrategy `protobuf:"varint,5,rep,packed,name=supported_retrieve_strategies,json=supportedRetrieveStrategies,proto3,enum=proto.KnowledgeBaseRetrieveStrategy" json:"supported_retrieve_strategies,omitempty"`
	Health                      *KnowledgeBaseHealthOverview    `protobuf:"bytes,6,opt,name=health,proto3" json:"health,omitempty"`
}

func (x *KnowledgeBaseInfo) Reset() {
	*x = KnowledgeBaseInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KnowledgeBaseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnowledgeBaseInfo) ProtoMessage() {}

func (x *KnowledgeBaseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnowledgeBaseInfo.ProtoReflect.Descriptor instead.
func (*KnowledgeBaseInfo) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{3}
}

func (x *KnowledgeBaseInfo) GetKnowledgeBase() *KnowledgeBase {
	if x != nil {
		return x.KnowledgeBase
	}
	return nil
}

func (x *KnowledgeBaseInfo) GetNumDocs() int32 {
	if x != nil {
		return x.NumDocs
	}
	return 0
}

func (x *KnowledgeBaseInfo) GetNumApps() int32 {
	if x != nil {
		return x.NumApps
	}
	return 0
}

func (x *KnowledgeBaseInfo) GetAppRelations() []*AppRelation {
	if x != nil {
		return x.AppRelations
	}
	return nil
}

func (x *KnowledgeBaseInfo) GetSupportedRetrieveStrategies() []KnowledgeBaseRetrieveStrategy {
	if x != nil {
		return x.SupportedRetrieveStrategies
	}
	return nil
}

func (x *KnowledgeBaseInfo) GetHealth() *KnowledgeBaseHealthOverview {
	if x != nil {
		return x.Health
	}
	return nil
}

type ListKnowledgeBasesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext  *UserContext                `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	ListSelector *ListKnowledgeBasesSelector `protobuf:"bytes,2,opt,name=list_selector,json=listSelector,proto3" json:"list_selector,omitempty"`
	// @gotags: description:"是否查询公共空间；查询公共空间时，额外查询所有项目的公共知识库，否则只查询当前项目下的知识库"
	IsPublic bool `protobuf:"varint,3,opt,name=is_public,json=isPublic,proto3" json:"is_public,omitempty" description:"是否查询公共空间；查询公共空间时，额外查询所有项目的公共知识库，否则只查询当前项目下的知识库"`
}

func (x *ListKnowledgeBasesReq) Reset() {
	*x = ListKnowledgeBasesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListKnowledgeBasesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListKnowledgeBasesReq) ProtoMessage() {}

func (x *ListKnowledgeBasesReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListKnowledgeBasesReq.ProtoReflect.Descriptor instead.
func (*ListKnowledgeBasesReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{4}
}

func (x *ListKnowledgeBasesReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *ListKnowledgeBasesReq) GetListSelector() *ListKnowledgeBasesSelector {
	if x != nil {
		return x.ListSelector
	}
	return nil
}

func (x *ListKnowledgeBasesReq) GetIsPublic() bool {
	if x != nil {
		return x.IsPublic
	}
	return false
}

type ListKnowledgeBasesSelector struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContentTypes  []KnowledgeBaseContentType  `protobuf:"varint,1,rep,packed,name=content_types,json=contentTypes,proto3,enum=proto.KnowledgeBaseContentType" json:"content_types,omitempty"`
	SourceTypes   []KnowledgeBaseSourceType   `protobuf:"varint,2,rep,packed,name=source_types,json=sourceTypes,proto3,enum=proto.KnowledgeBaseSourceType" json:"source_types,omitempty"`
	RegistryTypes []KnowledgeBaseRegistryType `protobuf:"varint,3,rep,packed,name=registry_types,json=registryTypes,proto3,enum=proto.KnowledgeBaseRegistryType" json:"registry_types,omitempty"`
	IsPublished   []bool                      `protobuf:"varint,4,rep,packed,name=is_published,json=isPublished,proto3" json:"is_published,omitempty"`
	SceneTypes    []KnowledgeBaseSceneType    `protobuf:"varint,5,rep,packed,name=scene_types,json=sceneTypes,proto3,enum=proto.KnowledgeBaseSceneType" json:"scene_types,omitempty"`
}

func (x *ListKnowledgeBasesSelector) Reset() {
	*x = ListKnowledgeBasesSelector{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListKnowledgeBasesSelector) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListKnowledgeBasesSelector) ProtoMessage() {}

func (x *ListKnowledgeBasesSelector) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListKnowledgeBasesSelector.ProtoReflect.Descriptor instead.
func (*ListKnowledgeBasesSelector) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{5}
}

func (x *ListKnowledgeBasesSelector) GetContentTypes() []KnowledgeBaseContentType {
	if x != nil {
		return x.ContentTypes
	}
	return nil
}

func (x *ListKnowledgeBasesSelector) GetSourceTypes() []KnowledgeBaseSourceType {
	if x != nil {
		return x.SourceTypes
	}
	return nil
}

func (x *ListKnowledgeBasesSelector) GetRegistryTypes() []KnowledgeBaseRegistryType {
	if x != nil {
		return x.RegistryTypes
	}
	return nil
}

func (x *ListKnowledgeBasesSelector) GetIsPublished() []bool {
	if x != nil {
		return x.IsPublished
	}
	return nil
}

func (x *ListKnowledgeBasesSelector) GetSceneTypes() []KnowledgeBaseSceneType {
	if x != nil {
		return x.SceneTypes
	}
	return nil
}

type ListKnowledgeBasesRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result []*KnowledgeBaseInfo `protobuf:"bytes,1,rep,name=result,proto3" json:"result,omitempty"`
}

func (x *ListKnowledgeBasesRsp) Reset() {
	*x = ListKnowledgeBasesRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListKnowledgeBasesRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListKnowledgeBasesRsp) ProtoMessage() {}

func (x *ListKnowledgeBasesRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListKnowledgeBasesRsp.ProtoReflect.Descriptor instead.
func (*ListKnowledgeBasesRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{6}
}

func (x *ListKnowledgeBasesRsp) GetResult() []*KnowledgeBaseInfo {
	if x != nil {
		return x.Result
	}
	return nil
}

type GetKnowledgeBaseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext *UserContext `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	Id          string       `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetKnowledgeBaseReq) Reset() {
	*x = GetKnowledgeBaseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetKnowledgeBaseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKnowledgeBaseReq) ProtoMessage() {}

func (x *GetKnowledgeBaseReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKnowledgeBaseReq.ProtoReflect.Descriptor instead.
func (*GetKnowledgeBaseReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{7}
}

func (x *GetKnowledgeBaseReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *GetKnowledgeBaseReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetKnowledgeBaseRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *KnowledgeBaseInfo `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *GetKnowledgeBaseRsp) Reset() {
	*x = GetKnowledgeBaseRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetKnowledgeBaseRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKnowledgeBaseRsp) ProtoMessage() {}

func (x *GetKnowledgeBaseRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKnowledgeBaseRsp.ProtoReflect.Descriptor instead.
func (*GetKnowledgeBaseRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{8}
}

func (x *GetKnowledgeBaseRsp) GetResult() *KnowledgeBaseInfo {
	if x != nil {
		return x.Result
	}
	return nil
}

type CreateKnowledgeBaseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext *UserContext   `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	Base        *KnowledgeBase `protobuf:"bytes,2,opt,name=base,proto3" json:"base,omitempty"`
}

func (x *CreateKnowledgeBaseReq) Reset() {
	*x = CreateKnowledgeBaseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateKnowledgeBaseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateKnowledgeBaseReq) ProtoMessage() {}

func (x *CreateKnowledgeBaseReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateKnowledgeBaseReq.ProtoReflect.Descriptor instead.
func (*CreateKnowledgeBaseReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{9}
}

func (x *CreateKnowledgeBaseReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *CreateKnowledgeBaseReq) GetBase() *KnowledgeBase {
	if x != nil {
		return x.Base
	}
	return nil
}

type CreateKnowledgeBaseRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *KnowledgeBase `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *CreateKnowledgeBaseRsp) Reset() {
	*x = CreateKnowledgeBaseRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateKnowledgeBaseRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateKnowledgeBaseRsp) ProtoMessage() {}

func (x *CreateKnowledgeBaseRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateKnowledgeBaseRsp.ProtoReflect.Descriptor instead.
func (*CreateKnowledgeBaseRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{10}
}

func (x *CreateKnowledgeBaseRsp) GetResult() *KnowledgeBase {
	if x != nil {
		return x.Result
	}
	return nil
}

type UpdateKnowledgeBaseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext *UserContext   `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	Base        *KnowledgeBase `protobuf:"bytes,2,opt,name=base,proto3" json:"base,omitempty"`
}

func (x *UpdateKnowledgeBaseReq) Reset() {
	*x = UpdateKnowledgeBaseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateKnowledgeBaseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateKnowledgeBaseReq) ProtoMessage() {}

func (x *UpdateKnowledgeBaseReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateKnowledgeBaseReq.ProtoReflect.Descriptor instead.
func (*UpdateKnowledgeBaseReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateKnowledgeBaseReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *UpdateKnowledgeBaseReq) GetBase() *KnowledgeBase {
	if x != nil {
		return x.Base
	}
	return nil
}

type UpdateKnowledgeBaseRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *KnowledgeBase `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *UpdateKnowledgeBaseRsp) Reset() {
	*x = UpdateKnowledgeBaseRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateKnowledgeBaseRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateKnowledgeBaseRsp) ProtoMessage() {}

func (x *UpdateKnowledgeBaseRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateKnowledgeBaseRsp.ProtoReflect.Descriptor instead.
func (*UpdateKnowledgeBaseRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateKnowledgeBaseRsp) GetResult() *KnowledgeBase {
	if x != nil {
		return x.Result
	}
	return nil
}

type DeleteKnowledgeBasesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext *UserContext `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	Ids         []string     `protobuf:"bytes,2,rep,name=ids,proto3" json:"ids,omitempty"` // 可同时删除多个
}

func (x *DeleteKnowledgeBasesReq) Reset() {
	*x = DeleteKnowledgeBasesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteKnowledgeBasesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteKnowledgeBasesReq) ProtoMessage() {}

func (x *DeleteKnowledgeBasesReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteKnowledgeBasesReq.ProtoReflect.Descriptor instead.
func (*DeleteKnowledgeBasesReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{13}
}

func (x *DeleteKnowledgeBasesReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *DeleteKnowledgeBasesReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type DeleteKnowledgeBasesRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteKnowledgeBasesRsp) Reset() {
	*x = DeleteKnowledgeBasesRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteKnowledgeBasesRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteKnowledgeBasesRsp) ProtoMessage() {}

func (x *DeleteKnowledgeBasesRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteKnowledgeBasesRsp.ProtoReflect.Descriptor instead.
func (*DeleteKnowledgeBasesRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{14}
}

type RetrieveKnowledgeBaseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext *UserContext `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	// @gotags: description:"知识库id"
	KnowledgeBaseId string `protobuf:"bytes,2,opt,name=knowledge_base_id,json=knowledgeBaseId,proto3" json:"knowledge_base_id,omitempty" description:"知识库id"`
	// @gotags: description:"查询内容"
	Query string `protobuf:"bytes,3,opt,name=query,proto3" json:"query,omitempty" description:"查询内容"`
	// @gotags: description:"检索的文档范围，文档id列表"
	DocRange []string `protobuf:"bytes,4,rep,name=doc_range,json=docRange,proto3" json:"doc_range,omitempty" description:"检索的文档范围，文档id列表"`
	// @gotags: description:"检索配置"
	RetrievalConfig *RetrievalConfig `protobuf:"bytes,5,opt,name=retrieval_config,json=retrievalConfig,proto3" json:"retrieval_config,omitempty" description:"检索配置"`
	// @gotags: description:"额外的开关，是否关闭Rerank"
	DisableRerank bool `protobuf:"varint,6,opt,name=disable_rerank,json=disableRerank,proto3" json:"disable_rerank,omitempty" description:"额外的开关，是否关闭Rerank"`
}

func (x *RetrieveKnowledgeBaseReq) Reset() {
	*x = RetrieveKnowledgeBaseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrieveKnowledgeBaseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrieveKnowledgeBaseReq) ProtoMessage() {}

func (x *RetrieveKnowledgeBaseReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrieveKnowledgeBaseReq.ProtoReflect.Descriptor instead.
func (*RetrieveKnowledgeBaseReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{15}
}

func (x *RetrieveKnowledgeBaseReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *RetrieveKnowledgeBaseReq) GetKnowledgeBaseId() string {
	if x != nil {
		return x.KnowledgeBaseId
	}
	return ""
}

func (x *RetrieveKnowledgeBaseReq) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *RetrieveKnowledgeBaseReq) GetDocRange() []string {
	if x != nil {
		return x.DocRange
	}
	return nil
}

func (x *RetrieveKnowledgeBaseReq) GetRetrievalConfig() *RetrievalConfig {
	if x != nil {
		return x.RetrievalConfig
	}
	return nil
}

func (x *RetrieveKnowledgeBaseReq) GetDisableRerank() bool {
	if x != nil {
		return x.DisableRerank
	}
	return false
}

type Hit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"是否命中"
	Hit bool `protobuf:"varint,1,opt,name=hit,proto3" json:"hit,omitempty" description:"是否命中"`
	// @gotags: description:"分数"
	Score float32 `protobuf:"fixed32,2,opt,name=score,proto3" json:"score,omitempty" description:"分数"`
}

func (x *Hit) Reset() {
	*x = Hit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Hit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Hit) ProtoMessage() {}

func (x *Hit) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Hit.ProtoReflect.Descriptor instead.
func (*Hit) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{16}
}

func (x *Hit) GetHit() bool {
	if x != nil {
		return x.Hit
	}
	return false
}

func (x *Hit) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

type RetrieveKnowledgeBaseRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"原检索请求"
	Request *RetrieveKnowledgeBaseReq `protobuf:"bytes,1,opt,name=request,proto3" json:"request,omitempty" description:"原检索请求"`
	// @gotags: description:"分段结果列表"
	Result []*ChunkRetrieveResult `protobuf:"bytes,2,rep,name=result,proto3" json:"result,omitempty" description:"分段结果列表"`
	// @gotags: description:"向量索引召回命中情况, key为chunk id, key不存在表示未命中"
	VectorIndexHits map[string]*Hit `protobuf:"bytes,3,rep,name=vector_index_hits,json=vectorIndexHits,proto3" json:"vector_index_hits,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"向量索引召回命中情况, key为chunk id, key不存在表示未命中"`
	// @gotags: description:"全文索引召回命中情况, key为chunk id, key不存在表示未命中"
	FullTextIndexHits map[string]*Hit `protobuf:"bytes,4,rep,name=full_text_index_hits,json=fullTextIndexHits,proto3" json:"full_text_index_hits,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" description:"全文索引召回命中情况, key为chunk id, key不存在表示未命中"`
}

func (x *RetrieveKnowledgeBaseRsp) Reset() {
	*x = RetrieveKnowledgeBaseRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrieveKnowledgeBaseRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrieveKnowledgeBaseRsp) ProtoMessage() {}

func (x *RetrieveKnowledgeBaseRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrieveKnowledgeBaseRsp.ProtoReflect.Descriptor instead.
func (*RetrieveKnowledgeBaseRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{17}
}

func (x *RetrieveKnowledgeBaseRsp) GetRequest() *RetrieveKnowledgeBaseReq {
	if x != nil {
		return x.Request
	}
	return nil
}

func (x *RetrieveKnowledgeBaseRsp) GetResult() []*ChunkRetrieveResult {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *RetrieveKnowledgeBaseRsp) GetVectorIndexHits() map[string]*Hit {
	if x != nil {
		return x.VectorIndexHits
	}
	return nil
}

func (x *RetrieveKnowledgeBaseRsp) GetFullTextIndexHits() map[string]*Hit {
	if x != nil {
		return x.FullTextIndexHits
	}
	return nil
}

type ChunkRetrieveResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Chunk *Chunk `protobuf:"bytes,1,opt,name=chunk,proto3" json:"chunk,omitempty"`
	// @gotags: description:"召回：原文与相关增强chunk命中里的最高score；重排序：原文score"
	Score             float32 `protobuf:"fixed32,2,opt,name=score,proto3" json:"score,omitempty" description:"召回：原文与相关增强chunk命中里的最高score；重排序：原文score"`
	KnowledgeBaseId   string  `protobuf:"bytes,4,opt,name=knowledge_base_id,json=knowledgeBaseId,proto3" json:"knowledge_base_id,omitempty"`
	DocId             string  `protobuf:"bytes,5,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	KnowledgeBaseName string  `protobuf:"bytes,6,opt,name=knowledge_base_name,json=knowledgeBaseName,proto3" json:"knowledge_base_name,omitempty"`
	DocName           string  `protobuf:"bytes,7,opt,name=doc_name,json=docName,proto3" json:"doc_name,omitempty"`
	// @gotags: description:"召回：chunk的前文切片"
	AboveChunks []*Chunk `protobuf:"bytes,8,rep,name=above_chunks,json=aboveChunks,proto3" json:"above_chunks,omitempty" description:"召回：chunk的前文切片"`
	// @gotags: description:"召回：chunk的后文切片"
	LaterChunks []*Chunk `protobuf:"bytes,9,rep,name=later_chunks,json=laterChunks,proto3" json:"later_chunks,omitempty" description:"召回：chunk的后文切片"`
}

func (x *ChunkRetrieveResult) Reset() {
	*x = ChunkRetrieveResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChunkRetrieveResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChunkRetrieveResult) ProtoMessage() {}

func (x *ChunkRetrieveResult) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChunkRetrieveResult.ProtoReflect.Descriptor instead.
func (*ChunkRetrieveResult) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{18}
}

func (x *ChunkRetrieveResult) GetChunk() *Chunk {
	if x != nil {
		return x.Chunk
	}
	return nil
}

func (x *ChunkRetrieveResult) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *ChunkRetrieveResult) GetKnowledgeBaseId() string {
	if x != nil {
		return x.KnowledgeBaseId
	}
	return ""
}

func (x *ChunkRetrieveResult) GetDocId() string {
	if x != nil {
		return x.DocId
	}
	return ""
}

func (x *ChunkRetrieveResult) GetKnowledgeBaseName() string {
	if x != nil {
		return x.KnowledgeBaseName
	}
	return ""
}

func (x *ChunkRetrieveResult) GetDocName() string {
	if x != nil {
		return x.DocName
	}
	return ""
}

func (x *ChunkRetrieveResult) GetAboveChunks() []*Chunk {
	if x != nil {
		return x.AboveChunks
	}
	return nil
}

func (x *ChunkRetrieveResult) GetLaterChunks() []*Chunk {
	if x != nil {
		return x.LaterChunks
	}
	return nil
}

type DocumentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"文档"
	Doc *Document `protobuf:"bytes,1,opt,name=doc,proto3" json:"doc,omitempty" description:"文档"`
	// @gotags: description:"召回次数（暂不实现）"
	NumRecall int64 `protobuf:"varint,2,opt,name=num_recall,json=numRecall,proto3" json:"num_recall,omitempty" description:"召回次数（暂不实现）"`
	// @gotags: description:"文档段落数"
	NumChunks int64 `protobuf:"varint,3,opt,name=num_chunks,json=numChunks,proto3" json:"num_chunks,omitempty" description:"文档段落数"`
	// @gotags: description:"文档处理进度"
	Prog *DocumentProcessingProgress `protobuf:"bytes,4,opt,name=prog,proto3" json:"prog,omitempty" description:"文档处理进度"`
	// @gotags: description:"文档处理策略"
	StrategyOrigin StrategyOrigin `protobuf:"varint,5,opt,name=strategy_origin,json=strategyOrigin,proto3,enum=proto.StrategyOrigin" json:"strategy_origin,omitempty" description:"文档处理策略"`
}

func (x *DocumentInfo) Reset() {
	*x = DocumentInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DocumentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocumentInfo) ProtoMessage() {}

func (x *DocumentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocumentInfo.ProtoReflect.Descriptor instead.
func (*DocumentInfo) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{19}
}

func (x *DocumentInfo) GetDoc() *Document {
	if x != nil {
		return x.Doc
	}
	return nil
}

func (x *DocumentInfo) GetNumRecall() int64 {
	if x != nil {
		return x.NumRecall
	}
	return 0
}

func (x *DocumentInfo) GetNumChunks() int64 {
	if x != nil {
		return x.NumChunks
	}
	return 0
}

func (x *DocumentInfo) GetProg() *DocumentProcessingProgress {
	if x != nil {
		return x.Prog
	}
	return nil
}

func (x *DocumentInfo) GetStrategyOrigin() StrategyOrigin {
	if x != nil {
		return x.StrategyOrigin
	}
	return StrategyOrigin_PRESET
}

type ListDocumentsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext     *UserContext `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	KnowledgeBaseId string       `protobuf:"bytes,2,opt,name=knowledge_base_id,json=knowledgeBaseId,proto3" json:"knowledge_base_id,omitempty"`
}

func (x *ListDocumentsReq) Reset() {
	*x = ListDocumentsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDocumentsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDocumentsReq) ProtoMessage() {}

func (x *ListDocumentsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDocumentsReq.ProtoReflect.Descriptor instead.
func (*ListDocumentsReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{20}
}

func (x *ListDocumentsReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *ListDocumentsReq) GetKnowledgeBaseId() string {
	if x != nil {
		return x.KnowledgeBaseId
	}
	return ""
}

type ListDocumentsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result []*DocumentInfo `protobuf:"bytes,1,rep,name=result,proto3" json:"result,omitempty"`
}

func (x *ListDocumentsRsp) Reset() {
	*x = ListDocumentsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDocumentsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDocumentsRsp) ProtoMessage() {}

func (x *ListDocumentsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDocumentsRsp.ProtoReflect.Descriptor instead.
func (*ListDocumentsRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{21}
}

func (x *ListDocumentsRsp) GetResult() []*DocumentInfo {
	if x != nil {
		return x.Result
	}
	return nil
}

type DisableDocumentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext     *UserContext `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	KnowledgeBaseId string       `protobuf:"bytes,2,opt,name=knowledge_base_id,json=knowledgeBaseId,proto3" json:"knowledge_base_id,omitempty"`
	DocId           string       `protobuf:"bytes,3,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	// @gotags: description:"调整知识库文档的启/禁用状态，调整后该文档是否被禁用"
	Disabled bool `protobuf:"varint,4,opt,name=disabled,proto3" json:"disabled,omitempty" description:"调整知识库文档的启/禁用状态，调整后该文档是否被禁用"`
}

func (x *DisableDocumentReq) Reset() {
	*x = DisableDocumentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisableDocumentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisableDocumentReq) ProtoMessage() {}

func (x *DisableDocumentReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisableDocumentReq.ProtoReflect.Descriptor instead.
func (*DisableDocumentReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{22}
}

func (x *DisableDocumentReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *DisableDocumentReq) GetKnowledgeBaseId() string {
	if x != nil {
		return x.KnowledgeBaseId
	}
	return ""
}

func (x *DisableDocumentReq) GetDocId() string {
	if x != nil {
		return x.DocId
	}
	return ""
}

func (x *DisableDocumentReq) GetDisabled() bool {
	if x != nil {
		return x.Disabled
	}
	return false
}

type DisableDocumentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DisableDocumentRsp) Reset() {
	*x = DisableDocumentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisableDocumentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisableDocumentRsp) ProtoMessage() {}

func (x *DisableDocumentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisableDocumentRsp.ProtoReflect.Descriptor instead.
func (*DisableDocumentRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{23}
}

type ListDocumentChunksReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext        *UserContext      `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	KnowledgeBaseId    string            `protobuf:"bytes,2,opt,name=knowledge_base_id,json=knowledgeBaseId,proto3" json:"knowledge_base_id,omitempty"`
	DocId              string            `protobuf:"bytes,3,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	PageReq            *PageReq          `protobuf:"bytes,4,opt,name=page_req,json=pageReq,proto3" json:"page_req,omitempty"`
	SourceTypeSelector []ChunkSourceType `protobuf:"varint,5,rep,packed,name=source_type_selector,json=sourceTypeSelector,proto3,enum=proto.ChunkSourceType" json:"source_type_selector,omitempty"`
	SearchContent      string            `protobuf:"bytes,6,opt,name=search_content,json=searchContent,proto3" json:"search_content,omitempty"`
}

func (x *ListDocumentChunksReq) Reset() {
	*x = ListDocumentChunksReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDocumentChunksReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDocumentChunksReq) ProtoMessage() {}

func (x *ListDocumentChunksReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDocumentChunksReq.ProtoReflect.Descriptor instead.
func (*ListDocumentChunksReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{24}
}

func (x *ListDocumentChunksReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *ListDocumentChunksReq) GetKnowledgeBaseId() string {
	if x != nil {
		return x.KnowledgeBaseId
	}
	return ""
}

func (x *ListDocumentChunksReq) GetDocId() string {
	if x != nil {
		return x.DocId
	}
	return ""
}

func (x *ListDocumentChunksReq) GetPageReq() *PageReq {
	if x != nil {
		return x.PageReq
	}
	return nil
}

func (x *ListDocumentChunksReq) GetSourceTypeSelector() []ChunkSourceType {
	if x != nil {
		return x.SourceTypeSelector
	}
	return nil
}

func (x *ListDocumentChunksReq) GetSearchContent() string {
	if x != nil {
		return x.SearchContent
	}
	return ""
}

type ListDocumentChunksRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result   []*ChunkInfo `protobuf:"bytes,1,rep,name=result,proto3" json:"result,omitempty"`
	Total    int32        `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	PageNum  int32        `protobuf:"varint,3,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	PageSize int32        `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *ListDocumentChunksRsp) Reset() {
	*x = ListDocumentChunksRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDocumentChunksRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDocumentChunksRsp) ProtoMessage() {}

func (x *ListDocumentChunksRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDocumentChunksRsp.ProtoReflect.Descriptor instead.
func (*ListDocumentChunksRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{25}
}

func (x *ListDocumentChunksRsp) GetResult() []*ChunkInfo {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *ListDocumentChunksRsp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListDocumentChunksRsp) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *ListDocumentChunksRsp) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ListConnectionTablesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext *UserContext    `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	Connection  *DataConnection `protobuf:"bytes,2,opt,name=connection,proto3" json:"connection,omitempty"`
}

func (x *ListConnectionTablesReq) Reset() {
	*x = ListConnectionTablesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListConnectionTablesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListConnectionTablesReq) ProtoMessage() {}

func (x *ListConnectionTablesReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListConnectionTablesReq.ProtoReflect.Descriptor instead.
func (*ListConnectionTablesReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{26}
}

func (x *ListConnectionTablesReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *ListConnectionTablesReq) GetConnection() *DataConnection {
	if x != nil {
		return x.Connection
	}
	return nil
}

type ListConnectionTablesRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tables    []*TableInfo `protobuf:"bytes,1,rep,name=tables,proto3" json:"tables,omitempty"`
	EsIndices []string     `protobuf:"bytes,2,rep,name=es_indices,json=esIndices,proto3" json:"es_indices,omitempty"`
}

func (x *ListConnectionTablesRsp) Reset() {
	*x = ListConnectionTablesRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListConnectionTablesRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListConnectionTablesRsp) ProtoMessage() {}

func (x *ListConnectionTablesRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListConnectionTablesRsp.ProtoReflect.Descriptor instead.
func (*ListConnectionTablesRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{27}
}

func (x *ListConnectionTablesRsp) GetTables() []*TableInfo {
	if x != nil {
		return x.Tables
	}
	return nil
}

func (x *ListConnectionTablesRsp) GetEsIndices() []string {
	if x != nil {
		return x.EsIndices
	}
	return nil
}

//	message GetTableSchemaReq {
//	  UserContext user_context  = 1;
//	  string      connection_id = 2;
//	  string      table_name    = 3;
//	}
//
//	message GetTableSchemaRsp {
//	  string schema = 1;
//	}
type GetDocumentTreeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext     *UserContext `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	KnowledgeBaseId string       `protobuf:"bytes,2,opt,name=knowledge_base_id,json=knowledgeBaseId,proto3" json:"knowledge_base_id,omitempty"`
}

func (x *GetDocumentTreeReq) Reset() {
	*x = GetDocumentTreeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDocumentTreeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDocumentTreeReq) ProtoMessage() {}

func (x *GetDocumentTreeReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDocumentTreeReq.ProtoReflect.Descriptor instead.
func (*GetDocumentTreeReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{28}
}

func (x *GetDocumentTreeReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *GetDocumentTreeReq) GetKnowledgeBaseId() string {
	if x != nil {
		return x.KnowledgeBaseId
	}
	return ""
}

type GetDocumentTreeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// http://*************:9999/infinity/base/doc.html#/default/%E7%9F%A5%E8%AF%86%E5%BA%93%E6%96%87%E6%A1%A3%E6%93%8D%E4%BD%9C/getFullTreeByIdUsingPOST
	Tree *DocumentTree `protobuf:"bytes,1,opt,name=tree,proto3" json:"tree,omitempty"`
}

func (x *GetDocumentTreeRsp) Reset() {
	*x = GetDocumentTreeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDocumentTreeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDocumentTreeRsp) ProtoMessage() {}

func (x *GetDocumentTreeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDocumentTreeRsp.ProtoReflect.Descriptor instead.
func (*GetDocumentTreeRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{29}
}

func (x *GetDocumentTreeRsp) GetTree() *DocumentTree {
	if x != nil {
		return x.Tree
	}
	return nil
}

type SubmitFileToKnowledgeBaseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext      *UserContext        `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	SubmissionSource FileOperationSource `protobuf:"varint,2,opt,name=submission_source,json=submissionSource,proto3,enum=proto.FileOperationSource" json:"submission_source,omitempty"`
	// @gotags: description:"知识库id，若id对应的知识库不存在，则先自动创建临时知识库（可检索但不可见）"
	KnowledgeBaseId string `protobuf:"bytes,3,opt,name=knowledge_base_id,json=knowledgeBaseId,proto3" json:"knowledge_base_id,omitempty" description:"知识库id，若id对应的知识库不存在，则先自动创建临时知识库（可检索但不可见）"`
	// @gotags: description:"文件上传后的路径"
	FilePath string `protobuf:"bytes,4,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty" description:"文件上传后的路径"`
	// @gotags: description:"是否自动创建知识库"
	AutoCreateKb bool `protobuf:"varint,5,opt,name=auto_create_kb,json=autoCreateKb,proto3" json:"auto_create_kb,omitempty" description:"是否自动创建知识库"`
	// @gotags: description:"文档加工配置, 默认为空，表示使用知识库默认的加工配置"
	DocProcessingConfig *DocProcessingConfig `protobuf:"bytes,6,opt,name=doc_processing_config,json=docProcessingConfig,proto3" json:"doc_processing_config,omitempty" description:"文档加工配置, 默认为空，表示使用知识库默认的加工配置"`
	// @gotags: description:"仅表格知识库指定"
	TableConfig *TableConfig `protobuf:"bytes,7,opt,name=table_config,json=tableConfig,proto3" json:"table_config,omitempty" description:"仅表格知识库指定"`
	// @gotags: description:"语料来源配置；仅来源为语料时使用"
	CorpusConfig *CorpusConfig `protobuf:"bytes,8,opt,name=corpus_config,json=corpusConfig,proto3" json:"corpus_config,omitempty" description:"语料来源配置；仅来源为语料时使用"`
}

func (x *SubmitFileToKnowledgeBaseReq) Reset() {
	*x = SubmitFileToKnowledgeBaseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitFileToKnowledgeBaseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitFileToKnowledgeBaseReq) ProtoMessage() {}

func (x *SubmitFileToKnowledgeBaseReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitFileToKnowledgeBaseReq.ProtoReflect.Descriptor instead.
func (*SubmitFileToKnowledgeBaseReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{30}
}

func (x *SubmitFileToKnowledgeBaseReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *SubmitFileToKnowledgeBaseReq) GetSubmissionSource() FileOperationSource {
	if x != nil {
		return x.SubmissionSource
	}
	return FileOperationSource_AGENT_CONFIG
}

func (x *SubmitFileToKnowledgeBaseReq) GetKnowledgeBaseId() string {
	if x != nil {
		return x.KnowledgeBaseId
	}
	return ""
}

func (x *SubmitFileToKnowledgeBaseReq) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *SubmitFileToKnowledgeBaseReq) GetAutoCreateKb() bool {
	if x != nil {
		return x.AutoCreateKb
	}
	return false
}

func (x *SubmitFileToKnowledgeBaseReq) GetDocProcessingConfig() *DocProcessingConfig {
	if x != nil {
		return x.DocProcessingConfig
	}
	return nil
}

func (x *SubmitFileToKnowledgeBaseReq) GetTableConfig() *TableConfig {
	if x != nil {
		return x.TableConfig
	}
	return nil
}

func (x *SubmitFileToKnowledgeBaseReq) GetCorpusConfig() *CorpusConfig {
	if x != nil {
		return x.CorpusConfig
	}
	return nil
}

type SubmitFileToKnowledgeBaseRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Doc *Document `protobuf:"bytes,1,opt,name=doc,proto3" json:"doc,omitempty"`
}

func (x *SubmitFileToKnowledgeBaseRsp) Reset() {
	*x = SubmitFileToKnowledgeBaseRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitFileToKnowledgeBaseRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitFileToKnowledgeBaseRsp) ProtoMessage() {}

func (x *SubmitFileToKnowledgeBaseRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitFileToKnowledgeBaseRsp.ProtoReflect.Descriptor instead.
func (*SubmitFileToKnowledgeBaseRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{31}
}

func (x *SubmitFileToKnowledgeBaseRsp) GetDoc() *Document {
	if x != nil {
		return x.Doc
	}
	return nil
}

type RemoveFileFromKnowledgeBaseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext      *UserContext        `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	SubmissionSource FileOperationSource `protobuf:"varint,2,opt,name=submission_source,json=submissionSource,proto3,enum=proto.FileOperationSource" json:"submission_source,omitempty"`
	// @gotags: description:"知识库id，用于从知识库删除文件"
	KnowledgeBaseId string `protobuf:"bytes,3,opt,name=knowledge_base_id,json=knowledgeBaseId,proto3" json:"knowledge_base_id,omitempty" description:"知识库id，用于从知识库删除文件"`
	// @gotags: description:"文档id"
	DocId string `protobuf:"bytes,4,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty" description:"文档id"`
}

func (x *RemoveFileFromKnowledgeBaseReq) Reset() {
	*x = RemoveFileFromKnowledgeBaseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveFileFromKnowledgeBaseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveFileFromKnowledgeBaseReq) ProtoMessage() {}

func (x *RemoveFileFromKnowledgeBaseReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveFileFromKnowledgeBaseReq.ProtoReflect.Descriptor instead.
func (*RemoveFileFromKnowledgeBaseReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{32}
}

func (x *RemoveFileFromKnowledgeBaseReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *RemoveFileFromKnowledgeBaseReq) GetSubmissionSource() FileOperationSource {
	if x != nil {
		return x.SubmissionSource
	}
	return FileOperationSource_AGENT_CONFIG
}

func (x *RemoveFileFromKnowledgeBaseReq) GetKnowledgeBaseId() string {
	if x != nil {
		return x.KnowledgeBaseId
	}
	return ""
}

func (x *RemoveFileFromKnowledgeBaseReq) GetDocId() string {
	if x != nil {
		return x.DocId
	}
	return ""
}

type RemoveFileFromKnowledgeBaseRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RemoveFileFromKnowledgeBaseRsp) Reset() {
	*x = RemoveFileFromKnowledgeBaseRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveFileFromKnowledgeBaseRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveFileFromKnowledgeBaseRsp) ProtoMessage() {}

func (x *RemoveFileFromKnowledgeBaseRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveFileFromKnowledgeBaseRsp.ProtoReflect.Descriptor instead.
func (*RemoveFileFromKnowledgeBaseRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{33}
}

type DocumentNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category DocumentNodeCategory `protobuf:"varint,1,opt,name=category,proto3,enum=proto.DocumentNodeCategory" json:"category,omitempty"`
	Id       string               `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Name     string               `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DocumentNode) Reset() {
	*x = DocumentNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DocumentNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocumentNode) ProtoMessage() {}

func (x *DocumentNode) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocumentNode.ProtoReflect.Descriptor instead.
func (*DocumentNode) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{34}
}

func (x *DocumentNode) GetCategory() DocumentNodeCategory {
	if x != nil {
		return x.Category
	}
	return DocumentNodeCategory_DIR
}

func (x *DocumentNode) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DocumentNode) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DocumentTree struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Node     *DocumentNode   `protobuf:"bytes,1,opt,name=node,proto3" json:"node,omitempty"`
	Children []*DocumentTree `protobuf:"bytes,2,rep,name=children,proto3" json:"children,omitempty"`
}

func (x *DocumentTree) Reset() {
	*x = DocumentTree{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DocumentTree) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocumentTree) ProtoMessage() {}

func (x *DocumentTree) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocumentTree.ProtoReflect.Descriptor instead.
func (*DocumentTree) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{35}
}

func (x *DocumentTree) GetNode() *DocumentNode {
	if x != nil {
		return x.Node
	}
	return nil
}

func (x *DocumentTree) GetChildren() []*DocumentTree {
	if x != nil {
		return x.Children
	}
	return nil
}

type UpdateKnowledgeBaseStateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext   *UserContext `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	Id            string       `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	IsVisible     bool         `protobuf:"varint,3,opt,name=is_visible,json=isVisible,proto3" json:"is_visible,omitempty"`
	IsRetrievable bool         `protobuf:"varint,4,opt,name=is_retrievable,json=isRetrievable,proto3" json:"is_retrievable,omitempty"`
}

func (x *UpdateKnowledgeBaseStateReq) Reset() {
	*x = UpdateKnowledgeBaseStateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateKnowledgeBaseStateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateKnowledgeBaseStateReq) ProtoMessage() {}

func (x *UpdateKnowledgeBaseStateReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateKnowledgeBaseStateReq.ProtoReflect.Descriptor instead.
func (*UpdateKnowledgeBaseStateReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{36}
}

func (x *UpdateKnowledgeBaseStateReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *UpdateKnowledgeBaseStateReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateKnowledgeBaseStateReq) GetIsVisible() bool {
	if x != nil {
		return x.IsVisible
	}
	return false
}

func (x *UpdateKnowledgeBaseStateReq) GetIsRetrievable() bool {
	if x != nil {
		return x.IsRetrievable
	}
	return false
}

type UpdateKnowledgeBaseStateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	KnowledgeBase *KnowledgeBase `protobuf:"bytes,1,opt,name=knowledge_base,json=knowledgeBase,proto3" json:"knowledge_base,omitempty"`
}

func (x *UpdateKnowledgeBaseStateRsp) Reset() {
	*x = UpdateKnowledgeBaseStateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateKnowledgeBaseStateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateKnowledgeBaseStateRsp) ProtoMessage() {}

func (x *UpdateKnowledgeBaseStateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateKnowledgeBaseStateRsp.ProtoReflect.Descriptor instead.
func (*UpdateKnowledgeBaseStateRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{37}
}

func (x *UpdateKnowledgeBaseStateRsp) GetKnowledgeBase() *KnowledgeBase {
	if x != nil {
		return x.KnowledgeBase
	}
	return nil
}

type CreateChunkReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext     *UserContext `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	KnowledgeBaseId string       `protobuf:"bytes,2,opt,name=knowledge_base_id,json=knowledgeBaseId,proto3" json:"knowledge_base_id,omitempty"`
	DocId           string       `protobuf:"bytes,3,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	OriChunkId      string       `protobuf:"bytes,4,opt,name=ori_chunk_id,json=oriChunkId,proto3" json:"ori_chunk_id,omitempty"`
	Content         string       `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	// @gotags: description:"chunk原文内容类型，目前仅支持原文文本和原文图像"
	ContentType OriginalContentType `protobuf:"varint,6,opt,name=content_type,json=contentType,proto3,enum=proto.OriginalContentType" json:"content_type,omitempty" description:"chunk原文内容类型，目前仅支持原文文本和原文图像"`
}

func (x *CreateChunkReq) Reset() {
	*x = CreateChunkReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateChunkReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateChunkReq) ProtoMessage() {}

func (x *CreateChunkReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateChunkReq.ProtoReflect.Descriptor instead.
func (*CreateChunkReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{38}
}

func (x *CreateChunkReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *CreateChunkReq) GetKnowledgeBaseId() string {
	if x != nil {
		return x.KnowledgeBaseId
	}
	return ""
}

func (x *CreateChunkReq) GetDocId() string {
	if x != nil {
		return x.DocId
	}
	return ""
}

func (x *CreateChunkReq) GetOriChunkId() string {
	if x != nil {
		return x.OriChunkId
	}
	return ""
}

func (x *CreateChunkReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *CreateChunkReq) GetContentType() OriginalContentType {
	if x != nil {
		return x.ContentType
	}
	return OriginalContentType_ORIGINAL_CONTENT_TYPE_TEXT
}

type CreateChunkRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Chunk *Chunk `protobuf:"bytes,1,opt,name=chunk,proto3" json:"chunk,omitempty"`
}

func (x *CreateChunkRsp) Reset() {
	*x = CreateChunkRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateChunkRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateChunkRsp) ProtoMessage() {}

func (x *CreateChunkRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateChunkRsp.ProtoReflect.Descriptor instead.
func (*CreateChunkRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{39}
}

func (x *CreateChunkRsp) GetChunk() *Chunk {
	if x != nil {
		return x.Chunk
	}
	return nil
}

type DeleteChunkReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext     *UserContext `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	KnowledgeBaseId string       `protobuf:"bytes,2,opt,name=knowledge_base_id,json=knowledgeBaseId,proto3" json:"knowledge_base_id,omitempty"`
	DocId           string       `protobuf:"bytes,3,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	ChunkId         string       `protobuf:"bytes,4,opt,name=chunk_id,json=chunkId,proto3" json:"chunk_id,omitempty"`
	// @gotags: description:"原文chunk id, 删除原文chunk id时chunk_id与ori_chunk_id一致"
	OriChunkId string `protobuf:"bytes,5,opt,name=ori_chunk_id,json=oriChunkId,proto3" json:"ori_chunk_id,omitempty" description:"原文chunk id, 删除原文chunk id时chunk_id与ori_chunk_id一致"`
}

func (x *DeleteChunkReq) Reset() {
	*x = DeleteChunkReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteChunkReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteChunkReq) ProtoMessage() {}

func (x *DeleteChunkReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteChunkReq.ProtoReflect.Descriptor instead.
func (*DeleteChunkReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{40}
}

func (x *DeleteChunkReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *DeleteChunkReq) GetKnowledgeBaseId() string {
	if x != nil {
		return x.KnowledgeBaseId
	}
	return ""
}

func (x *DeleteChunkReq) GetDocId() string {
	if x != nil {
		return x.DocId
	}
	return ""
}

func (x *DeleteChunkReq) GetChunkId() string {
	if x != nil {
		return x.ChunkId
	}
	return ""
}

func (x *DeleteChunkReq) GetOriChunkId() string {
	if x != nil {
		return x.OriChunkId
	}
	return ""
}

type DeleteChunkRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"删除增强切片时返回，删除原文切片则为空"
	Chunk *Chunk `protobuf:"bytes,1,opt,name=chunk,proto3" json:"chunk,omitempty" description:"删除增强切片时返回，删除原文切片则为空"`
}

func (x *DeleteChunkRsp) Reset() {
	*x = DeleteChunkRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteChunkRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteChunkRsp) ProtoMessage() {}

func (x *DeleteChunkRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteChunkRsp.ProtoReflect.Descriptor instead.
func (*DeleteChunkRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{41}
}

func (x *DeleteChunkRsp) GetChunk() *Chunk {
	if x != nil {
		return x.Chunk
	}
	return nil
}

type UpdateChunkReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext     *UserContext `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	KnowledgeBaseId string       `protobuf:"bytes,2,opt,name=knowledge_base_id,json=knowledgeBaseId,proto3" json:"knowledge_base_id,omitempty"`
	DocId           string       `protobuf:"bytes,3,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	ChunkId         string       `protobuf:"bytes,4,opt,name=chunk_id,json=chunkId,proto3" json:"chunk_id,omitempty"`
	// @gotags: description:"更新原文切片时该值与chunk_id一致；更新增强切片时为所属的原文切片的chunk_id"
	OriChunkId string `protobuf:"bytes,5,opt,name=ori_chunk_id,json=oriChunkId,proto3" json:"ori_chunk_id,omitempty" description:"更新原文切片时该值与chunk_id一致；更新增强切片时为所属的原文切片的chunk_id"`
	Content    string `protobuf:"bytes,6,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *UpdateChunkReq) Reset() {
	*x = UpdateChunkReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateChunkReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateChunkReq) ProtoMessage() {}

func (x *UpdateChunkReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateChunkReq.ProtoReflect.Descriptor instead.
func (*UpdateChunkReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{42}
}

func (x *UpdateChunkReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *UpdateChunkReq) GetKnowledgeBaseId() string {
	if x != nil {
		return x.KnowledgeBaseId
	}
	return ""
}

func (x *UpdateChunkReq) GetDocId() string {
	if x != nil {
		return x.DocId
	}
	return ""
}

func (x *UpdateChunkReq) GetChunkId() string {
	if x != nil {
		return x.ChunkId
	}
	return ""
}

func (x *UpdateChunkReq) GetOriChunkId() string {
	if x != nil {
		return x.OriChunkId
	}
	return ""
}

func (x *UpdateChunkReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type UpdateChunkRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Chunk *Chunk `protobuf:"bytes,1,opt,name=chunk,proto3" json:"chunk,omitempty"`
}

func (x *UpdateChunkRsp) Reset() {
	*x = UpdateChunkRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateChunkRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateChunkRsp) ProtoMessage() {}

func (x *UpdateChunkRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateChunkRsp.ProtoReflect.Descriptor instead.
func (*UpdateChunkRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{43}
}

func (x *UpdateChunkRsp) GetChunk() *Chunk {
	if x != nil {
		return x.Chunk
	}
	return nil
}

type PreviewDocumentProcessReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext         *UserContext         `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	FilePath            string               `protobuf:"bytes,2,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
	DocProcessingConfig *DocProcessingConfig `protobuf:"bytes,3,opt,name=doc_processing_config,json=docProcessingConfig,proto3" json:"doc_processing_config,omitempty"`
}

func (x *PreviewDocumentProcessReq) Reset() {
	*x = PreviewDocumentProcessReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewDocumentProcessReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewDocumentProcessReq) ProtoMessage() {}

func (x *PreviewDocumentProcessReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewDocumentProcessReq.ProtoReflect.Descriptor instead.
func (*PreviewDocumentProcessReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{44}
}

func (x *PreviewDocumentProcessReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *PreviewDocumentProcessReq) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *PreviewDocumentProcessReq) GetDocProcessingConfig() *DocProcessingConfig {
	if x != nil {
		return x.DocProcessingConfig
	}
	return nil
}

type SyncSubmitFilesToKnowledgeBaseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext      *UserContext        `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	SubmissionSource FileOperationSource `protobuf:"varint,2,opt,name=submission_source,json=submissionSource,proto3,enum=proto.FileOperationSource" json:"submission_source,omitempty"`
	// @gotags: description:"知识库id，若id对应的知识库不存在，则根据auto_create_kb确定是否自动创建临时知识库（可检索但不可见）"
	KnowledgeBaseId string `protobuf:"bytes,3,opt,name=knowledge_base_id,json=knowledgeBaseId,proto3" json:"knowledge_base_id,omitempty" description:"知识库id，若id对应的知识库不存在，则根据auto_create_kb确定是否自动创建临时知识库（可检索但不可见）"`
	// @gotags: description:"文件路径列表, 支持批量上传多个文件；仅本地来源的数据使用"
	FilePaths []string `protobuf:"bytes,4,rep,name=file_paths,json=filePaths,proto3" json:"file_paths,omitempty" description:"文件路径列表, 支持批量上传多个文件；仅本地来源的数据使用"`
	// @gotags: description:"文档加工配置, 默认为空，表示使用知识库默认的加工配置"
	DocProcessingConfig *DocProcessingConfig `protobuf:"bytes,5,opt,name=doc_processing_config,json=docProcessingConfig,proto3" json:"doc_processing_config,omitempty" description:"文档加工配置, 默认为空，表示使用知识库默认的加工配置"`
	// @gotags: description:"是否自动创建知识库"
	AutoCreateKb bool `protobuf:"varint,6,opt,name=auto_create_kb,json=autoCreateKb,proto3" json:"auto_create_kb,omitempty" description:"是否自动创建知识库"`
	// @gotags: description:"表格配置, 仅表格知识库指定"
	TableConfig *TableConfig `protobuf:"bytes,7,opt,name=table_config,json=tableConfig,proto3" json:"table_config,omitempty" description:"表格配置, 仅表格知识库指定"`
	// @gotags: description:"数据来源"
	DocumentFileSource DocumentFileSource `protobuf:"varint,8,opt,name=document_file_source,json=documentFileSource,proto3,enum=proto.DocumentFileSource" json:"document_file_source,omitempty" description:"数据来源"`
	// @gotags: description:"语料来源配置；仅来源为语料时使用"
	CorpusConfig *CorpusConfig `protobuf:"bytes,9,opt,name=corpus_config,json=corpusConfig,proto3" json:"corpus_config,omitempty" description:"语料来源配置；仅来源为语料时使用"`
}

func (x *SyncSubmitFilesToKnowledgeBaseReq) Reset() {
	*x = SyncSubmitFilesToKnowledgeBaseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncSubmitFilesToKnowledgeBaseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncSubmitFilesToKnowledgeBaseReq) ProtoMessage() {}

func (x *SyncSubmitFilesToKnowledgeBaseReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncSubmitFilesToKnowledgeBaseReq.ProtoReflect.Descriptor instead.
func (*SyncSubmitFilesToKnowledgeBaseReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{45}
}

func (x *SyncSubmitFilesToKnowledgeBaseReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *SyncSubmitFilesToKnowledgeBaseReq) GetSubmissionSource() FileOperationSource {
	if x != nil {
		return x.SubmissionSource
	}
	return FileOperationSource_AGENT_CONFIG
}

func (x *SyncSubmitFilesToKnowledgeBaseReq) GetKnowledgeBaseId() string {
	if x != nil {
		return x.KnowledgeBaseId
	}
	return ""
}

func (x *SyncSubmitFilesToKnowledgeBaseReq) GetFilePaths() []string {
	if x != nil {
		return x.FilePaths
	}
	return nil
}

func (x *SyncSubmitFilesToKnowledgeBaseReq) GetDocProcessingConfig() *DocProcessingConfig {
	if x != nil {
		return x.DocProcessingConfig
	}
	return nil
}

func (x *SyncSubmitFilesToKnowledgeBaseReq) GetAutoCreateKb() bool {
	if x != nil {
		return x.AutoCreateKb
	}
	return false
}

func (x *SyncSubmitFilesToKnowledgeBaseReq) GetTableConfig() *TableConfig {
	if x != nil {
		return x.TableConfig
	}
	return nil
}

func (x *SyncSubmitFilesToKnowledgeBaseReq) GetDocumentFileSource() DocumentFileSource {
	if x != nil {
		return x.DocumentFileSource
	}
	return DocumentFileSource_LOCAL_FILE
}

func (x *SyncSubmitFilesToKnowledgeBaseReq) GetCorpusConfig() *CorpusConfig {
	if x != nil {
		return x.CorpusConfig
	}
	return nil
}

type DocumentProcessingProgress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: description:"文档"
	Document *Document `protobuf:"bytes,1,opt,name=document,proto3" json:"document,omitempty" description:"文档"`
	// @gotags: description:"进度百分比" json:"percentage"
	Percentage float32 `protobuf:"fixed32,2,opt,name=percentage,proto3" json:"percentage" description:"进度百分比"`
	// @gotags: description:"是否完成；完成且进度不为100%表示处理错误" json:"finished"
	Finished bool `protobuf:"varint,3,opt,name=finished,proto3" json:"finished" description:"是否完成；完成且进度不为100%表示处理错误"`
	// @gotags: description:"错误消息"
	ErrorMessage string `protobuf:"bytes,4,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty" description:"错误消息"`
	// @gotags: description:"当前处理阶段" json:"stage"
	Stage DocumentTaskStage `protobuf:"varint,5,opt,name=stage,proto3,enum=proto.DocumentTaskStage" json:"stage" description:"当前处理阶段"`
}

func (x *DocumentProcessingProgress) Reset() {
	*x = DocumentProcessingProgress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DocumentProcessingProgress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocumentProcessingProgress) ProtoMessage() {}

func (x *DocumentProcessingProgress) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocumentProcessingProgress.ProtoReflect.Descriptor instead.
func (*DocumentProcessingProgress) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{46}
}

func (x *DocumentProcessingProgress) GetDocument() *Document {
	if x != nil {
		return x.Document
	}
	return nil
}

func (x *DocumentProcessingProgress) GetPercentage() float32 {
	if x != nil {
		return x.Percentage
	}
	return 0
}

func (x *DocumentProcessingProgress) GetFinished() bool {
	if x != nil {
		return x.Finished
	}
	return false
}

func (x *DocumentProcessingProgress) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *DocumentProcessingProgress) GetStage() DocumentTaskStage {
	if x != nil {
		return x.Stage
	}
	return DocumentTaskStage_PENDING
}

type SyncSubmitFilesToKnowledgeBaseRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: json:"done"
	Done bool `protobuf:"varint,1,opt,name=done,proto3" json:"done"`
	// @gotags: json:"num_succeeded"
	NumSucceeded int32 `protobuf:"varint,2,opt,name=num_succeeded,json=numSucceeded,proto3" json:"num_succeeded"`
	// @gotags: json:"num_failed"
	NumFailed int32 `protobuf:"varint,3,opt,name=num_failed,json=numFailed,proto3" json:"num_failed"`
	// @gotags: json:"total"
	Total int32 `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	// @gotags: json:"documents"
	Documents []*DocumentProcessingProgress `protobuf:"bytes,5,rep,name=documents,proto3" json:"documents"`
}

func (x *SyncSubmitFilesToKnowledgeBaseRsp) Reset() {
	*x = SyncSubmitFilesToKnowledgeBaseRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncSubmitFilesToKnowledgeBaseRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncSubmitFilesToKnowledgeBaseRsp) ProtoMessage() {}

func (x *SyncSubmitFilesToKnowledgeBaseRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncSubmitFilesToKnowledgeBaseRsp.ProtoReflect.Descriptor instead.
func (*SyncSubmitFilesToKnowledgeBaseRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{47}
}

func (x *SyncSubmitFilesToKnowledgeBaseRsp) GetDone() bool {
	if x != nil {
		return x.Done
	}
	return false
}

func (x *SyncSubmitFilesToKnowledgeBaseRsp) GetNumSucceeded() int32 {
	if x != nil {
		return x.NumSucceeded
	}
	return 0
}

func (x *SyncSubmitFilesToKnowledgeBaseRsp) GetNumFailed() int32 {
	if x != nil {
		return x.NumFailed
	}
	return 0
}

func (x *SyncSubmitFilesToKnowledgeBaseRsp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *SyncSubmitFilesToKnowledgeBaseRsp) GetDocuments() []*DocumentProcessingProgress {
	if x != nil {
		return x.Documents
	}
	return nil
}

type RetrieveRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	KnowledgeBaseId string           `protobuf:"bytes,1,opt,name=knowledge_base_id,json=knowledgeBaseId,proto3" json:"knowledge_base_id,omitempty"`
	DocRange        []string         `protobuf:"bytes,2,rep,name=doc_range,json=docRange,proto3" json:"doc_range,omitempty"`
	RetrievalConfig *RetrievalConfig `protobuf:"bytes,3,opt,name=retrieval_config,json=retrievalConfig,proto3" json:"retrieval_config,omitempty"`
}

func (x *RetrieveRange) Reset() {
	*x = RetrieveRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrieveRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrieveRange) ProtoMessage() {}

func (x *RetrieveRange) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrieveRange.ProtoReflect.Descriptor instead.
func (*RetrieveRange) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{48}
}

func (x *RetrieveRange) GetKnowledgeBaseId() string {
	if x != nil {
		return x.KnowledgeBaseId
	}
	return ""
}

func (x *RetrieveRange) GetDocRange() []string {
	if x != nil {
		return x.DocRange
	}
	return nil
}

func (x *RetrieveRange) GetRetrievalConfig() *RetrievalConfig {
	if x != nil {
		return x.RetrievalConfig
	}
	return nil
}

type RetrieveCrossKnowledgeBaseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext   *UserContext     `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	Query         string           `protobuf:"bytes,2,opt,name=query,proto3" json:"query,omitempty"`
	Ranges        []*RetrieveRange `protobuf:"bytes,3,rep,name=ranges,proto3" json:"ranges,omitempty"`
	RerankParams  *RerankParams    `protobuf:"bytes,4,opt,name=rerank_params,json=rerankParams,proto3" json:"rerank_params,omitempty"`
	DisableRerank bool             `protobuf:"varint,5,opt,name=disable_rerank,json=disableRerank,proto3" json:"disable_rerank,omitempty"`
	// @gotags: description:"是否为istio模式,留下审计记录"
	IstioMode bool `protobuf:"varint,7,opt,name=istio_mode,json=istioMode,proto3" json:"istio_mode,omitempty" description:"是否为istio模式,留下审计记录"`
}

func (x *RetrieveCrossKnowledgeBaseReq) Reset() {
	*x = RetrieveCrossKnowledgeBaseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrieveCrossKnowledgeBaseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrieveCrossKnowledgeBaseReq) ProtoMessage() {}

func (x *RetrieveCrossKnowledgeBaseReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrieveCrossKnowledgeBaseReq.ProtoReflect.Descriptor instead.
func (*RetrieveCrossKnowledgeBaseReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{49}
}

func (x *RetrieveCrossKnowledgeBaseReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *RetrieveCrossKnowledgeBaseReq) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *RetrieveCrossKnowledgeBaseReq) GetRanges() []*RetrieveRange {
	if x != nil {
		return x.Ranges
	}
	return nil
}

func (x *RetrieveCrossKnowledgeBaseReq) GetRerankParams() *RerankParams {
	if x != nil {
		return x.RerankParams
	}
	return nil
}

func (x *RetrieveCrossKnowledgeBaseReq) GetDisableRerank() bool {
	if x != nil {
		return x.DisableRerank
	}
	return false
}

func (x *RetrieveCrossKnowledgeBaseReq) GetIstioMode() bool {
	if x != nil {
		return x.IstioMode
	}
	return false
}

type RetrieveCrossKnowledgeBaseRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Request *RetrieveCrossKnowledgeBaseReq `protobuf:"bytes,1,opt,name=request,proto3" json:"request,omitempty"`
	// @gotags: description:"分段结果列表"
	Result []*ChunkRetrieveResult `protobuf:"bytes,2,rep,name=result,proto3" json:"result,omitempty" description:"分段结果列表"`
}

func (x *RetrieveCrossKnowledgeBaseRsp) Reset() {
	*x = RetrieveCrossKnowledgeBaseRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrieveCrossKnowledgeBaseRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrieveCrossKnowledgeBaseRsp) ProtoMessage() {}

func (x *RetrieveCrossKnowledgeBaseRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrieveCrossKnowledgeBaseRsp.ProtoReflect.Descriptor instead.
func (*RetrieveCrossKnowledgeBaseRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{50}
}

func (x *RetrieveCrossKnowledgeBaseRsp) GetRequest() *RetrieveCrossKnowledgeBaseReq {
	if x != nil {
		return x.Request
	}
	return nil
}

func (x *RetrieveCrossKnowledgeBaseRsp) GetResult() []*ChunkRetrieveResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type SubmitChunksToKnowledgeBaseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext     *UserContext  `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	KnowledgeBaseId string        `protobuf:"bytes,2,opt,name=knowledge_base_id,json=knowledgeBaseId,proto3" json:"knowledge_base_id,omitempty"`
	FilePath        string        `protobuf:"bytes,3,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
	FileBase64      string        `protobuf:"bytes,4,opt,name=file_base64,json=fileBase64,proto3" json:"file_base64,omitempty"`
	Chunks          []*Chunk      `protobuf:"bytes,5,rep,name=chunks,proto3" json:"chunks,omitempty"`
	Elements        []*DocElement `protobuf:"bytes,6,rep,name=elements,proto3" json:"elements,omitempty"`
}

func (x *SubmitChunksToKnowledgeBaseReq) Reset() {
	*x = SubmitChunksToKnowledgeBaseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitChunksToKnowledgeBaseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitChunksToKnowledgeBaseReq) ProtoMessage() {}

func (x *SubmitChunksToKnowledgeBaseReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitChunksToKnowledgeBaseReq.ProtoReflect.Descriptor instead.
func (*SubmitChunksToKnowledgeBaseReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{51}
}

func (x *SubmitChunksToKnowledgeBaseReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *SubmitChunksToKnowledgeBaseReq) GetKnowledgeBaseId() string {
	if x != nil {
		return x.KnowledgeBaseId
	}
	return ""
}

func (x *SubmitChunksToKnowledgeBaseReq) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *SubmitChunksToKnowledgeBaseReq) GetFileBase64() string {
	if x != nil {
		return x.FileBase64
	}
	return ""
}

func (x *SubmitChunksToKnowledgeBaseReq) GetChunks() []*Chunk {
	if x != nil {
		return x.Chunks
	}
	return nil
}

func (x *SubmitChunksToKnowledgeBaseReq) GetElements() []*DocElement {
	if x != nil {
		return x.Elements
	}
	return nil
}

type SubmitChunksToKnowledgeBaseRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Document *Document `protobuf:"bytes,1,opt,name=document,proto3" json:"document,omitempty"`
}

func (x *SubmitChunksToKnowledgeBaseRsp) Reset() {
	*x = SubmitChunksToKnowledgeBaseRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitChunksToKnowledgeBaseRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitChunksToKnowledgeBaseRsp) ProtoMessage() {}

func (x *SubmitChunksToKnowledgeBaseRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitChunksToKnowledgeBaseRsp.ProtoReflect.Descriptor instead.
func (*SubmitChunksToKnowledgeBaseRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{52}
}

func (x *SubmitChunksToKnowledgeBaseRsp) GetDocument() *Document {
	if x != nil {
		return x.Document
	}
	return nil
}

type IsDocumentExistentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext     *UserContext `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	KnowledgeBaseId string       `protobuf:"bytes,2,opt,name=knowledge_base_id,json=knowledgeBaseId,proto3" json:"knowledge_base_id,omitempty"`
	FilePath        string       `protobuf:"bytes,3,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
	FileBase64      string       `protobuf:"bytes,4,opt,name=file_base64,json=fileBase64,proto3" json:"file_base64,omitempty"`
}

func (x *IsDocumentExistentReq) Reset() {
	*x = IsDocumentExistentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsDocumentExistentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsDocumentExistentReq) ProtoMessage() {}

func (x *IsDocumentExistentReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsDocumentExistentReq.ProtoReflect.Descriptor instead.
func (*IsDocumentExistentReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{53}
}

func (x *IsDocumentExistentReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *IsDocumentExistentReq) GetKnowledgeBaseId() string {
	if x != nil {
		return x.KnowledgeBaseId
	}
	return ""
}

func (x *IsDocumentExistentReq) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *IsDocumentExistentReq) GetFileBase64() string {
	if x != nil {
		return x.FileBase64
	}
	return ""
}

type IsDocumentExistentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Existent bool      `protobuf:"varint,1,opt,name=existent,proto3" json:"existent,omitempty"`
	Document *Document `protobuf:"bytes,2,opt,name=document,proto3" json:"document,omitempty"`
}

func (x *IsDocumentExistentRsp) Reset() {
	*x = IsDocumentExistentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsDocumentExistentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsDocumentExistentRsp) ProtoMessage() {}

func (x *IsDocumentExistentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsDocumentExistentRsp.ProtoReflect.Descriptor instead.
func (*IsDocumentExistentRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{54}
}

func (x *IsDocumentExistentRsp) GetExistent() bool {
	if x != nil {
		return x.Existent
	}
	return false
}

func (x *IsDocumentExistentRsp) GetDocument() *Document {
	if x != nil {
		return x.Document
	}
	return nil
}

type DocSvcLoadChunkRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Chunks   []*Chunk      `protobuf:"bytes,1,rep,name=chunks,proto3" json:"chunks,omitempty"`
	Elements []*DocElement `protobuf:"bytes,2,rep,name=elements,proto3" json:"elements,omitempty"`
}

func (x *DocSvcLoadChunkRsp) Reset() {
	*x = DocSvcLoadChunkRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DocSvcLoadChunkRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocSvcLoadChunkRsp) ProtoMessage() {}

func (x *DocSvcLoadChunkRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocSvcLoadChunkRsp.ProtoReflect.Descriptor instead.
func (*DocSvcLoadChunkRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{55}
}

func (x *DocSvcLoadChunkRsp) GetChunks() []*Chunk {
	if x != nil {
		return x.Chunks
	}
	return nil
}

func (x *DocSvcLoadChunkRsp) GetElements() []*DocElement {
	if x != nil {
		return x.Elements
	}
	return nil
}

type CollectKnowledgeBaseStatsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext *UserContext `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
}

func (x *CollectKnowledgeBaseStatsReq) Reset() {
	*x = CollectKnowledgeBaseStatsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectKnowledgeBaseStatsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectKnowledgeBaseStatsReq) ProtoMessage() {}

func (x *CollectKnowledgeBaseStatsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectKnowledgeBaseStatsReq.ProtoReflect.Descriptor instead.
func (*CollectKnowledgeBaseStatsReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{56}
}

func (x *CollectKnowledgeBaseStatsReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

type CollectKnowledgeBaseStatsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NumKnowledgeBases int64 `protobuf:"varint,1,opt,name=num_knowledge_bases,json=numKnowledgeBases,proto3" json:"num_knowledge_bases,omitempty"`
	NumDocs           int64 `protobuf:"varint,2,opt,name=num_docs,json=numDocs,proto3" json:"num_docs,omitempty"`
	NumChunks         int64 `protobuf:"varint,3,opt,name=num_chunks,json=numChunks,proto3" json:"num_chunks,omitempty"`
	NumsAugChunks     int64 `protobuf:"varint,4,opt,name=nums_aug_chunks,json=numsAugChunks,proto3" json:"nums_aug_chunks,omitempty"`
}

func (x *CollectKnowledgeBaseStatsRsp) Reset() {
	*x = CollectKnowledgeBaseStatsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectKnowledgeBaseStatsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectKnowledgeBaseStatsRsp) ProtoMessage() {}

func (x *CollectKnowledgeBaseStatsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectKnowledgeBaseStatsRsp.ProtoReflect.Descriptor instead.
func (*CollectKnowledgeBaseStatsRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{57}
}

func (x *CollectKnowledgeBaseStatsRsp) GetNumKnowledgeBases() int64 {
	if x != nil {
		return x.NumKnowledgeBases
	}
	return 0
}

func (x *CollectKnowledgeBaseStatsRsp) GetNumDocs() int64 {
	if x != nil {
		return x.NumDocs
	}
	return 0
}

func (x *CollectKnowledgeBaseStatsRsp) GetNumChunks() int64 {
	if x != nil {
		return x.NumChunks
	}
	return 0
}

func (x *CollectKnowledgeBaseStatsRsp) GetNumsAugChunks() int64 {
	if x != nil {
		return x.NumsAugChunks
	}
	return 0
}

type ShareKnowledgeBaseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext     *UserContext `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	KnowledgeBaseId string       `protobuf:"bytes,2,opt,name=knowledge_base_id,json=knowledgeBaseId,proto3" json:"knowledge_base_id,omitempty"`
	// @gotags: description:"是否共享到公共空间"
	IsPublic bool `protobuf:"varint,3,opt,name=is_public,json=isPublic,proto3" json:"is_public,omitempty" description:"是否共享到公共空间"`
}

func (x *ShareKnowledgeBaseReq) Reset() {
	*x = ShareKnowledgeBaseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShareKnowledgeBaseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShareKnowledgeBaseReq) ProtoMessage() {}

func (x *ShareKnowledgeBaseReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShareKnowledgeBaseReq.ProtoReflect.Descriptor instead.
func (*ShareKnowledgeBaseReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{58}
}

func (x *ShareKnowledgeBaseReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *ShareKnowledgeBaseReq) GetKnowledgeBaseId() string {
	if x != nil {
		return x.KnowledgeBaseId
	}
	return ""
}

func (x *ShareKnowledgeBaseReq) GetIsPublic() bool {
	if x != nil {
		return x.IsPublic
	}
	return false
}

type ShareKnowledgeBaseRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ShareKnowledgeBaseRsp) Reset() {
	*x = ShareKnowledgeBaseRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShareKnowledgeBaseRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShareKnowledgeBaseRsp) ProtoMessage() {}

func (x *ShareKnowledgeBaseRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShareKnowledgeBaseRsp.ProtoReflect.Descriptor instead.
func (*ShareKnowledgeBaseRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{59}
}

type PublishKnowledgeBaseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         string             `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name       string             `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc       string             `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	RateLimit  *serving.RateLimit `protobuf:"bytes,4,opt,name=rate_limit,json=rateLimit,proto3" json:"rate_limit,omitempty"`
	IsSecurity bool               `protobuf:"varint,5,opt,name=is_security,json=isSecurity,proto3" json:"is_security,omitempty"`
	Ctx        *UserContext       `protobuf:"bytes,6,opt,name=ctx,proto3" json:"ctx,omitempty"`
}

func (x *PublishKnowledgeBaseReq) Reset() {
	*x = PublishKnowledgeBaseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PublishKnowledgeBaseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishKnowledgeBaseReq) ProtoMessage() {}

func (x *PublishKnowledgeBaseReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishKnowledgeBaseReq.ProtoReflect.Descriptor instead.
func (*PublishKnowledgeBaseReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{60}
}

func (x *PublishKnowledgeBaseReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PublishKnowledgeBaseReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PublishKnowledgeBaseReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *PublishKnowledgeBaseReq) GetRateLimit() *serving.RateLimit {
	if x != nil {
		return x.RateLimit
	}
	return nil
}

func (x *PublishKnowledgeBaseReq) GetIsSecurity() bool {
	if x != nil {
		return x.IsSecurity
	}
	return false
}

func (x *PublishKnowledgeBaseReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

type PublishKnowledgeBaseRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	VirtualSvcUrl string `protobuf:"bytes,2,opt,name=virtual_svc_url,json=virtualSvcUrl,proto3" json:"virtual_svc_url,omitempty"`
}

func (x *PublishKnowledgeBaseRsp) Reset() {
	*x = PublishKnowledgeBaseRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PublishKnowledgeBaseRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishKnowledgeBaseRsp) ProtoMessage() {}

func (x *PublishKnowledgeBaseRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishKnowledgeBaseRsp.ProtoReflect.Descriptor instead.
func (*PublishKnowledgeBaseRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{61}
}

func (x *PublishKnowledgeBaseRsp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PublishKnowledgeBaseRsp) GetVirtualSvcUrl() string {
	if x != nil {
		return x.VirtualSvcUrl
	}
	return ""
}

type TraceDocElementsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext *UserContext `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	ChunkId     string       `protobuf:"bytes,2,opt,name=chunk_id,json=chunkId,proto3" json:"chunk_id,omitempty"`
}

func (x *TraceDocElementsReq) Reset() {
	*x = TraceDocElementsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraceDocElementsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraceDocElementsReq) ProtoMessage() {}

func (x *TraceDocElementsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraceDocElementsReq.ProtoReflect.Descriptor instead.
func (*TraceDocElementsReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{62}
}

func (x *TraceDocElementsReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *TraceDocElementsReq) GetChunkId() string {
	if x != nil {
		return x.ChunkId
	}
	return ""
}

type TraceDocElementsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Elements []*DocElement `protobuf:"bytes,1,rep,name=elements,proto3" json:"elements,omitempty"`
}

func (x *TraceDocElementsRsp) Reset() {
	*x = TraceDocElementsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraceDocElementsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraceDocElementsRsp) ProtoMessage() {}

func (x *TraceDocElementsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraceDocElementsRsp.ProtoReflect.Descriptor instead.
func (*TraceDocElementsRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{63}
}

func (x *TraceDocElementsRsp) GetElements() []*DocElement {
	if x != nil {
		return x.Elements
	}
	return nil
}

type RetryDocumentProcessReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContext     *UserContext `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"`
	KnowledgeBaseId string       `protobuf:"bytes,2,opt,name=knowledge_base_id,json=knowledgeBaseId,proto3" json:"knowledge_base_id,omitempty"`
	// @gotags: description:"文档id， 为空时自动重试知识库的所有失败文档"
	DocIds []string `protobuf:"bytes,3,rep,name=doc_ids,json=docIds,proto3" json:"doc_ids,omitempty" description:"文档id， 为空时自动重试知识库的所有失败文档"`
}

func (x *RetryDocumentProcessReq) Reset() {
	*x = RetryDocumentProcessReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetryDocumentProcessReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetryDocumentProcessReq) ProtoMessage() {}

func (x *RetryDocumentProcessReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetryDocumentProcessReq.ProtoReflect.Descriptor instead.
func (*RetryDocumentProcessReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{64}
}

func (x *RetryDocumentProcessReq) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *RetryDocumentProcessReq) GetKnowledgeBaseId() string {
	if x != nil {
		return x.KnowledgeBaseId
	}
	return ""
}

func (x *RetryDocumentProcessReq) GetDocIds() []string {
	if x != nil {
		return x.DocIds
	}
	return nil
}

type RetryDocumentProcessRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RetryDocumentProcessRsp) Reset() {
	*x = RetryDocumentProcessRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_knowledge_base_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetryDocumentProcessRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetryDocumentProcessRsp) ProtoMessage() {}

func (x *RetryDocumentProcessRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_knowledge_base_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetryDocumentProcessRsp.ProtoReflect.Descriptor instead.
func (*RetryDocumentProcessRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_knowledge_base_proto_rawDescGZIP(), []int{65}
}

var File_proto_rpc_knowledge_base_proto protoreflect.FileDescriptor

var file_proto_rpc_knowledge_base_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x70, 0x63, 0x5f, 0x6b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x12, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x6e, 0x67, 0x2f, 0x6d, 0x6c, 0x6f, 0x70, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3f, 0x0a, 0x0b, 0x41, 0x70, 0x70, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x42, 0x0a, 0x0c, 0x48, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x9d, 0x05, 0x0a,
	0x1b, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x48, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x12, 0x18, 0x0a, 0x07,
	0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x12, 0x39, 0x0a, 0x0e, 0x64, 0x6f, 0x63, 0x5f, 0x73, 0x76,
	0x63, 0x5f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x0c, 0x64, 0x6f, 0x63, 0x53, 0x76, 0x63, 0x48, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x12, 0x36, 0x0a, 0x0c, 0x68, 0x69, 0x70, 0x70, 0x6f, 0x5f, 0x68, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0b, 0x68, 0x69,
	0x70, 0x70, 0x6f, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x12, 0x36, 0x0a, 0x0c, 0x73, 0x63, 0x6f,
	0x70, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x0b, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x48, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x12, 0x3e, 0x0a, 0x10, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x68,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x0f, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x48, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x12, 0x38, 0x0a, 0x0d, 0x72, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x68, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x72,
	0x65, 0x72, 0x61, 0x6e, 0x6b, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x12, 0x3a, 0x0a, 0x0e, 0x61,
	0x75, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x61, 0x75, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x12, 0x49, 0x0a, 0x16, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x5f, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x5f, 0x68, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x14, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x48, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x12, 0x45, 0x0a, 0x14, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x61, 0x75, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x12, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x41, 0x75, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x12, 0x3f, 0x0a, 0x11, 0x6f, 0x63, 0x72,
	0x5f, 0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0f, 0x6f, 0x63, 0x72, 0x4c, 0x61,
	0x79, 0x6f, 0x75, 0x74, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x12, 0x30, 0x0a, 0x09, 0x76, 0x6c,
	0x5f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x08, 0x76, 0x6c, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x22, 0xe5, 0x02, 0x0a,
	0x11, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x3b, 0x0a, 0x0e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f,
	0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x52, 0x0d, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x6e, 0x75, 0x6d, 0x5f, 0x64, 0x6f, 0x63, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x6e, 0x75, 0x6d, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x6e, 0x75,
	0x6d, 0x5f, 0x61, 0x70, 0x70, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6e, 0x75,
	0x6d, 0x41, 0x70, 0x70, 0x73, 0x12, 0x37, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x5f, 0x72, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0c, 0x61, 0x70, 0x70, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x68,
	0x0a, 0x1d, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x74, 0x72,
	0x69, 0x65, 0x76, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x69, 0x65, 0x73, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x76, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x1b, 0x73, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x53, 0x74,
	0x72, 0x61, 0x74, 0x65, 0x67, 0x69, 0x65, 0x73, 0x12, 0x3a, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x48, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x52, 0x06, 0x68, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x22, 0xb3, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x4b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x35,
	0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x46, 0x0a, 0x0d, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x73, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x73, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52,
	0x0c, 0x6c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x1b, 0x0a,
	0x09, 0x69, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x69, 0x73, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x22, 0xd1, 0x02, 0x0a, 0x1a, 0x4c,
	0x69, 0x73, 0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x73, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x44, 0x0a, 0x0d, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12,
	0x41, 0x0a, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x12, 0x47, 0x0a, 0x0e, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x72, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x69,
	0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x08, 0x52, 0x0b, 0x69, 0x73, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x12, 0x3e,
	0x0a, 0x0b, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0a, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0x49,
	0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x73, 0x52, 0x73, 0x70, 0x12, 0x30, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x5c, 0x0a, 0x13, 0x47, 0x65, 0x74,
	0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71,
	0x12, 0x35, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x47, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x73, 0x70, 0x12, 0x30,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x79, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x12, 0x28, 0x0a, 0x04, 0x62, 0x61, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x04, 0x62, 0x61, 0x73, 0x65, 0x22, 0x46, 0x0a, 0x16, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61,
	0x73, 0x65, 0x52, 0x73, 0x70, 0x12, 0x2c, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x79, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a,
	0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x12, 0x28, 0x0a, 0x04, 0x62, 0x61, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x04, 0x62, 0x61, 0x73, 0x65, 0x22, 0x46,
	0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x73, 0x70, 0x12, 0x2c, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x62, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x19, 0x0a, 0x17, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73,
	0x65, 0x73, 0x52, 0x73, 0x70, 0x22, 0x9a, 0x02, 0x0a, 0x18, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65,
	0x76, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x75, 0x73,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x6b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x64,
	0x6f, 0x63, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08,
	0x64, 0x6f, 0x63, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x41, 0x0a, 0x10, 0x72, 0x65, 0x74, 0x72,
	0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x76, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0f, 0x72, 0x65, 0x74, 0x72,
	0x69, 0x65, 0x76, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x25, 0x0a, 0x0e, 0x64,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x72, 0x61,
	0x6e, 0x6b, 0x22, 0x2d, 0x0a, 0x03, 0x48, 0x69, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x68, 0x69, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x68, 0x69, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x22, 0xf6, 0x03, 0x0a, 0x18, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x73, 0x70, 0x12, 0x39,
	0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65,
	0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71,
	0x52, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x32, 0x0a, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x60, 0x0a,
	0x11, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x68, 0x69,
	0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x73, 0x70, 0x2e, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x48, 0x69, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f,
	0x76, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x48, 0x69, 0x74, 0x73, 0x12,
	0x67, 0x0a, 0x14, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x5f, 0x68, 0x69, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x73, 0x70, 0x2e, 0x46,
	0x75, 0x6c, 0x6c, 0x54, 0x65, 0x78, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x48, 0x69, 0x74, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x66, 0x75, 0x6c, 0x6c, 0x54, 0x65, 0x78, 0x74, 0x49,
	0x6e, 0x64, 0x65, 0x78, 0x48, 0x69, 0x74, 0x73, 0x1a, 0x4e, 0x0a, 0x14, 0x56, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x48, 0x69, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x20, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x69, 0x74, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x50, 0x0a, 0x16, 0x46, 0x75, 0x6c, 0x6c,
	0x54, 0x65, 0x78, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x48, 0x69, 0x74, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x20, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x69, 0x74, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xbf, 0x02, 0x0a, 0x13, 0x43,
	0x68, 0x75, 0x6e, 0x6b, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x22, 0x0a, 0x05, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52,
	0x05, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x2a, 0x0a, 0x11,
	0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x6f, 0x63, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x6f, 0x63, 0x49, 0x64, 0x12,
	0x2e, 0x0a, 0x13, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x73,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x64, 0x6f, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x64, 0x6f, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x0c, 0x61, 0x62,
	0x6f, 0x76, 0x65, 0x5f, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52, 0x0b,
	0x61, 0x62, 0x6f, 0x76, 0x65, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x12, 0x2f, 0x0a, 0x0c, 0x6c,
	0x61, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52,
	0x0b, 0x6c, 0x61, 0x74, 0x65, 0x72, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x22, 0xe6, 0x01, 0x0a,
	0x0c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a,
	0x03, 0x64, 0x6f, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x03, 0x64, 0x6f, 0x63,
	0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x75, 0x6d, 0x5f, 0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6e, 0x75, 0x6d, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x12,
	0x1d, 0x0a, 0x0a, 0x6e, 0x75, 0x6d, 0x5f, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x6e, 0x75, 0x6d, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x12, 0x35,
	0x0a, 0x04, 0x70, 0x72, 0x6f, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52,
	0x04, 0x70, 0x72, 0x6f, 0x67, 0x12, 0x3e, 0x0a, 0x0f, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67,
	0x79, 0x5f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x4f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x52, 0x0e, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x4f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x22, 0x75, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x12, 0x2a, 0x0a, 0x11, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x61,
	0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x64, 0x22, 0x3f, 0x0a, 0x10,
	0x4c, 0x69, 0x73, 0x74, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x73, 0x70,
	0x12, 0x2b, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xaa, 0x01,
	0x0a, 0x12, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x0b,
	0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x6b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x6f, 0x63, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x6f, 0x63, 0x49, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x14, 0x0a, 0x12, 0x44, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70,
	0x22, 0xad, 0x02, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x12, 0x2a, 0x0a, 0x11, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62,
	0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x15, 0x0a,
	0x06, 0x64, 0x6f, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64,
	0x6f, 0x63, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x71,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50,
	0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x48, 0x0a, 0x14, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x73,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x16, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x22, 0x8f, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x52, 0x73, 0x70, 0x12, 0x28, 0x0a, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x61,
	0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x22, 0x87, 0x01, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x35,
	0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x35, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x62, 0x0a, 0x17,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61,
	0x62, 0x6c, 0x65, 0x73, 0x52, 0x73, 0x70, 0x12, 0x28, 0x0a, 0x06, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x54, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x73, 0x5f, 0x69, 0x6e, 0x64, 0x69, 0x63, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x65, 0x73, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x65, 0x73,
	0x22, 0x77, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x72, 0x65, 0x65, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x2a, 0x0a,
	0x11, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x64, 0x22, 0x3d, 0x0a, 0x12, 0x47, 0x65, 0x74,
	0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x65, 0x65, 0x52, 0x73, 0x70, 0x12,
	0x27, 0x0a, 0x04, 0x74, 0x72, 0x65, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72,
	0x65, 0x65, 0x52, 0x04, 0x74, 0x72, 0x65, 0x65, 0x22, 0xce, 0x03, 0x0a, 0x1c, 0x53, 0x75, 0x62,
	0x6d, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x6f, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x12, 0x47, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x10, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61,
	0x74, 0x68, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x5f, 0x6b, 0x62, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x61, 0x75, 0x74, 0x6f,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4b, 0x62, 0x12, 0x4e, 0x0a, 0x15, 0x64, 0x6f, 0x63, 0x5f,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x44, 0x6f, 0x63, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x13, 0x64, 0x6f, 0x63, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x35, 0x0a, 0x0c, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x0b, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x38, 0x0a, 0x0d, 0x63, 0x6f, 0x72, 0x70, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43,
	0x6f, 0x72, 0x70, 0x75, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c, 0x63, 0x6f, 0x72,
	0x70, 0x75, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x41, 0x0a, 0x1c, 0x53, 0x75, 0x62,
	0x6d, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x6f, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x73, 0x70, 0x12, 0x21, 0x0a, 0x03, 0x64, 0x6f, 0x63,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x03, 0x64, 0x6f, 0x63, 0x22, 0xe3, 0x01, 0x0a,
	0x1e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x35, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x47, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x10, 0x73,
	0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12,
	0x2a, 0x0a, 0x11, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x73,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x64,
	0x6f, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x6f, 0x63,
	0x49, 0x64, 0x22, 0x20, 0x0a, 0x1e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x46, 0x69, 0x6c, 0x65,
	0x46, 0x72, 0x6f, 0x6d, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73,
	0x65, 0x52, 0x73, 0x70, 0x22, 0x6b, 0x0a, 0x0c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x4e, 0x6f, 0x64, 0x65, 0x12, 0x37, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x22, 0x68, 0x0a, 0x0c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x65,
	0x65, 0x12, 0x27, 0x0a, 0x04, 0x6e, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x4e, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x6e, 0x6f, 0x64, 0x65, 0x12, 0x2f, 0x0a, 0x08, 0x63, 0x68,
	0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x65,
	0x65, 0x52, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x22, 0xaa, 0x01, 0x0a, 0x1b,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x56, 0x69, 0x73, 0x69, 0x62, 0x6c,
	0x65, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61,
	0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x52, 0x65, 0x74,
	0x72, 0x69, 0x65, 0x76, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x5a, 0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x3b, 0x0a, 0x0e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x0d, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x42, 0x61, 0x73, 0x65, 0x22, 0x85, 0x02, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43,
	0x68, 0x75, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x2a,
	0x0a, 0x11, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x6f,
	0x63, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x6f, 0x63, 0x49,
	0x64, 0x12, 0x20, 0x0a, 0x0c, 0x6f, 0x72, 0x69, 0x5f, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x72, 0x69, 0x43, 0x68, 0x75, 0x6e,
	0x6b, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x3d, 0x0a,
	0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4f, 0x72, 0x69, 0x67,
	0x69, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x34, 0x0a, 0x0e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x22,
	0x0a, 0x05, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52, 0x05, 0x63, 0x68, 0x75,
	0x6e, 0x6b, 0x22, 0xc7, 0x01, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x68, 0x75,
	0x6e, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52,
	0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x2a, 0x0a, 0x11,
	0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x6f, 0x63, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x6f, 0x63, 0x49, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x6f, 0x72,
	0x69, 0x5f, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6f, 0x72, 0x69, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x64, 0x22, 0x34, 0x0a, 0x0e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x22,
	0x0a, 0x05, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52, 0x05, 0x63, 0x68, 0x75,
	0x6e, 0x6b, 0x22, 0xe1, 0x01, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x68, 0x75,
	0x6e, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52,
	0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x2a, 0x0a, 0x11,
	0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x6f, 0x63, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x6f, 0x63, 0x49, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x6f, 0x72,
	0x69, 0x5f, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6f, 0x72, 0x69, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x34, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x22, 0x0a, 0x05, 0x63, 0x68, 0x75, 0x6e,
	0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52, 0x05, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x22, 0xbf, 0x01, 0x0a,
	0x19, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x4e,
	0x0a, 0x15, 0x64, 0x6f, 0x63, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x63, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x13, 0x64, 0x6f, 0x63, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0xa2,
	0x04, 0x0a, 0x21, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x46, 0x69, 0x6c,
	0x65, 0x73, 0x54, 0x6f, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x0b,
	0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x47, 0x0a, 0x11, 0x73,
	0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x46,
	0x69, 0x6c, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x52, 0x10, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x73, 0x12,
	0x4e, 0x0a, 0x15, 0x64, 0x6f, 0x63, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x63, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x13, 0x64, 0x6f, 0x63, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x24, 0x0a, 0x0e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x6b,
	0x62, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x61, 0x75, 0x74, 0x6f, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4b, 0x62, 0x12, 0x35, 0x0a, 0x0c, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x0b, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4b, 0x0a, 0x14,
	0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x12, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x46,
	0x69, 0x6c, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x38, 0x0a, 0x0d, 0x63, 0x6f, 0x72,
	0x70, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x72, 0x70, 0x75, 0x73, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c, 0x63, 0x6f, 0x72, 0x70, 0x75, 0x73, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x22, 0xda, 0x01, 0x0a, 0x1a, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x2b, 0x0a, 0x08, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x1e, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x2e, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65,
	0x22, 0xd2, 0x01, 0x0a, 0x21, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x46,
	0x69, 0x6c, 0x65, 0x73, 0x54, 0x6f, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x6f, 0x6e, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x64, 0x6f, 0x6e, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6e, 0x75,
	0x6d, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0c, 0x6e, 0x75, 0x6d, 0x53, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x6e, 0x75, 0x6d, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x6e, 0x75, 0x6d, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x12, 0x3f, 0x0a, 0x09, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x09, 0x64, 0x6f, 0x63, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x9b, 0x01, 0x0a, 0x0d, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65,
	0x76, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73,
	0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x6f, 0x63, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x64, 0x6f, 0x63, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x12, 0x41, 0x0a, 0x10, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x0f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x22, 0x9a, 0x02, 0x0a, 0x1d, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65,
	0x43, 0x72, 0x6f, 0x73, 0x73, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61,
	0x73, 0x65, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52,
	0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65,
	0x72, 0x79, 0x12, 0x2c, 0x0a, 0x06, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x76, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x06, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x73,
	0x12, 0x38, 0x0a, 0x0d, 0x72, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x52, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x0c, 0x72, 0x65,
	0x72, 0x61, 0x6e, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x72, 0x61, 0x6e,
	0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x4d, 0x6f, 0x64, 0x65,
	0x22, 0x93, 0x01, 0x0a, 0x1d, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x43, 0x72, 0x6f,
	0x73, 0x73, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52,
	0x73, 0x70, 0x12, 0x3e, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x74, 0x72,
	0x69, 0x65, 0x76, 0x65, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x52, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x32, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b,
	0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x96, 0x02, 0x0a, 0x1e, 0x53, 0x75, 0x62, 0x6d, 0x69,
	0x74, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x54, 0x6f, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x12, 0x2a, 0x0a, 0x11, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x61,
	0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x36, 0x34, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x66, 0x69, 0x6c, 0x65, 0x42, 0x61, 0x73, 0x65, 0x36, 0x34, 0x12, 0x24, 0x0a, 0x06, 0x63, 0x68,
	0x75, 0x6e, 0x6b, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52, 0x06, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x73,
	0x12, 0x2d, 0x0a, 0x08, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x63, 0x45, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22,
	0x4d, 0x0a, 0x1e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x54,
	0x6f, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x73,
	0x70, 0x12, 0x2b, 0x0a, 0x08, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x63, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0xb8,
	0x01, 0x0a, 0x15, 0x49, 0x73, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x69,
	0x73, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12,
	0x2a, 0x0a, 0x11, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x73,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x62, 0x61, 0x73, 0x65, 0x36, 0x34, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66,
	0x69, 0x6c, 0x65, 0x42, 0x61, 0x73, 0x65, 0x36, 0x34, 0x22, 0x60, 0x0a, 0x15, 0x49, 0x73, 0x44,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x52,
	0x73, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x78, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x65, 0x78, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x2b,
	0x0a, 0x08, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x08, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x69, 0x0a, 0x12, 0x44,
	0x6f, 0x63, 0x53, 0x76, 0x63, 0x4c, 0x6f, 0x61, 0x64, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52, 0x73,
	0x70, 0x12, 0x24, 0x0a, 0x06, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52,
	0x06, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x12, 0x2d, 0x0a, 0x08, 0x65, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x44, 0x6f, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x65, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x55, 0x0a, 0x1c, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0xb0, 0x01,
	0x0a, 0x1c, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x73, 0x70, 0x12, 0x2e,
	0x0a, 0x13, 0x6e, 0x75, 0x6d, 0x5f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f,
	0x62, 0x61, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x6e, 0x75, 0x6d,
	0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x73, 0x12, 0x19,
	0x0a, 0x08, 0x6e, 0x75, 0x6d, 0x5f, 0x64, 0x6f, 0x63, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x6e, 0x75, 0x6d, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x75, 0x6d,
	0x5f, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6e,
	0x75, 0x6d, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x75, 0x6d, 0x73,
	0x5f, 0x61, 0x75, 0x67, 0x5f, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0d, 0x6e, 0x75, 0x6d, 0x73, 0x41, 0x75, 0x67, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x73,
	0x22, 0x97, 0x01, 0x0a, 0x15, 0x53, 0x68, 0x61, 0x72, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x12, 0x2a, 0x0a, 0x11, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62,
	0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x69, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x69, 0x73, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x22, 0x17, 0x0a, 0x15, 0x53, 0x68,
	0x61, 0x72, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x52, 0x73, 0x70, 0x22, 0xcb, 0x01, 0x0a, 0x17, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x31, 0x0a, 0x0a, 0x72, 0x61, 0x74, 0x65, 0x5f,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52,
	0x09, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73,
	0x5f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0a, 0x69, 0x73, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x12, 0x24, 0x0a, 0x03, 0x63,
	0x74, 0x78, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74,
	0x78, 0x22, 0x51, 0x0a, 0x17, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0f,
	0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x73, 0x76, 0x63, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x53, 0x76,
	0x63, 0x55, 0x72, 0x6c, 0x22, 0x67, 0x0a, 0x13, 0x54, 0x72, 0x61, 0x63, 0x65, 0x44, 0x6f, 0x63,
	0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0c, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x64, 0x22, 0x44, 0x0a,
	0x13, 0x54, 0x72, 0x61, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x52, 0x73, 0x70, 0x12, 0x2d, 0x0a, 0x08, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44,
	0x6f, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x65, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x22, 0x95, 0x01, 0x0a, 0x17, 0x52, 0x65, 0x74, 0x72, 0x79, 0x44, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x12,
	0x35, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x6f, 0x63, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x63, 0x49, 0x64, 0x73, 0x22, 0x19, 0x0a, 0x17, 0x52,
	0x65, 0x74, 0x72, 0x79, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x52, 0x73, 0x70, 0x2a, 0x36, 0x0a, 0x13, 0x46, 0x69, 0x6c, 0x65, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x10, 0x0a,
	0x0c, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47, 0x10, 0x00, 0x12,
	0x0d, 0x0a, 0x09, 0x4b, 0x42, 0x5f, 0x4d, 0x41, 0x4e, 0x41, 0x47, 0x45, 0x10, 0x01, 0x2a, 0x29,
	0x0a, 0x14, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x07, 0x0a, 0x03, 0x44, 0x49, 0x52, 0x10, 0x00, 0x12,
	0x08, 0x0a, 0x04, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x01, 0x2a, 0x72, 0x0a, 0x11, 0x44, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x0b,
	0x0a, 0x07, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x4c,
	0x4f, 0x41, 0x44, 0x5f, 0x43, 0x48, 0x55, 0x4e, 0x4b, 0x10, 0x0b, 0x12, 0x15, 0x0a, 0x11, 0x4b,
	0x4e, 0x4f, 0x57, 0x4c, 0x45, 0x44, 0x47, 0x45, 0x5f, 0x41, 0x55, 0x47, 0x4d, 0x45, 0x4e, 0x54,
	0x10, 0x0c, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x45,
	0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x49, 0x4e, 0x44, 0x45, 0x58, 0x5f, 0x42, 0x55, 0x49, 0x4c,
	0x44, 0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x44, 0x4f, 0x4e, 0x45, 0x10, 0x04, 0x32, 0xac, 0x12,
	0x0a, 0x14, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x4d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x50, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x73, 0x12, 0x1c, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x42, 0x61, 0x73, 0x65, 0x73, 0x52, 0x73, 0x70, 0x12, 0x4a, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x12, 0x1a, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x47, 0x65, 0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73,
	0x65, 0x52, 0x73, 0x70, 0x12, 0x53, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x12, 0x1d, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x73, 0x70, 0x12, 0x53, 0x0a, 0x13, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x12, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x73, 0x70, 0x12, 0x56,
	0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x73, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61,
	0x73, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61,
	0x73, 0x65, 0x73, 0x52, 0x73, 0x70, 0x12, 0x56, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x12, 0x1e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x52, 0x73, 0x70, 0x12, 0x41,
	0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12,
	0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x6f, 0x63, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x73,
	0x70, 0x12, 0x47, 0x0a, 0x0f, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x6f, 0x63, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x69, 0x73,
	0x61, 0x62, 0x6c, 0x65, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x44,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x50, 0x0a, 0x12, 0x4c, 0x69,
	0x73, 0x74, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x73,
	0x12, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1c,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x6f, 0x63, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x52, 0x73, 0x70, 0x12, 0x59, 0x0a, 0x15,
	0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x12, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65,
	0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52,
	0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x42, 0x61, 0x73, 0x65, 0x52, 0x73, 0x70, 0x12, 0x47, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x44, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x65, 0x65, 0x12, 0x19, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72,
	0x65, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65,
	0x74, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x65, 0x65, 0x52, 0x73, 0x70,
	0x12, 0x65, 0x0a, 0x19, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x6f,
	0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x12, 0x23, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65,
	0x54, 0x6f, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52,
	0x65, 0x71, 0x1a, 0x23, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69,
	0x74, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x6f, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x42, 0x61, 0x73, 0x65, 0x52, 0x73, 0x70, 0x12, 0x6b, 0x0a, 0x1b, 0x52, 0x65, 0x6d, 0x6f, 0x76,
	0x65, 0x46, 0x69, 0x6c, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x12, 0x25, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52,
	0x65, 0x6d, 0x6f, 0x76, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x4b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x46, 0x69, 0x6c, 0x65,
	0x46, 0x72, 0x6f, 0x6d, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73,
	0x65, 0x52, 0x73, 0x70, 0x12, 0x62, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x22, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x3b, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x12, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x15,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x68, 0x75,
	0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x3b, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43,
	0x68, 0x75, 0x6e, 0x6b, 0x12, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52,
	0x73, 0x70, 0x12, 0x3b, 0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x68, 0x75, 0x6e,
	0x6b, 0x12, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12,
	0x55, 0x0a, 0x16, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x20, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x63, 0x53, 0x76, 0x63, 0x4c, 0x6f, 0x61, 0x64, 0x43, 0x68,
	0x75, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x76, 0x0a, 0x1e, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x75,
	0x62, 0x6d, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x54, 0x6f, 0x4b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x12, 0x28, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x73,
	0x54, 0x6f, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52,
	0x65, 0x71, 0x1a, 0x28, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x53,
	0x75, 0x62, 0x6d, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x54, 0x6f, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x73, 0x70, 0x30, 0x01, 0x12, 0x68,
	0x0a, 0x1a, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x12, 0x24, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x43, 0x72, 0x6f,
	0x73, 0x73, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52,
	0x65, 0x71, 0x1a, 0x24, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x76, 0x65, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x73, 0x70, 0x12, 0x6b, 0x0a, 0x1b, 0x53, 0x75, 0x62, 0x6d,
	0x69, 0x74, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x54, 0x6f, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x12, 0x25, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x54, 0x6f, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x25,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x43, 0x68, 0x75,
	0x6e, 0x6b, 0x73, 0x54, 0x6f, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61,
	0x73, 0x65, 0x52, 0x73, 0x70, 0x12, 0x50, 0x0a, 0x12, 0x49, 0x73, 0x44, 0x6f, 0x63, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x73, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x78,
	0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x49, 0x73, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x69, 0x73,
	0x74, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x65, 0x0a, 0x19, 0x43, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x73, 0x12, 0x23, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x73, 0x70, 0x12, 0x50,
	0x0a, 0x12, 0x53, 0x68, 0x61, 0x72, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x42, 0x61, 0x73, 0x65, 0x12, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x68, 0x61,
	0x72, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52,
	0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x68, 0x61, 0x72, 0x65,
	0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x73, 0x70,
	0x12, 0x56, 0x0a, 0x14, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x4b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x73, 0x70, 0x12, 0x4a, 0x0a, 0x10, 0x54, 0x72, 0x61, 0x63,
	0x65, 0x44, 0x6f, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x1a, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x45, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x54, 0x72, 0x61, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x52, 0x73, 0x70, 0x12, 0x56, 0x0a, 0x14, 0x52, 0x65, 0x74, 0x72, 0x79, 0x44, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x79, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x79, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x73, 0x70, 0x42, 0x26, 0x5a, 0x24,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70,
	0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70,
	0x62, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_rpc_knowledge_base_proto_rawDescOnce sync.Once
	file_proto_rpc_knowledge_base_proto_rawDescData = file_proto_rpc_knowledge_base_proto_rawDesc
)

func file_proto_rpc_knowledge_base_proto_rawDescGZIP() []byte {
	file_proto_rpc_knowledge_base_proto_rawDescOnce.Do(func() {
		file_proto_rpc_knowledge_base_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_rpc_knowledge_base_proto_rawDescData)
	})
	return file_proto_rpc_knowledge_base_proto_rawDescData
}

var file_proto_rpc_knowledge_base_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_proto_rpc_knowledge_base_proto_msgTypes = make([]protoimpl.MessageInfo, 68)
var file_proto_rpc_knowledge_base_proto_goTypes = []interface{}{
	(FileOperationSource)(0),                  // 0: proto.FileOperationSource
	(DocumentNodeCategory)(0),                 // 1: proto.DocumentNodeCategory
	(DocumentTaskStage)(0),                    // 2: proto.DocumentTaskStage
	(*AppRelation)(nil),                       // 3: proto.AppRelation
	(*HealthStatus)(nil),                      // 4: proto.HealthStatus
	(*KnowledgeBaseHealthOverview)(nil),       // 5: proto.KnowledgeBaseHealthOverview
	(*KnowledgeBaseInfo)(nil),                 // 6: proto.KnowledgeBaseInfo
	(*ListKnowledgeBasesReq)(nil),             // 7: proto.ListKnowledgeBasesReq
	(*ListKnowledgeBasesSelector)(nil),        // 8: proto.ListKnowledgeBasesSelector
	(*ListKnowledgeBasesRsp)(nil),             // 9: proto.ListKnowledgeBasesRsp
	(*GetKnowledgeBaseReq)(nil),               // 10: proto.GetKnowledgeBaseReq
	(*GetKnowledgeBaseRsp)(nil),               // 11: proto.GetKnowledgeBaseRsp
	(*CreateKnowledgeBaseReq)(nil),            // 12: proto.CreateKnowledgeBaseReq
	(*CreateKnowledgeBaseRsp)(nil),            // 13: proto.CreateKnowledgeBaseRsp
	(*UpdateKnowledgeBaseReq)(nil),            // 14: proto.UpdateKnowledgeBaseReq
	(*UpdateKnowledgeBaseRsp)(nil),            // 15: proto.UpdateKnowledgeBaseRsp
	(*DeleteKnowledgeBasesReq)(nil),           // 16: proto.DeleteKnowledgeBasesReq
	(*DeleteKnowledgeBasesRsp)(nil),           // 17: proto.DeleteKnowledgeBasesRsp
	(*RetrieveKnowledgeBaseReq)(nil),          // 18: proto.RetrieveKnowledgeBaseReq
	(*Hit)(nil),                               // 19: proto.Hit
	(*RetrieveKnowledgeBaseRsp)(nil),          // 20: proto.RetrieveKnowledgeBaseRsp
	(*ChunkRetrieveResult)(nil),               // 21: proto.ChunkRetrieveResult
	(*DocumentInfo)(nil),                      // 22: proto.DocumentInfo
	(*ListDocumentsReq)(nil),                  // 23: proto.ListDocumentsReq
	(*ListDocumentsRsp)(nil),                  // 24: proto.ListDocumentsRsp
	(*DisableDocumentReq)(nil),                // 25: proto.DisableDocumentReq
	(*DisableDocumentRsp)(nil),                // 26: proto.DisableDocumentRsp
	(*ListDocumentChunksReq)(nil),             // 27: proto.ListDocumentChunksReq
	(*ListDocumentChunksRsp)(nil),             // 28: proto.ListDocumentChunksRsp
	(*ListConnectionTablesReq)(nil),           // 29: proto.ListConnectionTablesReq
	(*ListConnectionTablesRsp)(nil),           // 30: proto.ListConnectionTablesRsp
	(*GetDocumentTreeReq)(nil),                // 31: proto.GetDocumentTreeReq
	(*GetDocumentTreeRsp)(nil),                // 32: proto.GetDocumentTreeRsp
	(*SubmitFileToKnowledgeBaseReq)(nil),      // 33: proto.SubmitFileToKnowledgeBaseReq
	(*SubmitFileToKnowledgeBaseRsp)(nil),      // 34: proto.SubmitFileToKnowledgeBaseRsp
	(*RemoveFileFromKnowledgeBaseReq)(nil),    // 35: proto.RemoveFileFromKnowledgeBaseReq
	(*RemoveFileFromKnowledgeBaseRsp)(nil),    // 36: proto.RemoveFileFromKnowledgeBaseRsp
	(*DocumentNode)(nil),                      // 37: proto.DocumentNode
	(*DocumentTree)(nil),                      // 38: proto.DocumentTree
	(*UpdateKnowledgeBaseStateReq)(nil),       // 39: proto.UpdateKnowledgeBaseStateReq
	(*UpdateKnowledgeBaseStateRsp)(nil),       // 40: proto.UpdateKnowledgeBaseStateRsp
	(*CreateChunkReq)(nil),                    // 41: proto.CreateChunkReq
	(*CreateChunkRsp)(nil),                    // 42: proto.CreateChunkRsp
	(*DeleteChunkReq)(nil),                    // 43: proto.DeleteChunkReq
	(*DeleteChunkRsp)(nil),                    // 44: proto.DeleteChunkRsp
	(*UpdateChunkReq)(nil),                    // 45: proto.UpdateChunkReq
	(*UpdateChunkRsp)(nil),                    // 46: proto.UpdateChunkRsp
	(*PreviewDocumentProcessReq)(nil),         // 47: proto.PreviewDocumentProcessReq
	(*SyncSubmitFilesToKnowledgeBaseReq)(nil), // 48: proto.SyncSubmitFilesToKnowledgeBaseReq
	(*DocumentProcessingProgress)(nil),        // 49: proto.DocumentProcessingProgress
	(*SyncSubmitFilesToKnowledgeBaseRsp)(nil), // 50: proto.SyncSubmitFilesToKnowledgeBaseRsp
	(*RetrieveRange)(nil),                     // 51: proto.RetrieveRange
	(*RetrieveCrossKnowledgeBaseReq)(nil),     // 52: proto.RetrieveCrossKnowledgeBaseReq
	(*RetrieveCrossKnowledgeBaseRsp)(nil),     // 53: proto.RetrieveCrossKnowledgeBaseRsp
	(*SubmitChunksToKnowledgeBaseReq)(nil),    // 54: proto.SubmitChunksToKnowledgeBaseReq
	(*SubmitChunksToKnowledgeBaseRsp)(nil),    // 55: proto.SubmitChunksToKnowledgeBaseRsp
	(*IsDocumentExistentReq)(nil),             // 56: proto.IsDocumentExistentReq
	(*IsDocumentExistentRsp)(nil),             // 57: proto.IsDocumentExistentRsp
	(*DocSvcLoadChunkRsp)(nil),                // 58: proto.DocSvcLoadChunkRsp
	(*CollectKnowledgeBaseStatsReq)(nil),      // 59: proto.CollectKnowledgeBaseStatsReq
	(*CollectKnowledgeBaseStatsRsp)(nil),      // 60: proto.CollectKnowledgeBaseStatsRsp
	(*ShareKnowledgeBaseReq)(nil),             // 61: proto.ShareKnowledgeBaseReq
	(*ShareKnowledgeBaseRsp)(nil),             // 62: proto.ShareKnowledgeBaseRsp
	(*PublishKnowledgeBaseReq)(nil),           // 63: proto.PublishKnowledgeBaseReq
	(*PublishKnowledgeBaseRsp)(nil),           // 64: proto.PublishKnowledgeBaseRsp
	(*TraceDocElementsReq)(nil),               // 65: proto.TraceDocElementsReq
	(*TraceDocElementsRsp)(nil),               // 66: proto.TraceDocElementsRsp
	(*RetryDocumentProcessReq)(nil),           // 67: proto.RetryDocumentProcessReq
	(*RetryDocumentProcessRsp)(nil),           // 68: proto.RetryDocumentProcessRsp
	nil,                                       // 69: proto.RetrieveKnowledgeBaseRsp.VectorIndexHitsEntry
	nil,                                       // 70: proto.RetrieveKnowledgeBaseRsp.FullTextIndexHitsEntry
	(*KnowledgeBase)(nil),                     // 71: proto.KnowledgeBase
	(KnowledgeBaseRetrieveStrategy)(0),        // 72: proto.KnowledgeBaseRetrieveStrategy
	(*UserContext)(nil),                       // 73: proto.UserContext
	(KnowledgeBaseContentType)(0),             // 74: proto.KnowledgeBaseContentType
	(KnowledgeBaseSourceType)(0),              // 75: proto.KnowledgeBaseSourceType
	(KnowledgeBaseRegistryType)(0),            // 76: proto.KnowledgeBaseRegistryType
	(KnowledgeBaseSceneType)(0),               // 77: proto.KnowledgeBaseSceneType
	(*RetrievalConfig)(nil),                   // 78: proto.RetrievalConfig
	(*Chunk)(nil),                             // 79: proto.Chunk
	(*Document)(nil),                          // 80: proto.Document
	(StrategyOrigin)(0),                       // 81: proto.StrategyOrigin
	(*PageReq)(nil),                           // 82: proto.PageReq
	(ChunkSourceType)(0),                      // 83: proto.ChunkSourceType
	(*ChunkInfo)(nil),                         // 84: proto.ChunkInfo
	(*DataConnection)(nil),                    // 85: proto.DataConnection
	(*TableInfo)(nil),                         // 86: proto.TableInfo
	(*DocProcessingConfig)(nil),               // 87: proto.DocProcessingConfig
	(*TableConfig)(nil),                       // 88: proto.TableConfig
	(*CorpusConfig)(nil),                      // 89: proto.CorpusConfig
	(OriginalContentType)(0),                  // 90: proto.OriginalContentType
	(DocumentFileSource)(0),                   // 91: proto.DocumentFileSource
	(*RerankParams)(nil),                      // 92: proto.RerankParams
	(*DocElement)(nil),                        // 93: proto.DocElement
	(*serving.RateLimit)(nil),                 // 94: serving.RateLimit
}
var file_proto_rpc_knowledge_base_proto_depIdxs = []int32{
	4,   // 0: proto.KnowledgeBaseHealthOverview.doc_svc_health:type_name -> proto.HealthStatus
	4,   // 1: proto.KnowledgeBaseHealthOverview.hippo_health:type_name -> proto.HealthStatus
	4,   // 2: proto.KnowledgeBaseHealthOverview.scope_health:type_name -> proto.HealthStatus
	4,   // 3: proto.KnowledgeBaseHealthOverview.embedding_health:type_name -> proto.HealthStatus
	4,   // 4: proto.KnowledgeBaseHealthOverview.rerank_health:type_name -> proto.HealthStatus
	4,   // 5: proto.KnowledgeBaseHealthOverview.augment_health:type_name -> proto.HealthStatus
	4,   // 6: proto.KnowledgeBaseHealthOverview.custom_strategy_health:type_name -> proto.HealthStatus
	4,   // 7: proto.KnowledgeBaseHealthOverview.image_augment_health:type_name -> proto.HealthStatus
	4,   // 8: proto.KnowledgeBaseHealthOverview.ocr_layout_health:type_name -> proto.HealthStatus
	4,   // 9: proto.KnowledgeBaseHealthOverview.vl_health:type_name -> proto.HealthStatus
	71,  // 10: proto.KnowledgeBaseInfo.knowledge_base:type_name -> proto.KnowledgeBase
	3,   // 11: proto.KnowledgeBaseInfo.app_relations:type_name -> proto.AppRelation
	72,  // 12: proto.KnowledgeBaseInfo.supported_retrieve_strategies:type_name -> proto.KnowledgeBaseRetrieveStrategy
	5,   // 13: proto.KnowledgeBaseInfo.health:type_name -> proto.KnowledgeBaseHealthOverview
	73,  // 14: proto.ListKnowledgeBasesReq.user_context:type_name -> proto.UserContext
	8,   // 15: proto.ListKnowledgeBasesReq.list_selector:type_name -> proto.ListKnowledgeBasesSelector
	74,  // 16: proto.ListKnowledgeBasesSelector.content_types:type_name -> proto.KnowledgeBaseContentType
	75,  // 17: proto.ListKnowledgeBasesSelector.source_types:type_name -> proto.KnowledgeBaseSourceType
	76,  // 18: proto.ListKnowledgeBasesSelector.registry_types:type_name -> proto.KnowledgeBaseRegistryType
	77,  // 19: proto.ListKnowledgeBasesSelector.scene_types:type_name -> proto.KnowledgeBaseSceneType
	6,   // 20: proto.ListKnowledgeBasesRsp.result:type_name -> proto.KnowledgeBaseInfo
	73,  // 21: proto.GetKnowledgeBaseReq.user_context:type_name -> proto.UserContext
	6,   // 22: proto.GetKnowledgeBaseRsp.result:type_name -> proto.KnowledgeBaseInfo
	73,  // 23: proto.CreateKnowledgeBaseReq.user_context:type_name -> proto.UserContext
	71,  // 24: proto.CreateKnowledgeBaseReq.base:type_name -> proto.KnowledgeBase
	71,  // 25: proto.CreateKnowledgeBaseRsp.result:type_name -> proto.KnowledgeBase
	73,  // 26: proto.UpdateKnowledgeBaseReq.user_context:type_name -> proto.UserContext
	71,  // 27: proto.UpdateKnowledgeBaseReq.base:type_name -> proto.KnowledgeBase
	71,  // 28: proto.UpdateKnowledgeBaseRsp.result:type_name -> proto.KnowledgeBase
	73,  // 29: proto.DeleteKnowledgeBasesReq.user_context:type_name -> proto.UserContext
	73,  // 30: proto.RetrieveKnowledgeBaseReq.user_context:type_name -> proto.UserContext
	78,  // 31: proto.RetrieveKnowledgeBaseReq.retrieval_config:type_name -> proto.RetrievalConfig
	18,  // 32: proto.RetrieveKnowledgeBaseRsp.request:type_name -> proto.RetrieveKnowledgeBaseReq
	21,  // 33: proto.RetrieveKnowledgeBaseRsp.result:type_name -> proto.ChunkRetrieveResult
	69,  // 34: proto.RetrieveKnowledgeBaseRsp.vector_index_hits:type_name -> proto.RetrieveKnowledgeBaseRsp.VectorIndexHitsEntry
	70,  // 35: proto.RetrieveKnowledgeBaseRsp.full_text_index_hits:type_name -> proto.RetrieveKnowledgeBaseRsp.FullTextIndexHitsEntry
	79,  // 36: proto.ChunkRetrieveResult.chunk:type_name -> proto.Chunk
	79,  // 37: proto.ChunkRetrieveResult.above_chunks:type_name -> proto.Chunk
	79,  // 38: proto.ChunkRetrieveResult.later_chunks:type_name -> proto.Chunk
	80,  // 39: proto.DocumentInfo.doc:type_name -> proto.Document
	49,  // 40: proto.DocumentInfo.prog:type_name -> proto.DocumentProcessingProgress
	81,  // 41: proto.DocumentInfo.strategy_origin:type_name -> proto.StrategyOrigin
	73,  // 42: proto.ListDocumentsReq.user_context:type_name -> proto.UserContext
	22,  // 43: proto.ListDocumentsRsp.result:type_name -> proto.DocumentInfo
	73,  // 44: proto.DisableDocumentReq.user_context:type_name -> proto.UserContext
	73,  // 45: proto.ListDocumentChunksReq.user_context:type_name -> proto.UserContext
	82,  // 46: proto.ListDocumentChunksReq.page_req:type_name -> proto.PageReq
	83,  // 47: proto.ListDocumentChunksReq.source_type_selector:type_name -> proto.ChunkSourceType
	84,  // 48: proto.ListDocumentChunksRsp.result:type_name -> proto.ChunkInfo
	73,  // 49: proto.ListConnectionTablesReq.user_context:type_name -> proto.UserContext
	85,  // 50: proto.ListConnectionTablesReq.connection:type_name -> proto.DataConnection
	86,  // 51: proto.ListConnectionTablesRsp.tables:type_name -> proto.TableInfo
	73,  // 52: proto.GetDocumentTreeReq.user_context:type_name -> proto.UserContext
	38,  // 53: proto.GetDocumentTreeRsp.tree:type_name -> proto.DocumentTree
	73,  // 54: proto.SubmitFileToKnowledgeBaseReq.user_context:type_name -> proto.UserContext
	0,   // 55: proto.SubmitFileToKnowledgeBaseReq.submission_source:type_name -> proto.FileOperationSource
	87,  // 56: proto.SubmitFileToKnowledgeBaseReq.doc_processing_config:type_name -> proto.DocProcessingConfig
	88,  // 57: proto.SubmitFileToKnowledgeBaseReq.table_config:type_name -> proto.TableConfig
	89,  // 58: proto.SubmitFileToKnowledgeBaseReq.corpus_config:type_name -> proto.CorpusConfig
	80,  // 59: proto.SubmitFileToKnowledgeBaseRsp.doc:type_name -> proto.Document
	73,  // 60: proto.RemoveFileFromKnowledgeBaseReq.user_context:type_name -> proto.UserContext
	0,   // 61: proto.RemoveFileFromKnowledgeBaseReq.submission_source:type_name -> proto.FileOperationSource
	1,   // 62: proto.DocumentNode.category:type_name -> proto.DocumentNodeCategory
	37,  // 63: proto.DocumentTree.node:type_name -> proto.DocumentNode
	38,  // 64: proto.DocumentTree.children:type_name -> proto.DocumentTree
	73,  // 65: proto.UpdateKnowledgeBaseStateReq.user_context:type_name -> proto.UserContext
	71,  // 66: proto.UpdateKnowledgeBaseStateRsp.knowledge_base:type_name -> proto.KnowledgeBase
	73,  // 67: proto.CreateChunkReq.user_context:type_name -> proto.UserContext
	90,  // 68: proto.CreateChunkReq.content_type:type_name -> proto.OriginalContentType
	79,  // 69: proto.CreateChunkRsp.chunk:type_name -> proto.Chunk
	73,  // 70: proto.DeleteChunkReq.user_context:type_name -> proto.UserContext
	79,  // 71: proto.DeleteChunkRsp.chunk:type_name -> proto.Chunk
	73,  // 72: proto.UpdateChunkReq.user_context:type_name -> proto.UserContext
	79,  // 73: proto.UpdateChunkRsp.chunk:type_name -> proto.Chunk
	73,  // 74: proto.PreviewDocumentProcessReq.user_context:type_name -> proto.UserContext
	87,  // 75: proto.PreviewDocumentProcessReq.doc_processing_config:type_name -> proto.DocProcessingConfig
	73,  // 76: proto.SyncSubmitFilesToKnowledgeBaseReq.user_context:type_name -> proto.UserContext
	0,   // 77: proto.SyncSubmitFilesToKnowledgeBaseReq.submission_source:type_name -> proto.FileOperationSource
	87,  // 78: proto.SyncSubmitFilesToKnowledgeBaseReq.doc_processing_config:type_name -> proto.DocProcessingConfig
	88,  // 79: proto.SyncSubmitFilesToKnowledgeBaseReq.table_config:type_name -> proto.TableConfig
	91,  // 80: proto.SyncSubmitFilesToKnowledgeBaseReq.document_file_source:type_name -> proto.DocumentFileSource
	89,  // 81: proto.SyncSubmitFilesToKnowledgeBaseReq.corpus_config:type_name -> proto.CorpusConfig
	80,  // 82: proto.DocumentProcessingProgress.document:type_name -> proto.Document
	2,   // 83: proto.DocumentProcessingProgress.stage:type_name -> proto.DocumentTaskStage
	49,  // 84: proto.SyncSubmitFilesToKnowledgeBaseRsp.documents:type_name -> proto.DocumentProcessingProgress
	78,  // 85: proto.RetrieveRange.retrieval_config:type_name -> proto.RetrievalConfig
	73,  // 86: proto.RetrieveCrossKnowledgeBaseReq.user_context:type_name -> proto.UserContext
	51,  // 87: proto.RetrieveCrossKnowledgeBaseReq.ranges:type_name -> proto.RetrieveRange
	92,  // 88: proto.RetrieveCrossKnowledgeBaseReq.rerank_params:type_name -> proto.RerankParams
	52,  // 89: proto.RetrieveCrossKnowledgeBaseRsp.request:type_name -> proto.RetrieveCrossKnowledgeBaseReq
	21,  // 90: proto.RetrieveCrossKnowledgeBaseRsp.result:type_name -> proto.ChunkRetrieveResult
	73,  // 91: proto.SubmitChunksToKnowledgeBaseReq.user_context:type_name -> proto.UserContext
	79,  // 92: proto.SubmitChunksToKnowledgeBaseReq.chunks:type_name -> proto.Chunk
	93,  // 93: proto.SubmitChunksToKnowledgeBaseReq.elements:type_name -> proto.DocElement
	80,  // 94: proto.SubmitChunksToKnowledgeBaseRsp.document:type_name -> proto.Document
	73,  // 95: proto.IsDocumentExistentReq.user_context:type_name -> proto.UserContext
	80,  // 96: proto.IsDocumentExistentRsp.document:type_name -> proto.Document
	79,  // 97: proto.DocSvcLoadChunkRsp.chunks:type_name -> proto.Chunk
	93,  // 98: proto.DocSvcLoadChunkRsp.elements:type_name -> proto.DocElement
	73,  // 99: proto.CollectKnowledgeBaseStatsReq.user_context:type_name -> proto.UserContext
	73,  // 100: proto.ShareKnowledgeBaseReq.user_context:type_name -> proto.UserContext
	94,  // 101: proto.PublishKnowledgeBaseReq.rate_limit:type_name -> serving.RateLimit
	73,  // 102: proto.PublishKnowledgeBaseReq.ctx:type_name -> proto.UserContext
	73,  // 103: proto.TraceDocElementsReq.user_context:type_name -> proto.UserContext
	93,  // 104: proto.TraceDocElementsRsp.elements:type_name -> proto.DocElement
	73,  // 105: proto.RetryDocumentProcessReq.user_context:type_name -> proto.UserContext
	19,  // 106: proto.RetrieveKnowledgeBaseRsp.VectorIndexHitsEntry.value:type_name -> proto.Hit
	19,  // 107: proto.RetrieveKnowledgeBaseRsp.FullTextIndexHitsEntry.value:type_name -> proto.Hit
	7,   // 108: proto.KnowledgeBaseManager.ListKnowledgeBases:input_type -> proto.ListKnowledgeBasesReq
	10,  // 109: proto.KnowledgeBaseManager.GetKnowledgeBase:input_type -> proto.GetKnowledgeBaseReq
	12,  // 110: proto.KnowledgeBaseManager.CreateKnowledgeBase:input_type -> proto.CreateKnowledgeBaseReq
	14,  // 111: proto.KnowledgeBaseManager.UpdateKnowledgeBase:input_type -> proto.UpdateKnowledgeBaseReq
	16,  // 112: proto.KnowledgeBaseManager.DeleteKnowledgeBases:input_type -> proto.DeleteKnowledgeBasesReq
	29,  // 113: proto.KnowledgeBaseManager.ListConnectionTables:input_type -> proto.ListConnectionTablesReq
	23,  // 114: proto.KnowledgeBaseManager.ListDocuments:input_type -> proto.ListDocumentsReq
	25,  // 115: proto.KnowledgeBaseManager.DisableDocument:input_type -> proto.DisableDocumentReq
	27,  // 116: proto.KnowledgeBaseManager.ListDocumentChunks:input_type -> proto.ListDocumentChunksReq
	18,  // 117: proto.KnowledgeBaseManager.RetrieveKnowledgeBase:input_type -> proto.RetrieveKnowledgeBaseReq
	31,  // 118: proto.KnowledgeBaseManager.GetDocumentTree:input_type -> proto.GetDocumentTreeReq
	33,  // 119: proto.KnowledgeBaseManager.SubmitFileToKnowledgeBase:input_type -> proto.SubmitFileToKnowledgeBaseReq
	35,  // 120: proto.KnowledgeBaseManager.RemoveFileFromKnowledgeBase:input_type -> proto.RemoveFileFromKnowledgeBaseReq
	39,  // 121: proto.KnowledgeBaseManager.UpdateKnowledgeBaseState:input_type -> proto.UpdateKnowledgeBaseStateReq
	41,  // 122: proto.KnowledgeBaseManager.CreateChunk:input_type -> proto.CreateChunkReq
	43,  // 123: proto.KnowledgeBaseManager.DeleteChunk:input_type -> proto.DeleteChunkReq
	45,  // 124: proto.KnowledgeBaseManager.UpdateChunk:input_type -> proto.UpdateChunkReq
	47,  // 125: proto.KnowledgeBaseManager.PreviewDocumentProcess:input_type -> proto.PreviewDocumentProcessReq
	48,  // 126: proto.KnowledgeBaseManager.SyncSubmitFilesToKnowledgeBase:input_type -> proto.SyncSubmitFilesToKnowledgeBaseReq
	52,  // 127: proto.KnowledgeBaseManager.RetrieveCrossKnowledgeBase:input_type -> proto.RetrieveCrossKnowledgeBaseReq
	54,  // 128: proto.KnowledgeBaseManager.SubmitChunksToKnowledgeBase:input_type -> proto.SubmitChunksToKnowledgeBaseReq
	56,  // 129: proto.KnowledgeBaseManager.IsDocumentExistent:input_type -> proto.IsDocumentExistentReq
	59,  // 130: proto.KnowledgeBaseManager.CollectKnowledgeBaseStats:input_type -> proto.CollectKnowledgeBaseStatsReq
	61,  // 131: proto.KnowledgeBaseManager.ShareKnowledgeBase:input_type -> proto.ShareKnowledgeBaseReq
	63,  // 132: proto.KnowledgeBaseManager.PublishKnowledgeBase:input_type -> proto.PublishKnowledgeBaseReq
	65,  // 133: proto.KnowledgeBaseManager.TraceDocElements:input_type -> proto.TraceDocElementsReq
	67,  // 134: proto.KnowledgeBaseManager.RetryDocumentProcess:input_type -> proto.RetryDocumentProcessReq
	9,   // 135: proto.KnowledgeBaseManager.ListKnowledgeBases:output_type -> proto.ListKnowledgeBasesRsp
	11,  // 136: proto.KnowledgeBaseManager.GetKnowledgeBase:output_type -> proto.GetKnowledgeBaseRsp
	13,  // 137: proto.KnowledgeBaseManager.CreateKnowledgeBase:output_type -> proto.CreateKnowledgeBaseRsp
	15,  // 138: proto.KnowledgeBaseManager.UpdateKnowledgeBase:output_type -> proto.UpdateKnowledgeBaseRsp
	17,  // 139: proto.KnowledgeBaseManager.DeleteKnowledgeBases:output_type -> proto.DeleteKnowledgeBasesRsp
	30,  // 140: proto.KnowledgeBaseManager.ListConnectionTables:output_type -> proto.ListConnectionTablesRsp
	24,  // 141: proto.KnowledgeBaseManager.ListDocuments:output_type -> proto.ListDocumentsRsp
	26,  // 142: proto.KnowledgeBaseManager.DisableDocument:output_type -> proto.DisableDocumentRsp
	28,  // 143: proto.KnowledgeBaseManager.ListDocumentChunks:output_type -> proto.ListDocumentChunksRsp
	20,  // 144: proto.KnowledgeBaseManager.RetrieveKnowledgeBase:output_type -> proto.RetrieveKnowledgeBaseRsp
	32,  // 145: proto.KnowledgeBaseManager.GetDocumentTree:output_type -> proto.GetDocumentTreeRsp
	34,  // 146: proto.KnowledgeBaseManager.SubmitFileToKnowledgeBase:output_type -> proto.SubmitFileToKnowledgeBaseRsp
	36,  // 147: proto.KnowledgeBaseManager.RemoveFileFromKnowledgeBase:output_type -> proto.RemoveFileFromKnowledgeBaseRsp
	40,  // 148: proto.KnowledgeBaseManager.UpdateKnowledgeBaseState:output_type -> proto.UpdateKnowledgeBaseStateRsp
	42,  // 149: proto.KnowledgeBaseManager.CreateChunk:output_type -> proto.CreateChunkRsp
	44,  // 150: proto.KnowledgeBaseManager.DeleteChunk:output_type -> proto.DeleteChunkRsp
	46,  // 151: proto.KnowledgeBaseManager.UpdateChunk:output_type -> proto.UpdateChunkRsp
	58,  // 152: proto.KnowledgeBaseManager.PreviewDocumentProcess:output_type -> proto.DocSvcLoadChunkRsp
	50,  // 153: proto.KnowledgeBaseManager.SyncSubmitFilesToKnowledgeBase:output_type -> proto.SyncSubmitFilesToKnowledgeBaseRsp
	53,  // 154: proto.KnowledgeBaseManager.RetrieveCrossKnowledgeBase:output_type -> proto.RetrieveCrossKnowledgeBaseRsp
	55,  // 155: proto.KnowledgeBaseManager.SubmitChunksToKnowledgeBase:output_type -> proto.SubmitChunksToKnowledgeBaseRsp
	57,  // 156: proto.KnowledgeBaseManager.IsDocumentExistent:output_type -> proto.IsDocumentExistentRsp
	60,  // 157: proto.KnowledgeBaseManager.CollectKnowledgeBaseStats:output_type -> proto.CollectKnowledgeBaseStatsRsp
	62,  // 158: proto.KnowledgeBaseManager.ShareKnowledgeBase:output_type -> proto.ShareKnowledgeBaseRsp
	64,  // 159: proto.KnowledgeBaseManager.PublishKnowledgeBase:output_type -> proto.PublishKnowledgeBaseRsp
	66,  // 160: proto.KnowledgeBaseManager.TraceDocElements:output_type -> proto.TraceDocElementsRsp
	68,  // 161: proto.KnowledgeBaseManager.RetryDocumentProcess:output_type -> proto.RetryDocumentProcessRsp
	135, // [135:162] is the sub-list for method output_type
	108, // [108:135] is the sub-list for method input_type
	108, // [108:108] is the sub-list for extension type_name
	108, // [108:108] is the sub-list for extension extendee
	0,   // [0:108] is the sub-list for field type_name
}

func init() { file_proto_rpc_knowledge_base_proto_init() }
func file_proto_rpc_knowledge_base_proto_init() {
	if File_proto_rpc_knowledge_base_proto != nil {
		return
	}
	file_proto_knowledge_base_proto_init()
	file_proto_data_connection_proto_init()
	file_proto_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_proto_rpc_knowledge_base_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppRelation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HealthStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KnowledgeBaseHealthOverview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KnowledgeBaseInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListKnowledgeBasesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListKnowledgeBasesSelector); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListKnowledgeBasesRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetKnowledgeBaseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetKnowledgeBaseRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateKnowledgeBaseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateKnowledgeBaseRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateKnowledgeBaseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateKnowledgeBaseRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteKnowledgeBasesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteKnowledgeBasesRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrieveKnowledgeBaseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Hit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrieveKnowledgeBaseRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChunkRetrieveResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DocumentInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDocumentsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDocumentsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DisableDocumentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DisableDocumentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDocumentChunksReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDocumentChunksRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListConnectionTablesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListConnectionTablesRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDocumentTreeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDocumentTreeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitFileToKnowledgeBaseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitFileToKnowledgeBaseRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveFileFromKnowledgeBaseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveFileFromKnowledgeBaseRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DocumentNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DocumentTree); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateKnowledgeBaseStateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateKnowledgeBaseStateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateChunkReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateChunkRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteChunkReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteChunkRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateChunkReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateChunkRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewDocumentProcessReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncSubmitFilesToKnowledgeBaseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DocumentProcessingProgress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncSubmitFilesToKnowledgeBaseRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrieveRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrieveCrossKnowledgeBaseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrieveCrossKnowledgeBaseRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitChunksToKnowledgeBaseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitChunksToKnowledgeBaseRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsDocumentExistentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsDocumentExistentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DocSvcLoadChunkRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectKnowledgeBaseStatsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectKnowledgeBaseStatsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ShareKnowledgeBaseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ShareKnowledgeBaseRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PublishKnowledgeBaseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PublishKnowledgeBaseRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraceDocElementsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraceDocElementsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetryDocumentProcessReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_knowledge_base_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetryDocumentProcessRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_rpc_knowledge_base_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   68,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_rpc_knowledge_base_proto_goTypes,
		DependencyIndexes: file_proto_rpc_knowledge_base_proto_depIdxs,
		EnumInfos:         file_proto_rpc_knowledge_base_proto_enumTypes,
		MessageInfos:      file_proto_rpc_knowledge_base_proto_msgTypes,
	}.Build()
	File_proto_rpc_knowledge_base_proto = out.File
	file_proto_rpc_knowledge_base_proto_rawDesc = nil
	file_proto_rpc_knowledge_base_proto_goTypes = nil
	file_proto_rpc_knowledge_base_proto_depIdxs = nil
}
