// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/image_template.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TemplateSource int32

const (
	TemplateSource_TEMPLATE_SOURCE_SYSTEM TemplateSource = 0 // 系统内置
	TemplateSource_TEMPLATE_SOURCE_CSM    TemplateSource = 1 // 代码空间
	TemplateSource_TEMPLATE_SOURCE_UPLOAD TemplateSource = 2 // 手动上传
)

// Enum value maps for TemplateSource.
var (
	TemplateSource_name = map[int32]string{
		0: "TEMPLATE_SOURCE_SYSTEM",
		1: "TEMPLATE_SOURCE_CSM",
		2: "TEMPLATE_SOURCE_UPLOAD",
	}
	TemplateSource_value = map[string]int32{
		"TEMPLATE_SOURCE_SYSTEM": 0,
		"TEMPLATE_SOURCE_CSM":    1,
		"TEMPLATE_SOURCE_UPLOAD": 2,
	}
)

func (x TemplateSource) Enum() *TemplateSource {
	p := new(TemplateSource)
	*p = x
	return p
}

func (x TemplateSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TemplateSource) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_image_template_proto_enumTypes[0].Descriptor()
}

func (TemplateSource) Type() protoreflect.EnumType {
	return &file_proto_image_template_proto_enumTypes[0]
}

func (x TemplateSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TemplateSource.Descriptor instead.
func (TemplateSource) EnumDescriptor() ([]byte, []int) {
	return file_proto_image_template_proto_rawDescGZIP(), []int{0}
}

type TemplateStatus int32

const (
	TemplateStatus_TEMPLATE_STATUS_INVALID    TemplateStatus = 0 // 已失效
	TemplateStatus_TEMPLATE_STATUS_UNRELEASED TemplateStatus = 1 // 待编辑
	TemplateStatus_TEMPLATE_STATUS_RELEASED   TemplateStatus = 2 // 已发布
)

// Enum value maps for TemplateStatus.
var (
	TemplateStatus_name = map[int32]string{
		0: "TEMPLATE_STATUS_INVALID",
		1: "TEMPLATE_STATUS_UNRELEASED",
		2: "TEMPLATE_STATUS_RELEASED",
	}
	TemplateStatus_value = map[string]int32{
		"TEMPLATE_STATUS_INVALID":    0,
		"TEMPLATE_STATUS_UNRELEASED": 1,
		"TEMPLATE_STATUS_RELEASED":   2,
	}
)

func (x TemplateStatus) Enum() *TemplateStatus {
	p := new(TemplateStatus)
	*p = x
	return p
}

func (x TemplateStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TemplateStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_image_template_proto_enumTypes[1].Descriptor()
}

func (TemplateStatus) Type() protoreflect.EnumType {
	return &file_proto_image_template_proto_enumTypes[1]
}

func (x TemplateStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TemplateStatus.Descriptor instead.
func (TemplateStatus) EnumDescriptor() ([]byte, []int) {
	return file_proto_image_template_proto_rawDescGZIP(), []int{1}
}

// 和mlops定义保持一致
type TemplateType int32

const (
	TemplateType_TEMPLATE_TYPE_PIPELINE    TemplateType = 0 // pipeline
	TemplateType_TEMPLATE_TYPE_TRAIN       TemplateType = 1 // 训练模板
	TemplateType_TEMPLATE_TYPE_MODEL_EVAL  TemplateType = 2 // 模型评估模板
	TemplateType_TEMPLATE_TYPE_MODEL_QUANT TemplateType = 3
)

// Enum value maps for TemplateType.
var (
	TemplateType_name = map[int32]string{
		0: "TEMPLATE_TYPE_PIPELINE",
		1: "TEMPLATE_TYPE_TRAIN",
		2: "TEMPLATE_TYPE_MODEL_EVAL",
		3: "TEMPLATE_TYPE_MODEL_QUANT",
	}
	TemplateType_value = map[string]int32{
		"TEMPLATE_TYPE_PIPELINE":    0,
		"TEMPLATE_TYPE_TRAIN":       1,
		"TEMPLATE_TYPE_MODEL_EVAL":  2,
		"TEMPLATE_TYPE_MODEL_QUANT": 3,
	}
)

func (x TemplateType) Enum() *TemplateType {
	p := new(TemplateType)
	*p = x
	return p
}

func (x TemplateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TemplateType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_image_template_proto_enumTypes[2].Descriptor()
}

func (TemplateType) Type() protoreflect.EnumType {
	return &file_proto_image_template_proto_enumTypes[2]
}

func (x TemplateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TemplateType.Descriptor instead.
func (TemplateType) EnumDescriptor() ([]byte, []int) {
	return file_proto_image_template_proto_rawDescGZIP(), []int{2}
}

type TemplateEnvType int32

const (
	TemplateEnvType_TEMPLATE_ENV_TYPE_STRING  TemplateEnvType = 0 // 字符串
	TemplateEnvType_TEMPLATE_ENV_TYPE_INTEGER TemplateEnvType = 1 // 整型
	TemplateEnvType_TEMPLATE_ENV_TYPE_FLOAT   TemplateEnvType = 2 // 浮点型
	TemplateEnvType_TEMPLATE_ENV_TYPE_BOOL    TemplateEnvType = 3 // 布尔型
)

// Enum value maps for TemplateEnvType.
var (
	TemplateEnvType_name = map[int32]string{
		0: "TEMPLATE_ENV_TYPE_STRING",
		1: "TEMPLATE_ENV_TYPE_INTEGER",
		2: "TEMPLATE_ENV_TYPE_FLOAT",
		3: "TEMPLATE_ENV_TYPE_BOOL",
	}
	TemplateEnvType_value = map[string]int32{
		"TEMPLATE_ENV_TYPE_STRING":  0,
		"TEMPLATE_ENV_TYPE_INTEGER": 1,
		"TEMPLATE_ENV_TYPE_FLOAT":   2,
		"TEMPLATE_ENV_TYPE_BOOL":    3,
	}
)

func (x TemplateEnvType) Enum() *TemplateEnvType {
	p := new(TemplateEnvType)
	*p = x
	return p
}

func (x TemplateEnvType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TemplateEnvType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_image_template_proto_enumTypes[3].Descriptor()
}

func (TemplateEnvType) Type() protoreflect.EnumType {
	return &file_proto_image_template_proto_enumTypes[3]
}

func (x TemplateEnvType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TemplateEnvType.Descriptor instead.
func (TemplateEnvType) EnumDescriptor() ([]byte, []int) {
	return file_proto_image_template_proto_rawDescGZIP(), []int{3}
}

type TemplateMountType int32

const (
	TemplateMountType_TEMPLATE_MOUNT_TYPE_SAMPLE     TemplateMountType = 0 // 样本
	TemplateMountType_TEMPLATE_MOUNT_TYPE_ANNOTATION TemplateMountType = 1 // 标注集
	TemplateMountType_TEMPLATE_MOUNT_TYPE_MODEL      TemplateMountType = 2 // 模型
	TemplateMountType_TEMPLATE_MOUNT_TYPE_SFS        TemplateMountType = 3 // 文件
	TemplateMountType_TEMPLATE_MOUNT_TYPE_HOST_PATH  TemplateMountType = 4 // hostpath挂载
	TemplateMountType_TEMPLATE_MOUNT_TYPE_MEMORY     TemplateMountType = 5 // 一般用于提高shm size
)

// Enum value maps for TemplateMountType.
var (
	TemplateMountType_name = map[int32]string{
		0: "TEMPLATE_MOUNT_TYPE_SAMPLE",
		1: "TEMPLATE_MOUNT_TYPE_ANNOTATION",
		2: "TEMPLATE_MOUNT_TYPE_MODEL",
		3: "TEMPLATE_MOUNT_TYPE_SFS",
		4: "TEMPLATE_MOUNT_TYPE_HOST_PATH",
		5: "TEMPLATE_MOUNT_TYPE_MEMORY",
	}
	TemplateMountType_value = map[string]int32{
		"TEMPLATE_MOUNT_TYPE_SAMPLE":     0,
		"TEMPLATE_MOUNT_TYPE_ANNOTATION": 1,
		"TEMPLATE_MOUNT_TYPE_MODEL":      2,
		"TEMPLATE_MOUNT_TYPE_SFS":        3,
		"TEMPLATE_MOUNT_TYPE_HOST_PATH":  4,
		"TEMPLATE_MOUNT_TYPE_MEMORY":     5,
	}
)

func (x TemplateMountType) Enum() *TemplateMountType {
	p := new(TemplateMountType)
	*p = x
	return p
}

func (x TemplateMountType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TemplateMountType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_image_template_proto_enumTypes[4].Descriptor()
}

func (TemplateMountType) Type() protoreflect.EnumType {
	return &file_proto_image_template_proto_enumTypes[4]
}

func (x TemplateMountType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TemplateMountType.Descriptor instead.
func (TemplateMountType) EnumDescriptor() ([]byte, []int) {
	return file_proto_image_template_proto_rawDescGZIP(), []int{4}
}

// 模板的大类，目前用于任务模板，后续有新的再加
type TemplateKind int32

const (
	TemplateKind_TEMPLATE_KIND_TASK         TemplateKind = 0 // 任务模板
	TemplateKind_TEMPLATE_KIND_SERVICE      TemplateKind = 1 // 服务模板
	TemplateKind_TEMPLATE_KIND_APP          TemplateKind = 2 // 应用模板
	TemplateKind_TEMPLATE_KIND_CORPUS_IMAGE TemplateKind = 3 // 语料镜像模板
)

// Enum value maps for TemplateKind.
var (
	TemplateKind_name = map[int32]string{
		0: "TEMPLATE_KIND_TASK",
		1: "TEMPLATE_KIND_SERVICE",
		2: "TEMPLATE_KIND_APP",
		3: "TEMPLATE_KIND_CORPUS_IMAGE",
	}
	TemplateKind_value = map[string]int32{
		"TEMPLATE_KIND_TASK":         0,
		"TEMPLATE_KIND_SERVICE":      1,
		"TEMPLATE_KIND_APP":          2,
		"TEMPLATE_KIND_CORPUS_IMAGE": 3,
	}
)

func (x TemplateKind) Enum() *TemplateKind {
	p := new(TemplateKind)
	*p = x
	return p
}

func (x TemplateKind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TemplateKind) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_image_template_proto_enumTypes[5].Descriptor()
}

func (TemplateKind) Type() protoreflect.EnumType {
	return &file_proto_image_template_proto_enumTypes[5]
}

func (x TemplateKind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TemplateKind.Descriptor instead.
func (TemplateKind) EnumDescriptor() ([]byte, []int) {
	return file_proto_image_template_proto_rawDescGZIP(), []int{5}
}

type ImageTemplate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                 string                   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                                                                 // 模板的唯一id，由uuid生成
	Name               string                   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                                                                             // 模板名称
	Desc               string                   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`                                                                                             // 模板描述
	ImageInfo          *ImageInfo               `protobuf:"bytes,4,opt,name=image_info,json=imageInfo,proto3" json:"image_info,omitempty"`                                                                  // 镜像的信息
	Labels             map[string]string        `protobuf:"bytes,5,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 标签
	Source             TemplateSource           `protobuf:"varint,6,opt,name=source,proto3,enum=proto.TemplateSource" json:"source,omitempty"`                                                              // 模板来源
	Type               TemplateType             `protobuf:"varint,7,opt,name=type,proto3,enum=proto.TemplateType" json:"type,omitempty"`                                                                    // 模板类型
	Status             TemplateStatus           `protobuf:"varint,8,opt,name=status,proto3,enum=proto.TemplateStatus" json:"status,omitempty"`                                                              // 模板状态
	Creator            string                   `protobuf:"bytes,9,opt,name=creator,proto3" json:"creator,omitempty"`                                                                                       // 创建者
	CreatedAtMills     int64                    `protobuf:"varint,10,opt,name=created_at_mills,json=createdAtMills,proto3" json:"created_at_mills,omitempty"`                                               // 创建时间
	UpdatedAtMills     int64                    `protobuf:"varint,11,opt,name=updated_at_mills,json=updatedAtMills,proto3" json:"updated_at_mills,omitempty"`                                               // 更新时间
	TemplateMounts     []*TemplateMount         `protobuf:"bytes,12,rep,name=template_mounts,json=templateMounts,proto3" json:"template_mounts,omitempty"`                                                  // 数据挂载配置
	TemplateEnvs       []*TemplateEnv           `protobuf:"bytes,13,rep,name=template_envs,json=templateEnvs,proto3" json:"template_envs,omitempty"`                                                        // 环境变量
	Args               []string                 `protobuf:"bytes,14,rep,name=args,proto3" json:"args,omitempty"`                                                                                            // 启动参数
	TemplateConfigMaps []*TemplateConfigMap     `protobuf:"bytes,15,rep,name=template_config_maps,json=templateConfigMaps,proto3" json:"template_config_maps,omitempty"`                                    // 自定义文件映射
	DefaultResource    *TemplateDefaultResource `protobuf:"bytes,16,opt,name=default_resource,json=defaultResource,proto3" json:"default_resource,omitempty"`                                               // 默认资源
	Config             []byte                   `protobuf:"bytes,17,opt,name=config,proto3" json:"config,omitempty"`                                                                                        // 各个模块自定义的模板配置，json bytes
	ProjectId          string                   `protobuf:"bytes,18,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                                                 // 项目id
	TemplateKind       TemplateKind             `protobuf:"varint,19,opt,name=template_kind,json=templateKind,proto3,enum=proto.TemplateKind" json:"template_kind,omitempty"`                               // 模板大类
}

func (x *ImageTemplate) Reset() {
	*x = ImageTemplate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_image_template_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageTemplate) ProtoMessage() {}

func (x *ImageTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_proto_image_template_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageTemplate.ProtoReflect.Descriptor instead.
func (*ImageTemplate) Descriptor() ([]byte, []int) {
	return file_proto_image_template_proto_rawDescGZIP(), []int{0}
}

func (x *ImageTemplate) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ImageTemplate) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ImageTemplate) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *ImageTemplate) GetImageInfo() *ImageInfo {
	if x != nil {
		return x.ImageInfo
	}
	return nil
}

func (x *ImageTemplate) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ImageTemplate) GetSource() TemplateSource {
	if x != nil {
		return x.Source
	}
	return TemplateSource_TEMPLATE_SOURCE_SYSTEM
}

func (x *ImageTemplate) GetType() TemplateType {
	if x != nil {
		return x.Type
	}
	return TemplateType_TEMPLATE_TYPE_PIPELINE
}

func (x *ImageTemplate) GetStatus() TemplateStatus {
	if x != nil {
		return x.Status
	}
	return TemplateStatus_TEMPLATE_STATUS_INVALID
}

func (x *ImageTemplate) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ImageTemplate) GetCreatedAtMills() int64 {
	if x != nil {
		return x.CreatedAtMills
	}
	return 0
}

func (x *ImageTemplate) GetUpdatedAtMills() int64 {
	if x != nil {
		return x.UpdatedAtMills
	}
	return 0
}

func (x *ImageTemplate) GetTemplateMounts() []*TemplateMount {
	if x != nil {
		return x.TemplateMounts
	}
	return nil
}

func (x *ImageTemplate) GetTemplateEnvs() []*TemplateEnv {
	if x != nil {
		return x.TemplateEnvs
	}
	return nil
}

func (x *ImageTemplate) GetArgs() []string {
	if x != nil {
		return x.Args
	}
	return nil
}

func (x *ImageTemplate) GetTemplateConfigMaps() []*TemplateConfigMap {
	if x != nil {
		return x.TemplateConfigMaps
	}
	return nil
}

func (x *ImageTemplate) GetDefaultResource() *TemplateDefaultResource {
	if x != nil {
		return x.DefaultResource
	}
	return nil
}

func (x *ImageTemplate) GetConfig() []byte {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *ImageTemplate) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *ImageTemplate) GetTemplateKind() TemplateKind {
	if x != nil {
		return x.TemplateKind
	}
	return TemplateKind_TEMPLATE_KIND_TASK
}

type TemplateMount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type     TemplateMountType `protobuf:"varint,1,opt,name=type,proto3,enum=proto.TemplateMountType" json:"type,omitempty"` // 挂载类型
	Path     string            `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"`                               // 容器内路径
	Required bool              `protobuf:"varint,3,opt,name=required,proto3" json:"required,omitempty"`                      // 是否必填
}

func (x *TemplateMount) Reset() {
	*x = TemplateMount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_image_template_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemplateMount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplateMount) ProtoMessage() {}

func (x *TemplateMount) ProtoReflect() protoreflect.Message {
	mi := &file_proto_image_template_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplateMount.ProtoReflect.Descriptor instead.
func (*TemplateMount) Descriptor() ([]byte, []int) {
	return file_proto_image_template_proto_rawDescGZIP(), []int{1}
}

func (x *TemplateMount) GetType() TemplateMountType {
	if x != nil {
		return x.Type
	}
	return TemplateMountType_TEMPLATE_MOUNT_TYPE_SAMPLE
}

func (x *TemplateMount) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *TemplateMount) GetRequired() bool {
	if x != nil {
		return x.Required
	}
	return false
}

type TemplateEnv struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string          `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                             // 环境变量名称
	Key      string          `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`                               // 环境变量的key值
	Type     TemplateEnvType `protobuf:"varint,3,opt,name=type,proto3,enum=proto.TemplateEnvType" json:"type,omitempty"` // 数据类型
	Default  string          `protobuf:"bytes,4,opt,name=default,proto3" json:"default,omitempty"`                       // 默认值
	Min      float32         `protobuf:"fixed32,5,opt,name=min,proto3" json:"min,omitempty"`                             // 最小值
	Max      float32         `protobuf:"fixed32,6,opt,name=max,proto3" json:"max,omitempty"`                             // 最大值
	Desc     string          `protobuf:"bytes,7,opt,name=desc,proto3" json:"desc,omitempty"`                             // 描述
	Required bool            `protobuf:"varint,8,opt,name=required,proto3" json:"required,omitempty"`                    // 是否必填
}

func (x *TemplateEnv) Reset() {
	*x = TemplateEnv{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_image_template_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemplateEnv) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplateEnv) ProtoMessage() {}

func (x *TemplateEnv) ProtoReflect() protoreflect.Message {
	mi := &file_proto_image_template_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplateEnv.ProtoReflect.Descriptor instead.
func (*TemplateEnv) Descriptor() ([]byte, []int) {
	return file_proto_image_template_proto_rawDescGZIP(), []int{2}
}

func (x *TemplateEnv) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TemplateEnv) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *TemplateEnv) GetType() TemplateEnvType {
	if x != nil {
		return x.Type
	}
	return TemplateEnvType_TEMPLATE_ENV_TYPE_STRING
}

func (x *TemplateEnv) GetDefault() string {
	if x != nil {
		return x.Default
	}
	return ""
}

func (x *TemplateEnv) GetMin() float32 {
	if x != nil {
		return x.Min
	}
	return 0
}

func (x *TemplateEnv) GetMax() float32 {
	if x != nil {
		return x.Max
	}
	return 0
}

func (x *TemplateEnv) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *TemplateEnv) GetRequired() bool {
	if x != nil {
		return x.Required
	}
	return false
}

type TemplateDefaultResource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable       bool  `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`                                   // 是否配置默认资源
	Cpu          int32 `protobuf:"varint,2,opt,name=cpu,proto3" json:"cpu,omitempty"`                                         // cpu个数
	MemoryMib    int32 `protobuf:"varint,3,opt,name=memory_mib,json=memoryMib,proto3" json:"memory_mib,omitempty"`            // 内存大小，单位Mi
	Gpu          int32 `protobuf:"varint,4,opt,name=gpu,proto3" json:"gpu,omitempty"`                                         // gpu个数
	GpuMemoryMib int32 `protobuf:"varint,5,opt,name=gpu_memory_mib,json=gpuMemoryMib,proto3" json:"gpu_memory_mib,omitempty"` // gpu显存大小，单位Mi
}

func (x *TemplateDefaultResource) Reset() {
	*x = TemplateDefaultResource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_image_template_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemplateDefaultResource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplateDefaultResource) ProtoMessage() {}

func (x *TemplateDefaultResource) ProtoReflect() protoreflect.Message {
	mi := &file_proto_image_template_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplateDefaultResource.ProtoReflect.Descriptor instead.
func (*TemplateDefaultResource) Descriptor() ([]byte, []int) {
	return file_proto_image_template_proto_rawDescGZIP(), []int{3}
}

func (x *TemplateDefaultResource) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *TemplateDefaultResource) GetCpu() int32 {
	if x != nil {
		return x.Cpu
	}
	return 0
}

func (x *TemplateDefaultResource) GetMemoryMib() int32 {
	if x != nil {
		return x.MemoryMib
	}
	return 0
}

func (x *TemplateDefaultResource) GetGpu() int32 {
	if x != nil {
		return x.Gpu
	}
	return 0
}

func (x *TemplateDefaultResource) GetGpuMemoryMib() int32 {
	if x != nil {
		return x.GpuMemoryMib
	}
	return 0
}

type TemplateConfigMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path           string `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
	DefaultContent string `protobuf:"bytes,2,opt,name=default_content,json=defaultContent,proto3" json:"default_content,omitempty"`
}

func (x *TemplateConfigMap) Reset() {
	*x = TemplateConfigMap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_image_template_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemplateConfigMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplateConfigMap) ProtoMessage() {}

func (x *TemplateConfigMap) ProtoReflect() protoreflect.Message {
	mi := &file_proto_image_template_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplateConfigMap.ProtoReflect.Descriptor instead.
func (*TemplateConfigMap) Descriptor() ([]byte, []int) {
	return file_proto_image_template_proto_rawDescGZIP(), []int{4}
}

func (x *TemplateConfigMap) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *TemplateConfigMap) GetDefaultContent() string {
	if x != nil {
		return x.DefaultContent
	}
	return ""
}

// 和镜像管理结构一致
type ImageInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Repo      string            `protobuf:"bytes,1,opt,name=repo,proto3" json:"repo,omitempty"`
	Tag       string            `protobuf:"bytes,2,opt,name=tag,proto3" json:"tag,omitempty"`
	Arch      string            `protobuf:"bytes,3,opt,name=arch,proto3" json:"arch,omitempty"`
	Digest    string            `protobuf:"bytes,4,opt,name=digest,proto3" json:"digest,omitempty"`
	Size      int64             `protobuf:"varint,5,opt,name=size,proto3" json:"size,omitempty"`
	Labels    map[string]string `protobuf:"bytes,6,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Desc      string            `protobuf:"bytes,7,opt,name=desc,proto3" json:"desc,omitempty"`
	BuildTime string            `protobuf:"bytes,8,opt,name=build_time,json=buildTime,proto3" json:"build_time,omitempty"`
}

func (x *ImageInfo) Reset() {
	*x = ImageInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_image_template_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageInfo) ProtoMessage() {}

func (x *ImageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_image_template_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageInfo.ProtoReflect.Descriptor instead.
func (*ImageInfo) Descriptor() ([]byte, []int) {
	return file_proto_image_template_proto_rawDescGZIP(), []int{5}
}

func (x *ImageInfo) GetRepo() string {
	if x != nil {
		return x.Repo
	}
	return ""
}

func (x *ImageInfo) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *ImageInfo) GetArch() string {
	if x != nil {
		return x.Arch
	}
	return ""
}

func (x *ImageInfo) GetDigest() string {
	if x != nil {
		return x.Digest
	}
	return ""
}

func (x *ImageInfo) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ImageInfo) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ImageInfo) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *ImageInfo) GetBuildTime() string {
	if x != nil {
		return x.BuildTime
	}
	return ""
}

var File_proto_image_template_proto protoreflect.FileDescriptor

var file_proto_image_template_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xf6, 0x06, 0x0a, 0x0d, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73,
	0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x2f, 0x0a,
	0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x38,
	0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x2d, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x27, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x2d, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x28, 0x0a, 0x10, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x5f, 0x6d, 0x69, 0x6c, 0x6c, 0x73, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x4d, 0x69,
	0x6c, 0x6c, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x5f, 0x6d, 0x69, 0x6c, 0x6c, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x4d, 0x69, 0x6c, 0x6c, 0x73, 0x12, 0x3d, 0x0a,
	0x0f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x0e, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x37, 0x0a, 0x0d,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x65, 0x6e, 0x76, 0x73, 0x18, 0x0d, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x76, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x45, 0x6e, 0x76, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x72, 0x67, 0x73, 0x18, 0x0e, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x04, 0x61, 0x72, 0x67, 0x73, 0x12, 0x4a, 0x0a, 0x14, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x6d, 0x61, 0x70,
	0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61,
	0x70, 0x52, 0x12, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x4d, 0x61, 0x70, 0x73, 0x12, 0x49, 0x0a, 0x10, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52,
	0x0f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4b,
	0x69, 0x6e, 0x64, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4b, 0x69, 0x6e,
	0x64, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x6d, 0x0a, 0x0d,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2c, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x75, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12,
	0x1a, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x22, 0xcd, 0x01, 0x0a, 0x0b,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x76, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x2a, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x45, 0x6e, 0x76, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x69, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x6d, 0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x78,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x6d, 0x61, 0x78, 0x12, 0x12, 0x0a, 0x04, 0x64,
	0x65, 0x73, 0x63, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12,
	0x1a, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x22, 0x9a, 0x01, 0x0a, 0x17,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x63, 0x70, 0x75, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x63, 0x70,
	0x75, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x6d, 0x69, 0x62, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x4d, 0x69, 0x62,
	0x12, 0x10, 0x0a, 0x03, 0x67, 0x70, 0x75, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x67,
	0x70, 0x75, 0x12, 0x24, 0x0a, 0x0e, 0x67, 0x70, 0x75, 0x5f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79,
	0x5f, 0x6d, 0x69, 0x62, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x67, 0x70, 0x75, 0x4d,
	0x65, 0x6d, 0x6f, 0x72, 0x79, 0x4d, 0x69, 0x62, 0x22, 0x50, 0x0a, 0x11, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74,
	0x68, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x95, 0x02, 0x0a, 0x09, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x65, 0x70, 0x6f,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x65, 0x70, 0x6f, 0x12, 0x10, 0x0a, 0x03,
	0x74, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x12,
	0x0a, 0x04, 0x61, 0x72, 0x63, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x72,
	0x63, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x69, 0x67, 0x65, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x64, 0x69, 0x67, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x34,
	0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x75, 0x69, 0x6c,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x75,
	0x69, 0x6c, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x2a, 0x61, 0x0a, 0x0e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45,
	0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x53, 0x59, 0x53, 0x54, 0x45, 0x4d, 0x10, 0x00,
	0x12, 0x17, 0x0a, 0x13, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x4f, 0x55,
	0x52, 0x43, 0x45, 0x5f, 0x43, 0x53, 0x4d, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x45, 0x4d,
	0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x55, 0x50, 0x4c,
	0x4f, 0x41, 0x44, 0x10, 0x02, 0x2a, 0x6b, 0x0a, 0x0e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x17, 0x54, 0x45, 0x4d, 0x50, 0x4c,
	0x41, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x52, 0x45, 0x4c, 0x45, 0x41, 0x53,
	0x45, 0x44, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x4c, 0x45, 0x41, 0x53, 0x45, 0x44,
	0x10, 0x02, 0x2a, 0x80, 0x01, 0x0a, 0x0c, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x49, 0x50, 0x45, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x00, 0x12,
	0x17, 0x0a, 0x13, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x54, 0x52, 0x41, 0x49, 0x4e, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x54, 0x45, 0x4d, 0x50,
	0x4c, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f,
	0x45, 0x56, 0x41, 0x4c, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41,
	0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x51, 0x55,
	0x41, 0x4e, 0x54, 0x10, 0x03, 0x2a, 0x87, 0x01, 0x0a, 0x0f, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x45, 0x6e, 0x76, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x54, 0x45, 0x4d,
	0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4e, 0x56, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53,
	0x54, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x45, 0x4d, 0x50, 0x4c,
	0x41, 0x54, 0x45, 0x5f, 0x45, 0x4e, 0x56, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x54,
	0x45, 0x47, 0x45, 0x52, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41,
	0x54, 0x45, 0x5f, 0x45, 0x4e, 0x56, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x4c, 0x4f, 0x41,
	0x54, 0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f,
	0x45, 0x4e, 0x56, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4c, 0x10, 0x03, 0x2a,
	0xd6, 0x01, 0x0a, 0x11, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x75, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54,
	0x45, 0x5f, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x41, 0x4d,
	0x50, 0x4c, 0x45, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54,
	0x45, 0x5f, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x4e, 0x4e,
	0x4f, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x45, 0x4d,
	0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x54, 0x45, 0x4d, 0x50,
	0x4c, 0x41, 0x54, 0x45, 0x5f, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x53, 0x46, 0x53, 0x10, 0x03, 0x12, 0x21, 0x0a, 0x1d, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54,
	0x45, 0x5f, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x48, 0x4f, 0x53,
	0x54, 0x5f, 0x50, 0x41, 0x54, 0x48, 0x10, 0x04, 0x12, 0x1e, 0x0a, 0x1a, 0x54, 0x45, 0x4d, 0x50,
	0x4c, 0x41, 0x54, 0x45, 0x5f, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x4d, 0x45, 0x4d, 0x4f, 0x52, 0x59, 0x10, 0x05, 0x2a, 0x78, 0x0a, 0x0c, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x16, 0x0a, 0x12, 0x54, 0x45, 0x4d, 0x50,
	0x4c, 0x41, 0x54, 0x45, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x10, 0x00,
	0x12, 0x19, 0x0a, 0x15, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x4b, 0x49, 0x4e,
	0x44, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x54,
	0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x41, 0x50, 0x50,
	0x10, 0x02, 0x12, 0x1e, 0x0a, 0x1a, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x4b,
	0x49, 0x4e, 0x44, 0x5f, 0x43, 0x4f, 0x52, 0x50, 0x55, 0x53, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45,
	0x10, 0x03, 0x42, 0x26, 0x5a, 0x24, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e,
	0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_proto_image_template_proto_rawDescOnce sync.Once
	file_proto_image_template_proto_rawDescData = file_proto_image_template_proto_rawDesc
)

func file_proto_image_template_proto_rawDescGZIP() []byte {
	file_proto_image_template_proto_rawDescOnce.Do(func() {
		file_proto_image_template_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_image_template_proto_rawDescData)
	})
	return file_proto_image_template_proto_rawDescData
}

var file_proto_image_template_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_proto_image_template_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_proto_image_template_proto_goTypes = []interface{}{
	(TemplateSource)(0),             // 0: proto.TemplateSource
	(TemplateStatus)(0),             // 1: proto.TemplateStatus
	(TemplateType)(0),               // 2: proto.TemplateType
	(TemplateEnvType)(0),            // 3: proto.TemplateEnvType
	(TemplateMountType)(0),          // 4: proto.TemplateMountType
	(TemplateKind)(0),               // 5: proto.TemplateKind
	(*ImageTemplate)(nil),           // 6: proto.ImageTemplate
	(*TemplateMount)(nil),           // 7: proto.TemplateMount
	(*TemplateEnv)(nil),             // 8: proto.TemplateEnv
	(*TemplateDefaultResource)(nil), // 9: proto.TemplateDefaultResource
	(*TemplateConfigMap)(nil),       // 10: proto.TemplateConfigMap
	(*ImageInfo)(nil),               // 11: proto.ImageInfo
	nil,                             // 12: proto.ImageTemplate.LabelsEntry
	nil,                             // 13: proto.ImageInfo.LabelsEntry
}
var file_proto_image_template_proto_depIdxs = []int32{
	11, // 0: proto.ImageTemplate.image_info:type_name -> proto.ImageInfo
	12, // 1: proto.ImageTemplate.labels:type_name -> proto.ImageTemplate.LabelsEntry
	0,  // 2: proto.ImageTemplate.source:type_name -> proto.TemplateSource
	2,  // 3: proto.ImageTemplate.type:type_name -> proto.TemplateType
	1,  // 4: proto.ImageTemplate.status:type_name -> proto.TemplateStatus
	7,  // 5: proto.ImageTemplate.template_mounts:type_name -> proto.TemplateMount
	8,  // 6: proto.ImageTemplate.template_envs:type_name -> proto.TemplateEnv
	10, // 7: proto.ImageTemplate.template_config_maps:type_name -> proto.TemplateConfigMap
	9,  // 8: proto.ImageTemplate.default_resource:type_name -> proto.TemplateDefaultResource
	5,  // 9: proto.ImageTemplate.template_kind:type_name -> proto.TemplateKind
	4,  // 10: proto.TemplateMount.type:type_name -> proto.TemplateMountType
	3,  // 11: proto.TemplateEnv.type:type_name -> proto.TemplateEnvType
	13, // 12: proto.ImageInfo.labels:type_name -> proto.ImageInfo.LabelsEntry
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_proto_image_template_proto_init() }
func file_proto_image_template_proto_init() {
	if File_proto_image_template_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_image_template_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImageTemplate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_image_template_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemplateMount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_image_template_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemplateEnv); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_image_template_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemplateDefaultResource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_image_template_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemplateConfigMap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_image_template_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImageInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_image_template_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_image_template_proto_goTypes,
		DependencyIndexes: file_proto_image_template_proto_depIdxs,
		EnumInfos:         file_proto_image_template_proto_enumTypes,
		MessageInfos:      file_proto_image_template_proto_msgTypes,
	}.Build()
	File_proto_image_template_proto = out.File
	file_proto_image_template_proto_rawDesc = nil
	file_proto_image_template_proto_goTypes = nil
	file_proto_image_template_proto_depIdxs = nil
}
