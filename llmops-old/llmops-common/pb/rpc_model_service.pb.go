// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/rpc_model_service.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
	serving "transwarp.io/aip/llmops-common/pb/serving"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReadModelServiceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                string            `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ModelId           string            `protobuf:"bytes,2,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ReleaseId         string            `protobuf:"bytes,3,opt,name=release_id,json=releaseId,proto3" json:"release_id,omitempty"`
	PageReq           *PageReq          `protobuf:"bytes,4,opt,name=page_req,json=pageReq,proto3" json:"page_req,omitempty"`
	Ctx               *UserContext      `protobuf:"bytes,5,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Type              DeploymentType    `protobuf:"varint,6,opt,name=type,proto3,enum=proto.DeploymentType" json:"type,omitempty"`
	Kind              ModelKind         `protobuf:"varint,7,opt,name=kind,proto3,enum=proto.ModelKind" json:"kind,omitempty"`
	SubKind           ModelSubKind      `protobuf:"varint,8,opt,name=sub_kind,json=subKind,proto3,enum=proto.ModelSubKind" json:"sub_kind,omitempty"`
	IsSeldonMode      bool              `protobuf:"varint,9,opt,name=is_seldon_mode,json=isSeldonMode,proto3" json:"is_seldon_mode,omitempty"`
	MatchLabels       map[string]string `protobuf:"bytes,10,rep,name=match_labels,json=matchLabels,proto3" json:"match_labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	WithRemoteService bool              `protobuf:"varint,11,opt,name=with_remote_service,json=withRemoteService,proto3" json:"with_remote_service,omitempty"`
	IsMwMode          bool              `protobuf:"varint,12,opt,name=is_mw_mode,json=isMwMode,proto3" json:"is_mw_mode,omitempty"`
	OnlyRunning       bool              `protobuf:"varint,13,opt,name=only_running,json=onlyRunning,proto3" json:"only_running,omitempty"`
	Contain           string            `protobuf:"bytes,14,opt,name=contain,proto3" json:"contain,omitempty"`
	WithAsset         bool              `protobuf:"varint,15,opt,name=with_asset,json=withAsset,proto3" json:"with_asset,omitempty"`
	InterfaceSpec     string            `protobuf:"bytes,16,opt,name=interface_spec,json=interfaceSpec,proto3" json:"interface_spec,omitempty"`
	OmitCensored      bool              `protobuf:"varint,17,opt,name=omit_censored,json=omitCensored,proto3" json:"omit_censored,omitempty"`
	WithCustomService bool              `protobuf:"varint,18,opt,name=with_custom_service,json=withCustomService,proto3" json:"with_custom_service,omitempty"`
	LiteMode          bool              `protobuf:"varint,19,opt,name=lite_mode,json=liteMode,proto3" json:"lite_mode,omitempty"`
}

func (x *ReadModelServiceReq) Reset() {
	*x = ReadModelServiceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadModelServiceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadModelServiceReq) ProtoMessage() {}

func (x *ReadModelServiceReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadModelServiceReq.ProtoReflect.Descriptor instead.
func (*ReadModelServiceReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_service_proto_rawDescGZIP(), []int{0}
}

func (x *ReadModelServiceReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ReadModelServiceReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ReadModelServiceReq) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

func (x *ReadModelServiceReq) GetPageReq() *PageReq {
	if x != nil {
		return x.PageReq
	}
	return nil
}

func (x *ReadModelServiceReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *ReadModelServiceReq) GetType() DeploymentType {
	if x != nil {
		return x.Type
	}
	return DeploymentType_DEPLOYMENT_TYPE_UNSPECIFIED
}

func (x *ReadModelServiceReq) GetKind() ModelKind {
	if x != nil {
		return x.Kind
	}
	return ModelKind_MODEL_KIND_UNSPECIFIED
}

func (x *ReadModelServiceReq) GetSubKind() ModelSubKind {
	if x != nil {
		return x.SubKind
	}
	return ModelSubKind_MODEL_SUB_KIND_UNSPECIFIED
}

func (x *ReadModelServiceReq) GetIsSeldonMode() bool {
	if x != nil {
		return x.IsSeldonMode
	}
	return false
}

func (x *ReadModelServiceReq) GetMatchLabels() map[string]string {
	if x != nil {
		return x.MatchLabels
	}
	return nil
}

func (x *ReadModelServiceReq) GetWithRemoteService() bool {
	if x != nil {
		return x.WithRemoteService
	}
	return false
}

func (x *ReadModelServiceReq) GetIsMwMode() bool {
	if x != nil {
		return x.IsMwMode
	}
	return false
}

func (x *ReadModelServiceReq) GetOnlyRunning() bool {
	if x != nil {
		return x.OnlyRunning
	}
	return false
}

func (x *ReadModelServiceReq) GetContain() string {
	if x != nil {
		return x.Contain
	}
	return ""
}

func (x *ReadModelServiceReq) GetWithAsset() bool {
	if x != nil {
		return x.WithAsset
	}
	return false
}

func (x *ReadModelServiceReq) GetInterfaceSpec() string {
	if x != nil {
		return x.InterfaceSpec
	}
	return ""
}

func (x *ReadModelServiceReq) GetOmitCensored() bool {
	if x != nil {
		return x.OmitCensored
	}
	return false
}

func (x *ReadModelServiceReq) GetWithCustomService() bool {
	if x != nil {
		return x.WithCustomService
	}
	return false
}

func (x *ReadModelServiceReq) GetLiteMode() bool {
	if x != nil {
		return x.LiteMode
	}
	return false
}

type ReadModelServiceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Services []*ModelService `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
	Total    int32           `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	PageNum  int32           `protobuf:"varint,3,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	PageSize int32           `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *ReadModelServiceRsp) Reset() {
	*x = ReadModelServiceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadModelServiceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadModelServiceRsp) ProtoMessage() {}

func (x *ReadModelServiceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadModelServiceRsp.ProtoReflect.Descriptor instead.
func (*ReadModelServiceRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_service_proto_rawDescGZIP(), []int{1}
}

func (x *ReadModelServiceRsp) GetServices() []*ModelService {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *ReadModelServiceRsp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ReadModelServiceRsp) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *ReadModelServiceRsp) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// TestModelServiceReq 是进行模型体验或者模型测试时用户向后端传递的结构，可以通过多种条件进行推理
type TestModelServiceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             string         `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                          // 按照 Deployment id进行推理，仅指定该项则默认使用第一个接口
	ModelId        string         `protobuf:"bytes,2,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"` // 按照 model id 跟 release id 进行推理
	ReleaseId      string         `protobuf:"bytes,3,opt,name=release_id,json=releaseId,proto3" json:"release_id,omitempty"`
	Url            string         `protobuf:"bytes,4,opt,name=url,proto3" json:"url,omitempty"` // 按照具体的 url进行推理
	Body           string         `protobuf:"bytes,5,opt,name=body,proto3" json:"body,omitempty"`
	TimeoutSeconds int32          `protobuf:"varint,6,opt,name=timeout_seconds,json=timeoutSeconds,proto3" json:"timeout_seconds,omitempty"`
	Ctx            *UserContext   `protobuf:"bytes,7,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Path           string         `protobuf:"bytes,8,opt,name=path,proto3" json:"path,omitempty"`                                            // 指定path进行推理,与url应该能对应起来
	Type           DeploymentType `protobuf:"varint,9,opt,name=type,proto3,enum=proto.DeploymentType" json:"type,omitempty"`                 // 判断是模型服务测试还是模型体验
	ConversationId string         `protobuf:"bytes,10,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"` // LLM 推理时,对话id
	QuestionId     string         `protobuf:"bytes,11,opt,name=question_id,json=questionId,proto3" json:"question_id,omitempty"`
	IsSeldonMode   bool           `protobuf:"varint,12,opt,name=is_seldon_mode,json=isSeldonMode,proto3" json:"is_seldon_mode,omitempty"`
	SeldonDp       string         `protobuf:"bytes,13,opt,name=seldon_dp,json=seldonDp,proto3" json:"seldon_dp,omitempty"`
	Namespace      string         `protobuf:"bytes,14,opt,name=namespace,proto3" json:"namespace,omitempty"`
	UseGrpc        bool           `protobuf:"varint,15,opt,name=use_grpc,json=useGrpc,proto3" json:"use_grpc,omitempty"`
}

func (x *TestModelServiceReq) Reset() {
	*x = TestModelServiceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestModelServiceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestModelServiceReq) ProtoMessage() {}

func (x *TestModelServiceReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestModelServiceReq.ProtoReflect.Descriptor instead.
func (*TestModelServiceReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_service_proto_rawDescGZIP(), []int{2}
}

func (x *TestModelServiceReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TestModelServiceReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *TestModelServiceReq) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

func (x *TestModelServiceReq) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *TestModelServiceReq) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *TestModelServiceReq) GetTimeoutSeconds() int32 {
	if x != nil {
		return x.TimeoutSeconds
	}
	return 0
}

func (x *TestModelServiceReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *TestModelServiceReq) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *TestModelServiceReq) GetType() DeploymentType {
	if x != nil {
		return x.Type
	}
	return DeploymentType_DEPLOYMENT_TYPE_UNSPECIFIED
}

func (x *TestModelServiceReq) GetConversationId() string {
	if x != nil {
		return x.ConversationId
	}
	return ""
}

func (x *TestModelServiceReq) GetQuestionId() string {
	if x != nil {
		return x.QuestionId
	}
	return ""
}

func (x *TestModelServiceReq) GetIsSeldonMode() bool {
	if x != nil {
		return x.IsSeldonMode
	}
	return false
}

func (x *TestModelServiceReq) GetSeldonDp() string {
	if x != nil {
		return x.SeldonDp
	}
	return ""
}

func (x *TestModelServiceReq) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *TestModelServiceReq) GetUseGrpc() bool {
	if x != nil {
		return x.UseGrpc
	}
	return false
}

type TestModelServiceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Payload *anypb.Any `protobuf:"bytes,1,opt,name=payload,proto3" json:"payload,omitempty"`
}

func (x *TestModelServiceRsp) Reset() {
	*x = TestModelServiceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestModelServiceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestModelServiceRsp) ProtoMessage() {}

func (x *TestModelServiceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestModelServiceRsp.ProtoReflect.Descriptor instead.
func (*TestModelServiceRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_service_proto_rawDescGZIP(), []int{3}
}

func (x *TestModelServiceRsp) GetPayload() *anypb.Any {
	if x != nil {
		return x.Payload
	}
	return nil
}

type SaveModelServiceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Service *ModelService `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	Ctx     *UserContext  `protobuf:"bytes,2,opt,name=ctx,proto3" json:"ctx,omitempty"`
}

func (x *SaveModelServiceReq) Reset() {
	*x = SaveModelServiceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveModelServiceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveModelServiceReq) ProtoMessage() {}

func (x *SaveModelServiceReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveModelServiceReq.ProtoReflect.Descriptor instead.
func (*SaveModelServiceReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_service_proto_rawDescGZIP(), []int{4}
}

func (x *SaveModelServiceReq) GetService() *ModelService {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *SaveModelServiceReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

type SaveModelServiceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Service *ModelService `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *SaveModelServiceRsp) Reset() {
	*x = SaveModelServiceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveModelServiceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveModelServiceRsp) ProtoMessage() {}

func (x *SaveModelServiceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveModelServiceRsp.ProtoReflect.Descriptor instead.
func (*SaveModelServiceRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_service_proto_rawDescGZIP(), []int{5}
}

func (x *SaveModelServiceRsp) GetService() *ModelService {
	if x != nil {
		return x.Service
	}
	return nil
}

type ModelStreamInferRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ModelName      string `protobuf:"bytes,2,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
	ModelVersion   string `protobuf:"bytes,3,opt,name=model_version,json=modelVersion,proto3" json:"model_version,omitempty"`
	Response       string `protobuf:"bytes,4,opt,name=response,proto3" json:"response,omitempty"`
	ErrorMessage   string `protobuf:"bytes,5,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	ConversationId string `protobuf:"bytes,6,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	QuestionId     string `protobuf:"bytes,7,opt,name=question_id,json=questionId,proto3" json:"question_id,omitempty"`
	ServiceId      string `protobuf:"bytes,8,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
}

func (x *ModelStreamInferRsp) Reset() {
	*x = ModelStreamInferRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelStreamInferRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelStreamInferRsp) ProtoMessage() {}

func (x *ModelStreamInferRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelStreamInferRsp.ProtoReflect.Descriptor instead.
func (*ModelStreamInferRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_service_proto_rawDescGZIP(), []int{6}
}

func (x *ModelStreamInferRsp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ModelStreamInferRsp) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *ModelStreamInferRsp) GetModelVersion() string {
	if x != nil {
		return x.ModelVersion
	}
	return ""
}

func (x *ModelStreamInferRsp) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

func (x *ModelStreamInferRsp) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *ModelStreamInferRsp) GetConversationId() string {
	if x != nil {
		return x.ConversationId
	}
	return ""
}

func (x *ModelStreamInferRsp) GetQuestionId() string {
	if x != nil {
		return x.QuestionId
	}
	return ""
}

func (x *ModelStreamInferRsp) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

type CreateRemoteServiceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx     *UserContext   `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Service *RemoteService `protobuf:"bytes,2,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *CreateRemoteServiceReq) Reset() {
	*x = CreateRemoteServiceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRemoteServiceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRemoteServiceReq) ProtoMessage() {}

func (x *CreateRemoteServiceReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRemoteServiceReq.ProtoReflect.Descriptor instead.
func (*CreateRemoteServiceReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_service_proto_rawDescGZIP(), []int{7}
}

func (x *CreateRemoteServiceReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *CreateRemoteServiceReq) GetService() *RemoteService {
	if x != nil {
		return x.Service
	}
	return nil
}

type CreateRemoteServiceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Service *RemoteService `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *CreateRemoteServiceRsp) Reset() {
	*x = CreateRemoteServiceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRemoteServiceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRemoteServiceRsp) ProtoMessage() {}

func (x *CreateRemoteServiceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRemoteServiceRsp.ProtoReflect.Descriptor instead.
func (*CreateRemoteServiceRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_service_proto_rawDescGZIP(), []int{8}
}

func (x *CreateRemoteServiceRsp) GetService() *RemoteService {
	if x != nil {
		return x.Service
	}
	return nil
}

type ReadRemoteServiceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx            *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Id             string       `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	CheckReachable bool         `protobuf:"varint,3,opt,name=check_reachable,json=checkReachable,proto3" json:"check_reachable,omitempty"`
	IsPublishedId  bool         `protobuf:"varint,4,opt,name=is_published_id,json=isPublishedId,proto3" json:"is_published_id,omitempty"` // id是否是发布之后的id
}

func (x *ReadRemoteServiceReq) Reset() {
	*x = ReadRemoteServiceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadRemoteServiceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadRemoteServiceReq) ProtoMessage() {}

func (x *ReadRemoteServiceReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadRemoteServiceReq.ProtoReflect.Descriptor instead.
func (*ReadRemoteServiceReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_service_proto_rawDescGZIP(), []int{9}
}

func (x *ReadRemoteServiceReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *ReadRemoteServiceReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ReadRemoteServiceReq) GetCheckReachable() bool {
	if x != nil {
		return x.CheckReachable
	}
	return false
}

func (x *ReadRemoteServiceReq) GetIsPublishedId() bool {
	if x != nil {
		return x.IsPublishedId
	}
	return false
}

type ReadRemoteServiceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Services []*RemoteService `protobuf:"bytes,2,rep,name=services,proto3" json:"services,omitempty"`
}

func (x *ReadRemoteServiceRsp) Reset() {
	*x = ReadRemoteServiceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadRemoteServiceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadRemoteServiceRsp) ProtoMessage() {}

func (x *ReadRemoteServiceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadRemoteServiceRsp.ProtoReflect.Descriptor instead.
func (*ReadRemoteServiceRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_service_proto_rawDescGZIP(), []int{10}
}

func (x *ReadRemoteServiceRsp) GetServices() []*RemoteService {
	if x != nil {
		return x.Services
	}
	return nil
}

type UpdateRemoteServiceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx     *UserContext   `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Service *RemoteService `protobuf:"bytes,2,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *UpdateRemoteServiceReq) Reset() {
	*x = UpdateRemoteServiceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRemoteServiceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRemoteServiceReq) ProtoMessage() {}

func (x *UpdateRemoteServiceReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRemoteServiceReq.ProtoReflect.Descriptor instead.
func (*UpdateRemoteServiceReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_service_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateRemoteServiceReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *UpdateRemoteServiceReq) GetService() *RemoteService {
	if x != nil {
		return x.Service
	}
	return nil
}

type UpdateRemoteServiceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Service *RemoteService `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *UpdateRemoteServiceRsp) Reset() {
	*x = UpdateRemoteServiceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRemoteServiceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRemoteServiceRsp) ProtoMessage() {}

func (x *UpdateRemoteServiceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRemoteServiceRsp.ProtoReflect.Descriptor instead.
func (*UpdateRemoteServiceRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_service_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateRemoteServiceRsp) GetService() *RemoteService {
	if x != nil {
		return x.Service
	}
	return nil
}

type DeleteRemoteServiceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Ids []string     `protobuf:"bytes,2,rep,name=ids,proto3" json:"ids,omitempty"`
}

func (x *DeleteRemoteServiceReq) Reset() {
	*x = DeleteRemoteServiceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRemoteServiceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRemoteServiceReq) ProtoMessage() {}

func (x *DeleteRemoteServiceReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRemoteServiceReq.ProtoReflect.Descriptor instead.
func (*DeleteRemoteServiceReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_service_proto_rawDescGZIP(), []int{13}
}

func (x *DeleteRemoteServiceReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *DeleteRemoteServiceReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type DeleteRemoteServiceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteRemoteServiceRsp) Reset() {
	*x = DeleteRemoteServiceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRemoteServiceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRemoteServiceRsp) ProtoMessage() {}

func (x *DeleteRemoteServiceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRemoteServiceRsp.ProtoReflect.Descriptor instead.
func (*DeleteRemoteServiceRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_service_proto_rawDescGZIP(), []int{14}
}

type TestRemoteServiceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             string               `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Url            string               `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Body           string               `protobuf:"bytes,3,opt,name=body,proto3" json:"body,omitempty"`
	TimeoutSeconds int32                `protobuf:"varint,4,opt,name=timeout_seconds,json=timeoutSeconds,proto3" json:"timeout_seconds,omitempty"`
	Ctx            *UserContext         `protobuf:"bytes,5,opt,name=ctx,proto3" json:"ctx,omitempty"`
	ServiceId      string               `protobuf:"bytes,6,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	ChatMode       bool                 `protobuf:"varint,7,opt,name=chat_mode,json=chatMode,proto3" json:"chat_mode,omitempty"`       // 测试页面是否开启对话界面
	SeldonMode     bool                 `protobuf:"varint,8,opt,name=seldon_mode,json=seldonMode,proto3" json:"seldon_mode,omitempty"` // 转发已经发布到mlops seldon的远程模型请求
	FormData       []*MultipartFormdata `protobuf:"bytes,9,rep,name=form_data,json=formData,proto3" json:"form_data,omitempty"`
	ContentType    string               `protobuf:"bytes,10,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
}

func (x *TestRemoteServiceReq) Reset() {
	*x = TestRemoteServiceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestRemoteServiceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestRemoteServiceReq) ProtoMessage() {}

func (x *TestRemoteServiceReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestRemoteServiceReq.ProtoReflect.Descriptor instead.
func (*TestRemoteServiceReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_service_proto_rawDescGZIP(), []int{15}
}

func (x *TestRemoteServiceReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TestRemoteServiceReq) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *TestRemoteServiceReq) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *TestRemoteServiceReq) GetTimeoutSeconds() int32 {
	if x != nil {
		return x.TimeoutSeconds
	}
	return 0
}

func (x *TestRemoteServiceReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *TestRemoteServiceReq) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *TestRemoteServiceReq) GetChatMode() bool {
	if x != nil {
		return x.ChatMode
	}
	return false
}

func (x *TestRemoteServiceReq) GetSeldonMode() bool {
	if x != nil {
		return x.SeldonMode
	}
	return false
}

func (x *TestRemoteServiceReq) GetFormData() []*MultipartFormdata {
	if x != nil {
		return x.FormData
	}
	return nil
}

func (x *TestRemoteServiceReq) GetContentType() string {
	if x != nil {
		return x.ContentType
	}
	return ""
}

type MultipartFormdata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key         string                         `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Type        string                         `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"` // text or file
	Content     string                         `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	ContentDesc *MultipartFormdata_ContentDesc `protobuf:"bytes,4,opt,name=content_desc,json=contentDesc,proto3" json:"content_desc,omitempty"`
}

func (x *MultipartFormdata) Reset() {
	*x = MultipartFormdata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MultipartFormdata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MultipartFormdata) ProtoMessage() {}

func (x *MultipartFormdata) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MultipartFormdata.ProtoReflect.Descriptor instead.
func (*MultipartFormdata) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_service_proto_rawDescGZIP(), []int{16}
}

func (x *MultipartFormdata) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *MultipartFormdata) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *MultipartFormdata) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *MultipartFormdata) GetContentDesc() *MultipartFormdata_ContentDesc {
	if x != nil {
		return x.ContentDesc
	}
	return nil
}

type TestRemoteServiceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ServiceName  string `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	Response     string `protobuf:"bytes,3,opt,name=response,proto3" json:"response,omitempty"`
	ErrorMsg     string `protobuf:"bytes,4,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
	EventName    string `protobuf:"bytes,5,opt,name=event_name,json=eventName,proto3" json:"event_name,omitempty"`
	ResponseCode int32  `protobuf:"varint,6,opt,name=response_code,json=responseCode,proto3" json:"response_code,omitempty"`
}

func (x *TestRemoteServiceRsp) Reset() {
	*x = TestRemoteServiceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestRemoteServiceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestRemoteServiceRsp) ProtoMessage() {}

func (x *TestRemoteServiceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestRemoteServiceRsp.ProtoReflect.Descriptor instead.
func (*TestRemoteServiceRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_service_proto_rawDescGZIP(), []int{17}
}

func (x *TestRemoteServiceRsp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TestRemoteServiceRsp) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *TestRemoteServiceRsp) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

func (x *TestRemoteServiceRsp) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

func (x *TestRemoteServiceRsp) GetEventName() string {
	if x != nil {
		return x.EventName
	}
	return ""
}

func (x *TestRemoteServiceRsp) GetResponseCode() int32 {
	if x != nil {
		return x.ResponseCode
	}
	return 0
}

type GetLoraBaseServicesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx       *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	ModelId   string       `protobuf:"bytes,2,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ReleaseId string       `protobuf:"bytes,3,opt,name=release_id,json=releaseId,proto3" json:"release_id,omitempty"`
}

func (x *GetLoraBaseServicesReq) Reset() {
	*x = GetLoraBaseServicesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoraBaseServicesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoraBaseServicesReq) ProtoMessage() {}

func (x *GetLoraBaseServicesReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoraBaseServicesReq.ProtoReflect.Descriptor instead.
func (*GetLoraBaseServicesReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_service_proto_rawDescGZIP(), []int{18}
}

func (x *GetLoraBaseServicesReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *GetLoraBaseServicesReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *GetLoraBaseServicesReq) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

type GetLoraBaseServicesRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseModelServices []*serving.MLOpsServiceBaseInfo `protobuf:"bytes,1,rep,name=base_model_services,json=baseModelServices,proto3" json:"base_model_services,omitempty"`
}

func (x *GetLoraBaseServicesRsp) Reset() {
	*x = GetLoraBaseServicesRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoraBaseServicesRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoraBaseServicesRsp) ProtoMessage() {}

func (x *GetLoraBaseServicesRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoraBaseServicesRsp.ProtoReflect.Descriptor instead.
func (*GetLoraBaseServicesRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_service_proto_rawDescGZIP(), []int{19}
}

func (x *GetLoraBaseServicesRsp) GetBaseModelServices() []*serving.MLOpsServiceBaseInfo {
	if x != nil {
		return x.BaseModelServices
	}
	return nil
}

type LoadLoraReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx                *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	ModelId            string       `protobuf:"bytes,2,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ReleaseId          string       `protobuf:"bytes,3,opt,name=release_id,json=releaseId,proto3" json:"release_id,omitempty"`
	BaseModelServiceId string       `protobuf:"bytes,4,opt,name=base_model_service_id,json=baseModelServiceId,proto3" json:"base_model_service_id,omitempty"`
	LoraName           string       `protobuf:"bytes,5,opt,name=lora_name,json=loraName,proto3" json:"lora_name,omitempty"`
}

func (x *LoadLoraReq) Reset() {
	*x = LoadLoraReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadLoraReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadLoraReq) ProtoMessage() {}

func (x *LoadLoraReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadLoraReq.ProtoReflect.Descriptor instead.
func (*LoadLoraReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_service_proto_rawDescGZIP(), []int{20}
}

func (x *LoadLoraReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *LoadLoraReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *LoadLoraReq) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

func (x *LoadLoraReq) GetBaseModelServiceId() string {
	if x != nil {
		return x.BaseModelServiceId
	}
	return ""
}

func (x *LoadLoraReq) GetLoraName() string {
	if x != nil {
		return x.LoraName
	}
	return ""
}

type LoadLoraRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseModelServiceId string `protobuf:"bytes,1,opt,name=base_model_service_id,json=baseModelServiceId,proto3" json:"base_model_service_id,omitempty"`
	LoraName           string `protobuf:"bytes,2,opt,name=lora_name,json=loraName,proto3" json:"lora_name,omitempty"`
	LoadStatusCode     int32  `protobuf:"varint,3,opt,name=load_status_code,json=loadStatusCode,proto3" json:"load_status_code,omitempty"`
	Msg                string `protobuf:"bytes,4,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *LoadLoraRsp) Reset() {
	*x = LoadLoraRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadLoraRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadLoraRsp) ProtoMessage() {}

func (x *LoadLoraRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadLoraRsp.ProtoReflect.Descriptor instead.
func (*LoadLoraRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_service_proto_rawDescGZIP(), []int{21}
}

func (x *LoadLoraRsp) GetBaseModelServiceId() string {
	if x != nil {
		return x.BaseModelServiceId
	}
	return ""
}

func (x *LoadLoraRsp) GetLoraName() string {
	if x != nil {
		return x.LoraName
	}
	return ""
}

func (x *LoadLoraRsp) GetLoadStatusCode() int32 {
	if x != nil {
		return x.LoadStatusCode
	}
	return 0
}

func (x *LoadLoraRsp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type MultipartFormdata_ContentDesc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Size int64  `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
}

func (x *MultipartFormdata_ContentDesc) Reset() {
	*x = MultipartFormdata_ContentDesc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MultipartFormdata_ContentDesc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MultipartFormdata_ContentDesc) ProtoMessage() {}

func (x *MultipartFormdata_ContentDesc) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MultipartFormdata_ContentDesc.ProtoReflect.Descriptor instead.
func (*MultipartFormdata_ContentDesc) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_service_proto_rawDescGZIP(), []int{16, 0}
}

func (x *MultipartFormdata_ContentDesc) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MultipartFormdata_ContentDesc) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

var File_proto_rpc_model_service_proto protoreflect.FileDescriptor

var file_proto_rpc_model_service_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x70, 0x63, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61,
	0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2f, 0x6d, 0x6c, 0x6f, 0x70, 0x73, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xaa, 0x06, 0x0a, 0x13,
	0x52, 0x65, 0x61, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x29, 0x0a,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x52,
	0x07, 0x70, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x29,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x04, 0x6b, 0x69, 0x6e,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12,
	0x2e, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53,
	0x75, 0x62, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x07, 0x73, 0x75, 0x62, 0x4b, 0x69, 0x6e, 0x64, 0x12,
	0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x73, 0x65, 0x6c, 0x64, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x53, 0x65, 0x6c, 0x64, 0x6f,
	0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x4e, 0x0a, 0x0c, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x72, 0x65,
	0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x11, 0x77, 0x69, 0x74, 0x68, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1c, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x6d, 0x77, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x4d, 0x77, 0x4d,
	0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x6e, 0x6c, 0x79, 0x5f, 0x72, 0x75, 0x6e, 0x6e,
	0x69, 0x6e, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x6f, 0x6e, 0x6c, 0x79, 0x52,
	0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69,
	0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x77, 0x69, 0x74, 0x68, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12,
	0x25, 0x0a, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x73, 0x70, 0x65,
	0x63, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61,
	0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x6d, 0x69, 0x74, 0x5f, 0x63,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x65, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x6f,
	0x6d, 0x69, 0x74, 0x43, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x65, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x77,
	0x69, 0x74, 0x68, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x77, 0x69, 0x74, 0x68, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c,
	0x69, 0x74, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x6c, 0x69, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x1a, 0x3e, 0x0a, 0x10, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x94, 0x01, 0x0a, 0x13, 0x52, 0x65, 0x61,
	0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x73, 0x70,
	0x12, 0x2f, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e,
	0x75, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22,
	0xd9, 0x03, 0x0a, 0x13, 0x54, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49,
	0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0e, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73,
	0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x29, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x73, 0x65, 0x6c, 0x64, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x53, 0x65, 0x6c, 0x64, 0x6f,
	0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x6c, 0x64, 0x6f, 0x6e, 0x5f,
	0x64, 0x70, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x6c, 0x64, 0x6f, 0x6e,
	0x44, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x5f, 0x67, 0x72, 0x70, 0x63, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x75, 0x73, 0x65, 0x47, 0x72, 0x70, 0x63, 0x22, 0x45, 0x0a, 0x13, 0x54,
	0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x73, 0x70, 0x12, 0x2e, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f,
	0x61, 0x64, 0x22, 0x6a, 0x0a, 0x13, 0x53, 0x61, 0x76, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x2d, 0x0a, 0x07, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x22, 0x44,
	0x0a, 0x13, 0x53, 0x61, 0x76, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0x2d, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x07, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x22, 0x93, 0x02, 0x0a, 0x13, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x74,
	0x72, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x22, 0x6e, 0x0a, 0x16, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x2e, 0x0a, 0x07, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x22, 0x48, 0x0a, 0x16, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x73, 0x70, 0x12, 0x2e, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65,
	0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x07, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x22, 0x9d, 0x01, 0x0a, 0x14, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x6d,
	0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a,
	0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03,
	0x63, 0x74, 0x78, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x61,
	0x63, 0x68, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x52, 0x65, 0x61, 0x63, 0x68, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x26, 0x0a, 0x0f,
	0x69, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x65, 0x64, 0x49, 0x64, 0x22, 0x48, 0x0a, 0x14, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x6d, 0x6f,
	0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0x30, 0x0a, 0x08,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x22, 0x6e,
	0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x2e,
	0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x22, 0x48,
	0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0x2e, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x22, 0x50, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x18, 0x0a, 0x16, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x73, 0x70, 0x22, 0xd2, 0x02, 0x0a, 0x14, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x6d,
	0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12,
	0x12, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62,
	0x6f, 0x64, 0x79, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x5f, 0x73,
	0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x74, 0x69,
	0x6d, 0x65, 0x6f, 0x75, 0x74, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x24, 0x0a, 0x03,
	0x63, 0x74, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63,
	0x74, 0x78, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x63, 0x68, 0x61, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x73, 0x65, 0x6c, 0x64, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0a, 0x73, 0x65, 0x6c, 0x64, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12,
	0x35, 0x0a, 0x09, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x09, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69,
	0x70, 0x61, 0x72, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x66, 0x6f,
	0x72, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0xd3, 0x01, 0x0a, 0x11, 0x4d, 0x75,
	0x6c, 0x74, 0x69, 0x70, 0x61, 0x72, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12,
	0x47, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x75,
	0x6c, 0x74, 0x69, 0x70, 0x61, 0x72, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x64, 0x61, 0x74, 0x61, 0x2e,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x73, 0x63, 0x52, 0x0b, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x73, 0x63, 0x1a, 0x35, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x44, 0x65, 0x73, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22,
	0xc6, 0x01, 0x0a, 0x14, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x5f, 0x6d, 0x73, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x4d, 0x73, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x78, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4c,
	0x6f, 0x72, 0x61, 0x42, 0x61, 0x73, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x49, 0x64, 0x22, 0x67, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x72, 0x61, 0x42, 0x61, 0x73,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x73, 0x70, 0x12, 0x4d, 0x0a, 0x13,
	0x62, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x4c, 0x4f, 0x70, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x11, 0x62, 0x61, 0x73, 0x65, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x22, 0xbd, 0x01, 0x0a, 0x0b,
	0x4c, 0x6f, 0x61, 0x64, 0x4c, 0x6f, 0x72, 0x61, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63,
	0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74,
	0x78, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x15, 0x62,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x62, 0x61, 0x73, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x6c, 0x6f, 0x72, 0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6c, 0x6f, 0x72, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x99, 0x01, 0x0a, 0x0b,
	0x4c, 0x6f, 0x61, 0x64, 0x4c, 0x6f, 0x72, 0x61, 0x52, 0x73, 0x70, 0x12, 0x31, 0x0a, 0x15, 0x62,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x62, 0x61, 0x73, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x6c, 0x6f, 0x72, 0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6c, 0x6f, 0x72, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x6c,
	0x6f, 0x61, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x32, 0xcb, 0x02, 0x0a, 0x13, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12,
	0x4a, 0x0a, 0x10, 0x52, 0x65, 0x61, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x61, 0x64,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0x4a, 0x0a, 0x10, 0x54,
	0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0x4a, 0x0a, 0x10, 0x53, 0x61, 0x76, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1a, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x53, 0x61, 0x76, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x73, 0x70, 0x12, 0x50, 0x0a, 0x14, 0x54, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x12, 0x1a, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x65, 0x72,
	0x52, 0x73, 0x70, 0x30, 0x01, 0x32, 0xe4, 0x02, 0x0a, 0x14, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x53,
	0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x73, 0x70, 0x12, 0x4d, 0x0a, 0x11, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x6d, 0x6f, 0x74,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65,
	0x61, 0x64, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x73, 0x70, 0x12, 0x53, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x6d, 0x6f,
	0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0x53, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1d,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x6d,
	0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x6d, 0x6f,
	0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x73, 0x70, 0x42, 0x26, 0x5a, 0x24,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70,
	0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70,
	0x62, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_rpc_model_service_proto_rawDescOnce sync.Once
	file_proto_rpc_model_service_proto_rawDescData = file_proto_rpc_model_service_proto_rawDesc
)

func file_proto_rpc_model_service_proto_rawDescGZIP() []byte {
	file_proto_rpc_model_service_proto_rawDescOnce.Do(func() {
		file_proto_rpc_model_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_rpc_model_service_proto_rawDescData)
	})
	return file_proto_rpc_model_service_proto_rawDescData
}

var file_proto_rpc_model_service_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_proto_rpc_model_service_proto_goTypes = []interface{}{
	(*ReadModelServiceReq)(nil),           // 0: proto.ReadModelServiceReq
	(*ReadModelServiceRsp)(nil),           // 1: proto.ReadModelServiceRsp
	(*TestModelServiceReq)(nil),           // 2: proto.TestModelServiceReq
	(*TestModelServiceRsp)(nil),           // 3: proto.TestModelServiceRsp
	(*SaveModelServiceReq)(nil),           // 4: proto.SaveModelServiceReq
	(*SaveModelServiceRsp)(nil),           // 5: proto.SaveModelServiceRsp
	(*ModelStreamInferRsp)(nil),           // 6: proto.ModelStreamInferRsp
	(*CreateRemoteServiceReq)(nil),        // 7: proto.CreateRemoteServiceReq
	(*CreateRemoteServiceRsp)(nil),        // 8: proto.CreateRemoteServiceRsp
	(*ReadRemoteServiceReq)(nil),          // 9: proto.ReadRemoteServiceReq
	(*ReadRemoteServiceRsp)(nil),          // 10: proto.ReadRemoteServiceRsp
	(*UpdateRemoteServiceReq)(nil),        // 11: proto.UpdateRemoteServiceReq
	(*UpdateRemoteServiceRsp)(nil),        // 12: proto.UpdateRemoteServiceRsp
	(*DeleteRemoteServiceReq)(nil),        // 13: proto.DeleteRemoteServiceReq
	(*DeleteRemoteServiceRsp)(nil),        // 14: proto.DeleteRemoteServiceRsp
	(*TestRemoteServiceReq)(nil),          // 15: proto.TestRemoteServiceReq
	(*MultipartFormdata)(nil),             // 16: proto.MultipartFormdata
	(*TestRemoteServiceRsp)(nil),          // 17: proto.TestRemoteServiceRsp
	(*GetLoraBaseServicesReq)(nil),        // 18: proto.GetLoraBaseServicesReq
	(*GetLoraBaseServicesRsp)(nil),        // 19: proto.GetLoraBaseServicesRsp
	(*LoadLoraReq)(nil),                   // 20: proto.LoadLoraReq
	(*LoadLoraRsp)(nil),                   // 21: proto.LoadLoraRsp
	nil,                                   // 22: proto.ReadModelServiceReq.MatchLabelsEntry
	(*MultipartFormdata_ContentDesc)(nil), // 23: proto.MultipartFormdata.ContentDesc
	(*PageReq)(nil),                       // 24: proto.PageReq
	(*UserContext)(nil),                   // 25: proto.UserContext
	(DeploymentType)(0),                   // 26: proto.DeploymentType
	(ModelKind)(0),                        // 27: proto.ModelKind
	(ModelSubKind)(0),                     // 28: proto.ModelSubKind
	(*ModelService)(nil),                  // 29: proto.ModelService
	(*anypb.Any)(nil),                     // 30: google.protobuf.Any
	(*RemoteService)(nil),                 // 31: proto.RemoteService
	(*serving.MLOpsServiceBaseInfo)(nil),  // 32: serving.MLOpsServiceBaseInfo
}
var file_proto_rpc_model_service_proto_depIdxs = []int32{
	24, // 0: proto.ReadModelServiceReq.page_req:type_name -> proto.PageReq
	25, // 1: proto.ReadModelServiceReq.ctx:type_name -> proto.UserContext
	26, // 2: proto.ReadModelServiceReq.type:type_name -> proto.DeploymentType
	27, // 3: proto.ReadModelServiceReq.kind:type_name -> proto.ModelKind
	28, // 4: proto.ReadModelServiceReq.sub_kind:type_name -> proto.ModelSubKind
	22, // 5: proto.ReadModelServiceReq.match_labels:type_name -> proto.ReadModelServiceReq.MatchLabelsEntry
	29, // 6: proto.ReadModelServiceRsp.services:type_name -> proto.ModelService
	25, // 7: proto.TestModelServiceReq.ctx:type_name -> proto.UserContext
	26, // 8: proto.TestModelServiceReq.type:type_name -> proto.DeploymentType
	30, // 9: proto.TestModelServiceRsp.payload:type_name -> google.protobuf.Any
	29, // 10: proto.SaveModelServiceReq.service:type_name -> proto.ModelService
	25, // 11: proto.SaveModelServiceReq.ctx:type_name -> proto.UserContext
	29, // 12: proto.SaveModelServiceRsp.service:type_name -> proto.ModelService
	25, // 13: proto.CreateRemoteServiceReq.ctx:type_name -> proto.UserContext
	31, // 14: proto.CreateRemoteServiceReq.service:type_name -> proto.RemoteService
	31, // 15: proto.CreateRemoteServiceRsp.service:type_name -> proto.RemoteService
	25, // 16: proto.ReadRemoteServiceReq.ctx:type_name -> proto.UserContext
	31, // 17: proto.ReadRemoteServiceRsp.services:type_name -> proto.RemoteService
	25, // 18: proto.UpdateRemoteServiceReq.ctx:type_name -> proto.UserContext
	31, // 19: proto.UpdateRemoteServiceReq.service:type_name -> proto.RemoteService
	31, // 20: proto.UpdateRemoteServiceRsp.service:type_name -> proto.RemoteService
	25, // 21: proto.DeleteRemoteServiceReq.ctx:type_name -> proto.UserContext
	25, // 22: proto.TestRemoteServiceReq.ctx:type_name -> proto.UserContext
	16, // 23: proto.TestRemoteServiceReq.form_data:type_name -> proto.MultipartFormdata
	23, // 24: proto.MultipartFormdata.content_desc:type_name -> proto.MultipartFormdata.ContentDesc
	25, // 25: proto.GetLoraBaseServicesReq.ctx:type_name -> proto.UserContext
	32, // 26: proto.GetLoraBaseServicesRsp.base_model_services:type_name -> serving.MLOpsServiceBaseInfo
	25, // 27: proto.LoadLoraReq.ctx:type_name -> proto.UserContext
	0,  // 28: proto.ModelServiceManager.ReadModelService:input_type -> proto.ReadModelServiceReq
	2,  // 29: proto.ModelServiceManager.TestModelService:input_type -> proto.TestModelServiceReq
	4,  // 30: proto.ModelServiceManager.SaveModelService:input_type -> proto.SaveModelServiceReq
	2,  // 31: proto.ModelServiceManager.TestModelStreamInfer:input_type -> proto.TestModelServiceReq
	7,  // 32: proto.RemoteServiceManager.CreateRemoteService:input_type -> proto.CreateRemoteServiceReq
	9,  // 33: proto.RemoteServiceManager.ReadRemoteService:input_type -> proto.ReadRemoteServiceReq
	11, // 34: proto.RemoteServiceManager.UpdateRemoteService:input_type -> proto.UpdateRemoteServiceReq
	13, // 35: proto.RemoteServiceManager.DeleteRemoteService:input_type -> proto.DeleteRemoteServiceReq
	1,  // 36: proto.ModelServiceManager.ReadModelService:output_type -> proto.ReadModelServiceRsp
	3,  // 37: proto.ModelServiceManager.TestModelService:output_type -> proto.TestModelServiceRsp
	5,  // 38: proto.ModelServiceManager.SaveModelService:output_type -> proto.SaveModelServiceRsp
	6,  // 39: proto.ModelServiceManager.TestModelStreamInfer:output_type -> proto.ModelStreamInferRsp
	8,  // 40: proto.RemoteServiceManager.CreateRemoteService:output_type -> proto.CreateRemoteServiceRsp
	10, // 41: proto.RemoteServiceManager.ReadRemoteService:output_type -> proto.ReadRemoteServiceRsp
	12, // 42: proto.RemoteServiceManager.UpdateRemoteService:output_type -> proto.UpdateRemoteServiceRsp
	14, // 43: proto.RemoteServiceManager.DeleteRemoteService:output_type -> proto.DeleteRemoteServiceRsp
	36, // [36:44] is the sub-list for method output_type
	28, // [28:36] is the sub-list for method input_type
	28, // [28:28] is the sub-list for extension type_name
	28, // [28:28] is the sub-list for extension extendee
	0,  // [0:28] is the sub-list for field type_name
}

func init() { file_proto_rpc_model_service_proto_init() }
func file_proto_rpc_model_service_proto_init() {
	if File_proto_rpc_model_service_proto != nil {
		return
	}
	file_proto_common_proto_init()
	file_proto_model_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_proto_rpc_model_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadModelServiceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadModelServiceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestModelServiceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestModelServiceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveModelServiceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveModelServiceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelStreamInferRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRemoteServiceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRemoteServiceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadRemoteServiceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadRemoteServiceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRemoteServiceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRemoteServiceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRemoteServiceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRemoteServiceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestRemoteServiceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MultipartFormdata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestRemoteServiceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoraBaseServicesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoraBaseServicesRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadLoraReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadLoraRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MultipartFormdata_ContentDesc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_rpc_model_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_proto_rpc_model_service_proto_goTypes,
		DependencyIndexes: file_proto_rpc_model_service_proto_depIdxs,
		MessageInfos:      file_proto_rpc_model_service_proto_msgTypes,
	}.Build()
	File_proto_rpc_model_service_proto = out.File
	file_proto_rpc_model_service_proto_rawDesc = nil
	file_proto_rpc_model_service_proto_goTypes = nil
	file_proto_rpc_model_service_proto_depIdxs = nil
}
