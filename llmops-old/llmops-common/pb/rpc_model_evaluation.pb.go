// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.18.1
// source: proto/rpc_model_evaluation.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EvaluationMetricsKind int32

const (
	EvaluationMetricsKind_EVALUATION_METRICS_KIND_UNSPECIFIC  EvaluationMetricsKind = 0
	EvaluationMetricsKind_EVALUATION_METRICS_KIND_MODEL       EvaluationMetricsKind = 1 // 模型指标
	EvaluationMetricsKind_EVALUATION_METRICS_KIND_PERFORMANCE EvaluationMetricsKind = 2 // 性能指标
)

// Enum value maps for EvaluationMetricsKind.
var (
	EvaluationMetricsKind_name = map[int32]string{
		0: "EVALUATION_METRICS_KIND_UNSPECIFIC",
		1: "EVALUATION_METRICS_KIND_MODEL",
		2: "EVALUATION_METRICS_KIND_PERFORMANCE",
	}
	EvaluationMetricsKind_value = map[string]int32{
		"EVALUATION_METRICS_KIND_UNSPECIFIC":  0,
		"EVALUATION_METRICS_KIND_MODEL":       1,
		"EVALUATION_METRICS_KIND_PERFORMANCE": 2,
	}
)

func (x EvaluationMetricsKind) Enum() *EvaluationMetricsKind {
	p := new(EvaluationMetricsKind)
	*p = x
	return p
}

func (x EvaluationMetricsKind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EvaluationMetricsKind) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_rpc_model_evaluation_proto_enumTypes[0].Descriptor()
}

func (EvaluationMetricsKind) Type() protoreflect.EnumType {
	return &file_proto_rpc_model_evaluation_proto_enumTypes[0]
}

func (x EvaluationMetricsKind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EvaluationMetricsKind.Descriptor instead.
func (EvaluationMetricsKind) EnumDescriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{0}
}

type CreateModelEvaluationReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx        *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Evaluation *Evaluation  `protobuf:"bytes,2,opt,name=evaluation,proto3" json:"evaluation,omitempty"`
}

func (x *CreateModelEvaluationReq) Reset() {
	*x = CreateModelEvaluationReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateModelEvaluationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateModelEvaluationReq) ProtoMessage() {}

func (x *CreateModelEvaluationReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateModelEvaluationReq.ProtoReflect.Descriptor instead.
func (*CreateModelEvaluationReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{0}
}

func (x *CreateModelEvaluationReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *CreateModelEvaluationReq) GetEvaluation() *Evaluation {
	if x != nil {
		return x.Evaluation
	}
	return nil
}

type CreateModelEvaluationRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Evaluation *Evaluation `protobuf:"bytes,1,opt,name=evaluation,proto3" json:"evaluation,omitempty"`
}

func (x *CreateModelEvaluationRsp) Reset() {
	*x = CreateModelEvaluationRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateModelEvaluationRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateModelEvaluationRsp) ProtoMessage() {}

func (x *CreateModelEvaluationRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateModelEvaluationRsp.ProtoReflect.Descriptor instead.
func (*CreateModelEvaluationRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{1}
}

func (x *CreateModelEvaluationRsp) GetEvaluation() *Evaluation {
	if x != nil {
		return x.Evaluation
	}
	return nil
}

type ReadModelEvaluationReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         string       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ModelId    string       `protobuf:"bytes,2,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ReleaseIds []string     `protobuf:"bytes,3,rep,name=release_ids,json=releaseIds,proto3" json:"release_ids,omitempty"` // 需要对比的版本
	WithResult bool         `protobuf:"varint,4,opt,name=with_result,json=withResult,proto3" json:"with_result,omitempty"`
	WithStatus bool         `protobuf:"varint,5,opt,name=with_status,json=withStatus,proto3" json:"with_status,omitempty"`
	PageReq    *PageReq     `protobuf:"bytes,6,opt,name=page_req,json=pageReq,proto3" json:"page_req,omitempty"`
	Ctx        *UserContext `protobuf:"bytes,7,opt,name=ctx,proto3" json:"ctx,omitempty"`
}

func (x *ReadModelEvaluationReq) Reset() {
	*x = ReadModelEvaluationReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadModelEvaluationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadModelEvaluationReq) ProtoMessage() {}

func (x *ReadModelEvaluationReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadModelEvaluationReq.ProtoReflect.Descriptor instead.
func (*ReadModelEvaluationReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{2}
}

func (x *ReadModelEvaluationReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ReadModelEvaluationReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ReadModelEvaluationReq) GetReleaseIds() []string {
	if x != nil {
		return x.ReleaseIds
	}
	return nil
}

func (x *ReadModelEvaluationReq) GetWithResult() bool {
	if x != nil {
		return x.WithResult
	}
	return false
}

func (x *ReadModelEvaluationReq) GetWithStatus() bool {
	if x != nil {
		return x.WithStatus
	}
	return false
}

func (x *ReadModelEvaluationReq) GetPageReq() *PageReq {
	if x != nil {
		return x.PageReq
	}
	return nil
}

func (x *ReadModelEvaluationReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

type ExportModelEvaluationsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx     *UserContext          `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	ModelId string                `protobuf:"bytes,2,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	Metrics EvaluationMetricsKind `protobuf:"varint,3,opt,name=metrics,proto3,enum=proto.EvaluationMetricsKind" json:"metrics,omitempty"`
}

func (x *ExportModelEvaluationsReq) Reset() {
	*x = ExportModelEvaluationsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportModelEvaluationsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportModelEvaluationsReq) ProtoMessage() {}

func (x *ExportModelEvaluationsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportModelEvaluationsReq.ProtoReflect.Descriptor instead.
func (*ExportModelEvaluationsReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{3}
}

func (x *ExportModelEvaluationsReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *ExportModelEvaluationsReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ExportModelEvaluationsReq) GetMetrics() EvaluationMetricsKind {
	if x != nil {
		return x.Metrics
	}
	return EvaluationMetricsKind_EVALUATION_METRICS_KIND_UNSPECIFIC
}

type ExportModelEvaluationsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId string   `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	Ids     []string `protobuf:"bytes,2,rep,name=ids,proto3" json:"ids,omitempty"`
	Csv     *Csv     `protobuf:"bytes,3,opt,name=csv,proto3" json:"csv,omitempty"` // CSV文件内容
}

func (x *ExportModelEvaluationsRsp) Reset() {
	*x = ExportModelEvaluationsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportModelEvaluationsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportModelEvaluationsRsp) ProtoMessage() {}

func (x *ExportModelEvaluationsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportModelEvaluationsRsp.ProtoReflect.Descriptor instead.
func (*ExportModelEvaluationsRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{4}
}

func (x *ExportModelEvaluationsRsp) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ExportModelEvaluationsRsp) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ExportModelEvaluationsRsp) GetCsv() *Csv {
	if x != nil {
		return x.Csv
	}
	return nil
}

type CheckDataProcessStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx     *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Id      string       `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	PageReq *PageReq     `protobuf:"bytes,3,opt,name=page_req,json=pageReq,proto3" json:"page_req,omitempty"`
}

func (x *CheckDataProcessStatusReq) Reset() {
	*x = CheckDataProcessStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckDataProcessStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckDataProcessStatusReq) ProtoMessage() {}

func (x *CheckDataProcessStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckDataProcessStatusReq.ProtoReflect.Descriptor instead.
func (*CheckDataProcessStatusReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{5}
}

func (x *CheckDataProcessStatusReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *CheckDataProcessStatusReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CheckDataProcessStatusReq) GetPageReq() *PageReq {
	if x != nil {
		return x.PageReq
	}
	return nil
}

// CheckDataProcessStatusRsp 返回的是一张类似于csv的表格
type CheckDataProcessStatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total                int32    `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	PageNum              int32    `protobuf:"varint,2,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	PageSize             int32    `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Csv                  *Csv     `protobuf:"bytes,4,opt,name=csv,proto3" json:"csv,omitempty"`
	OriginalInputHeaders []string `protobuf:"bytes,5,rep,name=original_input_headers,json=originalInputHeaders,proto3" json:"original_input_headers,omitempty"`
}

func (x *CheckDataProcessStatusRsp) Reset() {
	*x = CheckDataProcessStatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckDataProcessStatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckDataProcessStatusRsp) ProtoMessage() {}

func (x *CheckDataProcessStatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckDataProcessStatusRsp.ProtoReflect.Descriptor instead.
func (*CheckDataProcessStatusRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{6}
}

func (x *CheckDataProcessStatusRsp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *CheckDataProcessStatusRsp) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *CheckDataProcessStatusRsp) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *CheckDataProcessStatusRsp) GetCsv() *Csv {
	if x != nil {
		return x.Csv
	}
	return nil
}

func (x *CheckDataProcessStatusRsp) GetOriginalInputHeaders() []string {
	if x != nil {
		return x.OriginalInputHeaders
	}
	return nil
}

type ReadModelEvaluationRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total       int32         `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	PageNum     int32         `protobuf:"varint,2,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	PageSize    int32         `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Evaluations []*Evaluation `protobuf:"bytes,4,rep,name=evaluations,proto3" json:"evaluations,omitempty"`
}

func (x *ReadModelEvaluationRsp) Reset() {
	*x = ReadModelEvaluationRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadModelEvaluationRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadModelEvaluationRsp) ProtoMessage() {}

func (x *ReadModelEvaluationRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadModelEvaluationRsp.ProtoReflect.Descriptor instead.
func (*ReadModelEvaluationRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{7}
}

func (x *ReadModelEvaluationRsp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ReadModelEvaluationRsp) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *ReadModelEvaluationRsp) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ReadModelEvaluationRsp) GetEvaluations() []*Evaluation {
	if x != nil {
		return x.Evaluations
	}
	return nil
}

type UpdateModelEvaluationReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx        *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Evaluation *Evaluation  `protobuf:"bytes,2,opt,name=evaluation,proto3" json:"evaluation,omitempty"`
}

func (x *UpdateModelEvaluationReq) Reset() {
	*x = UpdateModelEvaluationReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateModelEvaluationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateModelEvaluationReq) ProtoMessage() {}

func (x *UpdateModelEvaluationReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateModelEvaluationReq.ProtoReflect.Descriptor instead.
func (*UpdateModelEvaluationReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateModelEvaluationReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *UpdateModelEvaluationReq) GetEvaluation() *Evaluation {
	if x != nil {
		return x.Evaluation
	}
	return nil
}

type UpdateModelEvaluationRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Evaluation *Evaluation `protobuf:"bytes,1,opt,name=evaluation,proto3" json:"evaluation,omitempty"`
}

func (x *UpdateModelEvaluationRsp) Reset() {
	*x = UpdateModelEvaluationRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateModelEvaluationRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateModelEvaluationRsp) ProtoMessage() {}

func (x *UpdateModelEvaluationRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateModelEvaluationRsp.ProtoReflect.Descriptor instead.
func (*UpdateModelEvaluationRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateModelEvaluationRsp) GetEvaluation() *Evaluation {
	if x != nil {
		return x.Evaluation
	}
	return nil
}

type DeleteModelEvaluationReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Ids []string     `protobuf:"bytes,2,rep,name=ids,proto3" json:"ids,omitempty"`
}

func (x *DeleteModelEvaluationReq) Reset() {
	*x = DeleteModelEvaluationReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteModelEvaluationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteModelEvaluationReq) ProtoMessage() {}

func (x *DeleteModelEvaluationReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteModelEvaluationReq.ProtoReflect.Descriptor instead.
func (*DeleteModelEvaluationReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{10}
}

func (x *DeleteModelEvaluationReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *DeleteModelEvaluationReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type DeleteModelEvaluationRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteModelEvaluationRsp) Reset() {
	*x = DeleteModelEvaluationRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteModelEvaluationRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteModelEvaluationRsp) ProtoMessage() {}

func (x *DeleteModelEvaluationRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteModelEvaluationRsp.ProtoReflect.Descriptor instead.
func (*DeleteModelEvaluationRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{11}
}

type StartModelEvaluationsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id  string       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Ctx *UserContext `protobuf:"bytes,2,opt,name=ctx,proto3" json:"ctx,omitempty"`
}

func (x *StartModelEvaluationsReq) Reset() {
	*x = StartModelEvaluationsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartModelEvaluationsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartModelEvaluationsReq) ProtoMessage() {}

func (x *StartModelEvaluationsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartModelEvaluationsReq.ProtoReflect.Descriptor instead.
func (*StartModelEvaluationsReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{12}
}

func (x *StartModelEvaluationsReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *StartModelEvaluationsReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

type StartModelEvaluationsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StartModelEvaluationsRsp) Reset() {
	*x = StartModelEvaluationsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartModelEvaluationsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartModelEvaluationsRsp) ProtoMessage() {}

func (x *StartModelEvaluationsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartModelEvaluationsRsp.ProtoReflect.Descriptor instead.
func (*StartModelEvaluationsRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{13}
}

type StopModelEvaluationsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id  string       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Ctx *UserContext `protobuf:"bytes,2,opt,name=ctx,proto3" json:"ctx,omitempty"`
}

func (x *StopModelEvaluationsReq) Reset() {
	*x = StopModelEvaluationsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopModelEvaluationsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopModelEvaluationsReq) ProtoMessage() {}

func (x *StopModelEvaluationsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopModelEvaluationsReq.ProtoReflect.Descriptor instead.
func (*StopModelEvaluationsReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{14}
}

func (x *StopModelEvaluationsReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *StopModelEvaluationsReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

type StopModelEvaluationsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StopModelEvaluationsRsp) Reset() {
	*x = StopModelEvaluationsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopModelEvaluationsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopModelEvaluationsRsp) ProtoMessage() {}

func (x *StopModelEvaluationsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopModelEvaluationsRsp.ProtoReflect.Descriptor instead.
func (*StopModelEvaluationsRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{15}
}

type CheckEvaluationScriptExistenceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx     *UserContext `protobuf:"bytes,3,opt,name=ctx,proto3" json:"ctx,omitempty"`
	ModelId string       `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	Name    string       `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *CheckEvaluationScriptExistenceReq) Reset() {
	*x = CheckEvaluationScriptExistenceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckEvaluationScriptExistenceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckEvaluationScriptExistenceReq) ProtoMessage() {}

func (x *CheckEvaluationScriptExistenceReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckEvaluationScriptExistenceReq.ProtoReflect.Descriptor instead.
func (*CheckEvaluationScriptExistenceReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{16}
}

func (x *CheckEvaluationScriptExistenceReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *CheckEvaluationScriptExistenceReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *CheckEvaluationScriptExistenceReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type CheckEvaluationScriptExistenceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Exist bool   `protobuf:"varint,1,opt,name=exist,proto3" json:"exist,omitempty"`
	Msg   string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *CheckEvaluationScriptExistenceRsp) Reset() {
	*x = CheckEvaluationScriptExistenceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckEvaluationScriptExistenceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckEvaluationScriptExistenceRsp) ProtoMessage() {}

func (x *CheckEvaluationScriptExistenceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckEvaluationScriptExistenceRsp.ProtoReflect.Descriptor instead.
func (*CheckEvaluationScriptExistenceRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{17}
}

func (x *CheckEvaluationScriptExistenceRsp) GetExist() bool {
	if x != nil {
		return x.Exist
	}
	return false
}

func (x *CheckEvaluationScriptExistenceRsp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type CreateEvaluationScriptReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx    *UserContext      `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Script *EvaluationScript `protobuf:"bytes,2,opt,name=script,proto3" json:"script,omitempty"`
	// 通过上传文件的方式创建
	FilePath string `protobuf:"bytes,3,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
}

func (x *CreateEvaluationScriptReq) Reset() {
	*x = CreateEvaluationScriptReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEvaluationScriptReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEvaluationScriptReq) ProtoMessage() {}

func (x *CreateEvaluationScriptReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEvaluationScriptReq.ProtoReflect.Descriptor instead.
func (*CreateEvaluationScriptReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{18}
}

func (x *CreateEvaluationScriptReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *CreateEvaluationScriptReq) GetScript() *EvaluationScript {
	if x != nil {
		return x.Script
	}
	return nil
}

func (x *CreateEvaluationScriptReq) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

type CreateEvaluationScriptRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Script *EvaluationScript `protobuf:"bytes,1,opt,name=script,proto3" json:"script,omitempty"`
}

func (x *CreateEvaluationScriptRsp) Reset() {
	*x = CreateEvaluationScriptRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEvaluationScriptRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEvaluationScriptRsp) ProtoMessage() {}

func (x *CreateEvaluationScriptRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEvaluationScriptRsp.ProtoReflect.Descriptor instead.
func (*CreateEvaluationScriptRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{19}
}

func (x *CreateEvaluationScriptRsp) GetScript() *EvaluationScript {
	if x != nil {
		return x.Script
	}
	return nil
}

type ReadEvaluationScriptReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        string       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ModelId   string       `protobuf:"bytes,2,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ReleaseId string       `protobuf:"bytes,3,opt,name=release_id,json=releaseId,proto3" json:"release_id,omitempty"`
	PageReq   *PageReq     `protobuf:"bytes,4,opt,name=page_req,json=pageReq,proto3" json:"page_req,omitempty"`
	Ctx       *UserContext `protobuf:"bytes,5,opt,name=ctx,proto3" json:"ctx,omitempty"`
}

func (x *ReadEvaluationScriptReq) Reset() {
	*x = ReadEvaluationScriptReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadEvaluationScriptReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadEvaluationScriptReq) ProtoMessage() {}

func (x *ReadEvaluationScriptReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadEvaluationScriptReq.ProtoReflect.Descriptor instead.
func (*ReadEvaluationScriptReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{20}
}

func (x *ReadEvaluationScriptReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ReadEvaluationScriptReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ReadEvaluationScriptReq) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

func (x *ReadEvaluationScriptReq) GetPageReq() *PageReq {
	if x != nil {
		return x.PageReq
	}
	return nil
}

func (x *ReadEvaluationScriptReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

type ReadEvaluationScriptRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total    int32               `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	PageNum  int32               `protobuf:"varint,2,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	PageSize int32               `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Scripts  []*EvaluationScript `protobuf:"bytes,4,rep,name=scripts,proto3" json:"scripts,omitempty"`
}

func (x *ReadEvaluationScriptRsp) Reset() {
	*x = ReadEvaluationScriptRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadEvaluationScriptRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadEvaluationScriptRsp) ProtoMessage() {}

func (x *ReadEvaluationScriptRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadEvaluationScriptRsp.ProtoReflect.Descriptor instead.
func (*ReadEvaluationScriptRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{21}
}

func (x *ReadEvaluationScriptRsp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ReadEvaluationScriptRsp) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *ReadEvaluationScriptRsp) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ReadEvaluationScriptRsp) GetScripts() []*EvaluationScript {
	if x != nil {
		return x.Scripts
	}
	return nil
}

type UpdateEvaluationScriptReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx    *UserContext      `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Script *EvaluationScript `protobuf:"bytes,2,opt,name=script,proto3" json:"script,omitempty"`
}

func (x *UpdateEvaluationScriptReq) Reset() {
	*x = UpdateEvaluationScriptReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEvaluationScriptReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEvaluationScriptReq) ProtoMessage() {}

func (x *UpdateEvaluationScriptReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEvaluationScriptReq.ProtoReflect.Descriptor instead.
func (*UpdateEvaluationScriptReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{22}
}

func (x *UpdateEvaluationScriptReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *UpdateEvaluationScriptReq) GetScript() *EvaluationScript {
	if x != nil {
		return x.Script
	}
	return nil
}

type UpdateEvaluationScriptRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx    *UserContext      `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Script *EvaluationScript `protobuf:"bytes,2,opt,name=script,proto3" json:"script,omitempty"`
}

func (x *UpdateEvaluationScriptRsp) Reset() {
	*x = UpdateEvaluationScriptRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEvaluationScriptRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEvaluationScriptRsp) ProtoMessage() {}

func (x *UpdateEvaluationScriptRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEvaluationScriptRsp.ProtoReflect.Descriptor instead.
func (*UpdateEvaluationScriptRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{23}
}

func (x *UpdateEvaluationScriptRsp) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *UpdateEvaluationScriptRsp) GetScript() *EvaluationScript {
	if x != nil {
		return x.Script
	}
	return nil
}

type DeleteEvaluationScriptReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Ids []string     `protobuf:"bytes,2,rep,name=ids,proto3" json:"ids,omitempty"`
}

func (x *DeleteEvaluationScriptReq) Reset() {
	*x = DeleteEvaluationScriptReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteEvaluationScriptReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteEvaluationScriptReq) ProtoMessage() {}

func (x *DeleteEvaluationScriptReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteEvaluationScriptReq.ProtoReflect.Descriptor instead.
func (*DeleteEvaluationScriptReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{24}
}

func (x *DeleteEvaluationScriptReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *DeleteEvaluationScriptReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type DeleteEvaluationScriptRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteEvaluationScriptRsp) Reset() {
	*x = DeleteEvaluationScriptRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteEvaluationScriptRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteEvaluationScriptRsp) ProtoMessage() {}

func (x *DeleteEvaluationScriptRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteEvaluationScriptRsp.ProtoReflect.Descriptor instead.
func (*DeleteEvaluationScriptRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{25}
}

type UploadEvaluationScriptReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx      *UserContext    `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Id       string          `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Stage    EvaluationStage `protobuf:"varint,3,opt,name=stage,proto3,enum=proto.EvaluationStage" json:"stage,omitempty"`
	FilePath string          `protobuf:"bytes,4,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
}

func (x *UploadEvaluationScriptReq) Reset() {
	*x = UploadEvaluationScriptReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadEvaluationScriptReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadEvaluationScriptReq) ProtoMessage() {}

func (x *UploadEvaluationScriptReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadEvaluationScriptReq.ProtoReflect.Descriptor instead.
func (*UploadEvaluationScriptReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{26}
}

func (x *UploadEvaluationScriptReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *UploadEvaluationScriptReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UploadEvaluationScriptReq) GetStage() EvaluationStage {
	if x != nil {
		return x.Stage
	}
	return EvaluationStage_EVALUATION_STAGE_UNSPECIFIED
}

func (x *UploadEvaluationScriptReq) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

type UploadEvaluationScriptRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileName string `protobuf:"bytes,1,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	Size     int64  `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	Digest   string `protobuf:"bytes,3,opt,name=digest,proto3" json:"digest,omitempty"`
}

func (x *UploadEvaluationScriptRsp) Reset() {
	*x = UploadEvaluationScriptRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadEvaluationScriptRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadEvaluationScriptRsp) ProtoMessage() {}

func (x *UploadEvaluationScriptRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadEvaluationScriptRsp.ProtoReflect.Descriptor instead.
func (*UploadEvaluationScriptRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{27}
}

func (x *UploadEvaluationScriptRsp) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *UploadEvaluationScriptRsp) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *UploadEvaluationScriptRsp) GetDigest() string {
	if x != nil {
		return x.Digest
	}
	return ""
}

type DownloadEvaluationScriptReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx   *UserContext    `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Id    string          `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Stage EvaluationStage `protobuf:"varint,4,opt,name=stage,proto3,enum=proto.EvaluationStage" json:"stage,omitempty"`
}

func (x *DownloadEvaluationScriptReq) Reset() {
	*x = DownloadEvaluationScriptReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadEvaluationScriptReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadEvaluationScriptReq) ProtoMessage() {}

func (x *DownloadEvaluationScriptReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadEvaluationScriptReq.ProtoReflect.Descriptor instead.
func (*DownloadEvaluationScriptReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{28}
}

func (x *DownloadEvaluationScriptReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *DownloadEvaluationScriptReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DownloadEvaluationScriptReq) GetStage() EvaluationStage {
	if x != nil {
		return x.Stage
	}
	return EvaluationStage_EVALUATION_STAGE_UNSPECIFIED
}

type DownloadEvaluationScriptRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string          `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Stage       EvaluationStage `protobuf:"varint,2,opt,name=stage,proto3,enum=proto.EvaluationStage" json:"stage,omitempty"`
	FileName    string          `protobuf:"bytes,3,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	FileContent []byte          `protobuf:"bytes,4,opt,name=file_content,json=fileContent,proto3" json:"file_content,omitempty"`
}

func (x *DownloadEvaluationScriptRsp) Reset() {
	*x = DownloadEvaluationScriptRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadEvaluationScriptRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadEvaluationScriptRsp) ProtoMessage() {}

func (x *DownloadEvaluationScriptRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadEvaluationScriptRsp.ProtoReflect.Descriptor instead.
func (*DownloadEvaluationScriptRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{29}
}

func (x *DownloadEvaluationScriptRsp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DownloadEvaluationScriptRsp) GetStage() EvaluationStage {
	if x != nil {
		return x.Stage
	}
	return EvaluationStage_EVALUATION_STAGE_UNSPECIFIED
}

func (x *DownloadEvaluationScriptRsp) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *DownloadEvaluationScriptRsp) GetFileContent() []byte {
	if x != nil {
		return x.FileContent
	}
	return nil
}

type CreateEvaluationDatasetReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx      *UserContext       `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Dataset  *EvaluationDataset `protobuf:"bytes,2,opt,name=dataset,proto3" json:"dataset,omitempty"`
	FilePath string             `protobuf:"bytes,3,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
	CopyFrom string             `protobuf:"bytes,4,opt,name=copy_from,json=copyFrom,proto3" json:"copy_from,omitempty"` // 从一个已有的数据集copy生成新的数据集。
}

func (x *CreateEvaluationDatasetReq) Reset() {
	*x = CreateEvaluationDatasetReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEvaluationDatasetReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEvaluationDatasetReq) ProtoMessage() {}

func (x *CreateEvaluationDatasetReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEvaluationDatasetReq.ProtoReflect.Descriptor instead.
func (*CreateEvaluationDatasetReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{30}
}

func (x *CreateEvaluationDatasetReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *CreateEvaluationDatasetReq) GetDataset() *EvaluationDataset {
	if x != nil {
		return x.Dataset
	}
	return nil
}

func (x *CreateEvaluationDatasetReq) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *CreateEvaluationDatasetReq) GetCopyFrom() string {
	if x != nil {
		return x.CopyFrom
	}
	return ""
}

type CreateEvaluationDatasetRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dataset *EvaluationDataset `protobuf:"bytes,1,opt,name=dataset,proto3" json:"dataset,omitempty"`
}

func (x *CreateEvaluationDatasetRsp) Reset() {
	*x = CreateEvaluationDatasetRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEvaluationDatasetRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEvaluationDatasetRsp) ProtoMessage() {}

func (x *CreateEvaluationDatasetRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEvaluationDatasetRsp.ProtoReflect.Descriptor instead.
func (*CreateEvaluationDatasetRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{31}
}

func (x *CreateEvaluationDatasetRsp) GetDataset() *EvaluationDataset {
	if x != nil {
		return x.Dataset
	}
	return nil
}

type CheckEvaluationDatasetExistenceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx     *UserContext `protobuf:"bytes,3,opt,name=ctx,proto3" json:"ctx,omitempty"`
	ModelId string       `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	Name    string       `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *CheckEvaluationDatasetExistenceReq) Reset() {
	*x = CheckEvaluationDatasetExistenceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckEvaluationDatasetExistenceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckEvaluationDatasetExistenceReq) ProtoMessage() {}

func (x *CheckEvaluationDatasetExistenceReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckEvaluationDatasetExistenceReq.ProtoReflect.Descriptor instead.
func (*CheckEvaluationDatasetExistenceReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{32}
}

func (x *CheckEvaluationDatasetExistenceReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *CheckEvaluationDatasetExistenceReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *CheckEvaluationDatasetExistenceReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type CheckEvaluationDatasetExistenceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Exist bool   `protobuf:"varint,1,opt,name=exist,proto3" json:"exist,omitempty"`
	Msg   string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *CheckEvaluationDatasetExistenceRsp) Reset() {
	*x = CheckEvaluationDatasetExistenceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckEvaluationDatasetExistenceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckEvaluationDatasetExistenceRsp) ProtoMessage() {}

func (x *CheckEvaluationDatasetExistenceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckEvaluationDatasetExistenceRsp.ProtoReflect.Descriptor instead.
func (*CheckEvaluationDatasetExistenceRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{33}
}

func (x *CheckEvaluationDatasetExistenceRsp) GetExist() bool {
	if x != nil {
		return x.Exist
	}
	return false
}

func (x *CheckEvaluationDatasetExistenceRsp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type GetCsvHeadersReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx      *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	FilePath string       `protobuf:"bytes,2,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
}

func (x *GetCsvHeadersReq) Reset() {
	*x = GetCsvHeadersReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCsvHeadersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCsvHeadersReq) ProtoMessage() {}

func (x *GetCsvHeadersReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCsvHeadersReq.ProtoReflect.Descriptor instead.
func (*GetCsvHeadersReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{34}
}

func (x *GetCsvHeadersReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *GetCsvHeadersReq) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

type GetCsvHeadersRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileName string     `protobuf:"bytes,1,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	Headers  *CsvHeader `protobuf:"bytes,2,opt,name=headers,proto3" json:"headers,omitempty"`
}

func (x *GetCsvHeadersRsp) Reset() {
	*x = GetCsvHeadersRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCsvHeadersRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCsvHeadersRsp) ProtoMessage() {}

func (x *GetCsvHeadersRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCsvHeadersRsp.ProtoReflect.Descriptor instead.
func (*GetCsvHeadersRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{35}
}

func (x *GetCsvHeadersRsp) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *GetCsvHeadersRsp) GetHeaders() *CsvHeader {
	if x != nil {
		return x.Headers
	}
	return nil
}

type ValidateEvaluationDatasetReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx          *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	ModelId      string       `protobuf:"bytes,2,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	RealValueCol string       `protobuf:"bytes,3,opt,name=real_value_col,json=realValueCol,proto3" json:"real_value_col,omitempty"`
	FilePath     string       `protobuf:"bytes,4,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
}

func (x *ValidateEvaluationDatasetReq) Reset() {
	*x = ValidateEvaluationDatasetReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateEvaluationDatasetReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateEvaluationDatasetReq) ProtoMessage() {}

func (x *ValidateEvaluationDatasetReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateEvaluationDatasetReq.ProtoReflect.Descriptor instead.
func (*ValidateEvaluationDatasetReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{36}
}

func (x *ValidateEvaluationDatasetReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *ValidateEvaluationDatasetReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ValidateEvaluationDatasetReq) GetRealValueCol() string {
	if x != nil {
		return x.RealValueCol
	}
	return ""
}

func (x *ValidateEvaluationDatasetReq) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

type ValidateEvaluationDatasetRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Checked  []string `protobuf:"bytes,1,rep,name=checked,proto3" json:"checked,omitempty"`
	Expected []string `protobuf:"bytes,2,rep,name=expected,proto3" json:"expected,omitempty"`
	IsValid  bool     `protobuf:"varint,3,opt,name=is_valid,json=isValid,proto3" json:"is_valid,omitempty"`
}

func (x *ValidateEvaluationDatasetRsp) Reset() {
	*x = ValidateEvaluationDatasetRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateEvaluationDatasetRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateEvaluationDatasetRsp) ProtoMessage() {}

func (x *ValidateEvaluationDatasetRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateEvaluationDatasetRsp.ProtoReflect.Descriptor instead.
func (*ValidateEvaluationDatasetRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{37}
}

func (x *ValidateEvaluationDatasetRsp) GetChecked() []string {
	if x != nil {
		return x.Checked
	}
	return nil
}

func (x *ValidateEvaluationDatasetRsp) GetExpected() []string {
	if x != nil {
		return x.Expected
	}
	return nil
}

func (x *ValidateEvaluationDatasetRsp) GetIsValid() bool {
	if x != nil {
		return x.IsValid
	}
	return false
}

// PreviewEvaluationDatasetReq 仅用于预览CSV表格的请求
type PreviewEvaluationDatasetReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx        *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Id         string       `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	ModelId    string       `protobuf:"bytes,3,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	StartIndex int64        `protobuf:"varint,4,opt,name=start_index,json=startIndex,proto3" json:"start_index,omitempty"`
	Size       int64        `protobuf:"varint,5,opt,name=size,proto3" json:"size,omitempty"`
}

func (x *PreviewEvaluationDatasetReq) Reset() {
	*x = PreviewEvaluationDatasetReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewEvaluationDatasetReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewEvaluationDatasetReq) ProtoMessage() {}

func (x *PreviewEvaluationDatasetReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewEvaluationDatasetReq.ProtoReflect.Descriptor instead.
func (*PreviewEvaluationDatasetReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{38}
}

func (x *PreviewEvaluationDatasetReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *PreviewEvaluationDatasetReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PreviewEvaluationDatasetReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *PreviewEvaluationDatasetReq) GetStartIndex() int64 {
	if x != nil {
		return x.StartIndex
	}
	return 0
}

func (x *PreviewEvaluationDatasetReq) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

type PreviewEvaluationDatasetRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Csv *Csv `protobuf:"bytes,1,opt,name=csv,proto3" json:"csv,omitempty"`
}

func (x *PreviewEvaluationDatasetRsp) Reset() {
	*x = PreviewEvaluationDatasetRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewEvaluationDatasetRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewEvaluationDatasetRsp) ProtoMessage() {}

func (x *PreviewEvaluationDatasetRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewEvaluationDatasetRsp.ProtoReflect.Descriptor instead.
func (*PreviewEvaluationDatasetRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{39}
}

func (x *PreviewEvaluationDatasetRsp) GetCsv() *Csv {
	if x != nil {
		return x.Csv
	}
	return nil
}

type ReadEvaluationDatasetReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx       *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Id        string       `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	ModelId   string       `protobuf:"bytes,3,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ReleaseId string       `protobuf:"bytes,4,opt,name=release_id,json=releaseId,proto3" json:"release_id,omitempty"` // 获取组合模型指定版本的子模型的数据集
	PageReq   *PageReq     `protobuf:"bytes,5,opt,name=page_req,json=pageReq,proto3" json:"page_req,omitempty"`
	OnlySelf  bool         `protobuf:"varint,6,opt,name=only_self,json=onlySelf,proto3" json:"only_self,omitempty"` // 当为model_id所指模型为组合模型时，only_self为true时仅返回自身的数据集不返回子模型数据集
}

func (x *ReadEvaluationDatasetReq) Reset() {
	*x = ReadEvaluationDatasetReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadEvaluationDatasetReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadEvaluationDatasetReq) ProtoMessage() {}

func (x *ReadEvaluationDatasetReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadEvaluationDatasetReq.ProtoReflect.Descriptor instead.
func (*ReadEvaluationDatasetReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{40}
}

func (x *ReadEvaluationDatasetReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *ReadEvaluationDatasetReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ReadEvaluationDatasetReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ReadEvaluationDatasetReq) GetReleaseId() string {
	if x != nil {
		return x.ReleaseId
	}
	return ""
}

func (x *ReadEvaluationDatasetReq) GetPageReq() *PageReq {
	if x != nil {
		return x.PageReq
	}
	return nil
}

func (x *ReadEvaluationDatasetReq) GetOnlySelf() bool {
	if x != nil {
		return x.OnlySelf
	}
	return false
}

type ReadEvaluationDatasetRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total    int32                `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	PageNum  int32                `protobuf:"varint,2,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	PageSize int32                `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Datasets []*EvaluationDataset `protobuf:"bytes,4,rep,name=datasets,proto3" json:"datasets,omitempty"`
}

func (x *ReadEvaluationDatasetRsp) Reset() {
	*x = ReadEvaluationDatasetRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadEvaluationDatasetRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadEvaluationDatasetRsp) ProtoMessage() {}

func (x *ReadEvaluationDatasetRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadEvaluationDatasetRsp.ProtoReflect.Descriptor instead.
func (*ReadEvaluationDatasetRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{41}
}

func (x *ReadEvaluationDatasetRsp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ReadEvaluationDatasetRsp) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *ReadEvaluationDatasetRsp) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ReadEvaluationDatasetRsp) GetDatasets() []*EvaluationDataset {
	if x != nil {
		return x.Datasets
	}
	return nil
}

type UpdateEvaluationDatasetReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx     *UserContext       `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Dataset *EvaluationDataset `protobuf:"bytes,2,opt,name=dataset,proto3" json:"dataset,omitempty"`
}

func (x *UpdateEvaluationDatasetReq) Reset() {
	*x = UpdateEvaluationDatasetReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEvaluationDatasetReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEvaluationDatasetReq) ProtoMessage() {}

func (x *UpdateEvaluationDatasetReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEvaluationDatasetReq.ProtoReflect.Descriptor instead.
func (*UpdateEvaluationDatasetReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{42}
}

func (x *UpdateEvaluationDatasetReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *UpdateEvaluationDatasetReq) GetDataset() *EvaluationDataset {
	if x != nil {
		return x.Dataset
	}
	return nil
}

type UpdateEvaluationDatasetRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dataset *EvaluationDataset `protobuf:"bytes,1,opt,name=dataset,proto3" json:"dataset,omitempty"`
}

func (x *UpdateEvaluationDatasetRsp) Reset() {
	*x = UpdateEvaluationDatasetRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEvaluationDatasetRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEvaluationDatasetRsp) ProtoMessage() {}

func (x *UpdateEvaluationDatasetRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEvaluationDatasetRsp.ProtoReflect.Descriptor instead.
func (*UpdateEvaluationDatasetRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{43}
}

func (x *UpdateEvaluationDatasetRsp) GetDataset() *EvaluationDataset {
	if x != nil {
		return x.Dataset
	}
	return nil
}

type DeleteEvaluationDatasetReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Ids []string     `protobuf:"bytes,2,rep,name=ids,proto3" json:"ids,omitempty"`
}

func (x *DeleteEvaluationDatasetReq) Reset() {
	*x = DeleteEvaluationDatasetReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteEvaluationDatasetReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteEvaluationDatasetReq) ProtoMessage() {}

func (x *DeleteEvaluationDatasetReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteEvaluationDatasetReq.ProtoReflect.Descriptor instead.
func (*DeleteEvaluationDatasetReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{44}
}

func (x *DeleteEvaluationDatasetReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *DeleteEvaluationDatasetReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type DeleteEvaluationDatasetRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteEvaluationDatasetRsp) Reset() {
	*x = DeleteEvaluationDatasetRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteEvaluationDatasetRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteEvaluationDatasetRsp) ProtoMessage() {}

func (x *DeleteEvaluationDatasetRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteEvaluationDatasetRsp.ProtoReflect.Descriptor instead.
func (*DeleteEvaluationDatasetRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{45}
}

type UploadEvaluationDatasetReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx      *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Id       string       `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	FilePath string       `protobuf:"bytes,4,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
}

func (x *UploadEvaluationDatasetReq) Reset() {
	*x = UploadEvaluationDatasetReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadEvaluationDatasetReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadEvaluationDatasetReq) ProtoMessage() {}

func (x *UploadEvaluationDatasetReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadEvaluationDatasetReq.ProtoReflect.Descriptor instead.
func (*UploadEvaluationDatasetReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{46}
}

func (x *UploadEvaluationDatasetReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *UploadEvaluationDatasetReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UploadEvaluationDatasetReq) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

type UploadEvaluationDatasetRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileName string `protobuf:"bytes,1,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	Size     int64  `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	Digest   string `protobuf:"bytes,3,opt,name=digest,proto3" json:"digest,omitempty"`
}

func (x *UploadEvaluationDatasetRsp) Reset() {
	*x = UploadEvaluationDatasetRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadEvaluationDatasetRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadEvaluationDatasetRsp) ProtoMessage() {}

func (x *UploadEvaluationDatasetRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadEvaluationDatasetRsp.ProtoReflect.Descriptor instead.
func (*UploadEvaluationDatasetRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{47}
}

func (x *UploadEvaluationDatasetRsp) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *UploadEvaluationDatasetRsp) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *UploadEvaluationDatasetRsp) GetDigest() string {
	if x != nil {
		return x.Digest
	}
	return ""
}

type DownloadEvaluationDatasetReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Id  string       `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DownloadEvaluationDatasetReq) Reset() {
	*x = DownloadEvaluationDatasetReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadEvaluationDatasetReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadEvaluationDatasetReq) ProtoMessage() {}

func (x *DownloadEvaluationDatasetReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadEvaluationDatasetReq.ProtoReflect.Descriptor instead.
func (*DownloadEvaluationDatasetReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{48}
}

func (x *DownloadEvaluationDatasetReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *DownloadEvaluationDatasetReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DownloadEvaluationDatasetRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	FileName    string `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	FileContent []byte `protobuf:"bytes,4,opt,name=file_content,json=fileContent,proto3" json:"file_content,omitempty"`
}

func (x *DownloadEvaluationDatasetRsp) Reset() {
	*x = DownloadEvaluationDatasetRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadEvaluationDatasetRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadEvaluationDatasetRsp) ProtoMessage() {}

func (x *DownloadEvaluationDatasetRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadEvaluationDatasetRsp.ProtoReflect.Descriptor instead.
func (*DownloadEvaluationDatasetRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{49}
}

func (x *DownloadEvaluationDatasetRsp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DownloadEvaluationDatasetRsp) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *DownloadEvaluationDatasetRsp) GetFileContent() []byte {
	if x != nil {
		return x.FileContent
	}
	return nil
}

type CreateEvaluationScriptTemplateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx      *UserContext              `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Template *EvaluationScriptTemplate `protobuf:"bytes,2,opt,name=template,proto3" json:"template,omitempty"`
}

func (x *CreateEvaluationScriptTemplateReq) Reset() {
	*x = CreateEvaluationScriptTemplateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEvaluationScriptTemplateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEvaluationScriptTemplateReq) ProtoMessage() {}

func (x *CreateEvaluationScriptTemplateReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEvaluationScriptTemplateReq.ProtoReflect.Descriptor instead.
func (*CreateEvaluationScriptTemplateReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{50}
}

func (x *CreateEvaluationScriptTemplateReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *CreateEvaluationScriptTemplateReq) GetTemplate() *EvaluationScriptTemplate {
	if x != nil {
		return x.Template
	}
	return nil
}

type CreateEvaluationScriptTemplateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Template *EvaluationScriptTemplate `protobuf:"bytes,3,opt,name=template,proto3" json:"template,omitempty"`
}

func (x *CreateEvaluationScriptTemplateRsp) Reset() {
	*x = CreateEvaluationScriptTemplateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEvaluationScriptTemplateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEvaluationScriptTemplateRsp) ProtoMessage() {}

func (x *CreateEvaluationScriptTemplateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEvaluationScriptTemplateRsp.ProtoReflect.Descriptor instead.
func (*CreateEvaluationScriptTemplateRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{51}
}

func (x *CreateEvaluationScriptTemplateRsp) GetTemplate() *EvaluationScriptTemplate {
	if x != nil {
		return x.Template
	}
	return nil
}

type ReadEvaluationScriptTemplateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx     *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Name    string       `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	ModelId string       `protobuf:"bytes,3,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	PageReq *PageReq     `protobuf:"bytes,4,opt,name=page_req,json=pageReq,proto3" json:"page_req,omitempty"`
}

func (x *ReadEvaluationScriptTemplateReq) Reset() {
	*x = ReadEvaluationScriptTemplateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadEvaluationScriptTemplateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadEvaluationScriptTemplateReq) ProtoMessage() {}

func (x *ReadEvaluationScriptTemplateReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadEvaluationScriptTemplateReq.ProtoReflect.Descriptor instead.
func (*ReadEvaluationScriptTemplateReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{52}
}

func (x *ReadEvaluationScriptTemplateReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *ReadEvaluationScriptTemplateReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ReadEvaluationScriptTemplateReq) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ReadEvaluationScriptTemplateReq) GetPageReq() *PageReq {
	if x != nil {
		return x.PageReq
	}
	return nil
}

type ReadEvaluationScriptTemplateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total     int32                       `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	PageNum   int32                       `protobuf:"varint,2,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	PageSize  int32                       `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Templates []*EvaluationScriptTemplate `protobuf:"bytes,4,rep,name=templates,proto3" json:"templates,omitempty"`
}

func (x *ReadEvaluationScriptTemplateRsp) Reset() {
	*x = ReadEvaluationScriptTemplateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadEvaluationScriptTemplateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadEvaluationScriptTemplateRsp) ProtoMessage() {}

func (x *ReadEvaluationScriptTemplateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadEvaluationScriptTemplateRsp.ProtoReflect.Descriptor instead.
func (*ReadEvaluationScriptTemplateRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{53}
}

func (x *ReadEvaluationScriptTemplateRsp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ReadEvaluationScriptTemplateRsp) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *ReadEvaluationScriptTemplateRsp) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ReadEvaluationScriptTemplateRsp) GetTemplates() []*EvaluationScriptTemplate {
	if x != nil {
		return x.Templates
	}
	return nil
}

type UpdateEvaluationScriptTemplateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx      *UserContext              `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Template *EvaluationScriptTemplate `protobuf:"bytes,2,opt,name=template,proto3" json:"template,omitempty"`
}

func (x *UpdateEvaluationScriptTemplateReq) Reset() {
	*x = UpdateEvaluationScriptTemplateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEvaluationScriptTemplateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEvaluationScriptTemplateReq) ProtoMessage() {}

func (x *UpdateEvaluationScriptTemplateReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEvaluationScriptTemplateReq.ProtoReflect.Descriptor instead.
func (*UpdateEvaluationScriptTemplateReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{54}
}

func (x *UpdateEvaluationScriptTemplateReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *UpdateEvaluationScriptTemplateReq) GetTemplate() *EvaluationScriptTemplate {
	if x != nil {
		return x.Template
	}
	return nil
}

type UpdateEvaluationScriptTemplateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Template *EvaluationScriptTemplate `protobuf:"bytes,1,opt,name=template,proto3" json:"template,omitempty"`
}

func (x *UpdateEvaluationScriptTemplateRsp) Reset() {
	*x = UpdateEvaluationScriptTemplateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEvaluationScriptTemplateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEvaluationScriptTemplateRsp) ProtoMessage() {}

func (x *UpdateEvaluationScriptTemplateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEvaluationScriptTemplateRsp.ProtoReflect.Descriptor instead.
func (*UpdateEvaluationScriptTemplateRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{55}
}

func (x *UpdateEvaluationScriptTemplateRsp) GetTemplate() *EvaluationScriptTemplate {
	if x != nil {
		return x.Template
	}
	return nil
}

type DeleteEvaluationScriptTemplateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctx *UserContext `protobuf:"bytes,1,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Ids []string     `protobuf:"bytes,2,rep,name=ids,proto3" json:"ids,omitempty"`
}

func (x *DeleteEvaluationScriptTemplateReq) Reset() {
	*x = DeleteEvaluationScriptTemplateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteEvaluationScriptTemplateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteEvaluationScriptTemplateReq) ProtoMessage() {}

func (x *DeleteEvaluationScriptTemplateReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteEvaluationScriptTemplateReq.ProtoReflect.Descriptor instead.
func (*DeleteEvaluationScriptTemplateReq) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{56}
}

func (x *DeleteEvaluationScriptTemplateReq) GetCtx() *UserContext {
	if x != nil {
		return x.Ctx
	}
	return nil
}

func (x *DeleteEvaluationScriptTemplateReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type DeleteEvaluationScriptTemplateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteEvaluationScriptTemplateRsp) Reset() {
	*x = DeleteEvaluationScriptTemplateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rpc_model_evaluation_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteEvaluationScriptTemplateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteEvaluationScriptTemplateRsp) ProtoMessage() {}

func (x *DeleteEvaluationScriptTemplateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rpc_model_evaluation_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteEvaluationScriptTemplateRsp.ProtoReflect.Descriptor instead.
func (*DeleteEvaluationScriptTemplateRsp) Descriptor() ([]byte, []int) {
	return file_proto_rpc_model_evaluation_proto_rawDescGZIP(), []int{57}
}

var File_proto_rpc_model_evaluation_proto protoreflect.FileDescriptor

var file_proto_rpc_model_evaluation_proto_rawDesc = []byte{
	0x0a, 0x20, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x70, 0x63, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x16, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x73, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x31, 0x0a, 0x0a, 0x65, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0a, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x4d, 0x0a,
	0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x31, 0x0a, 0x0a, 0x65, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0a, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xf7, 0x01, 0x0a,
	0x16, 0x52, 0x65, 0x61, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x49, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x77, 0x69, 0x74, 0x68, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x77, 0x69, 0x74, 0x68, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x72, 0x65,
	0x71, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x22, 0x94, 0x01, 0x0a, 0x19, 0x45, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x4b, 0x69, 0x6e, 0x64, 0x52, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x22, 0x66, 0x0a,
	0x19, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x73, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x1c, 0x0a, 0x03, 0x63, 0x73, 0x76, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x73, 0x76,
	0x52, 0x03, 0x63, 0x73, 0x76, 0x22, 0x7c, 0x0a, 0x19, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x61,
	0x74, 0x61, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x29, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x72, 0x65, 0x71, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x22, 0xbd, 0x01, 0x0a, 0x19, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x61, 0x74,
	0x61, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73,
	0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e,
	0x75, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x1c, 0x0a, 0x03, 0x63, 0x73, 0x76, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x73, 0x76, 0x52, 0x03, 0x63, 0x73, 0x76, 0x12, 0x34, 0x0a,
	0x16, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x14, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x22, 0x9b, 0x01, 0x0a, 0x16, 0x52, 0x65, 0x61, 0x64, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12,
	0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x33, 0x0a, 0x0b,
	0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x22, 0x73, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a,
	0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03,
	0x63, 0x74, 0x78, 0x12, 0x31, 0x0a, 0x0a, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x65, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x4d, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x73, 0x70, 0x12, 0x31, 0x0a, 0x0a, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x65, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x52, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x1a, 0x0a, 0x18, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x73, 0x70, 0x22, 0x50, 0x0a, 0x18, 0x53, 0x74, 0x61, 0x72, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x22, 0x1a, 0x0a, 0x18, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x73, 0x70, 0x22, 0x4f, 0x0a, 0x17, 0x53, 0x74, 0x6f, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x24,
	0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52,
	0x03, 0x63, 0x74, 0x78, 0x22, 0x19, 0x0a, 0x17, 0x53, 0x74, 0x6f, 0x70, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x73, 0x70, 0x22,
	0x78, 0x0a, 0x21, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x4b, 0x0a, 0x21, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0x14,
	0x0a, 0x05, 0x65, 0x78, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x65,
	0x78, 0x69, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x8f, 0x01, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x52, 0x06, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x22, 0x4c, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x52, 0x73, 0x70, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x06,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x22, 0xb4, 0x01, 0x0a, 0x17, 0x52, 0x65, 0x61, 0x64, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x52, 0x07,
	0x70, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x22, 0x9a, 0x01,
	0x0a, 0x17, 0x52, 0x65, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12,
	0x19, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70,
	0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x31, 0x0a, 0x07, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x52, 0x07, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x73, 0x22, 0x72, 0x0a, 0x19, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x2f, 0x0a,
	0x06, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x06, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x22, 0x72,
	0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x73, 0x70, 0x12, 0x24, 0x0a, 0x03, 0x63,
	0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74,
	0x78, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x06, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x22, 0x53, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x1b, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x52, 0x73, 0x70, 0x22, 0x9c, 0x01, 0x0a, 0x19, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x67,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52,
	0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50,
	0x61, 0x74, 0x68, 0x22, 0x64, 0x0a, 0x19, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x73, 0x70,
	0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x69, 0x67, 0x65, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x64, 0x69, 0x67, 0x65, 0x73, 0x74, 0x22, 0x81, 0x01, 0x0a, 0x1b, 0x44, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x2c, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x22, 0x9b, 0x01,
	0x0a, 0x1b, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x73, 0x70, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2c, 0x0a,
	0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x67, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b,
	0x66, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0xb0, 0x01, 0x0a, 0x1a,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74,
	0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78,
	0x12, 0x32, 0x0a, 0x07, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x07, 0x64, 0x61, 0x74,
	0x61, 0x73, 0x65, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74,
	0x68, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x70, 0x79, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x70, 0x79, 0x46, 0x72, 0x6f, 0x6d, 0x22, 0x50,
	0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x73, 0x70, 0x12, 0x32, 0x0a, 0x07,
	0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x07, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74,
	0x22, 0x79, 0x0a, 0x22, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74, 0x65,
	0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x19, 0x0a, 0x08,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x4c, 0x0a, 0x22, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x61, 0x73, 0x65, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x73,
	0x70, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x78, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x05, 0x65, 0x78, 0x69, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x55, 0x0a, 0x10, 0x47, 0x65, 0x74,
	0x43, 0x73, 0x76, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a,
	0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03,
	0x63, 0x74, 0x78, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68,
	0x22, 0x5b, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x43, 0x73, 0x76, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x73, 0x52, 0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x2a, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x73, 0x76, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x22, 0xa2, 0x01,
	0x0a, 0x1c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x12, 0x24,
	0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52,
	0x03, 0x63, 0x74, 0x78, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12,
	0x24, 0x0a, 0x0e, 0x72, 0x65, 0x61, 0x6c, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x63, 0x6f,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x61, 0x6c, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x43, 0x6f, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61,
	0x74, 0x68, 0x22, 0x6f, 0x0a, 0x1c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52,
	0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x64, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08,
	0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x56, 0x61,
	0x6c, 0x69, 0x64, 0x22, 0xa3, 0x01, 0x0a, 0x1b, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x69, 0x6e,
	0x64, 0x65, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x3b, 0x0a, 0x1b, 0x50, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x73, 0x70, 0x12, 0x1c, 0x0a, 0x03, 0x63, 0x73, 0x76, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x73,
	0x76, 0x52, 0x03, 0x63, 0x73, 0x76, 0x22, 0xd2, 0x01, 0x0a, 0x18, 0x52, 0x65, 0x61, 0x64, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x71, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1b,
	0x0a, 0x09, 0x6f, 0x6e, 0x6c, 0x79, 0x5f, 0x73, 0x65, 0x6c, 0x66, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x6f, 0x6e, 0x6c, 0x79, 0x53, 0x65, 0x6c, 0x66, 0x22, 0x9e, 0x01, 0x0a, 0x18,
	0x52, 0x65, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x19,
	0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x34, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65,
	0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73,
	0x65, 0x74, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x73, 0x22, 0x76, 0x0a, 0x1a,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74,
	0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78,
	0x12, 0x32, 0x0a, 0x07, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x07, 0x64, 0x61, 0x74,
	0x61, 0x73, 0x65, 0x74, 0x22, 0x50, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52,
	0x73, 0x70, 0x12, 0x32, 0x0a, 0x07, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x07, 0x64,
	0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x22, 0x54, 0x0a, 0x1a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x1c, 0x0a, 0x1a,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x73, 0x70, 0x22, 0x6f, 0x0a, 0x1a, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x22, 0x65, 0x0a, 0x1a, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69,
	0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x69,
	0x67, 0x65, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x69, 0x67, 0x65,
	0x73, 0x74, 0x22, 0x54, 0x0a, 0x1c, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x6e, 0x0a, 0x1c, 0x44, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x66, 0x69, 0x6c,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x86, 0x01, 0x0a, 0x21, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x24,
	0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52,
	0x03, 0x63, 0x74, 0x78, 0x12, 0x3b, 0x0a, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x22, 0x60, 0x0a, 0x21, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x3b, 0x0a, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x22, 0xa1, 0x01, 0x0a, 0x1f, 0x52, 0x65, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x52, 0x07,
	0x70, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x22, 0xae, 0x01, 0x0a, 0x1f, 0x52, 0x65, 0x61, 0x64,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x3d, 0x0a, 0x09, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x09, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x22, 0x86, 0x01, 0x0a, 0x21, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x24,
	0x0a, 0x03, 0x63, 0x74, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52,
	0x03, 0x63, 0x74, 0x78, 0x12, 0x3b, 0x0a, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x22, 0x60, 0x0a, 0x21, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x3b, 0x0a, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x22, 0x5b, 0x0a, 0x21, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x03, 0x63, 0x74, 0x78, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x03, 0x63, 0x74, 0x78, 0x12, 0x10,
	0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73,
	0x22, 0x23, 0x0a, 0x21, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x52, 0x73, 0x70, 0x2a, 0x8b, 0x01, 0x0a, 0x15, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x4b, 0x69, 0x6e, 0x64, 0x12,
	0x26, 0x0a, 0x22, 0x45, 0x56, 0x41, 0x4c, 0x55, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x45,
	0x54, 0x52, 0x49, 0x43, 0x53, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x43, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x45, 0x56, 0x41, 0x4c, 0x55,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x53, 0x5f, 0x4b, 0x49,
	0x4e, 0x44, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x10, 0x01, 0x12, 0x27, 0x0a, 0x23, 0x45, 0x56,
	0x41, 0x4c, 0x55, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x53,
	0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x50, 0x45, 0x52, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x4e, 0x43,
	0x45, 0x10, 0x02, 0x32, 0xed, 0x05, 0x0a, 0x16, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x59,
	0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x54, 0x0a, 0x14, 0x52, 0x65, 0x61,
	0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x1a, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12,
	0x5c, 0x0a, 0x16, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x20, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x73, 0x70, 0x12, 0x5c, 0x0a,
	0x16, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x12, 0x59, 0x0a, 0x15, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x5a, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x1a, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x73, 0x70, 0x12, 0x58, 0x0a, 0x14, 0x53, 0x74, 0x61, 0x72, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x73, 0x70, 0x12, 0x55, 0x0a, 0x13,
	0x53, 0x74, 0x6f, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x6f, 0x70,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x6f, 0x70,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x73, 0x70, 0x32, 0xc3, 0x05, 0x0a, 0x17, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12,
	0x5c, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x12, 0x20, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x73, 0x70, 0x12, 0x56, 0x0a,
	0x14, 0x52, 0x65, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65,
	0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65,
	0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x52, 0x73, 0x70, 0x12, 0x5c, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x12,
	0x20, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x20, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x52, 0x73, 0x70, 0x12, 0x5c, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x12, 0x20, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x20, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x73,
	0x70, 0x12, 0x5c, 0x0a, 0x16, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x12, 0x20, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x73, 0x70, 0x12,
	0x62, 0x0a, 0x18, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x12, 0x22, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x22, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x52, 0x73, 0x70, 0x12, 0x74, 0x0a, 0x1e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x45, 0x78, 0x69, 0x73,
	0x74, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x28, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x28, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x45, 0x78, 0x69,
	0x73, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x73, 0x70, 0x32, 0xe7, 0x07, 0x0a, 0x18, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x4d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x5f, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65,
	0x74, 0x12, 0x21, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65,
	0x74, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74,
	0x61, 0x73, 0x65, 0x74, 0x52, 0x73, 0x70, 0x12, 0x77, 0x0a, 0x1f, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65,
	0x74, 0x45, 0x78, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x29, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74, 0x65, 0x6e,
	0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x29, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74,
	0x61, 0x73, 0x65, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x73, 0x70,
	0x12, 0x41, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x43, 0x73, 0x76, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x73, 0x12, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x73, 0x76,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x73, 0x76, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73,
	0x52, 0x73, 0x70, 0x12, 0x65, 0x0a, 0x19, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74,
	0x12, 0x23, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73,
	0x65, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x56, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x73, 0x70, 0x12, 0x62, 0x0a, 0x18, 0x50, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x12, 0x22, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x73, 0x70, 0x12, 0x59,
	0x0a, 0x15, 0x52, 0x65, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x12, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x52, 0x65, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x52, 0x65, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x73, 0x70, 0x12, 0x5f, 0x0a, 0x17, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74,
	0x61, 0x73, 0x65, 0x74, 0x12, 0x21, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74,
	0x61, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x73, 0x70, 0x12, 0x5f, 0x0a, 0x17, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x61, 0x73, 0x65, 0x74, 0x12, 0x21, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x73, 0x70, 0x12, 0x5f, 0x0a, 0x17, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x12, 0x21, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x73, 0x70, 0x12, 0x65, 0x0a, 0x19,
	0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x12, 0x23, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x23,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74,
	0x52, 0x73, 0x70, 0x32, 0xf3, 0x03, 0x0a, 0x1f, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x74, 0x0a, 0x1e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x28, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x1a, 0x28, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x6e, 0x0a,
	0x1c, 0x52, 0x65, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x26, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65,
	0x61, 0x64, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x74, 0x0a,
	0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12,
	0x28, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x28, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x52, 0x73, 0x70, 0x12, 0x74, 0x0a, 0x1e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x28, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x28, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x42, 0x26, 0x5a, 0x24, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x77, 0x61, 0x72, 0x70, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x69, 0x70, 0x2f, 0x6c, 0x6c,
	0x6d, 0x6f, 0x70, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x3b, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_rpc_model_evaluation_proto_rawDescOnce sync.Once
	file_proto_rpc_model_evaluation_proto_rawDescData = file_proto_rpc_model_evaluation_proto_rawDesc
)

func file_proto_rpc_model_evaluation_proto_rawDescGZIP() []byte {
	file_proto_rpc_model_evaluation_proto_rawDescOnce.Do(func() {
		file_proto_rpc_model_evaluation_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_rpc_model_evaluation_proto_rawDescData)
	})
	return file_proto_rpc_model_evaluation_proto_rawDescData
}

var file_proto_rpc_model_evaluation_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_proto_rpc_model_evaluation_proto_msgTypes = make([]protoimpl.MessageInfo, 58)
var file_proto_rpc_model_evaluation_proto_goTypes = []interface{}{
	(EvaluationMetricsKind)(0),                 // 0: proto.EvaluationMetricsKind
	(*CreateModelEvaluationReq)(nil),           // 1: proto.CreateModelEvaluationReq
	(*CreateModelEvaluationRsp)(nil),           // 2: proto.CreateModelEvaluationRsp
	(*ReadModelEvaluationReq)(nil),             // 3: proto.ReadModelEvaluationReq
	(*ExportModelEvaluationsReq)(nil),          // 4: proto.ExportModelEvaluationsReq
	(*ExportModelEvaluationsRsp)(nil),          // 5: proto.ExportModelEvaluationsRsp
	(*CheckDataProcessStatusReq)(nil),          // 6: proto.CheckDataProcessStatusReq
	(*CheckDataProcessStatusRsp)(nil),          // 7: proto.CheckDataProcessStatusRsp
	(*ReadModelEvaluationRsp)(nil),             // 8: proto.ReadModelEvaluationRsp
	(*UpdateModelEvaluationReq)(nil),           // 9: proto.UpdateModelEvaluationReq
	(*UpdateModelEvaluationRsp)(nil),           // 10: proto.UpdateModelEvaluationRsp
	(*DeleteModelEvaluationReq)(nil),           // 11: proto.DeleteModelEvaluationReq
	(*DeleteModelEvaluationRsp)(nil),           // 12: proto.DeleteModelEvaluationRsp
	(*StartModelEvaluationsReq)(nil),           // 13: proto.StartModelEvaluationsReq
	(*StartModelEvaluationsRsp)(nil),           // 14: proto.StartModelEvaluationsRsp
	(*StopModelEvaluationsReq)(nil),            // 15: proto.StopModelEvaluationsReq
	(*StopModelEvaluationsRsp)(nil),            // 16: proto.StopModelEvaluationsRsp
	(*CheckEvaluationScriptExistenceReq)(nil),  // 17: proto.CheckEvaluationScriptExistenceReq
	(*CheckEvaluationScriptExistenceRsp)(nil),  // 18: proto.CheckEvaluationScriptExistenceRsp
	(*CreateEvaluationScriptReq)(nil),          // 19: proto.CreateEvaluationScriptReq
	(*CreateEvaluationScriptRsp)(nil),          // 20: proto.CreateEvaluationScriptRsp
	(*ReadEvaluationScriptReq)(nil),            // 21: proto.ReadEvaluationScriptReq
	(*ReadEvaluationScriptRsp)(nil),            // 22: proto.ReadEvaluationScriptRsp
	(*UpdateEvaluationScriptReq)(nil),          // 23: proto.UpdateEvaluationScriptReq
	(*UpdateEvaluationScriptRsp)(nil),          // 24: proto.UpdateEvaluationScriptRsp
	(*DeleteEvaluationScriptReq)(nil),          // 25: proto.DeleteEvaluationScriptReq
	(*DeleteEvaluationScriptRsp)(nil),          // 26: proto.DeleteEvaluationScriptRsp
	(*UploadEvaluationScriptReq)(nil),          // 27: proto.UploadEvaluationScriptReq
	(*UploadEvaluationScriptRsp)(nil),          // 28: proto.UploadEvaluationScriptRsp
	(*DownloadEvaluationScriptReq)(nil),        // 29: proto.DownloadEvaluationScriptReq
	(*DownloadEvaluationScriptRsp)(nil),        // 30: proto.DownloadEvaluationScriptRsp
	(*CreateEvaluationDatasetReq)(nil),         // 31: proto.CreateEvaluationDatasetReq
	(*CreateEvaluationDatasetRsp)(nil),         // 32: proto.CreateEvaluationDatasetRsp
	(*CheckEvaluationDatasetExistenceReq)(nil), // 33: proto.CheckEvaluationDatasetExistenceReq
	(*CheckEvaluationDatasetExistenceRsp)(nil), // 34: proto.CheckEvaluationDatasetExistenceRsp
	(*GetCsvHeadersReq)(nil),                   // 35: proto.GetCsvHeadersReq
	(*GetCsvHeadersRsp)(nil),                   // 36: proto.GetCsvHeadersRsp
	(*ValidateEvaluationDatasetReq)(nil),       // 37: proto.ValidateEvaluationDatasetReq
	(*ValidateEvaluationDatasetRsp)(nil),       // 38: proto.ValidateEvaluationDatasetRsp
	(*PreviewEvaluationDatasetReq)(nil),        // 39: proto.PreviewEvaluationDatasetReq
	(*PreviewEvaluationDatasetRsp)(nil),        // 40: proto.PreviewEvaluationDatasetRsp
	(*ReadEvaluationDatasetReq)(nil),           // 41: proto.ReadEvaluationDatasetReq
	(*ReadEvaluationDatasetRsp)(nil),           // 42: proto.ReadEvaluationDatasetRsp
	(*UpdateEvaluationDatasetReq)(nil),         // 43: proto.UpdateEvaluationDatasetReq
	(*UpdateEvaluationDatasetRsp)(nil),         // 44: proto.UpdateEvaluationDatasetRsp
	(*DeleteEvaluationDatasetReq)(nil),         // 45: proto.DeleteEvaluationDatasetReq
	(*DeleteEvaluationDatasetRsp)(nil),         // 46: proto.DeleteEvaluationDatasetRsp
	(*UploadEvaluationDatasetReq)(nil),         // 47: proto.UploadEvaluationDatasetReq
	(*UploadEvaluationDatasetRsp)(nil),         // 48: proto.UploadEvaluationDatasetRsp
	(*DownloadEvaluationDatasetReq)(nil),       // 49: proto.DownloadEvaluationDatasetReq
	(*DownloadEvaluationDatasetRsp)(nil),       // 50: proto.DownloadEvaluationDatasetRsp
	(*CreateEvaluationScriptTemplateReq)(nil),  // 51: proto.CreateEvaluationScriptTemplateReq
	(*CreateEvaluationScriptTemplateRsp)(nil),  // 52: proto.CreateEvaluationScriptTemplateRsp
	(*ReadEvaluationScriptTemplateReq)(nil),    // 53: proto.ReadEvaluationScriptTemplateReq
	(*ReadEvaluationScriptTemplateRsp)(nil),    // 54: proto.ReadEvaluationScriptTemplateRsp
	(*UpdateEvaluationScriptTemplateReq)(nil),  // 55: proto.UpdateEvaluationScriptTemplateReq
	(*UpdateEvaluationScriptTemplateRsp)(nil),  // 56: proto.UpdateEvaluationScriptTemplateRsp
	(*DeleteEvaluationScriptTemplateReq)(nil),  // 57: proto.DeleteEvaluationScriptTemplateReq
	(*DeleteEvaluationScriptTemplateRsp)(nil),  // 58: proto.DeleteEvaluationScriptTemplateRsp
	(*UserContext)(nil),                        // 59: proto.UserContext
	(*Evaluation)(nil),                         // 60: proto.Evaluation
	(*PageReq)(nil),                            // 61: proto.PageReq
	(*Csv)(nil),                                // 62: proto.Csv
	(*EvaluationScript)(nil),                   // 63: proto.EvaluationScript
	(EvaluationStage)(0),                       // 64: proto.EvaluationStage
	(*EvaluationDataset)(nil),                  // 65: proto.EvaluationDataset
	(*CsvHeader)(nil),                          // 66: proto.CsvHeader
	(*EvaluationScriptTemplate)(nil),           // 67: proto.EvaluationScriptTemplate
}
var file_proto_rpc_model_evaluation_proto_depIdxs = []int32{
	59, // 0: proto.CreateModelEvaluationReq.ctx:type_name -> proto.UserContext
	60, // 1: proto.CreateModelEvaluationReq.evaluation:type_name -> proto.Evaluation
	60, // 2: proto.CreateModelEvaluationRsp.evaluation:type_name -> proto.Evaluation
	61, // 3: proto.ReadModelEvaluationReq.page_req:type_name -> proto.PageReq
	59, // 4: proto.ReadModelEvaluationReq.ctx:type_name -> proto.UserContext
	59, // 5: proto.ExportModelEvaluationsReq.ctx:type_name -> proto.UserContext
	0,  // 6: proto.ExportModelEvaluationsReq.metrics:type_name -> proto.EvaluationMetricsKind
	62, // 7: proto.ExportModelEvaluationsRsp.csv:type_name -> proto.Csv
	59, // 8: proto.CheckDataProcessStatusReq.ctx:type_name -> proto.UserContext
	61, // 9: proto.CheckDataProcessStatusReq.page_req:type_name -> proto.PageReq
	62, // 10: proto.CheckDataProcessStatusRsp.csv:type_name -> proto.Csv
	60, // 11: proto.ReadModelEvaluationRsp.evaluations:type_name -> proto.Evaluation
	59, // 12: proto.UpdateModelEvaluationReq.ctx:type_name -> proto.UserContext
	60, // 13: proto.UpdateModelEvaluationReq.evaluation:type_name -> proto.Evaluation
	60, // 14: proto.UpdateModelEvaluationRsp.evaluation:type_name -> proto.Evaluation
	59, // 15: proto.DeleteModelEvaluationReq.ctx:type_name -> proto.UserContext
	59, // 16: proto.StartModelEvaluationsReq.ctx:type_name -> proto.UserContext
	59, // 17: proto.StopModelEvaluationsReq.ctx:type_name -> proto.UserContext
	59, // 18: proto.CheckEvaluationScriptExistenceReq.ctx:type_name -> proto.UserContext
	59, // 19: proto.CreateEvaluationScriptReq.ctx:type_name -> proto.UserContext
	63, // 20: proto.CreateEvaluationScriptReq.script:type_name -> proto.EvaluationScript
	63, // 21: proto.CreateEvaluationScriptRsp.script:type_name -> proto.EvaluationScript
	61, // 22: proto.ReadEvaluationScriptReq.page_req:type_name -> proto.PageReq
	59, // 23: proto.ReadEvaluationScriptReq.ctx:type_name -> proto.UserContext
	63, // 24: proto.ReadEvaluationScriptRsp.scripts:type_name -> proto.EvaluationScript
	59, // 25: proto.UpdateEvaluationScriptReq.ctx:type_name -> proto.UserContext
	63, // 26: proto.UpdateEvaluationScriptReq.script:type_name -> proto.EvaluationScript
	59, // 27: proto.UpdateEvaluationScriptRsp.ctx:type_name -> proto.UserContext
	63, // 28: proto.UpdateEvaluationScriptRsp.script:type_name -> proto.EvaluationScript
	59, // 29: proto.DeleteEvaluationScriptReq.ctx:type_name -> proto.UserContext
	59, // 30: proto.UploadEvaluationScriptReq.ctx:type_name -> proto.UserContext
	64, // 31: proto.UploadEvaluationScriptReq.stage:type_name -> proto.EvaluationStage
	59, // 32: proto.DownloadEvaluationScriptReq.ctx:type_name -> proto.UserContext
	64, // 33: proto.DownloadEvaluationScriptReq.stage:type_name -> proto.EvaluationStage
	64, // 34: proto.DownloadEvaluationScriptRsp.stage:type_name -> proto.EvaluationStage
	59, // 35: proto.CreateEvaluationDatasetReq.ctx:type_name -> proto.UserContext
	65, // 36: proto.CreateEvaluationDatasetReq.dataset:type_name -> proto.EvaluationDataset
	65, // 37: proto.CreateEvaluationDatasetRsp.dataset:type_name -> proto.EvaluationDataset
	59, // 38: proto.CheckEvaluationDatasetExistenceReq.ctx:type_name -> proto.UserContext
	59, // 39: proto.GetCsvHeadersReq.ctx:type_name -> proto.UserContext
	66, // 40: proto.GetCsvHeadersRsp.headers:type_name -> proto.CsvHeader
	59, // 41: proto.ValidateEvaluationDatasetReq.ctx:type_name -> proto.UserContext
	59, // 42: proto.PreviewEvaluationDatasetReq.ctx:type_name -> proto.UserContext
	62, // 43: proto.PreviewEvaluationDatasetRsp.csv:type_name -> proto.Csv
	59, // 44: proto.ReadEvaluationDatasetReq.ctx:type_name -> proto.UserContext
	61, // 45: proto.ReadEvaluationDatasetReq.page_req:type_name -> proto.PageReq
	65, // 46: proto.ReadEvaluationDatasetRsp.datasets:type_name -> proto.EvaluationDataset
	59, // 47: proto.UpdateEvaluationDatasetReq.ctx:type_name -> proto.UserContext
	65, // 48: proto.UpdateEvaluationDatasetReq.dataset:type_name -> proto.EvaluationDataset
	65, // 49: proto.UpdateEvaluationDatasetRsp.dataset:type_name -> proto.EvaluationDataset
	59, // 50: proto.DeleteEvaluationDatasetReq.ctx:type_name -> proto.UserContext
	59, // 51: proto.UploadEvaluationDatasetReq.ctx:type_name -> proto.UserContext
	59, // 52: proto.DownloadEvaluationDatasetReq.ctx:type_name -> proto.UserContext
	59, // 53: proto.CreateEvaluationScriptTemplateReq.ctx:type_name -> proto.UserContext
	67, // 54: proto.CreateEvaluationScriptTemplateReq.template:type_name -> proto.EvaluationScriptTemplate
	67, // 55: proto.CreateEvaluationScriptTemplateRsp.template:type_name -> proto.EvaluationScriptTemplate
	59, // 56: proto.ReadEvaluationScriptTemplateReq.ctx:type_name -> proto.UserContext
	61, // 57: proto.ReadEvaluationScriptTemplateReq.page_req:type_name -> proto.PageReq
	67, // 58: proto.ReadEvaluationScriptTemplateRsp.templates:type_name -> proto.EvaluationScriptTemplate
	59, // 59: proto.UpdateEvaluationScriptTemplateReq.ctx:type_name -> proto.UserContext
	67, // 60: proto.UpdateEvaluationScriptTemplateReq.template:type_name -> proto.EvaluationScriptTemplate
	67, // 61: proto.UpdateEvaluationScriptTemplateRsp.template:type_name -> proto.EvaluationScriptTemplate
	59, // 62: proto.DeleteEvaluationScriptTemplateReq.ctx:type_name -> proto.UserContext
	1,  // 63: proto.ModelEvaluationManager.CreateModelEvaluation:input_type -> proto.CreateModelEvaluationReq
	3,  // 64: proto.ModelEvaluationManager.ReadModelEvaluations:input_type -> proto.ReadModelEvaluationReq
	4,  // 65: proto.ModelEvaluationManager.ExportModelEvaluations:input_type -> proto.ExportModelEvaluationsReq
	6,  // 66: proto.ModelEvaluationManager.CheckDataProcessStatus:input_type -> proto.CheckDataProcessStatusReq
	9,  // 67: proto.ModelEvaluationManager.UpdateModelEvaluation:input_type -> proto.UpdateModelEvaluationReq
	11, // 68: proto.ModelEvaluationManager.DeleteModelEvaluations:input_type -> proto.DeleteModelEvaluationReq
	13, // 69: proto.ModelEvaluationManager.StartModelEvaluation:input_type -> proto.StartModelEvaluationsReq
	15, // 70: proto.ModelEvaluationManager.StopModelEvaluation:input_type -> proto.StopModelEvaluationsReq
	19, // 71: proto.EvaluationScriptManager.CreateEvaluationScript:input_type -> proto.CreateEvaluationScriptReq
	21, // 72: proto.EvaluationScriptManager.ReadEvaluationScript:input_type -> proto.ReadEvaluationScriptReq
	23, // 73: proto.EvaluationScriptManager.UpdateEvaluationScript:input_type -> proto.UpdateEvaluationScriptReq
	25, // 74: proto.EvaluationScriptManager.DeleteEvaluationScript:input_type -> proto.DeleteEvaluationScriptReq
	27, // 75: proto.EvaluationScriptManager.UploadEvaluationScript:input_type -> proto.UploadEvaluationScriptReq
	29, // 76: proto.EvaluationScriptManager.DownloadEvaluationScript:input_type -> proto.DownloadEvaluationScriptReq
	17, // 77: proto.EvaluationScriptManager.CheckEvaluationScriptExistence:input_type -> proto.CheckEvaluationScriptExistenceReq
	31, // 78: proto.EvaluationDatasetManager.CreateEvaluationDataset:input_type -> proto.CreateEvaluationDatasetReq
	33, // 79: proto.EvaluationDatasetManager.CheckEvaluationDatasetExistence:input_type -> proto.CheckEvaluationDatasetExistenceReq
	35, // 80: proto.EvaluationDatasetManager.GetCsvHeaders:input_type -> proto.GetCsvHeadersReq
	37, // 81: proto.EvaluationDatasetManager.ValidateEvaluationDataset:input_type -> proto.ValidateEvaluationDatasetReq
	39, // 82: proto.EvaluationDatasetManager.PreviewEvaluationDataset:input_type -> proto.PreviewEvaluationDatasetReq
	41, // 83: proto.EvaluationDatasetManager.ReadEvaluationDataset:input_type -> proto.ReadEvaluationDatasetReq
	43, // 84: proto.EvaluationDatasetManager.UpdateEvaluationDataset:input_type -> proto.UpdateEvaluationDatasetReq
	45, // 85: proto.EvaluationDatasetManager.DeleteEvaluationDataset:input_type -> proto.DeleteEvaluationDatasetReq
	47, // 86: proto.EvaluationDatasetManager.UploadEvaluationDataset:input_type -> proto.UploadEvaluationDatasetReq
	49, // 87: proto.EvaluationDatasetManager.DownloadEvaluationDataset:input_type -> proto.DownloadEvaluationDatasetReq
	51, // 88: proto.EvaluationScriptTemplateManager.CreateEvaluationScriptTemplate:input_type -> proto.CreateEvaluationScriptTemplateReq
	53, // 89: proto.EvaluationScriptTemplateManager.ReadEvaluationScriptTemplate:input_type -> proto.ReadEvaluationScriptTemplateReq
	55, // 90: proto.EvaluationScriptTemplateManager.UpdateEvaluationScriptTemplate:input_type -> proto.UpdateEvaluationScriptTemplateReq
	57, // 91: proto.EvaluationScriptTemplateManager.DeleteEvaluationScriptTemplate:input_type -> proto.DeleteEvaluationScriptTemplateReq
	2,  // 92: proto.ModelEvaluationManager.CreateModelEvaluation:output_type -> proto.CreateModelEvaluationRsp
	8,  // 93: proto.ModelEvaluationManager.ReadModelEvaluations:output_type -> proto.ReadModelEvaluationRsp
	5,  // 94: proto.ModelEvaluationManager.ExportModelEvaluations:output_type -> proto.ExportModelEvaluationsRsp
	7,  // 95: proto.ModelEvaluationManager.CheckDataProcessStatus:output_type -> proto.CheckDataProcessStatusRsp
	10, // 96: proto.ModelEvaluationManager.UpdateModelEvaluation:output_type -> proto.UpdateModelEvaluationRsp
	12, // 97: proto.ModelEvaluationManager.DeleteModelEvaluations:output_type -> proto.DeleteModelEvaluationRsp
	14, // 98: proto.ModelEvaluationManager.StartModelEvaluation:output_type -> proto.StartModelEvaluationsRsp
	16, // 99: proto.ModelEvaluationManager.StopModelEvaluation:output_type -> proto.StopModelEvaluationsRsp
	20, // 100: proto.EvaluationScriptManager.CreateEvaluationScript:output_type -> proto.CreateEvaluationScriptRsp
	22, // 101: proto.EvaluationScriptManager.ReadEvaluationScript:output_type -> proto.ReadEvaluationScriptRsp
	24, // 102: proto.EvaluationScriptManager.UpdateEvaluationScript:output_type -> proto.UpdateEvaluationScriptRsp
	26, // 103: proto.EvaluationScriptManager.DeleteEvaluationScript:output_type -> proto.DeleteEvaluationScriptRsp
	28, // 104: proto.EvaluationScriptManager.UploadEvaluationScript:output_type -> proto.UploadEvaluationScriptRsp
	30, // 105: proto.EvaluationScriptManager.DownloadEvaluationScript:output_type -> proto.DownloadEvaluationScriptRsp
	18, // 106: proto.EvaluationScriptManager.CheckEvaluationScriptExistence:output_type -> proto.CheckEvaluationScriptExistenceRsp
	32, // 107: proto.EvaluationDatasetManager.CreateEvaluationDataset:output_type -> proto.CreateEvaluationDatasetRsp
	34, // 108: proto.EvaluationDatasetManager.CheckEvaluationDatasetExistence:output_type -> proto.CheckEvaluationDatasetExistenceRsp
	36, // 109: proto.EvaluationDatasetManager.GetCsvHeaders:output_type -> proto.GetCsvHeadersRsp
	38, // 110: proto.EvaluationDatasetManager.ValidateEvaluationDataset:output_type -> proto.ValidateEvaluationDatasetRsp
	40, // 111: proto.EvaluationDatasetManager.PreviewEvaluationDataset:output_type -> proto.PreviewEvaluationDatasetRsp
	42, // 112: proto.EvaluationDatasetManager.ReadEvaluationDataset:output_type -> proto.ReadEvaluationDatasetRsp
	44, // 113: proto.EvaluationDatasetManager.UpdateEvaluationDataset:output_type -> proto.UpdateEvaluationDatasetRsp
	46, // 114: proto.EvaluationDatasetManager.DeleteEvaluationDataset:output_type -> proto.DeleteEvaluationDatasetRsp
	48, // 115: proto.EvaluationDatasetManager.UploadEvaluationDataset:output_type -> proto.UploadEvaluationDatasetRsp
	50, // 116: proto.EvaluationDatasetManager.DownloadEvaluationDataset:output_type -> proto.DownloadEvaluationDatasetRsp
	52, // 117: proto.EvaluationScriptTemplateManager.CreateEvaluationScriptTemplate:output_type -> proto.CreateEvaluationScriptTemplateRsp
	54, // 118: proto.EvaluationScriptTemplateManager.ReadEvaluationScriptTemplate:output_type -> proto.ReadEvaluationScriptTemplateRsp
	56, // 119: proto.EvaluationScriptTemplateManager.UpdateEvaluationScriptTemplate:output_type -> proto.UpdateEvaluationScriptTemplateRsp
	58, // 120: proto.EvaluationScriptTemplateManager.DeleteEvaluationScriptTemplate:output_type -> proto.DeleteEvaluationScriptTemplateRsp
	92, // [92:121] is the sub-list for method output_type
	63, // [63:92] is the sub-list for method input_type
	63, // [63:63] is the sub-list for extension type_name
	63, // [63:63] is the sub-list for extension extendee
	0,  // [0:63] is the sub-list for field type_name
}

func init() { file_proto_rpc_model_evaluation_proto_init() }
func file_proto_rpc_model_evaluation_proto_init() {
	if File_proto_rpc_model_evaluation_proto != nil {
		return
	}
	file_proto_model_proto_init()
	file_proto_common_proto_init()
	file_proto_evaluation_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_proto_rpc_model_evaluation_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateModelEvaluationReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateModelEvaluationRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadModelEvaluationReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportModelEvaluationsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportModelEvaluationsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckDataProcessStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckDataProcessStatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadModelEvaluationRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateModelEvaluationReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateModelEvaluationRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteModelEvaluationReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteModelEvaluationRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartModelEvaluationsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartModelEvaluationsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopModelEvaluationsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopModelEvaluationsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckEvaluationScriptExistenceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckEvaluationScriptExistenceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEvaluationScriptReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEvaluationScriptRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadEvaluationScriptReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadEvaluationScriptRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEvaluationScriptReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEvaluationScriptRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteEvaluationScriptReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteEvaluationScriptRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadEvaluationScriptReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadEvaluationScriptRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadEvaluationScriptReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadEvaluationScriptRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEvaluationDatasetReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEvaluationDatasetRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckEvaluationDatasetExistenceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckEvaluationDatasetExistenceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCsvHeadersReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCsvHeadersRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateEvaluationDatasetReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateEvaluationDatasetRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewEvaluationDatasetReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewEvaluationDatasetRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadEvaluationDatasetReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadEvaluationDatasetRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEvaluationDatasetReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEvaluationDatasetRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteEvaluationDatasetReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteEvaluationDatasetRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadEvaluationDatasetReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadEvaluationDatasetRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadEvaluationDatasetReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadEvaluationDatasetRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEvaluationScriptTemplateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEvaluationScriptTemplateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadEvaluationScriptTemplateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadEvaluationScriptTemplateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEvaluationScriptTemplateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEvaluationScriptTemplateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteEvaluationScriptTemplateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rpc_model_evaluation_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteEvaluationScriptTemplateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_rpc_model_evaluation_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   58,
			NumExtensions: 0,
			NumServices:   4,
		},
		GoTypes:           file_proto_rpc_model_evaluation_proto_goTypes,
		DependencyIndexes: file_proto_rpc_model_evaluation_proto_depIdxs,
		EnumInfos:         file_proto_rpc_model_evaluation_proto_enumTypes,
		MessageInfos:      file_proto_rpc_model_evaluation_proto_msgTypes,
	}.Build()
	File_proto_rpc_model_evaluation_proto = out.File
	file_proto_rpc_model_evaluation_proto_rawDesc = nil
	file_proto_rpc_model_evaluation_proto_goTypes = nil
	file_proto_rpc_model_evaluation_proto_depIdxs = nil
}
