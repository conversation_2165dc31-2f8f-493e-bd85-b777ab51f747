// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.18.1
// source: proto/rpc_knowledge_base.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	KnowledgeBaseManager_ListKnowledgeBases_FullMethodName             = "/proto.KnowledgeBaseManager/ListKnowledgeBases"
	KnowledgeBaseManager_GetKnowledgeBase_FullMethodName               = "/proto.KnowledgeBaseManager/GetKnowledgeBase"
	KnowledgeBaseManager_CreateKnowledgeBase_FullMethodName            = "/proto.KnowledgeBaseManager/CreateKnowledgeBase"
	KnowledgeBaseManager_UpdateKnowledgeBase_FullMethodName            = "/proto.KnowledgeBaseManager/UpdateKnowledgeBase"
	KnowledgeBaseManager_DeleteKnowledgeBases_FullMethodName           = "/proto.KnowledgeBaseManager/DeleteKnowledgeBases"
	KnowledgeBaseManager_ListConnectionTables_FullMethodName           = "/proto.KnowledgeBaseManager/ListConnectionTables"
	KnowledgeBaseManager_ListDocuments_FullMethodName                  = "/proto.KnowledgeBaseManager/ListDocuments"
	KnowledgeBaseManager_DisableDocument_FullMethodName                = "/proto.KnowledgeBaseManager/DisableDocument"
	KnowledgeBaseManager_ListDocumentChunks_FullMethodName             = "/proto.KnowledgeBaseManager/ListDocumentChunks"
	KnowledgeBaseManager_RetrieveKnowledgeBase_FullMethodName          = "/proto.KnowledgeBaseManager/RetrieveKnowledgeBase"
	KnowledgeBaseManager_GetDocumentTree_FullMethodName                = "/proto.KnowledgeBaseManager/GetDocumentTree"
	KnowledgeBaseManager_SubmitFileToKnowledgeBase_FullMethodName      = "/proto.KnowledgeBaseManager/SubmitFileToKnowledgeBase"
	KnowledgeBaseManager_RemoveFileFromKnowledgeBase_FullMethodName    = "/proto.KnowledgeBaseManager/RemoveFileFromKnowledgeBase"
	KnowledgeBaseManager_UpdateKnowledgeBaseState_FullMethodName       = "/proto.KnowledgeBaseManager/UpdateKnowledgeBaseState"
	KnowledgeBaseManager_CreateChunk_FullMethodName                    = "/proto.KnowledgeBaseManager/CreateChunk"
	KnowledgeBaseManager_DeleteChunk_FullMethodName                    = "/proto.KnowledgeBaseManager/DeleteChunk"
	KnowledgeBaseManager_UpdateChunk_FullMethodName                    = "/proto.KnowledgeBaseManager/UpdateChunk"
	KnowledgeBaseManager_PreviewDocumentProcess_FullMethodName         = "/proto.KnowledgeBaseManager/PreviewDocumentProcess"
	KnowledgeBaseManager_SyncSubmitFilesToKnowledgeBase_FullMethodName = "/proto.KnowledgeBaseManager/SyncSubmitFilesToKnowledgeBase"
	KnowledgeBaseManager_RetrieveCrossKnowledgeBase_FullMethodName     = "/proto.KnowledgeBaseManager/RetrieveCrossKnowledgeBase"
	KnowledgeBaseManager_SubmitChunksToKnowledgeBase_FullMethodName    = "/proto.KnowledgeBaseManager/SubmitChunksToKnowledgeBase"
	KnowledgeBaseManager_IsDocumentExistent_FullMethodName             = "/proto.KnowledgeBaseManager/IsDocumentExistent"
	KnowledgeBaseManager_CollectKnowledgeBaseStats_FullMethodName      = "/proto.KnowledgeBaseManager/CollectKnowledgeBaseStats"
	KnowledgeBaseManager_ShareKnowledgeBase_FullMethodName             = "/proto.KnowledgeBaseManager/ShareKnowledgeBase"
	KnowledgeBaseManager_PublishKnowledgeBase_FullMethodName           = "/proto.KnowledgeBaseManager/PublishKnowledgeBase"
	KnowledgeBaseManager_TraceDocElements_FullMethodName               = "/proto.KnowledgeBaseManager/TraceDocElements"
	KnowledgeBaseManager_RetryDocumentProcess_FullMethodName           = "/proto.KnowledgeBaseManager/RetryDocumentProcess"
)

// KnowledgeBaseManagerClient is the client API for KnowledgeBaseManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type KnowledgeBaseManagerClient interface {
	ListKnowledgeBases(ctx context.Context, in *ListKnowledgeBasesReq, opts ...grpc.CallOption) (*ListKnowledgeBasesRsp, error)
	GetKnowledgeBase(ctx context.Context, in *GetKnowledgeBaseReq, opts ...grpc.CallOption) (*GetKnowledgeBaseRsp, error)
	CreateKnowledgeBase(ctx context.Context, in *CreateKnowledgeBaseReq, opts ...grpc.CallOption) (*CreateKnowledgeBaseRsp, error)
	UpdateKnowledgeBase(ctx context.Context, in *UpdateKnowledgeBaseReq, opts ...grpc.CallOption) (*UpdateKnowledgeBaseRsp, error)
	DeleteKnowledgeBases(ctx context.Context, in *DeleteKnowledgeBasesReq, opts ...grpc.CallOption) (*DeleteKnowledgeBasesRsp, error)
	// 获取数据连接的库表列表, 包含表描述信息
	ListConnectionTables(ctx context.Context, in *ListConnectionTablesReq, opts ...grpc.CallOption) (*ListConnectionTablesRsp, error)
	// 获取知识库的文档列表，平铺形式
	ListDocuments(ctx context.Context, in *ListDocumentsReq, opts ...grpc.CallOption) (*ListDocumentsRsp, error)
	// 启用/禁用 知识库文档
	DisableDocument(ctx context.Context, in *DisableDocumentReq, opts ...grpc.CallOption) (*DisableDocumentRsp, error)
	// 获取知识库文档的分段内容列表，支持排序
	ListDocumentChunks(ctx context.Context, in *ListDocumentChunksReq, opts ...grpc.CallOption) (*ListDocumentChunksRsp, error)
	// 知识库检索接口
	RetrieveKnowledgeBase(ctx context.Context, in *RetrieveKnowledgeBaseReq, opts ...grpc.CallOption) (*RetrieveKnowledgeBaseRsp, error)
	// 获取知识库的文档列表，文档树形式
	GetDocumentTree(ctx context.Context, in *GetDocumentTreeReq, opts ...grpc.CallOption) (*GetDocumentTreeRsp, error)
	// 新增文档到知识库(异步处理),允许自动创建知识库
	SubmitFileToKnowledgeBase(ctx context.Context, in *SubmitFileToKnowledgeBaseReq, opts ...grpc.CallOption) (*SubmitFileToKnowledgeBaseRsp, error)
	// 从知识库移除文档
	RemoveFileFromKnowledgeBase(ctx context.Context, in *RemoveFileFromKnowledgeBaseReq, opts ...grpc.CallOption) (*RemoveFileFromKnowledgeBaseRsp, error)
	// 更新知识库状态(可见、可检索)
	UpdateKnowledgeBaseState(ctx context.Context, in *UpdateKnowledgeBaseStateReq, opts ...grpc.CallOption) (*UpdateKnowledgeBaseStateRsp, error)
	// 手动新建切片
	CreateChunk(ctx context.Context, in *CreateChunkReq, opts ...grpc.CallOption) (*CreateChunkRsp, error)
	// 删除切片
	DeleteChunk(ctx context.Context, in *DeleteChunkReq, opts ...grpc.CallOption) (*DeleteChunkRsp, error)
	// 更新切片
	UpdateChunk(ctx context.Context, in *UpdateChunkReq, opts ...grpc.CallOption) (*UpdateChunkRsp, error)
	// 预览文档加工效果(分段预览)
	PreviewDocumentProcess(ctx context.Context, in *PreviewDocumentProcessReq, opts ...grpc.CallOption) (*DocSvcLoadChunkRsp, error)
	// 新增文档到知识库(同步处理)，可指定文档处理配置或按照知识库默认值，可批量处理，处理中推送进度
	SyncSubmitFilesToKnowledgeBase(ctx context.Context, in *SyncSubmitFilesToKnowledgeBaseReq, opts ...grpc.CallOption) (KnowledgeBaseManager_SyncSubmitFilesToKnowledgeBaseClient, error)
	// 跨知识库检索
	RetrieveCrossKnowledgeBase(ctx context.Context, in *RetrieveCrossKnowledgeBaseReq, opts ...grpc.CallOption) (*RetrieveCrossKnowledgeBaseRsp, error)
	// 提交分段到知识库(chunks落库)
	SubmitChunksToKnowledgeBase(ctx context.Context, in *SubmitChunksToKnowledgeBaseReq, opts ...grpc.CallOption) (*SubmitChunksToKnowledgeBaseRsp, error)
	// 判断文档文件是否已经存在于知识库(根据文件MD5)
	IsDocumentExistent(ctx context.Context, in *IsDocumentExistentReq, opts ...grpc.CallOption) (*IsDocumentExistentRsp, error)
	// 统计接口
	CollectKnowledgeBaseStats(ctx context.Context, in *CollectKnowledgeBaseStatsReq, opts ...grpc.CallOption) (*CollectKnowledgeBaseStatsRsp, error)
	// 共享知识库到公共空间
	ShareKnowledgeBase(ctx context.Context, in *ShareKnowledgeBaseReq, opts ...grpc.CallOption) (*ShareKnowledgeBaseRsp, error)
	// 发布知识库服务
	PublishKnowledgeBase(ctx context.Context, in *PublishKnowledgeBaseReq, opts ...grpc.CallOption) (*PublishKnowledgeBaseRsp, error)
	// 溯源文档元素
	TraceDocElements(ctx context.Context, in *TraceDocElementsReq, opts ...grpc.CallOption) (*TraceDocElementsRsp, error)
	// 重试文档处理落库的流程
	RetryDocumentProcess(ctx context.Context, in *RetryDocumentProcessReq, opts ...grpc.CallOption) (*RetryDocumentProcessRsp, error)
}

type knowledgeBaseManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewKnowledgeBaseManagerClient(cc grpc.ClientConnInterface) KnowledgeBaseManagerClient {
	return &knowledgeBaseManagerClient{cc}
}

func (c *knowledgeBaseManagerClient) ListKnowledgeBases(ctx context.Context, in *ListKnowledgeBasesReq, opts ...grpc.CallOption) (*ListKnowledgeBasesRsp, error) {
	out := new(ListKnowledgeBasesRsp)
	err := c.cc.Invoke(ctx, KnowledgeBaseManager_ListKnowledgeBases_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseManagerClient) GetKnowledgeBase(ctx context.Context, in *GetKnowledgeBaseReq, opts ...grpc.CallOption) (*GetKnowledgeBaseRsp, error) {
	out := new(GetKnowledgeBaseRsp)
	err := c.cc.Invoke(ctx, KnowledgeBaseManager_GetKnowledgeBase_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseManagerClient) CreateKnowledgeBase(ctx context.Context, in *CreateKnowledgeBaseReq, opts ...grpc.CallOption) (*CreateKnowledgeBaseRsp, error) {
	out := new(CreateKnowledgeBaseRsp)
	err := c.cc.Invoke(ctx, KnowledgeBaseManager_CreateKnowledgeBase_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseManagerClient) UpdateKnowledgeBase(ctx context.Context, in *UpdateKnowledgeBaseReq, opts ...grpc.CallOption) (*UpdateKnowledgeBaseRsp, error) {
	out := new(UpdateKnowledgeBaseRsp)
	err := c.cc.Invoke(ctx, KnowledgeBaseManager_UpdateKnowledgeBase_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseManagerClient) DeleteKnowledgeBases(ctx context.Context, in *DeleteKnowledgeBasesReq, opts ...grpc.CallOption) (*DeleteKnowledgeBasesRsp, error) {
	out := new(DeleteKnowledgeBasesRsp)
	err := c.cc.Invoke(ctx, KnowledgeBaseManager_DeleteKnowledgeBases_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseManagerClient) ListConnectionTables(ctx context.Context, in *ListConnectionTablesReq, opts ...grpc.CallOption) (*ListConnectionTablesRsp, error) {
	out := new(ListConnectionTablesRsp)
	err := c.cc.Invoke(ctx, KnowledgeBaseManager_ListConnectionTables_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseManagerClient) ListDocuments(ctx context.Context, in *ListDocumentsReq, opts ...grpc.CallOption) (*ListDocumentsRsp, error) {
	out := new(ListDocumentsRsp)
	err := c.cc.Invoke(ctx, KnowledgeBaseManager_ListDocuments_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseManagerClient) DisableDocument(ctx context.Context, in *DisableDocumentReq, opts ...grpc.CallOption) (*DisableDocumentRsp, error) {
	out := new(DisableDocumentRsp)
	err := c.cc.Invoke(ctx, KnowledgeBaseManager_DisableDocument_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseManagerClient) ListDocumentChunks(ctx context.Context, in *ListDocumentChunksReq, opts ...grpc.CallOption) (*ListDocumentChunksRsp, error) {
	out := new(ListDocumentChunksRsp)
	err := c.cc.Invoke(ctx, KnowledgeBaseManager_ListDocumentChunks_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseManagerClient) RetrieveKnowledgeBase(ctx context.Context, in *RetrieveKnowledgeBaseReq, opts ...grpc.CallOption) (*RetrieveKnowledgeBaseRsp, error) {
	out := new(RetrieveKnowledgeBaseRsp)
	err := c.cc.Invoke(ctx, KnowledgeBaseManager_RetrieveKnowledgeBase_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseManagerClient) GetDocumentTree(ctx context.Context, in *GetDocumentTreeReq, opts ...grpc.CallOption) (*GetDocumentTreeRsp, error) {
	out := new(GetDocumentTreeRsp)
	err := c.cc.Invoke(ctx, KnowledgeBaseManager_GetDocumentTree_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseManagerClient) SubmitFileToKnowledgeBase(ctx context.Context, in *SubmitFileToKnowledgeBaseReq, opts ...grpc.CallOption) (*SubmitFileToKnowledgeBaseRsp, error) {
	out := new(SubmitFileToKnowledgeBaseRsp)
	err := c.cc.Invoke(ctx, KnowledgeBaseManager_SubmitFileToKnowledgeBase_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseManagerClient) RemoveFileFromKnowledgeBase(ctx context.Context, in *RemoveFileFromKnowledgeBaseReq, opts ...grpc.CallOption) (*RemoveFileFromKnowledgeBaseRsp, error) {
	out := new(RemoveFileFromKnowledgeBaseRsp)
	err := c.cc.Invoke(ctx, KnowledgeBaseManager_RemoveFileFromKnowledgeBase_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseManagerClient) UpdateKnowledgeBaseState(ctx context.Context, in *UpdateKnowledgeBaseStateReq, opts ...grpc.CallOption) (*UpdateKnowledgeBaseStateRsp, error) {
	out := new(UpdateKnowledgeBaseStateRsp)
	err := c.cc.Invoke(ctx, KnowledgeBaseManager_UpdateKnowledgeBaseState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseManagerClient) CreateChunk(ctx context.Context, in *CreateChunkReq, opts ...grpc.CallOption) (*CreateChunkRsp, error) {
	out := new(CreateChunkRsp)
	err := c.cc.Invoke(ctx, KnowledgeBaseManager_CreateChunk_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseManagerClient) DeleteChunk(ctx context.Context, in *DeleteChunkReq, opts ...grpc.CallOption) (*DeleteChunkRsp, error) {
	out := new(DeleteChunkRsp)
	err := c.cc.Invoke(ctx, KnowledgeBaseManager_DeleteChunk_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseManagerClient) UpdateChunk(ctx context.Context, in *UpdateChunkReq, opts ...grpc.CallOption) (*UpdateChunkRsp, error) {
	out := new(UpdateChunkRsp)
	err := c.cc.Invoke(ctx, KnowledgeBaseManager_UpdateChunk_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseManagerClient) PreviewDocumentProcess(ctx context.Context, in *PreviewDocumentProcessReq, opts ...grpc.CallOption) (*DocSvcLoadChunkRsp, error) {
	out := new(DocSvcLoadChunkRsp)
	err := c.cc.Invoke(ctx, KnowledgeBaseManager_PreviewDocumentProcess_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseManagerClient) SyncSubmitFilesToKnowledgeBase(ctx context.Context, in *SyncSubmitFilesToKnowledgeBaseReq, opts ...grpc.CallOption) (KnowledgeBaseManager_SyncSubmitFilesToKnowledgeBaseClient, error) {
	stream, err := c.cc.NewStream(ctx, &KnowledgeBaseManager_ServiceDesc.Streams[0], KnowledgeBaseManager_SyncSubmitFilesToKnowledgeBase_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &knowledgeBaseManagerSyncSubmitFilesToKnowledgeBaseClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type KnowledgeBaseManager_SyncSubmitFilesToKnowledgeBaseClient interface {
	Recv() (*SyncSubmitFilesToKnowledgeBaseRsp, error)
	grpc.ClientStream
}

type knowledgeBaseManagerSyncSubmitFilesToKnowledgeBaseClient struct {
	grpc.ClientStream
}

func (x *knowledgeBaseManagerSyncSubmitFilesToKnowledgeBaseClient) Recv() (*SyncSubmitFilesToKnowledgeBaseRsp, error) {
	m := new(SyncSubmitFilesToKnowledgeBaseRsp)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *knowledgeBaseManagerClient) RetrieveCrossKnowledgeBase(ctx context.Context, in *RetrieveCrossKnowledgeBaseReq, opts ...grpc.CallOption) (*RetrieveCrossKnowledgeBaseRsp, error) {
	out := new(RetrieveCrossKnowledgeBaseRsp)
	err := c.cc.Invoke(ctx, KnowledgeBaseManager_RetrieveCrossKnowledgeBase_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseManagerClient) SubmitChunksToKnowledgeBase(ctx context.Context, in *SubmitChunksToKnowledgeBaseReq, opts ...grpc.CallOption) (*SubmitChunksToKnowledgeBaseRsp, error) {
	out := new(SubmitChunksToKnowledgeBaseRsp)
	err := c.cc.Invoke(ctx, KnowledgeBaseManager_SubmitChunksToKnowledgeBase_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseManagerClient) IsDocumentExistent(ctx context.Context, in *IsDocumentExistentReq, opts ...grpc.CallOption) (*IsDocumentExistentRsp, error) {
	out := new(IsDocumentExistentRsp)
	err := c.cc.Invoke(ctx, KnowledgeBaseManager_IsDocumentExistent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseManagerClient) CollectKnowledgeBaseStats(ctx context.Context, in *CollectKnowledgeBaseStatsReq, opts ...grpc.CallOption) (*CollectKnowledgeBaseStatsRsp, error) {
	out := new(CollectKnowledgeBaseStatsRsp)
	err := c.cc.Invoke(ctx, KnowledgeBaseManager_CollectKnowledgeBaseStats_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseManagerClient) ShareKnowledgeBase(ctx context.Context, in *ShareKnowledgeBaseReq, opts ...grpc.CallOption) (*ShareKnowledgeBaseRsp, error) {
	out := new(ShareKnowledgeBaseRsp)
	err := c.cc.Invoke(ctx, KnowledgeBaseManager_ShareKnowledgeBase_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseManagerClient) PublishKnowledgeBase(ctx context.Context, in *PublishKnowledgeBaseReq, opts ...grpc.CallOption) (*PublishKnowledgeBaseRsp, error) {
	out := new(PublishKnowledgeBaseRsp)
	err := c.cc.Invoke(ctx, KnowledgeBaseManager_PublishKnowledgeBase_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseManagerClient) TraceDocElements(ctx context.Context, in *TraceDocElementsReq, opts ...grpc.CallOption) (*TraceDocElementsRsp, error) {
	out := new(TraceDocElementsRsp)
	err := c.cc.Invoke(ctx, KnowledgeBaseManager_TraceDocElements_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseManagerClient) RetryDocumentProcess(ctx context.Context, in *RetryDocumentProcessReq, opts ...grpc.CallOption) (*RetryDocumentProcessRsp, error) {
	out := new(RetryDocumentProcessRsp)
	err := c.cc.Invoke(ctx, KnowledgeBaseManager_RetryDocumentProcess_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// KnowledgeBaseManagerServer is the server API for KnowledgeBaseManager service.
// All implementations must embed UnimplementedKnowledgeBaseManagerServer
// for forward compatibility
type KnowledgeBaseManagerServer interface {
	ListKnowledgeBases(context.Context, *ListKnowledgeBasesReq) (*ListKnowledgeBasesRsp, error)
	GetKnowledgeBase(context.Context, *GetKnowledgeBaseReq) (*GetKnowledgeBaseRsp, error)
	CreateKnowledgeBase(context.Context, *CreateKnowledgeBaseReq) (*CreateKnowledgeBaseRsp, error)
	UpdateKnowledgeBase(context.Context, *UpdateKnowledgeBaseReq) (*UpdateKnowledgeBaseRsp, error)
	DeleteKnowledgeBases(context.Context, *DeleteKnowledgeBasesReq) (*DeleteKnowledgeBasesRsp, error)
	// 获取数据连接的库表列表, 包含表描述信息
	ListConnectionTables(context.Context, *ListConnectionTablesReq) (*ListConnectionTablesRsp, error)
	// 获取知识库的文档列表，平铺形式
	ListDocuments(context.Context, *ListDocumentsReq) (*ListDocumentsRsp, error)
	// 启用/禁用 知识库文档
	DisableDocument(context.Context, *DisableDocumentReq) (*DisableDocumentRsp, error)
	// 获取知识库文档的分段内容列表，支持排序
	ListDocumentChunks(context.Context, *ListDocumentChunksReq) (*ListDocumentChunksRsp, error)
	// 知识库检索接口
	RetrieveKnowledgeBase(context.Context, *RetrieveKnowledgeBaseReq) (*RetrieveKnowledgeBaseRsp, error)
	// 获取知识库的文档列表，文档树形式
	GetDocumentTree(context.Context, *GetDocumentTreeReq) (*GetDocumentTreeRsp, error)
	// 新增文档到知识库(异步处理),允许自动创建知识库
	SubmitFileToKnowledgeBase(context.Context, *SubmitFileToKnowledgeBaseReq) (*SubmitFileToKnowledgeBaseRsp, error)
	// 从知识库移除文档
	RemoveFileFromKnowledgeBase(context.Context, *RemoveFileFromKnowledgeBaseReq) (*RemoveFileFromKnowledgeBaseRsp, error)
	// 更新知识库状态(可见、可检索)
	UpdateKnowledgeBaseState(context.Context, *UpdateKnowledgeBaseStateReq) (*UpdateKnowledgeBaseStateRsp, error)
	// 手动新建切片
	CreateChunk(context.Context, *CreateChunkReq) (*CreateChunkRsp, error)
	// 删除切片
	DeleteChunk(context.Context, *DeleteChunkReq) (*DeleteChunkRsp, error)
	// 更新切片
	UpdateChunk(context.Context, *UpdateChunkReq) (*UpdateChunkRsp, error)
	// 预览文档加工效果(分段预览)
	PreviewDocumentProcess(context.Context, *PreviewDocumentProcessReq) (*DocSvcLoadChunkRsp, error)
	// 新增文档到知识库(同步处理)，可指定文档处理配置或按照知识库默认值，可批量处理，处理中推送进度
	SyncSubmitFilesToKnowledgeBase(*SyncSubmitFilesToKnowledgeBaseReq, KnowledgeBaseManager_SyncSubmitFilesToKnowledgeBaseServer) error
	// 跨知识库检索
	RetrieveCrossKnowledgeBase(context.Context, *RetrieveCrossKnowledgeBaseReq) (*RetrieveCrossKnowledgeBaseRsp, error)
	// 提交分段到知识库(chunks落库)
	SubmitChunksToKnowledgeBase(context.Context, *SubmitChunksToKnowledgeBaseReq) (*SubmitChunksToKnowledgeBaseRsp, error)
	// 判断文档文件是否已经存在于知识库(根据文件MD5)
	IsDocumentExistent(context.Context, *IsDocumentExistentReq) (*IsDocumentExistentRsp, error)
	// 统计接口
	CollectKnowledgeBaseStats(context.Context, *CollectKnowledgeBaseStatsReq) (*CollectKnowledgeBaseStatsRsp, error)
	// 共享知识库到公共空间
	ShareKnowledgeBase(context.Context, *ShareKnowledgeBaseReq) (*ShareKnowledgeBaseRsp, error)
	// 发布知识库服务
	PublishKnowledgeBase(context.Context, *PublishKnowledgeBaseReq) (*PublishKnowledgeBaseRsp, error)
	// 溯源文档元素
	TraceDocElements(context.Context, *TraceDocElementsReq) (*TraceDocElementsRsp, error)
	// 重试文档处理落库的流程
	RetryDocumentProcess(context.Context, *RetryDocumentProcessReq) (*RetryDocumentProcessRsp, error)
	mustEmbedUnimplementedKnowledgeBaseManagerServer()
}

// UnimplementedKnowledgeBaseManagerServer must be embedded to have forward compatible implementations.
type UnimplementedKnowledgeBaseManagerServer struct {
}

func (UnimplementedKnowledgeBaseManagerServer) ListKnowledgeBases(context.Context, *ListKnowledgeBasesReq) (*ListKnowledgeBasesRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListKnowledgeBases not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) GetKnowledgeBase(context.Context, *GetKnowledgeBaseReq) (*GetKnowledgeBaseRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKnowledgeBase not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) CreateKnowledgeBase(context.Context, *CreateKnowledgeBaseReq) (*CreateKnowledgeBaseRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateKnowledgeBase not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) UpdateKnowledgeBase(context.Context, *UpdateKnowledgeBaseReq) (*UpdateKnowledgeBaseRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateKnowledgeBase not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) DeleteKnowledgeBases(context.Context, *DeleteKnowledgeBasesReq) (*DeleteKnowledgeBasesRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteKnowledgeBases not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) ListConnectionTables(context.Context, *ListConnectionTablesReq) (*ListConnectionTablesRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListConnectionTables not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) ListDocuments(context.Context, *ListDocumentsReq) (*ListDocumentsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDocuments not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) DisableDocument(context.Context, *DisableDocumentReq) (*DisableDocumentRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DisableDocument not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) ListDocumentChunks(context.Context, *ListDocumentChunksReq) (*ListDocumentChunksRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDocumentChunks not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) RetrieveKnowledgeBase(context.Context, *RetrieveKnowledgeBaseReq) (*RetrieveKnowledgeBaseRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RetrieveKnowledgeBase not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) GetDocumentTree(context.Context, *GetDocumentTreeReq) (*GetDocumentTreeRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDocumentTree not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) SubmitFileToKnowledgeBase(context.Context, *SubmitFileToKnowledgeBaseReq) (*SubmitFileToKnowledgeBaseRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitFileToKnowledgeBase not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) RemoveFileFromKnowledgeBase(context.Context, *RemoveFileFromKnowledgeBaseReq) (*RemoveFileFromKnowledgeBaseRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveFileFromKnowledgeBase not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) UpdateKnowledgeBaseState(context.Context, *UpdateKnowledgeBaseStateReq) (*UpdateKnowledgeBaseStateRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateKnowledgeBaseState not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) CreateChunk(context.Context, *CreateChunkReq) (*CreateChunkRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateChunk not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) DeleteChunk(context.Context, *DeleteChunkReq) (*DeleteChunkRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteChunk not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) UpdateChunk(context.Context, *UpdateChunkReq) (*UpdateChunkRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateChunk not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) PreviewDocumentProcess(context.Context, *PreviewDocumentProcessReq) (*DocSvcLoadChunkRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreviewDocumentProcess not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) SyncSubmitFilesToKnowledgeBase(*SyncSubmitFilesToKnowledgeBaseReq, KnowledgeBaseManager_SyncSubmitFilesToKnowledgeBaseServer) error {
	return status.Errorf(codes.Unimplemented, "method SyncSubmitFilesToKnowledgeBase not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) RetrieveCrossKnowledgeBase(context.Context, *RetrieveCrossKnowledgeBaseReq) (*RetrieveCrossKnowledgeBaseRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RetrieveCrossKnowledgeBase not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) SubmitChunksToKnowledgeBase(context.Context, *SubmitChunksToKnowledgeBaseReq) (*SubmitChunksToKnowledgeBaseRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitChunksToKnowledgeBase not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) IsDocumentExistent(context.Context, *IsDocumentExistentReq) (*IsDocumentExistentRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsDocumentExistent not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) CollectKnowledgeBaseStats(context.Context, *CollectKnowledgeBaseStatsReq) (*CollectKnowledgeBaseStatsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CollectKnowledgeBaseStats not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) ShareKnowledgeBase(context.Context, *ShareKnowledgeBaseReq) (*ShareKnowledgeBaseRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShareKnowledgeBase not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) PublishKnowledgeBase(context.Context, *PublishKnowledgeBaseReq) (*PublishKnowledgeBaseRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishKnowledgeBase not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) TraceDocElements(context.Context, *TraceDocElementsReq) (*TraceDocElementsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TraceDocElements not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) RetryDocumentProcess(context.Context, *RetryDocumentProcessReq) (*RetryDocumentProcessRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RetryDocumentProcess not implemented")
}
func (UnimplementedKnowledgeBaseManagerServer) mustEmbedUnimplementedKnowledgeBaseManagerServer() {}

// UnsafeKnowledgeBaseManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to KnowledgeBaseManagerServer will
// result in compilation errors.
type UnsafeKnowledgeBaseManagerServer interface {
	mustEmbedUnimplementedKnowledgeBaseManagerServer()
}

func RegisterKnowledgeBaseManagerServer(s grpc.ServiceRegistrar, srv KnowledgeBaseManagerServer) {
	s.RegisterService(&KnowledgeBaseManager_ServiceDesc, srv)
}

func _KnowledgeBaseManager_ListKnowledgeBases_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListKnowledgeBasesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseManagerServer).ListKnowledgeBases(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBaseManager_ListKnowledgeBases_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseManagerServer).ListKnowledgeBases(ctx, req.(*ListKnowledgeBasesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBaseManager_GetKnowledgeBase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKnowledgeBaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseManagerServer).GetKnowledgeBase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBaseManager_GetKnowledgeBase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseManagerServer).GetKnowledgeBase(ctx, req.(*GetKnowledgeBaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBaseManager_CreateKnowledgeBase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateKnowledgeBaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseManagerServer).CreateKnowledgeBase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBaseManager_CreateKnowledgeBase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseManagerServer).CreateKnowledgeBase(ctx, req.(*CreateKnowledgeBaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBaseManager_UpdateKnowledgeBase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateKnowledgeBaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseManagerServer).UpdateKnowledgeBase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBaseManager_UpdateKnowledgeBase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseManagerServer).UpdateKnowledgeBase(ctx, req.(*UpdateKnowledgeBaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBaseManager_DeleteKnowledgeBases_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteKnowledgeBasesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseManagerServer).DeleteKnowledgeBases(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBaseManager_DeleteKnowledgeBases_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseManagerServer).DeleteKnowledgeBases(ctx, req.(*DeleteKnowledgeBasesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBaseManager_ListConnectionTables_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListConnectionTablesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseManagerServer).ListConnectionTables(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBaseManager_ListConnectionTables_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseManagerServer).ListConnectionTables(ctx, req.(*ListConnectionTablesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBaseManager_ListDocuments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDocumentsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseManagerServer).ListDocuments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBaseManager_ListDocuments_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseManagerServer).ListDocuments(ctx, req.(*ListDocumentsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBaseManager_DisableDocument_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisableDocumentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseManagerServer).DisableDocument(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBaseManager_DisableDocument_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseManagerServer).DisableDocument(ctx, req.(*DisableDocumentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBaseManager_ListDocumentChunks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDocumentChunksReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseManagerServer).ListDocumentChunks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBaseManager_ListDocumentChunks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseManagerServer).ListDocumentChunks(ctx, req.(*ListDocumentChunksReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBaseManager_RetrieveKnowledgeBase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RetrieveKnowledgeBaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseManagerServer).RetrieveKnowledgeBase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBaseManager_RetrieveKnowledgeBase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseManagerServer).RetrieveKnowledgeBase(ctx, req.(*RetrieveKnowledgeBaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBaseManager_GetDocumentTree_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDocumentTreeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseManagerServer).GetDocumentTree(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBaseManager_GetDocumentTree_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseManagerServer).GetDocumentTree(ctx, req.(*GetDocumentTreeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBaseManager_SubmitFileToKnowledgeBase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitFileToKnowledgeBaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseManagerServer).SubmitFileToKnowledgeBase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBaseManager_SubmitFileToKnowledgeBase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseManagerServer).SubmitFileToKnowledgeBase(ctx, req.(*SubmitFileToKnowledgeBaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBaseManager_RemoveFileFromKnowledgeBase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveFileFromKnowledgeBaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseManagerServer).RemoveFileFromKnowledgeBase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBaseManager_RemoveFileFromKnowledgeBase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseManagerServer).RemoveFileFromKnowledgeBase(ctx, req.(*RemoveFileFromKnowledgeBaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBaseManager_UpdateKnowledgeBaseState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateKnowledgeBaseStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseManagerServer).UpdateKnowledgeBaseState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBaseManager_UpdateKnowledgeBaseState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseManagerServer).UpdateKnowledgeBaseState(ctx, req.(*UpdateKnowledgeBaseStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBaseManager_CreateChunk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateChunkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseManagerServer).CreateChunk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBaseManager_CreateChunk_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseManagerServer).CreateChunk(ctx, req.(*CreateChunkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBaseManager_DeleteChunk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteChunkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseManagerServer).DeleteChunk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBaseManager_DeleteChunk_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseManagerServer).DeleteChunk(ctx, req.(*DeleteChunkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBaseManager_UpdateChunk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateChunkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseManagerServer).UpdateChunk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBaseManager_UpdateChunk_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseManagerServer).UpdateChunk(ctx, req.(*UpdateChunkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBaseManager_PreviewDocumentProcess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreviewDocumentProcessReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseManagerServer).PreviewDocumentProcess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBaseManager_PreviewDocumentProcess_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseManagerServer).PreviewDocumentProcess(ctx, req.(*PreviewDocumentProcessReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBaseManager_SyncSubmitFilesToKnowledgeBase_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(SyncSubmitFilesToKnowledgeBaseReq)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(KnowledgeBaseManagerServer).SyncSubmitFilesToKnowledgeBase(m, &knowledgeBaseManagerSyncSubmitFilesToKnowledgeBaseServer{stream})
}

type KnowledgeBaseManager_SyncSubmitFilesToKnowledgeBaseServer interface {
	Send(*SyncSubmitFilesToKnowledgeBaseRsp) error
	grpc.ServerStream
}

type knowledgeBaseManagerSyncSubmitFilesToKnowledgeBaseServer struct {
	grpc.ServerStream
}

func (x *knowledgeBaseManagerSyncSubmitFilesToKnowledgeBaseServer) Send(m *SyncSubmitFilesToKnowledgeBaseRsp) error {
	return x.ServerStream.SendMsg(m)
}

func _KnowledgeBaseManager_RetrieveCrossKnowledgeBase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RetrieveCrossKnowledgeBaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseManagerServer).RetrieveCrossKnowledgeBase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBaseManager_RetrieveCrossKnowledgeBase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseManagerServer).RetrieveCrossKnowledgeBase(ctx, req.(*RetrieveCrossKnowledgeBaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBaseManager_SubmitChunksToKnowledgeBase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitChunksToKnowledgeBaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseManagerServer).SubmitChunksToKnowledgeBase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBaseManager_SubmitChunksToKnowledgeBase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseManagerServer).SubmitChunksToKnowledgeBase(ctx, req.(*SubmitChunksToKnowledgeBaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBaseManager_IsDocumentExistent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsDocumentExistentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseManagerServer).IsDocumentExistent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBaseManager_IsDocumentExistent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseManagerServer).IsDocumentExistent(ctx, req.(*IsDocumentExistentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBaseManager_CollectKnowledgeBaseStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CollectKnowledgeBaseStatsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseManagerServer).CollectKnowledgeBaseStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBaseManager_CollectKnowledgeBaseStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseManagerServer).CollectKnowledgeBaseStats(ctx, req.(*CollectKnowledgeBaseStatsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBaseManager_ShareKnowledgeBase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShareKnowledgeBaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseManagerServer).ShareKnowledgeBase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBaseManager_ShareKnowledgeBase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseManagerServer).ShareKnowledgeBase(ctx, req.(*ShareKnowledgeBaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBaseManager_PublishKnowledgeBase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PublishKnowledgeBaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseManagerServer).PublishKnowledgeBase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBaseManager_PublishKnowledgeBase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseManagerServer).PublishKnowledgeBase(ctx, req.(*PublishKnowledgeBaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBaseManager_TraceDocElements_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TraceDocElementsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseManagerServer).TraceDocElements(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBaseManager_TraceDocElements_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseManagerServer).TraceDocElements(ctx, req.(*TraceDocElementsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBaseManager_RetryDocumentProcess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RetryDocumentProcessReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseManagerServer).RetryDocumentProcess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBaseManager_RetryDocumentProcess_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseManagerServer).RetryDocumentProcess(ctx, req.(*RetryDocumentProcessReq))
	}
	return interceptor(ctx, in, info, handler)
}

// KnowledgeBaseManager_ServiceDesc is the grpc.ServiceDesc for KnowledgeBaseManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var KnowledgeBaseManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "proto.KnowledgeBaseManager",
	HandlerType: (*KnowledgeBaseManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListKnowledgeBases",
			Handler:    _KnowledgeBaseManager_ListKnowledgeBases_Handler,
		},
		{
			MethodName: "GetKnowledgeBase",
			Handler:    _KnowledgeBaseManager_GetKnowledgeBase_Handler,
		},
		{
			MethodName: "CreateKnowledgeBase",
			Handler:    _KnowledgeBaseManager_CreateKnowledgeBase_Handler,
		},
		{
			MethodName: "UpdateKnowledgeBase",
			Handler:    _KnowledgeBaseManager_UpdateKnowledgeBase_Handler,
		},
		{
			MethodName: "DeleteKnowledgeBases",
			Handler:    _KnowledgeBaseManager_DeleteKnowledgeBases_Handler,
		},
		{
			MethodName: "ListConnectionTables",
			Handler:    _KnowledgeBaseManager_ListConnectionTables_Handler,
		},
		{
			MethodName: "ListDocuments",
			Handler:    _KnowledgeBaseManager_ListDocuments_Handler,
		},
		{
			MethodName: "DisableDocument",
			Handler:    _KnowledgeBaseManager_DisableDocument_Handler,
		},
		{
			MethodName: "ListDocumentChunks",
			Handler:    _KnowledgeBaseManager_ListDocumentChunks_Handler,
		},
		{
			MethodName: "RetrieveKnowledgeBase",
			Handler:    _KnowledgeBaseManager_RetrieveKnowledgeBase_Handler,
		},
		{
			MethodName: "GetDocumentTree",
			Handler:    _KnowledgeBaseManager_GetDocumentTree_Handler,
		},
		{
			MethodName: "SubmitFileToKnowledgeBase",
			Handler:    _KnowledgeBaseManager_SubmitFileToKnowledgeBase_Handler,
		},
		{
			MethodName: "RemoveFileFromKnowledgeBase",
			Handler:    _KnowledgeBaseManager_RemoveFileFromKnowledgeBase_Handler,
		},
		{
			MethodName: "UpdateKnowledgeBaseState",
			Handler:    _KnowledgeBaseManager_UpdateKnowledgeBaseState_Handler,
		},
		{
			MethodName: "CreateChunk",
			Handler:    _KnowledgeBaseManager_CreateChunk_Handler,
		},
		{
			MethodName: "DeleteChunk",
			Handler:    _KnowledgeBaseManager_DeleteChunk_Handler,
		},
		{
			MethodName: "UpdateChunk",
			Handler:    _KnowledgeBaseManager_UpdateChunk_Handler,
		},
		{
			MethodName: "PreviewDocumentProcess",
			Handler:    _KnowledgeBaseManager_PreviewDocumentProcess_Handler,
		},
		{
			MethodName: "RetrieveCrossKnowledgeBase",
			Handler:    _KnowledgeBaseManager_RetrieveCrossKnowledgeBase_Handler,
		},
		{
			MethodName: "SubmitChunksToKnowledgeBase",
			Handler:    _KnowledgeBaseManager_SubmitChunksToKnowledgeBase_Handler,
		},
		{
			MethodName: "IsDocumentExistent",
			Handler:    _KnowledgeBaseManager_IsDocumentExistent_Handler,
		},
		{
			MethodName: "CollectKnowledgeBaseStats",
			Handler:    _KnowledgeBaseManager_CollectKnowledgeBaseStats_Handler,
		},
		{
			MethodName: "ShareKnowledgeBase",
			Handler:    _KnowledgeBaseManager_ShareKnowledgeBase_Handler,
		},
		{
			MethodName: "PublishKnowledgeBase",
			Handler:    _KnowledgeBaseManager_PublishKnowledgeBase_Handler,
		},
		{
			MethodName: "TraceDocElements",
			Handler:    _KnowledgeBaseManager_TraceDocElements_Handler,
		},
		{
			MethodName: "RetryDocumentProcess",
			Handler:    _KnowledgeBaseManager_RetryDocumentProcess_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SyncSubmitFilesToKnowledgeBase",
			Handler:       _KnowledgeBaseManager_SyncSubmitFilesToKnowledgeBase_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "proto/rpc_knowledge_base.proto",
}
