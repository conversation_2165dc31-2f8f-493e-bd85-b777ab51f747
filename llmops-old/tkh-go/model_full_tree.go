/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// FullTree FullTree
type FullTree struct {
	Children *[]FullTree `json:"children,omitempty"`
	Node *DocumentTree `json:"node,omitempty"`
}

// NewFullTree instantiates a new FullTree object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewFullTree() *FullTree {
	this := FullTree{}
	return &this
}

// NewFullTreeWithDefaults instantiates a new FullTree object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewFullTreeWithDefaults() *FullTree {
	this := FullTree{}
	return &this
}

// GetChildren returns the Children field value if set, zero value otherwise.
func (o *FullTree) GetChildren() []FullTree {
	if o == nil || o.Children == nil {
		var ret []FullTree
		return ret
	}
	return *o.Children
}

// GetChildrenOk returns a tuple with the Children field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *FullTree) GetChildrenOk() (*[]FullTree, bool) {
	if o == nil || o.Children == nil {
		return nil, false
	}
	return o.Children, true
}

// HasChildren returns a boolean if a field has been set.
func (o *FullTree) HasChildren() bool {
	if o != nil && o.Children != nil {
		return true
	}

	return false
}

// SetChildren gets a reference to the given []FullTree and assigns it to the Children field.
func (o *FullTree) SetChildren(v []FullTree) {
	o.Children = &v
}

// GetNode returns the Node field value if set, zero value otherwise.
func (o *FullTree) GetNode() DocumentTree {
	if o == nil || o.Node == nil {
		var ret DocumentTree
		return ret
	}
	return *o.Node
}

// GetNodeOk returns a tuple with the Node field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *FullTree) GetNodeOk() (*DocumentTree, bool) {
	if o == nil || o.Node == nil {
		return nil, false
	}
	return o.Node, true
}

// HasNode returns a boolean if a field has been set.
func (o *FullTree) HasNode() bool {
	if o != nil && o.Node != nil {
		return true
	}

	return false
}

// SetNode gets a reference to the given DocumentTree and assigns it to the Node field.
func (o *FullTree) SetNode(v DocumentTree) {
	o.Node = &v
}

func (o FullTree) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Children != nil {
		toSerialize["children"] = o.Children
	}
	if o.Node != nil {
		toSerialize["node"] = o.Node
	}
	return json.Marshal(toSerialize)
}

type NullableFullTree struct {
	value *FullTree
	isSet bool
}

func (v NullableFullTree) Get() *FullTree {
	return v.value
}

func (v *NullableFullTree) Set(val *FullTree) {
	v.value = val
	v.isSet = true
}

func (v NullableFullTree) IsSet() bool {
	return v.isSet
}

func (v *NullableFullTree) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableFullTree(val *FullTree) *NullableFullTree {
	return &NullableFullTree{value: val, isSet: true}
}

func (v NullableFullTree) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableFullTree) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
