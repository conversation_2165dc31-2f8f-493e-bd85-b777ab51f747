/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// IndTrendRequest IndTrendRequest
type IndTrendRequest struct {
	EndDate *string `json:"endDate,omitempty"`
	IndName *string `json:"indName,omitempty"`
	IndType *string `json:"indType,omitempty"`
	StartDate *string `json:"startDate,omitempty"`
}

// NewIndTrendRequest instantiates a new IndTrendRequest object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewIndTrendRequest() *IndTrendRequest {
	this := IndTrendRequest{}
	return &this
}

// NewIndTrendRequestWithDefaults instantiates a new IndTrendRequest object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewIndTrendRequestWithDefaults() *IndTrendRequest {
	this := IndTrendRequest{}
	return &this
}

// GetEndDate returns the EndDate field value if set, zero value otherwise.
func (o *IndTrendRequest) GetEndDate() string {
	if o == nil || o.EndDate == nil {
		var ret string
		return ret
	}
	return *o.EndDate
}

// GetEndDateOk returns a tuple with the EndDate field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *IndTrendRequest) GetEndDateOk() (*string, bool) {
	if o == nil || o.EndDate == nil {
		return nil, false
	}
	return o.EndDate, true
}

// HasEndDate returns a boolean if a field has been set.
func (o *IndTrendRequest) HasEndDate() bool {
	if o != nil && o.EndDate != nil {
		return true
	}

	return false
}

// SetEndDate gets a reference to the given string and assigns it to the EndDate field.
func (o *IndTrendRequest) SetEndDate(v string) {
	o.EndDate = &v
}

// GetIndName returns the IndName field value if set, zero value otherwise.
func (o *IndTrendRequest) GetIndName() string {
	if o == nil || o.IndName == nil {
		var ret string
		return ret
	}
	return *o.IndName
}

// GetIndNameOk returns a tuple with the IndName field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *IndTrendRequest) GetIndNameOk() (*string, bool) {
	if o == nil || o.IndName == nil {
		return nil, false
	}
	return o.IndName, true
}

// HasIndName returns a boolean if a field has been set.
func (o *IndTrendRequest) HasIndName() bool {
	if o != nil && o.IndName != nil {
		return true
	}

	return false
}

// SetIndName gets a reference to the given string and assigns it to the IndName field.
func (o *IndTrendRequest) SetIndName(v string) {
	o.IndName = &v
}

// GetIndType returns the IndType field value if set, zero value otherwise.
func (o *IndTrendRequest) GetIndType() string {
	if o == nil || o.IndType == nil {
		var ret string
		return ret
	}
	return *o.IndType
}

// GetIndTypeOk returns a tuple with the IndType field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *IndTrendRequest) GetIndTypeOk() (*string, bool) {
	if o == nil || o.IndType == nil {
		return nil, false
	}
	return o.IndType, true
}

// HasIndType returns a boolean if a field has been set.
func (o *IndTrendRequest) HasIndType() bool {
	if o != nil && o.IndType != nil {
		return true
	}

	return false
}

// SetIndType gets a reference to the given string and assigns it to the IndType field.
func (o *IndTrendRequest) SetIndType(v string) {
	o.IndType = &v
}

// GetStartDate returns the StartDate field value if set, zero value otherwise.
func (o *IndTrendRequest) GetStartDate() string {
	if o == nil || o.StartDate == nil {
		var ret string
		return ret
	}
	return *o.StartDate
}

// GetStartDateOk returns a tuple with the StartDate field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *IndTrendRequest) GetStartDateOk() (*string, bool) {
	if o == nil || o.StartDate == nil {
		return nil, false
	}
	return o.StartDate, true
}

// HasStartDate returns a boolean if a field has been set.
func (o *IndTrendRequest) HasStartDate() bool {
	if o != nil && o.StartDate != nil {
		return true
	}

	return false
}

// SetStartDate gets a reference to the given string and assigns it to the StartDate field.
func (o *IndTrendRequest) SetStartDate(v string) {
	o.StartDate = &v
}

func (o IndTrendRequest) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.EndDate != nil {
		toSerialize["endDate"] = o.EndDate
	}
	if o.IndName != nil {
		toSerialize["indName"] = o.IndName
	}
	if o.IndType != nil {
		toSerialize["indType"] = o.IndType
	}
	if o.StartDate != nil {
		toSerialize["startDate"] = o.StartDate
	}
	return json.Marshal(toSerialize)
}

type NullableIndTrendRequest struct {
	value *IndTrendRequest
	isSet bool
}

func (v NullableIndTrendRequest) Get() *IndTrendRequest {
	return v.value
}

func (v *NullableIndTrendRequest) Set(val *IndTrendRequest) {
	v.value = val
	v.isSet = true
}

func (v NullableIndTrendRequest) IsSet() bool {
	return v.isSet
}

func (v *NullableIndTrendRequest) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableIndTrendRequest(val *IndTrendRequest) *NullableIndTrendRequest {
	return &NullableIndTrendRequest{value: val, isSet: true}
}

func (v NullableIndTrendRequest) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableIndTrendRequest) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
