/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// UserDocTreeResponse UserDocTreeResponse
type UserDocTreeResponse struct {
	FinanceTree *[]Repository `json:"financeTree,omitempty"`
	InternetTree *[]Repository `json:"internetTree,omitempty"`
	LawTree *[]Repository `json:"lawTree,omitempty"`
	PrivateTree *[]Repository `json:"privateTree,omitempty"`
	PublicTree *[]Repository `json:"publicTree,omitempty"`
}

// NewUserDocTreeResponse instantiates a new UserDocTreeResponse object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewUserDocTreeResponse() *UserDocTreeResponse {
	this := UserDocTreeResponse{}
	return &this
}

// NewUserDocTreeResponseWithDefaults instantiates a new UserDocTreeResponse object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewUserDocTreeResponseWithDefaults() *UserDocTreeResponse {
	this := UserDocTreeResponse{}
	return &this
}

// GetFinanceTree returns the FinanceTree field value if set, zero value otherwise.
func (o *UserDocTreeResponse) GetFinanceTree() []Repository {
	if o == nil || o.FinanceTree == nil {
		var ret []Repository
		return ret
	}
	return *o.FinanceTree
}

// GetFinanceTreeOk returns a tuple with the FinanceTree field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserDocTreeResponse) GetFinanceTreeOk() (*[]Repository, bool) {
	if o == nil || o.FinanceTree == nil {
		return nil, false
	}
	return o.FinanceTree, true
}

// HasFinanceTree returns a boolean if a field has been set.
func (o *UserDocTreeResponse) HasFinanceTree() bool {
	if o != nil && o.FinanceTree != nil {
		return true
	}

	return false
}

// SetFinanceTree gets a reference to the given []Repository and assigns it to the FinanceTree field.
func (o *UserDocTreeResponse) SetFinanceTree(v []Repository) {
	o.FinanceTree = &v
}

// GetInternetTree returns the InternetTree field value if set, zero value otherwise.
func (o *UserDocTreeResponse) GetInternetTree() []Repository {
	if o == nil || o.InternetTree == nil {
		var ret []Repository
		return ret
	}
	return *o.InternetTree
}

// GetInternetTreeOk returns a tuple with the InternetTree field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserDocTreeResponse) GetInternetTreeOk() (*[]Repository, bool) {
	if o == nil || o.InternetTree == nil {
		return nil, false
	}
	return o.InternetTree, true
}

// HasInternetTree returns a boolean if a field has been set.
func (o *UserDocTreeResponse) HasInternetTree() bool {
	if o != nil && o.InternetTree != nil {
		return true
	}

	return false
}

// SetInternetTree gets a reference to the given []Repository and assigns it to the InternetTree field.
func (o *UserDocTreeResponse) SetInternetTree(v []Repository) {
	o.InternetTree = &v
}

// GetLawTree returns the LawTree field value if set, zero value otherwise.
func (o *UserDocTreeResponse) GetLawTree() []Repository {
	if o == nil || o.LawTree == nil {
		var ret []Repository
		return ret
	}
	return *o.LawTree
}

// GetLawTreeOk returns a tuple with the LawTree field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserDocTreeResponse) GetLawTreeOk() (*[]Repository, bool) {
	if o == nil || o.LawTree == nil {
		return nil, false
	}
	return o.LawTree, true
}

// HasLawTree returns a boolean if a field has been set.
func (o *UserDocTreeResponse) HasLawTree() bool {
	if o != nil && o.LawTree != nil {
		return true
	}

	return false
}

// SetLawTree gets a reference to the given []Repository and assigns it to the LawTree field.
func (o *UserDocTreeResponse) SetLawTree(v []Repository) {
	o.LawTree = &v
}

// GetPrivateTree returns the PrivateTree field value if set, zero value otherwise.
func (o *UserDocTreeResponse) GetPrivateTree() []Repository {
	if o == nil || o.PrivateTree == nil {
		var ret []Repository
		return ret
	}
	return *o.PrivateTree
}

// GetPrivateTreeOk returns a tuple with the PrivateTree field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserDocTreeResponse) GetPrivateTreeOk() (*[]Repository, bool) {
	if o == nil || o.PrivateTree == nil {
		return nil, false
	}
	return o.PrivateTree, true
}

// HasPrivateTree returns a boolean if a field has been set.
func (o *UserDocTreeResponse) HasPrivateTree() bool {
	if o != nil && o.PrivateTree != nil {
		return true
	}

	return false
}

// SetPrivateTree gets a reference to the given []Repository and assigns it to the PrivateTree field.
func (o *UserDocTreeResponse) SetPrivateTree(v []Repository) {
	o.PrivateTree = &v
}

// GetPublicTree returns the PublicTree field value if set, zero value otherwise.
func (o *UserDocTreeResponse) GetPublicTree() []Repository {
	if o == nil || o.PublicTree == nil {
		var ret []Repository
		return ret
	}
	return *o.PublicTree
}

// GetPublicTreeOk returns a tuple with the PublicTree field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserDocTreeResponse) GetPublicTreeOk() (*[]Repository, bool) {
	if o == nil || o.PublicTree == nil {
		return nil, false
	}
	return o.PublicTree, true
}

// HasPublicTree returns a boolean if a field has been set.
func (o *UserDocTreeResponse) HasPublicTree() bool {
	if o != nil && o.PublicTree != nil {
		return true
	}

	return false
}

// SetPublicTree gets a reference to the given []Repository and assigns it to the PublicTree field.
func (o *UserDocTreeResponse) SetPublicTree(v []Repository) {
	o.PublicTree = &v
}

func (o UserDocTreeResponse) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.FinanceTree != nil {
		toSerialize["financeTree"] = o.FinanceTree
	}
	if o.InternetTree != nil {
		toSerialize["internetTree"] = o.InternetTree
	}
	if o.LawTree != nil {
		toSerialize["lawTree"] = o.LawTree
	}
	if o.PrivateTree != nil {
		toSerialize["privateTree"] = o.PrivateTree
	}
	if o.PublicTree != nil {
		toSerialize["publicTree"] = o.PublicTree
	}
	return json.Marshal(toSerialize)
}

type NullableUserDocTreeResponse struct {
	value *UserDocTreeResponse
	isSet bool
}

func (v NullableUserDocTreeResponse) Get() *UserDocTreeResponse {
	return v.value
}

func (v *NullableUserDocTreeResponse) Set(val *UserDocTreeResponse) {
	v.value = val
	v.isSet = true
}

func (v NullableUserDocTreeResponse) IsSet() bool {
	return v.isSet
}

func (v *NullableUserDocTreeResponse) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableUserDocTreeResponse(val *UserDocTreeResponse) *NullableUserDocTreeResponse {
	return &NullableUserDocTreeResponse{value: val, isSet: true}
}

func (v NullableUserDocTreeResponse) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableUserDocTreeResponse) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
