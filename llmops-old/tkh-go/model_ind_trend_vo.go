/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// IndTrendVo IndTrendVO
type IndTrendVo struct {
	Index *float64 `json:"index,omitempty"`
	TradeDate *string `json:"tradeDate,omitempty"`
}

// NewIndTrendVo instantiates a new IndTrendVo object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewIndTrendVo() *IndTrendVo {
	this := IndTrendVo{}
	return &this
}

// NewIndTrendVoWithDefaults instantiates a new IndTrendVo object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewIndTrendVoWithDefaults() *IndTrendVo {
	this := IndTrendVo{}
	return &this
}

// GetIndex returns the Index field value if set, zero value otherwise.
func (o *IndTrendVo) GetIndex() float64 {
	if o == nil || o.Index == nil {
		var ret float64
		return ret
	}
	return *o.Index
}

// GetIndexOk returns a tuple with the Index field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *IndTrendVo) GetIndexOk() (*float64, bool) {
	if o == nil || o.Index == nil {
		return nil, false
	}
	return o.Index, true
}

// HasIndex returns a boolean if a field has been set.
func (o *IndTrendVo) HasIndex() bool {
	if o != nil && o.Index != nil {
		return true
	}

	return false
}

// SetIndex gets a reference to the given float64 and assigns it to the Index field.
func (o *IndTrendVo) SetIndex(v float64) {
	o.Index = &v
}

// GetTradeDate returns the TradeDate field value if set, zero value otherwise.
func (o *IndTrendVo) GetTradeDate() string {
	if o == nil || o.TradeDate == nil {
		var ret string
		return ret
	}
	return *o.TradeDate
}

// GetTradeDateOk returns a tuple with the TradeDate field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *IndTrendVo) GetTradeDateOk() (*string, bool) {
	if o == nil || o.TradeDate == nil {
		return nil, false
	}
	return o.TradeDate, true
}

// HasTradeDate returns a boolean if a field has been set.
func (o *IndTrendVo) HasTradeDate() bool {
	if o != nil && o.TradeDate != nil {
		return true
	}

	return false
}

// SetTradeDate gets a reference to the given string and assigns it to the TradeDate field.
func (o *IndTrendVo) SetTradeDate(v string) {
	o.TradeDate = &v
}

func (o IndTrendVo) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Index != nil {
		toSerialize["index"] = o.Index
	}
	if o.TradeDate != nil {
		toSerialize["tradeDate"] = o.TradeDate
	}
	return json.Marshal(toSerialize)
}

type NullableIndTrendVo struct {
	value *IndTrendVo
	isSet bool
}

func (v NullableIndTrendVo) Get() *IndTrendVo {
	return v.value
}

func (v *NullableIndTrendVo) Set(val *IndTrendVo) {
	v.value = val
	v.isSet = true
}

func (v NullableIndTrendVo) IsSet() bool {
	return v.isSet
}

func (v *NullableIndTrendVo) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableIndTrendVo(val *IndTrendVo) *NullableIndTrendVo {
	return &NullableIndTrendVo{value: val, isSet: true}
}

func (v NullableIndTrendVo) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableIndTrendVo) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
