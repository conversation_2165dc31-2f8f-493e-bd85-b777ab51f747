/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// ResultDocumentTree Result«DocumentTree»
type ResultDocumentTree struct {
	Code *int32 `json:"code,omitempty"`
	Data *DocumentTree `json:"data,omitempty"`
	Message *string `json:"message,omitempty"`
}

// NewResultDocumentTree instantiates a new ResultDocumentTree object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewResultDocumentTree() *ResultDocumentTree {
	this := ResultDocumentTree{}
	return &this
}

// NewResultDocumentTreeWithDefaults instantiates a new ResultDocumentTree object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewResultDocumentTreeWithDefaults() *ResultDocumentTree {
	this := ResultDocumentTree{}
	return &this
}

// GetCode returns the Code field value if set, zero value otherwise.
func (o *ResultDocumentTree) GetCode() int32 {
	if o == nil || o.Code == nil {
		var ret int32
		return ret
	}
	return *o.Code
}

// GetCodeOk returns a tuple with the Code field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ResultDocumentTree) GetCodeOk() (*int32, bool) {
	if o == nil || o.Code == nil {
		return nil, false
	}
	return o.Code, true
}

// HasCode returns a boolean if a field has been set.
func (o *ResultDocumentTree) HasCode() bool {
	if o != nil && o.Code != nil {
		return true
	}

	return false
}

// SetCode gets a reference to the given int32 and assigns it to the Code field.
func (o *ResultDocumentTree) SetCode(v int32) {
	o.Code = &v
}

// GetData returns the Data field value if set, zero value otherwise.
func (o *ResultDocumentTree) GetData() DocumentTree {
	if o == nil || o.Data == nil {
		var ret DocumentTree
		return ret
	}
	return *o.Data
}

// GetDataOk returns a tuple with the Data field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ResultDocumentTree) GetDataOk() (*DocumentTree, bool) {
	if o == nil || o.Data == nil {
		return nil, false
	}
	return o.Data, true
}

// HasData returns a boolean if a field has been set.
func (o *ResultDocumentTree) HasData() bool {
	if o != nil && o.Data != nil {
		return true
	}

	return false
}

// SetData gets a reference to the given DocumentTree and assigns it to the Data field.
func (o *ResultDocumentTree) SetData(v DocumentTree) {
	o.Data = &v
}

// GetMessage returns the Message field value if set, zero value otherwise.
func (o *ResultDocumentTree) GetMessage() string {
	if o == nil || o.Message == nil {
		var ret string
		return ret
	}
	return *o.Message
}

// GetMessageOk returns a tuple with the Message field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ResultDocumentTree) GetMessageOk() (*string, bool) {
	if o == nil || o.Message == nil {
		return nil, false
	}
	return o.Message, true
}

// HasMessage returns a boolean if a field has been set.
func (o *ResultDocumentTree) HasMessage() bool {
	if o != nil && o.Message != nil {
		return true
	}

	return false
}

// SetMessage gets a reference to the given string and assigns it to the Message field.
func (o *ResultDocumentTree) SetMessage(v string) {
	o.Message = &v
}

func (o ResultDocumentTree) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Code != nil {
		toSerialize["code"] = o.Code
	}
	if o.Data != nil {
		toSerialize["data"] = o.Data
	}
	if o.Message != nil {
		toSerialize["message"] = o.Message
	}
	return json.Marshal(toSerialize)
}

type NullableResultDocumentTree struct {
	value *ResultDocumentTree
	isSet bool
}

func (v NullableResultDocumentTree) Get() *ResultDocumentTree {
	return v.value
}

func (v *NullableResultDocumentTree) Set(val *ResultDocumentTree) {
	v.value = val
	v.isSet = true
}

func (v NullableResultDocumentTree) IsSet() bool {
	return v.isSet
}

func (v *NullableResultDocumentTree) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableResultDocumentTree(val *ResultDocumentTree) *NullableResultDocumentTree {
	return &NullableResultDocumentTree{value: val, isSet: true}
}

func (v NullableResultDocumentTree) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableResultDocumentTree) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
