/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// UserInfoDuiXiang UserInfo对象
type UserInfoDuiXiang struct {
	Admin *bool `json:"admin,omitempty"`
	CreateTime *string `json:"createTime,omitempty"`
	Id *int32 `json:"id,omitempty"`
	Password *string `json:"password,omitempty"`
	Status *int32 `json:"status,omitempty"`
	UpdateTime *string `json:"updateTime,omitempty"`
	Username *string `json:"username,omitempty"`
}

// NewUserInfoDuiXiang instantiates a new UserInfoDuiXiang object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewUserInfoDuiXiang() *UserInfoDuiXiang {
	this := UserInfoDuiXiang{}
	return &this
}

// NewUserInfoDuiXiangWithDefaults instantiates a new UserInfoDuiXiang object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewUserInfoDuiXiangWithDefaults() *UserInfoDuiXiang {
	this := UserInfoDuiXiang{}
	return &this
}

// GetAdmin returns the Admin field value if set, zero value otherwise.
func (o *UserInfoDuiXiang) GetAdmin() bool {
	if o == nil || o.Admin == nil {
		var ret bool
		return ret
	}
	return *o.Admin
}

// GetAdminOk returns a tuple with the Admin field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoDuiXiang) GetAdminOk() (*bool, bool) {
	if o == nil || o.Admin == nil {
		return nil, false
	}
	return o.Admin, true
}

// HasAdmin returns a boolean if a field has been set.
func (o *UserInfoDuiXiang) HasAdmin() bool {
	if o != nil && o.Admin != nil {
		return true
	}

	return false
}

// SetAdmin gets a reference to the given bool and assigns it to the Admin field.
func (o *UserInfoDuiXiang) SetAdmin(v bool) {
	o.Admin = &v
}

// GetCreateTime returns the CreateTime field value if set, zero value otherwise.
func (o *UserInfoDuiXiang) GetCreateTime() string {
	if o == nil || o.CreateTime == nil {
		var ret string
		return ret
	}
	return *o.CreateTime
}

// GetCreateTimeOk returns a tuple with the CreateTime field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoDuiXiang) GetCreateTimeOk() (*string, bool) {
	if o == nil || o.CreateTime == nil {
		return nil, false
	}
	return o.CreateTime, true
}

// HasCreateTime returns a boolean if a field has been set.
func (o *UserInfoDuiXiang) HasCreateTime() bool {
	if o != nil && o.CreateTime != nil {
		return true
	}

	return false
}

// SetCreateTime gets a reference to the given string and assigns it to the CreateTime field.
func (o *UserInfoDuiXiang) SetCreateTime(v string) {
	o.CreateTime = &v
}

// GetId returns the Id field value if set, zero value otherwise.
func (o *UserInfoDuiXiang) GetId() int32 {
	if o == nil || o.Id == nil {
		var ret int32
		return ret
	}
	return *o.Id
}

// GetIdOk returns a tuple with the Id field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoDuiXiang) GetIdOk() (*int32, bool) {
	if o == nil || o.Id == nil {
		return nil, false
	}
	return o.Id, true
}

// HasId returns a boolean if a field has been set.
func (o *UserInfoDuiXiang) HasId() bool {
	if o != nil && o.Id != nil {
		return true
	}

	return false
}

// SetId gets a reference to the given int32 and assigns it to the Id field.
func (o *UserInfoDuiXiang) SetId(v int32) {
	o.Id = &v
}

// GetPassword returns the Password field value if set, zero value otherwise.
func (o *UserInfoDuiXiang) GetPassword() string {
	if o == nil || o.Password == nil {
		var ret string
		return ret
	}
	return *o.Password
}

// GetPasswordOk returns a tuple with the Password field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoDuiXiang) GetPasswordOk() (*string, bool) {
	if o == nil || o.Password == nil {
		return nil, false
	}
	return o.Password, true
}

// HasPassword returns a boolean if a field has been set.
func (o *UserInfoDuiXiang) HasPassword() bool {
	if o != nil && o.Password != nil {
		return true
	}

	return false
}

// SetPassword gets a reference to the given string and assigns it to the Password field.
func (o *UserInfoDuiXiang) SetPassword(v string) {
	o.Password = &v
}

// GetStatus returns the Status field value if set, zero value otherwise.
func (o *UserInfoDuiXiang) GetStatus() int32 {
	if o == nil || o.Status == nil {
		var ret int32
		return ret
	}
	return *o.Status
}

// GetStatusOk returns a tuple with the Status field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoDuiXiang) GetStatusOk() (*int32, bool) {
	if o == nil || o.Status == nil {
		return nil, false
	}
	return o.Status, true
}

// HasStatus returns a boolean if a field has been set.
func (o *UserInfoDuiXiang) HasStatus() bool {
	if o != nil && o.Status != nil {
		return true
	}

	return false
}

// SetStatus gets a reference to the given int32 and assigns it to the Status field.
func (o *UserInfoDuiXiang) SetStatus(v int32) {
	o.Status = &v
}

// GetUpdateTime returns the UpdateTime field value if set, zero value otherwise.
func (o *UserInfoDuiXiang) GetUpdateTime() string {
	if o == nil || o.UpdateTime == nil {
		var ret string
		return ret
	}
	return *o.UpdateTime
}

// GetUpdateTimeOk returns a tuple with the UpdateTime field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoDuiXiang) GetUpdateTimeOk() (*string, bool) {
	if o == nil || o.UpdateTime == nil {
		return nil, false
	}
	return o.UpdateTime, true
}

// HasUpdateTime returns a boolean if a field has been set.
func (o *UserInfoDuiXiang) HasUpdateTime() bool {
	if o != nil && o.UpdateTime != nil {
		return true
	}

	return false
}

// SetUpdateTime gets a reference to the given string and assigns it to the UpdateTime field.
func (o *UserInfoDuiXiang) SetUpdateTime(v string) {
	o.UpdateTime = &v
}

// GetUsername returns the Username field value if set, zero value otherwise.
func (o *UserInfoDuiXiang) GetUsername() string {
	if o == nil || o.Username == nil {
		var ret string
		return ret
	}
	return *o.Username
}

// GetUsernameOk returns a tuple with the Username field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoDuiXiang) GetUsernameOk() (*string, bool) {
	if o == nil || o.Username == nil {
		return nil, false
	}
	return o.Username, true
}

// HasUsername returns a boolean if a field has been set.
func (o *UserInfoDuiXiang) HasUsername() bool {
	if o != nil && o.Username != nil {
		return true
	}

	return false
}

// SetUsername gets a reference to the given string and assigns it to the Username field.
func (o *UserInfoDuiXiang) SetUsername(v string) {
	o.Username = &v
}

func (o UserInfoDuiXiang) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Admin != nil {
		toSerialize["admin"] = o.Admin
	}
	if o.CreateTime != nil {
		toSerialize["createTime"] = o.CreateTime
	}
	if o.Id != nil {
		toSerialize["id"] = o.Id
	}
	if o.Password != nil {
		toSerialize["password"] = o.Password
	}
	if o.Status != nil {
		toSerialize["status"] = o.Status
	}
	if o.UpdateTime != nil {
		toSerialize["updateTime"] = o.UpdateTime
	}
	if o.Username != nil {
		toSerialize["username"] = o.Username
	}
	return json.Marshal(toSerialize)
}

type NullableUserInfoDuiXiang struct {
	value *UserInfoDuiXiang
	isSet bool
}

func (v NullableUserInfoDuiXiang) Get() *UserInfoDuiXiang {
	return v.value
}

func (v *NullableUserInfoDuiXiang) Set(val *UserInfoDuiXiang) {
	v.value = val
	v.isSet = true
}

func (v NullableUserInfoDuiXiang) IsSet() bool {
	return v.isSet
}

func (v *NullableUserInfoDuiXiang) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableUserInfoDuiXiang(val *UserInfoDuiXiang) *NullableUserInfoDuiXiang {
	return &NullableUserInfoDuiXiang{value: val, isSet: true}
}

func (v NullableUserInfoDuiXiang) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableUserInfoDuiXiang) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
