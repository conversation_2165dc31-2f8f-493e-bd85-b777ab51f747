/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// StockTrend StockTrend
type StockTrend struct {
	Close *float64 `json:"close,omitempty"`
	High *float64 `json:"high,omitempty"`
	Low *float64 `json:"low,omitempty"`
	Open *float64 `json:"open,omitempty"`
	TradeDate *string `json:"tradeDate,omitempty"`
	UpAndDown *int32 `json:"upAndDown,omitempty"`
	Volume *float64 `json:"volume,omitempty"`
}

// NewStockTrend instantiates a new StockTrend object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewStockTrend() *StockTrend {
	this := StockTrend{}
	return &this
}

// NewStockTrendWithDefaults instantiates a new StockTrend object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewStockTrendWithDefaults() *StockTrend {
	this := StockTrend{}
	return &this
}

// GetClose returns the Close field value if set, zero value otherwise.
func (o *StockTrend) GetClose() float64 {
	if o == nil || o.Close == nil {
		var ret float64
		return ret
	}
	return *o.Close
}

// GetCloseOk returns a tuple with the Close field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *StockTrend) GetCloseOk() (*float64, bool) {
	if o == nil || o.Close == nil {
		return nil, false
	}
	return o.Close, true
}

// HasClose returns a boolean if a field has been set.
func (o *StockTrend) HasClose() bool {
	if o != nil && o.Close != nil {
		return true
	}

	return false
}

// SetClose gets a reference to the given float64 and assigns it to the Close field.
func (o *StockTrend) SetClose(v float64) {
	o.Close = &v
}

// GetHigh returns the High field value if set, zero value otherwise.
func (o *StockTrend) GetHigh() float64 {
	if o == nil || o.High == nil {
		var ret float64
		return ret
	}
	return *o.High
}

// GetHighOk returns a tuple with the High field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *StockTrend) GetHighOk() (*float64, bool) {
	if o == nil || o.High == nil {
		return nil, false
	}
	return o.High, true
}

// HasHigh returns a boolean if a field has been set.
func (o *StockTrend) HasHigh() bool {
	if o != nil && o.High != nil {
		return true
	}

	return false
}

// SetHigh gets a reference to the given float64 and assigns it to the High field.
func (o *StockTrend) SetHigh(v float64) {
	o.High = &v
}

// GetLow returns the Low field value if set, zero value otherwise.
func (o *StockTrend) GetLow() float64 {
	if o == nil || o.Low == nil {
		var ret float64
		return ret
	}
	return *o.Low
}

// GetLowOk returns a tuple with the Low field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *StockTrend) GetLowOk() (*float64, bool) {
	if o == nil || o.Low == nil {
		return nil, false
	}
	return o.Low, true
}

// HasLow returns a boolean if a field has been set.
func (o *StockTrend) HasLow() bool {
	if o != nil && o.Low != nil {
		return true
	}

	return false
}

// SetLow gets a reference to the given float64 and assigns it to the Low field.
func (o *StockTrend) SetLow(v float64) {
	o.Low = &v
}

// GetOpen returns the Open field value if set, zero value otherwise.
func (o *StockTrend) GetOpen() float64 {
	if o == nil || o.Open == nil {
		var ret float64
		return ret
	}
	return *o.Open
}

// GetOpenOk returns a tuple with the Open field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *StockTrend) GetOpenOk() (*float64, bool) {
	if o == nil || o.Open == nil {
		return nil, false
	}
	return o.Open, true
}

// HasOpen returns a boolean if a field has been set.
func (o *StockTrend) HasOpen() bool {
	if o != nil && o.Open != nil {
		return true
	}

	return false
}

// SetOpen gets a reference to the given float64 and assigns it to the Open field.
func (o *StockTrend) SetOpen(v float64) {
	o.Open = &v
}

// GetTradeDate returns the TradeDate field value if set, zero value otherwise.
func (o *StockTrend) GetTradeDate() string {
	if o == nil || o.TradeDate == nil {
		var ret string
		return ret
	}
	return *o.TradeDate
}

// GetTradeDateOk returns a tuple with the TradeDate field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *StockTrend) GetTradeDateOk() (*string, bool) {
	if o == nil || o.TradeDate == nil {
		return nil, false
	}
	return o.TradeDate, true
}

// HasTradeDate returns a boolean if a field has been set.
func (o *StockTrend) HasTradeDate() bool {
	if o != nil && o.TradeDate != nil {
		return true
	}

	return false
}

// SetTradeDate gets a reference to the given string and assigns it to the TradeDate field.
func (o *StockTrend) SetTradeDate(v string) {
	o.TradeDate = &v
}

// GetUpAndDown returns the UpAndDown field value if set, zero value otherwise.
func (o *StockTrend) GetUpAndDown() int32 {
	if o == nil || o.UpAndDown == nil {
		var ret int32
		return ret
	}
	return *o.UpAndDown
}

// GetUpAndDownOk returns a tuple with the UpAndDown field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *StockTrend) GetUpAndDownOk() (*int32, bool) {
	if o == nil || o.UpAndDown == nil {
		return nil, false
	}
	return o.UpAndDown, true
}

// HasUpAndDown returns a boolean if a field has been set.
func (o *StockTrend) HasUpAndDown() bool {
	if o != nil && o.UpAndDown != nil {
		return true
	}

	return false
}

// SetUpAndDown gets a reference to the given int32 and assigns it to the UpAndDown field.
func (o *StockTrend) SetUpAndDown(v int32) {
	o.UpAndDown = &v
}

// GetVolume returns the Volume field value if set, zero value otherwise.
func (o *StockTrend) GetVolume() float64 {
	if o == nil || o.Volume == nil {
		var ret float64
		return ret
	}
	return *o.Volume
}

// GetVolumeOk returns a tuple with the Volume field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *StockTrend) GetVolumeOk() (*float64, bool) {
	if o == nil || o.Volume == nil {
		return nil, false
	}
	return o.Volume, true
}

// HasVolume returns a boolean if a field has been set.
func (o *StockTrend) HasVolume() bool {
	if o != nil && o.Volume != nil {
		return true
	}

	return false
}

// SetVolume gets a reference to the given float64 and assigns it to the Volume field.
func (o *StockTrend) SetVolume(v float64) {
	o.Volume = &v
}

func (o StockTrend) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Close != nil {
		toSerialize["close"] = o.Close
	}
	if o.High != nil {
		toSerialize["high"] = o.High
	}
	if o.Low != nil {
		toSerialize["low"] = o.Low
	}
	if o.Open != nil {
		toSerialize["open"] = o.Open
	}
	if o.TradeDate != nil {
		toSerialize["tradeDate"] = o.TradeDate
	}
	if o.UpAndDown != nil {
		toSerialize["upAndDown"] = o.UpAndDown
	}
	if o.Volume != nil {
		toSerialize["volume"] = o.Volume
	}
	return json.Marshal(toSerialize)
}

type NullableStockTrend struct {
	value *StockTrend
	isSet bool
}

func (v NullableStockTrend) Get() *StockTrend {
	return v.value
}

func (v *NullableStockTrend) Set(val *StockTrend) {
	v.value = val
	v.isSet = true
}

func (v NullableStockTrend) IsSet() bool {
	return v.isSet
}

func (v *NullableStockTrend) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableStockTrend(val *StockTrend) *NullableStockTrend {
	return &NullableStockTrend{value: val, isSet: true}
}

func (v NullableStockTrend) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableStockTrend) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
