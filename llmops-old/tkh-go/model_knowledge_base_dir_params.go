/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// KnowledgeBaseDirParams KnowledgeBaseDirParams
type KnowledgeBaseDirParams struct {
	DirIds *[]string `json:"dir_ids,omitempty"`
	KbId *string `json:"kb_id,omitempty"`
}

// NewKnowledgeBaseDirParams instantiates a new KnowledgeBaseDirParams object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewKnowledgeBaseDirParams() *KnowledgeBaseDirParams {
	this := KnowledgeBaseDirParams{}
	return &this
}

// NewKnowledgeBaseDirParamsWithDefaults instantiates a new KnowledgeBaseDirParams object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewKnowledgeBaseDirParamsWithDefaults() *KnowledgeBaseDirParams {
	this := KnowledgeBaseDirParams{}
	return &this
}

// GetDirIds returns the DirIds field value if set, zero value otherwise.
func (o *KnowledgeBaseDirParams) GetDirIds() []string {
	if o == nil || o.DirIds == nil {
		var ret []string
		return ret
	}
	return *o.DirIds
}

// GetDirIdsOk returns a tuple with the DirIds field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *KnowledgeBaseDirParams) GetDirIdsOk() (*[]string, bool) {
	if o == nil || o.DirIds == nil {
		return nil, false
	}
	return o.DirIds, true
}

// HasDirIds returns a boolean if a field has been set.
func (o *KnowledgeBaseDirParams) HasDirIds() bool {
	if o != nil && o.DirIds != nil {
		return true
	}

	return false
}

// SetDirIds gets a reference to the given []string and assigns it to the DirIds field.
func (o *KnowledgeBaseDirParams) SetDirIds(v []string) {
	o.DirIds = &v
}

// GetKbId returns the KbId field value if set, zero value otherwise.
func (o *KnowledgeBaseDirParams) GetKbId() string {
	if o == nil || o.KbId == nil {
		var ret string
		return ret
	}
	return *o.KbId
}

// GetKbIdOk returns a tuple with the KbId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *KnowledgeBaseDirParams) GetKbIdOk() (*string, bool) {
	if o == nil || o.KbId == nil {
		return nil, false
	}
	return o.KbId, true
}

// HasKbId returns a boolean if a field has been set.
func (o *KnowledgeBaseDirParams) HasKbId() bool {
	if o != nil && o.KbId != nil {
		return true
	}

	return false
}

// SetKbId gets a reference to the given string and assigns it to the KbId field.
func (o *KnowledgeBaseDirParams) SetKbId(v string) {
	o.KbId = &v
}

func (o KnowledgeBaseDirParams) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.DirIds != nil {
		toSerialize["dir_ids"] = o.DirIds
	}
	if o.KbId != nil {
		toSerialize["kb_id"] = o.KbId
	}
	return json.Marshal(toSerialize)
}

type NullableKnowledgeBaseDirParams struct {
	value *KnowledgeBaseDirParams
	isSet bool
}

func (v NullableKnowledgeBaseDirParams) Get() *KnowledgeBaseDirParams {
	return v.value
}

func (v *NullableKnowledgeBaseDirParams) Set(val *KnowledgeBaseDirParams) {
	v.value = val
	v.isSet = true
}

func (v NullableKnowledgeBaseDirParams) IsSet() bool {
	return v.isSet
}

func (v *NullableKnowledgeBaseDirParams) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableKnowledgeBaseDirParams(val *KnowledgeBaseDirParams) *NullableKnowledgeBaseDirParams {
	return &NullableKnowledgeBaseDirParams{value: val, isSet: true}
}

func (v NullableKnowledgeBaseDirParams) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableKnowledgeBaseDirParams) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
