/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// KnowledgeSource KnowledgeSource
type KnowledgeSource struct {
	// Kb Id，知识库id
	KbId *string `json:"kb_id,omitempty"`
	// Doc Id，文档id
	DocId *string `json:"doc_id,omitempty"`
	// Dir Id
	DirId *string `json:"dir_id,omitempty"`
}

// NewKnowledgeSource instantiates a new KnowledgeSource object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewKnowledgeSource() *KnowledgeSource {
	this := KnowledgeSource{}
	return &this
}

// NewKnowledgeSourceWithDefaults instantiates a new KnowledgeSource object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewKnowledgeSourceWithDefaults() *KnowledgeSource {
	this := KnowledgeSource{}
	return &this
}

// GetKbId returns the KbId field value if set, zero value otherwise.
func (o *KnowledgeSource) GetKbId() string {
	if o == nil || o.KbId == nil {
		var ret string
		return ret
	}
	return *o.KbId
}

// GetKbIdOk returns a tuple with the KbId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *KnowledgeSource) GetKbIdOk() (*string, bool) {
	if o == nil || o.KbId == nil {
		return nil, false
	}
	return o.KbId, true
}

// HasKbId returns a boolean if a field has been set.
func (o *KnowledgeSource) HasKbId() bool {
	if o != nil && o.KbId != nil {
		return true
	}

	return false
}

// SetKbId gets a reference to the given string and assigns it to the KbId field.
func (o *KnowledgeSource) SetKbId(v string) {
	o.KbId = &v
}

// GetDocId returns the DocId field value if set, zero value otherwise.
func (o *KnowledgeSource) GetDocId() string {
	if o == nil || o.DocId == nil {
		var ret string
		return ret
	}
	return *o.DocId
}

// GetDocIdOk returns a tuple with the DocId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *KnowledgeSource) GetDocIdOk() (*string, bool) {
	if o == nil || o.DocId == nil {
		return nil, false
	}
	return o.DocId, true
}

// HasDocId returns a boolean if a field has been set.
func (o *KnowledgeSource) HasDocId() bool {
	if o != nil && o.DocId != nil {
		return true
	}

	return false
}

// SetDocId gets a reference to the given string and assigns it to the DocId field.
func (o *KnowledgeSource) SetDocId(v string) {
	o.DocId = &v
}

// GetDirId returns the DirId field value if set, zero value otherwise.
func (o *KnowledgeSource) GetDirId() string {
	if o == nil || o.DirId == nil {
		var ret string
		return ret
	}
	return *o.DirId
}

// GetDirIdOk returns a tuple with the DirId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *KnowledgeSource) GetDirIdOk() (*string, bool) {
	if o == nil || o.DirId == nil {
		return nil, false
	}
	return o.DirId, true
}

// HasDirId returns a boolean if a field has been set.
func (o *KnowledgeSource) HasDirId() bool {
	if o != nil && o.DirId != nil {
		return true
	}

	return false
}

// SetDirId gets a reference to the given string and assigns it to the DirId field.
func (o *KnowledgeSource) SetDirId(v string) {
	o.DirId = &v
}

func (o KnowledgeSource) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.KbId != nil {
		toSerialize["kb_id"] = o.KbId
	}
	if o.DocId != nil {
		toSerialize["doc_id"] = o.DocId
	}
	if o.DirId != nil {
		toSerialize["dir_id"] = o.DirId
	}
	return json.Marshal(toSerialize)
}

type NullableKnowledgeSource struct {
	value *KnowledgeSource
	isSet bool
}

func (v NullableKnowledgeSource) Get() *KnowledgeSource {
	return v.value
}

func (v *NullableKnowledgeSource) Set(val *KnowledgeSource) {
	v.value = val
	v.isSet = true
}

func (v NullableKnowledgeSource) IsSet() bool {
	return v.isSet
}

func (v *NullableKnowledgeSource) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableKnowledgeSource(val *KnowledgeSource) *NullableKnowledgeSource {
	return &NullableKnowledgeSource{value: val, isSet: true}
}

func (v NullableKnowledgeSource) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableKnowledgeSource) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
