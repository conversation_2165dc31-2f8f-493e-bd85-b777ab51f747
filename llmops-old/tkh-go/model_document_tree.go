/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// DocumentTree DocumentTree，文档树实体
type DocumentTree struct {
	// 类别 枚举:1=目录 2=文件
	Category *int32 `json:"category,omitempty"`
	// 创建时间
	CreateTime *string `json:"createTime,omitempty"`
	// 上传文档业务归类 1=PDF 2=IMG 3=DOCX 4=PPTX 
	FileBusinessType *int32 `json:"fileBusinessType,omitempty"`
	// 上传文档contentType枚举
	FileContentType *int32 `json:"fileContentType,omitempty"`
	// 文件MD5值
	FileMd5 *string `json:"fileMd5,omitempty"`
	// 上传文档大小-byte
	FileSize *int64 `json:"fileSize,omitempty"`
	// 枚举 1= waiting 2=uploading 3=upload_Failed  4= uploaded 5=parsing、6=parse_failed 、7= parse_success 、8= edited  9=saved_failed 10 saved
	FileStatus *int32 `json:"fileStatus,omitempty"`
	// 上传文档后缀枚举值 1=PDF 2=PNG 3=JPEG 4=GIF 5=BMP 6=PPT 7=PPTX 8=DOC 9=DOCX 10=XLSX 11=CSV 12=NONE
	FileSuffix *int32 `json:"fileSuffix,omitempty"`
	// 关联上传任务id
	FileUploadTaskId *int64 `json:"fileUploadTaskId,omitempty"`
	// 文件uuid
	FileUuid *string `json:"fileUuid,omitempty"`
	// ID
	Id *int64 `json:"id,omitempty"`
	// 文件夹或者节点name
	Name *string `json:"name,omitempty"`
	// 父级ID
	ParentId *int64 `json:"parentId,omitempty"`
	// 路径
	Path *string `json:"path,omitempty"`
	// 仓库ID
	RepositoryId *int64 `json:"repositoryId,omitempty"`
	// 更新时间
	UpdateTime *string `json:"updateTime,omitempty"`
}

// NewDocumentTree instantiates a new DocumentTree object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDocumentTree() *DocumentTree {
	this := DocumentTree{}
	return &this
}

// NewDocumentTreeWithDefaults instantiates a new DocumentTree object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDocumentTreeWithDefaults() *DocumentTree {
	this := DocumentTree{}
	return &this
}

// GetCategory returns the Category field value if set, zero value otherwise.
func (o *DocumentTree) GetCategory() int32 {
	if o == nil || o.Category == nil {
		var ret int32
		return ret
	}
	return *o.Category
}

// GetCategoryOk returns a tuple with the Category field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentTree) GetCategoryOk() (*int32, bool) {
	if o == nil || o.Category == nil {
		return nil, false
	}
	return o.Category, true
}

// HasCategory returns a boolean if a field has been set.
func (o *DocumentTree) HasCategory() bool {
	if o != nil && o.Category != nil {
		return true
	}

	return false
}

// SetCategory gets a reference to the given int32 and assigns it to the Category field.
func (o *DocumentTree) SetCategory(v int32) {
	o.Category = &v
}

// GetCreateTime returns the CreateTime field value if set, zero value otherwise.
func (o *DocumentTree) GetCreateTime() string {
	if o == nil || o.CreateTime == nil {
		var ret string
		return ret
	}
	return *o.CreateTime
}

// GetCreateTimeOk returns a tuple with the CreateTime field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentTree) GetCreateTimeOk() (*string, bool) {
	if o == nil || o.CreateTime == nil {
		return nil, false
	}
	return o.CreateTime, true
}

// HasCreateTime returns a boolean if a field has been set.
func (o *DocumentTree) HasCreateTime() bool {
	if o != nil && o.CreateTime != nil {
		return true
	}

	return false
}

// SetCreateTime gets a reference to the given string and assigns it to the CreateTime field.
func (o *DocumentTree) SetCreateTime(v string) {
	o.CreateTime = &v
}

// GetFileBusinessType returns the FileBusinessType field value if set, zero value otherwise.
func (o *DocumentTree) GetFileBusinessType() int32 {
	if o == nil || o.FileBusinessType == nil {
		var ret int32
		return ret
	}
	return *o.FileBusinessType
}

// GetFileBusinessTypeOk returns a tuple with the FileBusinessType field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentTree) GetFileBusinessTypeOk() (*int32, bool) {
	if o == nil || o.FileBusinessType == nil {
		return nil, false
	}
	return o.FileBusinessType, true
}

// HasFileBusinessType returns a boolean if a field has been set.
func (o *DocumentTree) HasFileBusinessType() bool {
	if o != nil && o.FileBusinessType != nil {
		return true
	}

	return false
}

// SetFileBusinessType gets a reference to the given int32 and assigns it to the FileBusinessType field.
func (o *DocumentTree) SetFileBusinessType(v int32) {
	o.FileBusinessType = &v
}

// GetFileContentType returns the FileContentType field value if set, zero value otherwise.
func (o *DocumentTree) GetFileContentType() int32 {
	if o == nil || o.FileContentType == nil {
		var ret int32
		return ret
	}
	return *o.FileContentType
}

// GetFileContentTypeOk returns a tuple with the FileContentType field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentTree) GetFileContentTypeOk() (*int32, bool) {
	if o == nil || o.FileContentType == nil {
		return nil, false
	}
	return o.FileContentType, true
}

// HasFileContentType returns a boolean if a field has been set.
func (o *DocumentTree) HasFileContentType() bool {
	if o != nil && o.FileContentType != nil {
		return true
	}

	return false
}

// SetFileContentType gets a reference to the given int32 and assigns it to the FileContentType field.
func (o *DocumentTree) SetFileContentType(v int32) {
	o.FileContentType = &v
}

// GetFileMd5 returns the FileMd5 field value if set, zero value otherwise.
func (o *DocumentTree) GetFileMd5() string {
	if o == nil || o.FileMd5 == nil {
		var ret string
		return ret
	}
	return *o.FileMd5
}

// GetFileMd5Ok returns a tuple with the FileMd5 field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentTree) GetFileMd5Ok() (*string, bool) {
	if o == nil || o.FileMd5 == nil {
		return nil, false
	}
	return o.FileMd5, true
}

// HasFileMd5 returns a boolean if a field has been set.
func (o *DocumentTree) HasFileMd5() bool {
	if o != nil && o.FileMd5 != nil {
		return true
	}

	return false
}

// SetFileMd5 gets a reference to the given string and assigns it to the FileMd5 field.
func (o *DocumentTree) SetFileMd5(v string) {
	o.FileMd5 = &v
}

// GetFileSize returns the FileSize field value if set, zero value otherwise.
func (o *DocumentTree) GetFileSize() int64 {
	if o == nil || o.FileSize == nil {
		var ret int64
		return ret
	}
	return *o.FileSize
}

// GetFileSizeOk returns a tuple with the FileSize field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentTree) GetFileSizeOk() (*int64, bool) {
	if o == nil || o.FileSize == nil {
		return nil, false
	}
	return o.FileSize, true
}

// HasFileSize returns a boolean if a field has been set.
func (o *DocumentTree) HasFileSize() bool {
	if o != nil && o.FileSize != nil {
		return true
	}

	return false
}

// SetFileSize gets a reference to the given int64 and assigns it to the FileSize field.
func (o *DocumentTree) SetFileSize(v int64) {
	o.FileSize = &v
}

// GetFileStatus returns the FileStatus field value if set, zero value otherwise.
func (o *DocumentTree) GetFileStatus() int32 {
	if o == nil || o.FileStatus == nil {
		var ret int32
		return ret
	}
	return *o.FileStatus
}

// GetFileStatusOk returns a tuple with the FileStatus field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentTree) GetFileStatusOk() (*int32, bool) {
	if o == nil || o.FileStatus == nil {
		return nil, false
	}
	return o.FileStatus, true
}

// HasFileStatus returns a boolean if a field has been set.
func (o *DocumentTree) HasFileStatus() bool {
	if o != nil && o.FileStatus != nil {
		return true
	}

	return false
}

// SetFileStatus gets a reference to the given int32 and assigns it to the FileStatus field.
func (o *DocumentTree) SetFileStatus(v int32) {
	o.FileStatus = &v
}

// GetFileSuffix returns the FileSuffix field value if set, zero value otherwise.
func (o *DocumentTree) GetFileSuffix() int32 {
	if o == nil || o.FileSuffix == nil {
		var ret int32
		return ret
	}
	return *o.FileSuffix
}

// GetFileSuffixOk returns a tuple with the FileSuffix field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentTree) GetFileSuffixOk() (*int32, bool) {
	if o == nil || o.FileSuffix == nil {
		return nil, false
	}
	return o.FileSuffix, true
}

// HasFileSuffix returns a boolean if a field has been set.
func (o *DocumentTree) HasFileSuffix() bool {
	if o != nil && o.FileSuffix != nil {
		return true
	}

	return false
}

// SetFileSuffix gets a reference to the given int32 and assigns it to the FileSuffix field.
func (o *DocumentTree) SetFileSuffix(v int32) {
	o.FileSuffix = &v
}

// GetFileUploadTaskId returns the FileUploadTaskId field value if set, zero value otherwise.
func (o *DocumentTree) GetFileUploadTaskId() int64 {
	if o == nil || o.FileUploadTaskId == nil {
		var ret int64
		return ret
	}
	return *o.FileUploadTaskId
}

// GetFileUploadTaskIdOk returns a tuple with the FileUploadTaskId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentTree) GetFileUploadTaskIdOk() (*int64, bool) {
	if o == nil || o.FileUploadTaskId == nil {
		return nil, false
	}
	return o.FileUploadTaskId, true
}

// HasFileUploadTaskId returns a boolean if a field has been set.
func (o *DocumentTree) HasFileUploadTaskId() bool {
	if o != nil && o.FileUploadTaskId != nil {
		return true
	}

	return false
}

// SetFileUploadTaskId gets a reference to the given int64 and assigns it to the FileUploadTaskId field.
func (o *DocumentTree) SetFileUploadTaskId(v int64) {
	o.FileUploadTaskId = &v
}

// GetFileUuid returns the FileUuid field value if set, zero value otherwise.
func (o *DocumentTree) GetFileUuid() string {
	if o == nil || o.FileUuid == nil {
		var ret string
		return ret
	}
	return *o.FileUuid
}

// GetFileUuidOk returns a tuple with the FileUuid field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentTree) GetFileUuidOk() (*string, bool) {
	if o == nil || o.FileUuid == nil {
		return nil, false
	}
	return o.FileUuid, true
}

// HasFileUuid returns a boolean if a field has been set.
func (o *DocumentTree) HasFileUuid() bool {
	if o != nil && o.FileUuid != nil {
		return true
	}

	return false
}

// SetFileUuid gets a reference to the given string and assigns it to the FileUuid field.
func (o *DocumentTree) SetFileUuid(v string) {
	o.FileUuid = &v
}

// GetId returns the Id field value if set, zero value otherwise.
func (o *DocumentTree) GetId() int64 {
	if o == nil || o.Id == nil {
		var ret int64
		return ret
	}
	return *o.Id
}

// GetIdOk returns a tuple with the Id field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentTree) GetIdOk() (*int64, bool) {
	if o == nil || o.Id == nil {
		return nil, false
	}
	return o.Id, true
}

// HasId returns a boolean if a field has been set.
func (o *DocumentTree) HasId() bool {
	if o != nil && o.Id != nil {
		return true
	}

	return false
}

// SetId gets a reference to the given int64 and assigns it to the Id field.
func (o *DocumentTree) SetId(v int64) {
	o.Id = &v
}

// GetName returns the Name field value if set, zero value otherwise.
func (o *DocumentTree) GetName() string {
	if o == nil || o.Name == nil {
		var ret string
		return ret
	}
	return *o.Name
}

// GetNameOk returns a tuple with the Name field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentTree) GetNameOk() (*string, bool) {
	if o == nil || o.Name == nil {
		return nil, false
	}
	return o.Name, true
}

// HasName returns a boolean if a field has been set.
func (o *DocumentTree) HasName() bool {
	if o != nil && o.Name != nil {
		return true
	}

	return false
}

// SetName gets a reference to the given string and assigns it to the Name field.
func (o *DocumentTree) SetName(v string) {
	o.Name = &v
}

// GetParentId returns the ParentId field value if set, zero value otherwise.
func (o *DocumentTree) GetParentId() int64 {
	if o == nil || o.ParentId == nil {
		var ret int64
		return ret
	}
	return *o.ParentId
}

// GetParentIdOk returns a tuple with the ParentId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentTree) GetParentIdOk() (*int64, bool) {
	if o == nil || o.ParentId == nil {
		return nil, false
	}
	return o.ParentId, true
}

// HasParentId returns a boolean if a field has been set.
func (o *DocumentTree) HasParentId() bool {
	if o != nil && o.ParentId != nil {
		return true
	}

	return false
}

// SetParentId gets a reference to the given int64 and assigns it to the ParentId field.
func (o *DocumentTree) SetParentId(v int64) {
	o.ParentId = &v
}

// GetPath returns the Path field value if set, zero value otherwise.
func (o *DocumentTree) GetPath() string {
	if o == nil || o.Path == nil {
		var ret string
		return ret
	}
	return *o.Path
}

// GetPathOk returns a tuple with the Path field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentTree) GetPathOk() (*string, bool) {
	if o == nil || o.Path == nil {
		return nil, false
	}
	return o.Path, true
}

// HasPath returns a boolean if a field has been set.
func (o *DocumentTree) HasPath() bool {
	if o != nil && o.Path != nil {
		return true
	}

	return false
}

// SetPath gets a reference to the given string and assigns it to the Path field.
func (o *DocumentTree) SetPath(v string) {
	o.Path = &v
}

// GetRepositoryId returns the RepositoryId field value if set, zero value otherwise.
func (o *DocumentTree) GetRepositoryId() int64 {
	if o == nil || o.RepositoryId == nil {
		var ret int64
		return ret
	}
	return *o.RepositoryId
}

// GetRepositoryIdOk returns a tuple with the RepositoryId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentTree) GetRepositoryIdOk() (*int64, bool) {
	if o == nil || o.RepositoryId == nil {
		return nil, false
	}
	return o.RepositoryId, true
}

// HasRepositoryId returns a boolean if a field has been set.
func (o *DocumentTree) HasRepositoryId() bool {
	if o != nil && o.RepositoryId != nil {
		return true
	}

	return false
}

// SetRepositoryId gets a reference to the given int64 and assigns it to the RepositoryId field.
func (o *DocumentTree) SetRepositoryId(v int64) {
	o.RepositoryId = &v
}

// GetUpdateTime returns the UpdateTime field value if set, zero value otherwise.
func (o *DocumentTree) GetUpdateTime() string {
	if o == nil || o.UpdateTime == nil {
		var ret string
		return ret
	}
	return *o.UpdateTime
}

// GetUpdateTimeOk returns a tuple with the UpdateTime field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentTree) GetUpdateTimeOk() (*string, bool) {
	if o == nil || o.UpdateTime == nil {
		return nil, false
	}
	return o.UpdateTime, true
}

// HasUpdateTime returns a boolean if a field has been set.
func (o *DocumentTree) HasUpdateTime() bool {
	if o != nil && o.UpdateTime != nil {
		return true
	}

	return false
}

// SetUpdateTime gets a reference to the given string and assigns it to the UpdateTime field.
func (o *DocumentTree) SetUpdateTime(v string) {
	o.UpdateTime = &v
}

func (o DocumentTree) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Category != nil {
		toSerialize["category"] = o.Category
	}
	if o.CreateTime != nil {
		toSerialize["createTime"] = o.CreateTime
	}
	if o.FileBusinessType != nil {
		toSerialize["fileBusinessType"] = o.FileBusinessType
	}
	if o.FileContentType != nil {
		toSerialize["fileContentType"] = o.FileContentType
	}
	if o.FileMd5 != nil {
		toSerialize["fileMd5"] = o.FileMd5
	}
	if o.FileSize != nil {
		toSerialize["fileSize"] = o.FileSize
	}
	if o.FileStatus != nil {
		toSerialize["fileStatus"] = o.FileStatus
	}
	if o.FileSuffix != nil {
		toSerialize["fileSuffix"] = o.FileSuffix
	}
	if o.FileUploadTaskId != nil {
		toSerialize["fileUploadTaskId"] = o.FileUploadTaskId
	}
	if o.FileUuid != nil {
		toSerialize["fileUuid"] = o.FileUuid
	}
	if o.Id != nil {
		toSerialize["id"] = o.Id
	}
	if o.Name != nil {
		toSerialize["name"] = o.Name
	}
	if o.ParentId != nil {
		toSerialize["parentId"] = o.ParentId
	}
	if o.Path != nil {
		toSerialize["path"] = o.Path
	}
	if o.RepositoryId != nil {
		toSerialize["repositoryId"] = o.RepositoryId
	}
	if o.UpdateTime != nil {
		toSerialize["updateTime"] = o.UpdateTime
	}
	return json.Marshal(toSerialize)
}

type NullableDocumentTree struct {
	value *DocumentTree
	isSet bool
}

func (v NullableDocumentTree) Get() *DocumentTree {
	return v.value
}

func (v *NullableDocumentTree) Set(val *DocumentTree) {
	v.value = val
	v.isSet = true
}

func (v NullableDocumentTree) IsSet() bool {
	return v.isSet
}

func (v *NullableDocumentTree) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDocumentTree(val *DocumentTree) *NullableDocumentTree {
	return &NullableDocumentTree{value: val, isSet: true}
}

func (v NullableDocumentTree) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDocumentTree) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
