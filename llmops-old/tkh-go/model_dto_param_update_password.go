/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// DtoParamUpdatePassword DtoParamUpdatePassword
type DtoParamUpdatePassword struct {
	NewPassword *string `json:"newPassword,omitempty"`
	OldPassword *string `json:"oldPassword,omitempty"`
}

// NewDtoParamUpdatePassword instantiates a new DtoParamUpdatePassword object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDtoParamUpdatePassword() *DtoParamUpdatePassword {
	this := DtoParamUpdatePassword{}
	return &this
}

// NewDtoParamUpdatePasswordWithDefaults instantiates a new DtoParamUpdatePassword object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDtoParamUpdatePasswordWithDefaults() *DtoParamUpdatePassword {
	this := DtoParamUpdatePassword{}
	return &this
}

// GetNewPassword returns the NewPassword field value if set, zero value otherwise.
func (o *DtoParamUpdatePassword) GetNewPassword() string {
	if o == nil || o.NewPassword == nil {
		var ret string
		return ret
	}
	return *o.NewPassword
}

// GetNewPasswordOk returns a tuple with the NewPassword field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DtoParamUpdatePassword) GetNewPasswordOk() (*string, bool) {
	if o == nil || o.NewPassword == nil {
		return nil, false
	}
	return o.NewPassword, true
}

// HasNewPassword returns a boolean if a field has been set.
func (o *DtoParamUpdatePassword) HasNewPassword() bool {
	if o != nil && o.NewPassword != nil {
		return true
	}

	return false
}

// SetNewPassword gets a reference to the given string and assigns it to the NewPassword field.
func (o *DtoParamUpdatePassword) SetNewPassword(v string) {
	o.NewPassword = &v
}

// GetOldPassword returns the OldPassword field value if set, zero value otherwise.
func (o *DtoParamUpdatePassword) GetOldPassword() string {
	if o == nil || o.OldPassword == nil {
		var ret string
		return ret
	}
	return *o.OldPassword
}

// GetOldPasswordOk returns a tuple with the OldPassword field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DtoParamUpdatePassword) GetOldPasswordOk() (*string, bool) {
	if o == nil || o.OldPassword == nil {
		return nil, false
	}
	return o.OldPassword, true
}

// HasOldPassword returns a boolean if a field has been set.
func (o *DtoParamUpdatePassword) HasOldPassword() bool {
	if o != nil && o.OldPassword != nil {
		return true
	}

	return false
}

// SetOldPassword gets a reference to the given string and assigns it to the OldPassword field.
func (o *DtoParamUpdatePassword) SetOldPassword(v string) {
	o.OldPassword = &v
}

func (o DtoParamUpdatePassword) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.NewPassword != nil {
		toSerialize["newPassword"] = o.NewPassword
	}
	if o.OldPassword != nil {
		toSerialize["oldPassword"] = o.OldPassword
	}
	return json.Marshal(toSerialize)
}

type NullableDtoParamUpdatePassword struct {
	value *DtoParamUpdatePassword
	isSet bool
}

func (v NullableDtoParamUpdatePassword) Get() *DtoParamUpdatePassword {
	return v.value
}

func (v *NullableDtoParamUpdatePassword) Set(val *DtoParamUpdatePassword) {
	v.value = val
	v.isSet = true
}

func (v NullableDtoParamUpdatePassword) IsSet() bool {
	return v.isSet
}

func (v *NullableDtoParamUpdatePassword) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDtoParamUpdatePassword(val *DtoParamUpdatePassword) *NullableDtoParamUpdatePassword {
	return &NullableDtoParamUpdatePassword{value: val, isSet: true}
}

func (v NullableDtoParamUpdatePassword) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDtoParamUpdatePassword) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
