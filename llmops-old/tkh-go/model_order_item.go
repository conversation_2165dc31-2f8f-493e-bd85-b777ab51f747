/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// OrderItem OrderItem
type OrderItem struct {
	Asc *bool `json:"asc,omitempty"`
	Column *string `json:"column,omitempty"`
}

// NewOrderItem instantiates a new OrderItem object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewOrderItem() *OrderItem {
	this := OrderItem{}
	return &this
}

// NewOrderItemWithDefaults instantiates a new OrderItem object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewOrderItemWithDefaults() *OrderItem {
	this := OrderItem{}
	return &this
}

// GetAsc returns the Asc field value if set, zero value otherwise.
func (o *OrderItem) GetAsc() bool {
	if o == nil || o.Asc == nil {
		var ret bool
		return ret
	}
	return *o.Asc
}

// GetAscOk returns a tuple with the Asc field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *OrderItem) GetAscOk() (*bool, bool) {
	if o == nil || o.Asc == nil {
		return nil, false
	}
	return o.Asc, true
}

// HasAsc returns a boolean if a field has been set.
func (o *OrderItem) HasAsc() bool {
	if o != nil && o.Asc != nil {
		return true
	}

	return false
}

// SetAsc gets a reference to the given bool and assigns it to the Asc field.
func (o *OrderItem) SetAsc(v bool) {
	o.Asc = &v
}

// GetColumn returns the Column field value if set, zero value otherwise.
func (o *OrderItem) GetColumn() string {
	if o == nil || o.Column == nil {
		var ret string
		return ret
	}
	return *o.Column
}

// GetColumnOk returns a tuple with the Column field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *OrderItem) GetColumnOk() (*string, bool) {
	if o == nil || o.Column == nil {
		return nil, false
	}
	return o.Column, true
}

// HasColumn returns a boolean if a field has been set.
func (o *OrderItem) HasColumn() bool {
	if o != nil && o.Column != nil {
		return true
	}

	return false
}

// SetColumn gets a reference to the given string and assigns it to the Column field.
func (o *OrderItem) SetColumn(v string) {
	o.Column = &v
}

func (o OrderItem) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Asc != nil {
		toSerialize["asc"] = o.Asc
	}
	if o.Column != nil {
		toSerialize["column"] = o.Column
	}
	return json.Marshal(toSerialize)
}

type NullableOrderItem struct {
	value *OrderItem
	isSet bool
}

func (v NullableOrderItem) Get() *OrderItem {
	return v.value
}

func (v *NullableOrderItem) Set(val *OrderItem) {
	v.value = val
	v.isSet = true
}

func (v NullableOrderItem) IsSet() bool {
	return v.isSet
}

func (v *NullableOrderItem) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableOrderItem(val *OrderItem) *NullableOrderItem {
	return &NullableOrderItem{value: val, isSet: true}
}

func (v NullableOrderItem) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableOrderItem) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
