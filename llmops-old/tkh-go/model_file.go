/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// File File
type File struct {
	Absolute *bool `json:"absolute,omitempty"`
	AbsoluteFile *File `json:"absoluteFile,omitempty"`
	AbsolutePath *string `json:"absolutePath,omitempty"`
	CanonicalFile *File `json:"canonicalFile,omitempty"`
	CanonicalPath *string `json:"canonicalPath,omitempty"`
	Directory *bool `json:"directory,omitempty"`
	Executable *bool `json:"executable,omitempty"`
	File *bool `json:"file,omitempty"`
	FreeSpace *int64 `json:"freeSpace,omitempty"`
	Hidden *bool `json:"hidden,omitempty"`
	LastModified *int64 `json:"lastModified,omitempty"`
	Name *string `json:"name,omitempty"`
	Parent *string `json:"parent,omitempty"`
	ParentFile *File `json:"parentFile,omitempty"`
	Path *string `json:"path,omitempty"`
	Readable *bool `json:"readable,omitempty"`
	TotalSpace *int64 `json:"totalSpace,omitempty"`
	UsableSpace *int64 `json:"usableSpace,omitempty"`
	Writable *bool `json:"writable,omitempty"`
}

// NewFile instantiates a new File object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewFile() *File {
	this := File{}
	return &this
}

// NewFileWithDefaults instantiates a new File object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewFileWithDefaults() *File {
	this := File{}
	return &this
}

// GetAbsolute returns the Absolute field value if set, zero value otherwise.
func (o *File) GetAbsolute() bool {
	if o == nil || o.Absolute == nil {
		var ret bool
		return ret
	}
	return *o.Absolute
}

// GetAbsoluteOk returns a tuple with the Absolute field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *File) GetAbsoluteOk() (*bool, bool) {
	if o == nil || o.Absolute == nil {
		return nil, false
	}
	return o.Absolute, true
}

// HasAbsolute returns a boolean if a field has been set.
func (o *File) HasAbsolute() bool {
	if o != nil && o.Absolute != nil {
		return true
	}

	return false
}

// SetAbsolute gets a reference to the given bool and assigns it to the Absolute field.
func (o *File) SetAbsolute(v bool) {
	o.Absolute = &v
}

// GetAbsoluteFile returns the AbsoluteFile field value if set, zero value otherwise.
func (o *File) GetAbsoluteFile() File {
	if o == nil || o.AbsoluteFile == nil {
		var ret File
		return ret
	}
	return *o.AbsoluteFile
}

// GetAbsoluteFileOk returns a tuple with the AbsoluteFile field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *File) GetAbsoluteFileOk() (*File, bool) {
	if o == nil || o.AbsoluteFile == nil {
		return nil, false
	}
	return o.AbsoluteFile, true
}

// HasAbsoluteFile returns a boolean if a field has been set.
func (o *File) HasAbsoluteFile() bool {
	if o != nil && o.AbsoluteFile != nil {
		return true
	}

	return false
}

// SetAbsoluteFile gets a reference to the given File and assigns it to the AbsoluteFile field.
func (o *File) SetAbsoluteFile(v File) {
	o.AbsoluteFile = &v
}

// GetAbsolutePath returns the AbsolutePath field value if set, zero value otherwise.
func (o *File) GetAbsolutePath() string {
	if o == nil || o.AbsolutePath == nil {
		var ret string
		return ret
	}
	return *o.AbsolutePath
}

// GetAbsolutePathOk returns a tuple with the AbsolutePath field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *File) GetAbsolutePathOk() (*string, bool) {
	if o == nil || o.AbsolutePath == nil {
		return nil, false
	}
	return o.AbsolutePath, true
}

// HasAbsolutePath returns a boolean if a field has been set.
func (o *File) HasAbsolutePath() bool {
	if o != nil && o.AbsolutePath != nil {
		return true
	}

	return false
}

// SetAbsolutePath gets a reference to the given string and assigns it to the AbsolutePath field.
func (o *File) SetAbsolutePath(v string) {
	o.AbsolutePath = &v
}

// GetCanonicalFile returns the CanonicalFile field value if set, zero value otherwise.
func (o *File) GetCanonicalFile() File {
	if o == nil || o.CanonicalFile == nil {
		var ret File
		return ret
	}
	return *o.CanonicalFile
}

// GetCanonicalFileOk returns a tuple with the CanonicalFile field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *File) GetCanonicalFileOk() (*File, bool) {
	if o == nil || o.CanonicalFile == nil {
		return nil, false
	}
	return o.CanonicalFile, true
}

// HasCanonicalFile returns a boolean if a field has been set.
func (o *File) HasCanonicalFile() bool {
	if o != nil && o.CanonicalFile != nil {
		return true
	}

	return false
}

// SetCanonicalFile gets a reference to the given File and assigns it to the CanonicalFile field.
func (o *File) SetCanonicalFile(v File) {
	o.CanonicalFile = &v
}

// GetCanonicalPath returns the CanonicalPath field value if set, zero value otherwise.
func (o *File) GetCanonicalPath() string {
	if o == nil || o.CanonicalPath == nil {
		var ret string
		return ret
	}
	return *o.CanonicalPath
}

// GetCanonicalPathOk returns a tuple with the CanonicalPath field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *File) GetCanonicalPathOk() (*string, bool) {
	if o == nil || o.CanonicalPath == nil {
		return nil, false
	}
	return o.CanonicalPath, true
}

// HasCanonicalPath returns a boolean if a field has been set.
func (o *File) HasCanonicalPath() bool {
	if o != nil && o.CanonicalPath != nil {
		return true
	}

	return false
}

// SetCanonicalPath gets a reference to the given string and assigns it to the CanonicalPath field.
func (o *File) SetCanonicalPath(v string) {
	o.CanonicalPath = &v
}

// GetDirectory returns the Directory field value if set, zero value otherwise.
func (o *File) GetDirectory() bool {
	if o == nil || o.Directory == nil {
		var ret bool
		return ret
	}
	return *o.Directory
}

// GetDirectoryOk returns a tuple with the Directory field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *File) GetDirectoryOk() (*bool, bool) {
	if o == nil || o.Directory == nil {
		return nil, false
	}
	return o.Directory, true
}

// HasDirectory returns a boolean if a field has been set.
func (o *File) HasDirectory() bool {
	if o != nil && o.Directory != nil {
		return true
	}

	return false
}

// SetDirectory gets a reference to the given bool and assigns it to the Directory field.
func (o *File) SetDirectory(v bool) {
	o.Directory = &v
}

// GetExecutable returns the Executable field value if set, zero value otherwise.
func (o *File) GetExecutable() bool {
	if o == nil || o.Executable == nil {
		var ret bool
		return ret
	}
	return *o.Executable
}

// GetExecutableOk returns a tuple with the Executable field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *File) GetExecutableOk() (*bool, bool) {
	if o == nil || o.Executable == nil {
		return nil, false
	}
	return o.Executable, true
}

// HasExecutable returns a boolean if a field has been set.
func (o *File) HasExecutable() bool {
	if o != nil && o.Executable != nil {
		return true
	}

	return false
}

// SetExecutable gets a reference to the given bool and assigns it to the Executable field.
func (o *File) SetExecutable(v bool) {
	o.Executable = &v
}

// GetFile returns the File field value if set, zero value otherwise.
func (o *File) GetFile() bool {
	if o == nil || o.File == nil {
		var ret bool
		return ret
	}
	return *o.File
}

// GetFileOk returns a tuple with the File field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *File) GetFileOk() (*bool, bool) {
	if o == nil || o.File == nil {
		return nil, false
	}
	return o.File, true
}

// HasFile returns a boolean if a field has been set.
func (o *File) HasFile() bool {
	if o != nil && o.File != nil {
		return true
	}

	return false
}

// SetFile gets a reference to the given bool and assigns it to the File field.
func (o *File) SetFile(v bool) {
	o.File = &v
}

// GetFreeSpace returns the FreeSpace field value if set, zero value otherwise.
func (o *File) GetFreeSpace() int64 {
	if o == nil || o.FreeSpace == nil {
		var ret int64
		return ret
	}
	return *o.FreeSpace
}

// GetFreeSpaceOk returns a tuple with the FreeSpace field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *File) GetFreeSpaceOk() (*int64, bool) {
	if o == nil || o.FreeSpace == nil {
		return nil, false
	}
	return o.FreeSpace, true
}

// HasFreeSpace returns a boolean if a field has been set.
func (o *File) HasFreeSpace() bool {
	if o != nil && o.FreeSpace != nil {
		return true
	}

	return false
}

// SetFreeSpace gets a reference to the given int64 and assigns it to the FreeSpace field.
func (o *File) SetFreeSpace(v int64) {
	o.FreeSpace = &v
}

// GetHidden returns the Hidden field value if set, zero value otherwise.
func (o *File) GetHidden() bool {
	if o == nil || o.Hidden == nil {
		var ret bool
		return ret
	}
	return *o.Hidden
}

// GetHiddenOk returns a tuple with the Hidden field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *File) GetHiddenOk() (*bool, bool) {
	if o == nil || o.Hidden == nil {
		return nil, false
	}
	return o.Hidden, true
}

// HasHidden returns a boolean if a field has been set.
func (o *File) HasHidden() bool {
	if o != nil && o.Hidden != nil {
		return true
	}

	return false
}

// SetHidden gets a reference to the given bool and assigns it to the Hidden field.
func (o *File) SetHidden(v bool) {
	o.Hidden = &v
}

// GetLastModified returns the LastModified field value if set, zero value otherwise.
func (o *File) GetLastModified() int64 {
	if o == nil || o.LastModified == nil {
		var ret int64
		return ret
	}
	return *o.LastModified
}

// GetLastModifiedOk returns a tuple with the LastModified field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *File) GetLastModifiedOk() (*int64, bool) {
	if o == nil || o.LastModified == nil {
		return nil, false
	}
	return o.LastModified, true
}

// HasLastModified returns a boolean if a field has been set.
func (o *File) HasLastModified() bool {
	if o != nil && o.LastModified != nil {
		return true
	}

	return false
}

// SetLastModified gets a reference to the given int64 and assigns it to the LastModified field.
func (o *File) SetLastModified(v int64) {
	o.LastModified = &v
}

// GetName returns the Name field value if set, zero value otherwise.
func (o *File) GetName() string {
	if o == nil || o.Name == nil {
		var ret string
		return ret
	}
	return *o.Name
}

// GetNameOk returns a tuple with the Name field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *File) GetNameOk() (*string, bool) {
	if o == nil || o.Name == nil {
		return nil, false
	}
	return o.Name, true
}

// HasName returns a boolean if a field has been set.
func (o *File) HasName() bool {
	if o != nil && o.Name != nil {
		return true
	}

	return false
}

// SetName gets a reference to the given string and assigns it to the Name field.
func (o *File) SetName(v string) {
	o.Name = &v
}

// GetParent returns the Parent field value if set, zero value otherwise.
func (o *File) GetParent() string {
	if o == nil || o.Parent == nil {
		var ret string
		return ret
	}
	return *o.Parent
}

// GetParentOk returns a tuple with the Parent field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *File) GetParentOk() (*string, bool) {
	if o == nil || o.Parent == nil {
		return nil, false
	}
	return o.Parent, true
}

// HasParent returns a boolean if a field has been set.
func (o *File) HasParent() bool {
	if o != nil && o.Parent != nil {
		return true
	}

	return false
}

// SetParent gets a reference to the given string and assigns it to the Parent field.
func (o *File) SetParent(v string) {
	o.Parent = &v
}

// GetParentFile returns the ParentFile field value if set, zero value otherwise.
func (o *File) GetParentFile() File {
	if o == nil || o.ParentFile == nil {
		var ret File
		return ret
	}
	return *o.ParentFile
}

// GetParentFileOk returns a tuple with the ParentFile field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *File) GetParentFileOk() (*File, bool) {
	if o == nil || o.ParentFile == nil {
		return nil, false
	}
	return o.ParentFile, true
}

// HasParentFile returns a boolean if a field has been set.
func (o *File) HasParentFile() bool {
	if o != nil && o.ParentFile != nil {
		return true
	}

	return false
}

// SetParentFile gets a reference to the given File and assigns it to the ParentFile field.
func (o *File) SetParentFile(v File) {
	o.ParentFile = &v
}

// GetPath returns the Path field value if set, zero value otherwise.
func (o *File) GetPath() string {
	if o == nil || o.Path == nil {
		var ret string
		return ret
	}
	return *o.Path
}

// GetPathOk returns a tuple with the Path field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *File) GetPathOk() (*string, bool) {
	if o == nil || o.Path == nil {
		return nil, false
	}
	return o.Path, true
}

// HasPath returns a boolean if a field has been set.
func (o *File) HasPath() bool {
	if o != nil && o.Path != nil {
		return true
	}

	return false
}

// SetPath gets a reference to the given string and assigns it to the Path field.
func (o *File) SetPath(v string) {
	o.Path = &v
}

// GetReadable returns the Readable field value if set, zero value otherwise.
func (o *File) GetReadable() bool {
	if o == nil || o.Readable == nil {
		var ret bool
		return ret
	}
	return *o.Readable
}

// GetReadableOk returns a tuple with the Readable field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *File) GetReadableOk() (*bool, bool) {
	if o == nil || o.Readable == nil {
		return nil, false
	}
	return o.Readable, true
}

// HasReadable returns a boolean if a field has been set.
func (o *File) HasReadable() bool {
	if o != nil && o.Readable != nil {
		return true
	}

	return false
}

// SetReadable gets a reference to the given bool and assigns it to the Readable field.
func (o *File) SetReadable(v bool) {
	o.Readable = &v
}

// GetTotalSpace returns the TotalSpace field value if set, zero value otherwise.
func (o *File) GetTotalSpace() int64 {
	if o == nil || o.TotalSpace == nil {
		var ret int64
		return ret
	}
	return *o.TotalSpace
}

// GetTotalSpaceOk returns a tuple with the TotalSpace field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *File) GetTotalSpaceOk() (*int64, bool) {
	if o == nil || o.TotalSpace == nil {
		return nil, false
	}
	return o.TotalSpace, true
}

// HasTotalSpace returns a boolean if a field has been set.
func (o *File) HasTotalSpace() bool {
	if o != nil && o.TotalSpace != nil {
		return true
	}

	return false
}

// SetTotalSpace gets a reference to the given int64 and assigns it to the TotalSpace field.
func (o *File) SetTotalSpace(v int64) {
	o.TotalSpace = &v
}

// GetUsableSpace returns the UsableSpace field value if set, zero value otherwise.
func (o *File) GetUsableSpace() int64 {
	if o == nil || o.UsableSpace == nil {
		var ret int64
		return ret
	}
	return *o.UsableSpace
}

// GetUsableSpaceOk returns a tuple with the UsableSpace field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *File) GetUsableSpaceOk() (*int64, bool) {
	if o == nil || o.UsableSpace == nil {
		return nil, false
	}
	return o.UsableSpace, true
}

// HasUsableSpace returns a boolean if a field has been set.
func (o *File) HasUsableSpace() bool {
	if o != nil && o.UsableSpace != nil {
		return true
	}

	return false
}

// SetUsableSpace gets a reference to the given int64 and assigns it to the UsableSpace field.
func (o *File) SetUsableSpace(v int64) {
	o.UsableSpace = &v
}

// GetWritable returns the Writable field value if set, zero value otherwise.
func (o *File) GetWritable() bool {
	if o == nil || o.Writable == nil {
		var ret bool
		return ret
	}
	return *o.Writable
}

// GetWritableOk returns a tuple with the Writable field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *File) GetWritableOk() (*bool, bool) {
	if o == nil || o.Writable == nil {
		return nil, false
	}
	return o.Writable, true
}

// HasWritable returns a boolean if a field has been set.
func (o *File) HasWritable() bool {
	if o != nil && o.Writable != nil {
		return true
	}

	return false
}

// SetWritable gets a reference to the given bool and assigns it to the Writable field.
func (o *File) SetWritable(v bool) {
	o.Writable = &v
}

func (o File) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Absolute != nil {
		toSerialize["absolute"] = o.Absolute
	}
	if o.AbsoluteFile != nil {
		toSerialize["absoluteFile"] = o.AbsoluteFile
	}
	if o.AbsolutePath != nil {
		toSerialize["absolutePath"] = o.AbsolutePath
	}
	if o.CanonicalFile != nil {
		toSerialize["canonicalFile"] = o.CanonicalFile
	}
	if o.CanonicalPath != nil {
		toSerialize["canonicalPath"] = o.CanonicalPath
	}
	if o.Directory != nil {
		toSerialize["directory"] = o.Directory
	}
	if o.Executable != nil {
		toSerialize["executable"] = o.Executable
	}
	if o.File != nil {
		toSerialize["file"] = o.File
	}
	if o.FreeSpace != nil {
		toSerialize["freeSpace"] = o.FreeSpace
	}
	if o.Hidden != nil {
		toSerialize["hidden"] = o.Hidden
	}
	if o.LastModified != nil {
		toSerialize["lastModified"] = o.LastModified
	}
	if o.Name != nil {
		toSerialize["name"] = o.Name
	}
	if o.Parent != nil {
		toSerialize["parent"] = o.Parent
	}
	if o.ParentFile != nil {
		toSerialize["parentFile"] = o.ParentFile
	}
	if o.Path != nil {
		toSerialize["path"] = o.Path
	}
	if o.Readable != nil {
		toSerialize["readable"] = o.Readable
	}
	if o.TotalSpace != nil {
		toSerialize["totalSpace"] = o.TotalSpace
	}
	if o.UsableSpace != nil {
		toSerialize["usableSpace"] = o.UsableSpace
	}
	if o.Writable != nil {
		toSerialize["writable"] = o.Writable
	}
	return json.Marshal(toSerialize)
}

type NullableFile struct {
	value *File
	isSet bool
}

func (v NullableFile) Get() *File {
	return v.value
}

func (v *NullableFile) Set(val *File) {
	v.value = val
	v.isSet = true
}

func (v NullableFile) IsSet() bool {
	return v.isSet
}

func (v *NullableFile) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableFile(val *File) *NullableFile {
	return &NullableFile{value: val, isSet: true}
}

func (v NullableFile) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableFile) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
