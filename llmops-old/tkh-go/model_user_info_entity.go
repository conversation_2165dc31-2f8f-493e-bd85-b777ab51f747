/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
	"time"
)

// UserInfoEntity UserInfoEntity
type UserInfoEntity struct {
	// 创建时间
	CreateTime *time.Time `json:"createTime,omitempty"`
	// 创建人
	CreateUser *string `json:"createUser,omitempty"`
	// 显示工作区
	DisplayWorkspace *string `json:"displayWorkspace,omitempty"`
	Email *string `json:"email,omitempty"`
	Id *string `json:"id,omitempty"`
	// 是否系统管理员
	IsAdmin *bool `json:"isAdmin,omitempty"`
	// 是否工作区管理员
	IsWorkspaceAdmin *bool `json:"isWorkspaceAdmin,omitempty"`
	// 语言环境
	Locale *string `json:"locale,omitempty"`
	// 用户机构
	OrganId *string `json:"organId,omitempty"`
	// 用户所属机构名称，前端展示需要
	OrganName *string `json:"organName,omitempty"`
	// 所属工作组
	OwnGroupNames *[]string `json:"ownGroupNames,omitempty"`
	// 管理的工作区列表
	OwnWorkspaceIds *[]string `json:"ownWorkspaceIds,omitempty"`
	Password *string `json:"password,omitempty"`
	RealName *string `json:"realName,omitempty"`
	// 用户角色列表，或组内角色
	RoleIds *[]string `json:"roleIds,omitempty"`
	// 用户所属角色ID和角色中文名对应关系Map，前端展示需要
	RoleInfoMap *map[string]string `json:"roleInfoMap,omitempty"`
	// 是否启用
	Status *string `json:"status,omitempty"`
	Telephone *string `json:"telephone,omitempty"`
	// 更新时间
	UpdateTime *time.Time `json:"updateTime,omitempty"`
	// 修改人
	UpdateUser *string `json:"updateUser,omitempty"`
	Username *string `json:"username,omitempty"`
	// 工作区名称
	WorkspaceName *string `json:"workspaceName,omitempty"`
	// 工作区角色
	WorkspaceRoleIds *map[string][]string `json:"workspaceRoleIds,omitempty"`
}

// NewUserInfoEntity instantiates a new UserInfoEntity object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewUserInfoEntity() *UserInfoEntity {
	this := UserInfoEntity{}
	return &this
}

// NewUserInfoEntityWithDefaults instantiates a new UserInfoEntity object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewUserInfoEntityWithDefaults() *UserInfoEntity {
	this := UserInfoEntity{}
	return &this
}

// GetCreateTime returns the CreateTime field value if set, zero value otherwise.
func (o *UserInfoEntity) GetCreateTime() time.Time {
	if o == nil || o.CreateTime == nil {
		var ret time.Time
		return ret
	}
	return *o.CreateTime
}

// GetCreateTimeOk returns a tuple with the CreateTime field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoEntity) GetCreateTimeOk() (*time.Time, bool) {
	if o == nil || o.CreateTime == nil {
		return nil, false
	}
	return o.CreateTime, true
}

// HasCreateTime returns a boolean if a field has been set.
func (o *UserInfoEntity) HasCreateTime() bool {
	if o != nil && o.CreateTime != nil {
		return true
	}

	return false
}

// SetCreateTime gets a reference to the given time.Time and assigns it to the CreateTime field.
func (o *UserInfoEntity) SetCreateTime(v time.Time) {
	o.CreateTime = &v
}

// GetCreateUser returns the CreateUser field value if set, zero value otherwise.
func (o *UserInfoEntity) GetCreateUser() string {
	if o == nil || o.CreateUser == nil {
		var ret string
		return ret
	}
	return *o.CreateUser
}

// GetCreateUserOk returns a tuple with the CreateUser field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoEntity) GetCreateUserOk() (*string, bool) {
	if o == nil || o.CreateUser == nil {
		return nil, false
	}
	return o.CreateUser, true
}

// HasCreateUser returns a boolean if a field has been set.
func (o *UserInfoEntity) HasCreateUser() bool {
	if o != nil && o.CreateUser != nil {
		return true
	}

	return false
}

// SetCreateUser gets a reference to the given string and assigns it to the CreateUser field.
func (o *UserInfoEntity) SetCreateUser(v string) {
	o.CreateUser = &v
}

// GetDisplayWorkspace returns the DisplayWorkspace field value if set, zero value otherwise.
func (o *UserInfoEntity) GetDisplayWorkspace() string {
	if o == nil || o.DisplayWorkspace == nil {
		var ret string
		return ret
	}
	return *o.DisplayWorkspace
}

// GetDisplayWorkspaceOk returns a tuple with the DisplayWorkspace field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoEntity) GetDisplayWorkspaceOk() (*string, bool) {
	if o == nil || o.DisplayWorkspace == nil {
		return nil, false
	}
	return o.DisplayWorkspace, true
}

// HasDisplayWorkspace returns a boolean if a field has been set.
func (o *UserInfoEntity) HasDisplayWorkspace() bool {
	if o != nil && o.DisplayWorkspace != nil {
		return true
	}

	return false
}

// SetDisplayWorkspace gets a reference to the given string and assigns it to the DisplayWorkspace field.
func (o *UserInfoEntity) SetDisplayWorkspace(v string) {
	o.DisplayWorkspace = &v
}

// GetEmail returns the Email field value if set, zero value otherwise.
func (o *UserInfoEntity) GetEmail() string {
	if o == nil || o.Email == nil {
		var ret string
		return ret
	}
	return *o.Email
}

// GetEmailOk returns a tuple with the Email field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoEntity) GetEmailOk() (*string, bool) {
	if o == nil || o.Email == nil {
		return nil, false
	}
	return o.Email, true
}

// HasEmail returns a boolean if a field has been set.
func (o *UserInfoEntity) HasEmail() bool {
	if o != nil && o.Email != nil {
		return true
	}

	return false
}

// SetEmail gets a reference to the given string and assigns it to the Email field.
func (o *UserInfoEntity) SetEmail(v string) {
	o.Email = &v
}

// GetId returns the Id field value if set, zero value otherwise.
func (o *UserInfoEntity) GetId() string {
	if o == nil || o.Id == nil {
		var ret string
		return ret
	}
	return *o.Id
}

// GetIdOk returns a tuple with the Id field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoEntity) GetIdOk() (*string, bool) {
	if o == nil || o.Id == nil {
		return nil, false
	}
	return o.Id, true
}

// HasId returns a boolean if a field has been set.
func (o *UserInfoEntity) HasId() bool {
	if o != nil && o.Id != nil {
		return true
	}

	return false
}

// SetId gets a reference to the given string and assigns it to the Id field.
func (o *UserInfoEntity) SetId(v string) {
	o.Id = &v
}

// GetIsAdmin returns the IsAdmin field value if set, zero value otherwise.
func (o *UserInfoEntity) GetIsAdmin() bool {
	if o == nil || o.IsAdmin == nil {
		var ret bool
		return ret
	}
	return *o.IsAdmin
}

// GetIsAdminOk returns a tuple with the IsAdmin field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoEntity) GetIsAdminOk() (*bool, bool) {
	if o == nil || o.IsAdmin == nil {
		return nil, false
	}
	return o.IsAdmin, true
}

// HasIsAdmin returns a boolean if a field has been set.
func (o *UserInfoEntity) HasIsAdmin() bool {
	if o != nil && o.IsAdmin != nil {
		return true
	}

	return false
}

// SetIsAdmin gets a reference to the given bool and assigns it to the IsAdmin field.
func (o *UserInfoEntity) SetIsAdmin(v bool) {
	o.IsAdmin = &v
}

// GetIsWorkspaceAdmin returns the IsWorkspaceAdmin field value if set, zero value otherwise.
func (o *UserInfoEntity) GetIsWorkspaceAdmin() bool {
	if o == nil || o.IsWorkspaceAdmin == nil {
		var ret bool
		return ret
	}
	return *o.IsWorkspaceAdmin
}

// GetIsWorkspaceAdminOk returns a tuple with the IsWorkspaceAdmin field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoEntity) GetIsWorkspaceAdminOk() (*bool, bool) {
	if o == nil || o.IsWorkspaceAdmin == nil {
		return nil, false
	}
	return o.IsWorkspaceAdmin, true
}

// HasIsWorkspaceAdmin returns a boolean if a field has been set.
func (o *UserInfoEntity) HasIsWorkspaceAdmin() bool {
	if o != nil && o.IsWorkspaceAdmin != nil {
		return true
	}

	return false
}

// SetIsWorkspaceAdmin gets a reference to the given bool and assigns it to the IsWorkspaceAdmin field.
func (o *UserInfoEntity) SetIsWorkspaceAdmin(v bool) {
	o.IsWorkspaceAdmin = &v
}

// GetLocale returns the Locale field value if set, zero value otherwise.
func (o *UserInfoEntity) GetLocale() string {
	if o == nil || o.Locale == nil {
		var ret string
		return ret
	}
	return *o.Locale
}

// GetLocaleOk returns a tuple with the Locale field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoEntity) GetLocaleOk() (*string, bool) {
	if o == nil || o.Locale == nil {
		return nil, false
	}
	return o.Locale, true
}

// HasLocale returns a boolean if a field has been set.
func (o *UserInfoEntity) HasLocale() bool {
	if o != nil && o.Locale != nil {
		return true
	}

	return false
}

// SetLocale gets a reference to the given string and assigns it to the Locale field.
func (o *UserInfoEntity) SetLocale(v string) {
	o.Locale = &v
}

// GetOrganId returns the OrganId field value if set, zero value otherwise.
func (o *UserInfoEntity) GetOrganId() string {
	if o == nil || o.OrganId == nil {
		var ret string
		return ret
	}
	return *o.OrganId
}

// GetOrganIdOk returns a tuple with the OrganId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoEntity) GetOrganIdOk() (*string, bool) {
	if o == nil || o.OrganId == nil {
		return nil, false
	}
	return o.OrganId, true
}

// HasOrganId returns a boolean if a field has been set.
func (o *UserInfoEntity) HasOrganId() bool {
	if o != nil && o.OrganId != nil {
		return true
	}

	return false
}

// SetOrganId gets a reference to the given string and assigns it to the OrganId field.
func (o *UserInfoEntity) SetOrganId(v string) {
	o.OrganId = &v
}

// GetOrganName returns the OrganName field value if set, zero value otherwise.
func (o *UserInfoEntity) GetOrganName() string {
	if o == nil || o.OrganName == nil {
		var ret string
		return ret
	}
	return *o.OrganName
}

// GetOrganNameOk returns a tuple with the OrganName field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoEntity) GetOrganNameOk() (*string, bool) {
	if o == nil || o.OrganName == nil {
		return nil, false
	}
	return o.OrganName, true
}

// HasOrganName returns a boolean if a field has been set.
func (o *UserInfoEntity) HasOrganName() bool {
	if o != nil && o.OrganName != nil {
		return true
	}

	return false
}

// SetOrganName gets a reference to the given string and assigns it to the OrganName field.
func (o *UserInfoEntity) SetOrganName(v string) {
	o.OrganName = &v
}

// GetOwnGroupNames returns the OwnGroupNames field value if set, zero value otherwise.
func (o *UserInfoEntity) GetOwnGroupNames() []string {
	if o == nil || o.OwnGroupNames == nil {
		var ret []string
		return ret
	}
	return *o.OwnGroupNames
}

// GetOwnGroupNamesOk returns a tuple with the OwnGroupNames field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoEntity) GetOwnGroupNamesOk() (*[]string, bool) {
	if o == nil || o.OwnGroupNames == nil {
		return nil, false
	}
	return o.OwnGroupNames, true
}

// HasOwnGroupNames returns a boolean if a field has been set.
func (o *UserInfoEntity) HasOwnGroupNames() bool {
	if o != nil && o.OwnGroupNames != nil {
		return true
	}

	return false
}

// SetOwnGroupNames gets a reference to the given []string and assigns it to the OwnGroupNames field.
func (o *UserInfoEntity) SetOwnGroupNames(v []string) {
	o.OwnGroupNames = &v
}

// GetOwnWorkspaceIds returns the OwnWorkspaceIds field value if set, zero value otherwise.
func (o *UserInfoEntity) GetOwnWorkspaceIds() []string {
	if o == nil || o.OwnWorkspaceIds == nil {
		var ret []string
		return ret
	}
	return *o.OwnWorkspaceIds
}

// GetOwnWorkspaceIdsOk returns a tuple with the OwnWorkspaceIds field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoEntity) GetOwnWorkspaceIdsOk() (*[]string, bool) {
	if o == nil || o.OwnWorkspaceIds == nil {
		return nil, false
	}
	return o.OwnWorkspaceIds, true
}

// HasOwnWorkspaceIds returns a boolean if a field has been set.
func (o *UserInfoEntity) HasOwnWorkspaceIds() bool {
	if o != nil && o.OwnWorkspaceIds != nil {
		return true
	}

	return false
}

// SetOwnWorkspaceIds gets a reference to the given []string and assigns it to the OwnWorkspaceIds field.
func (o *UserInfoEntity) SetOwnWorkspaceIds(v []string) {
	o.OwnWorkspaceIds = &v
}

// GetPassword returns the Password field value if set, zero value otherwise.
func (o *UserInfoEntity) GetPassword() string {
	if o == nil || o.Password == nil {
		var ret string
		return ret
	}
	return *o.Password
}

// GetPasswordOk returns a tuple with the Password field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoEntity) GetPasswordOk() (*string, bool) {
	if o == nil || o.Password == nil {
		return nil, false
	}
	return o.Password, true
}

// HasPassword returns a boolean if a field has been set.
func (o *UserInfoEntity) HasPassword() bool {
	if o != nil && o.Password != nil {
		return true
	}

	return false
}

// SetPassword gets a reference to the given string and assigns it to the Password field.
func (o *UserInfoEntity) SetPassword(v string) {
	o.Password = &v
}

// GetRealName returns the RealName field value if set, zero value otherwise.
func (o *UserInfoEntity) GetRealName() string {
	if o == nil || o.RealName == nil {
		var ret string
		return ret
	}
	return *o.RealName
}

// GetRealNameOk returns a tuple with the RealName field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoEntity) GetRealNameOk() (*string, bool) {
	if o == nil || o.RealName == nil {
		return nil, false
	}
	return o.RealName, true
}

// HasRealName returns a boolean if a field has been set.
func (o *UserInfoEntity) HasRealName() bool {
	if o != nil && o.RealName != nil {
		return true
	}

	return false
}

// SetRealName gets a reference to the given string and assigns it to the RealName field.
func (o *UserInfoEntity) SetRealName(v string) {
	o.RealName = &v
}

// GetRoleIds returns the RoleIds field value if set, zero value otherwise.
func (o *UserInfoEntity) GetRoleIds() []string {
	if o == nil || o.RoleIds == nil {
		var ret []string
		return ret
	}
	return *o.RoleIds
}

// GetRoleIdsOk returns a tuple with the RoleIds field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoEntity) GetRoleIdsOk() (*[]string, bool) {
	if o == nil || o.RoleIds == nil {
		return nil, false
	}
	return o.RoleIds, true
}

// HasRoleIds returns a boolean if a field has been set.
func (o *UserInfoEntity) HasRoleIds() bool {
	if o != nil && o.RoleIds != nil {
		return true
	}

	return false
}

// SetRoleIds gets a reference to the given []string and assigns it to the RoleIds field.
func (o *UserInfoEntity) SetRoleIds(v []string) {
	o.RoleIds = &v
}

// GetRoleInfoMap returns the RoleInfoMap field value if set, zero value otherwise.
func (o *UserInfoEntity) GetRoleInfoMap() map[string]string {
	if o == nil || o.RoleInfoMap == nil {
		var ret map[string]string
		return ret
	}
	return *o.RoleInfoMap
}

// GetRoleInfoMapOk returns a tuple with the RoleInfoMap field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoEntity) GetRoleInfoMapOk() (*map[string]string, bool) {
	if o == nil || o.RoleInfoMap == nil {
		return nil, false
	}
	return o.RoleInfoMap, true
}

// HasRoleInfoMap returns a boolean if a field has been set.
func (o *UserInfoEntity) HasRoleInfoMap() bool {
	if o != nil && o.RoleInfoMap != nil {
		return true
	}

	return false
}

// SetRoleInfoMap gets a reference to the given map[string]string and assigns it to the RoleInfoMap field.
func (o *UserInfoEntity) SetRoleInfoMap(v map[string]string) {
	o.RoleInfoMap = &v
}

// GetStatus returns the Status field value if set, zero value otherwise.
func (o *UserInfoEntity) GetStatus() string {
	if o == nil || o.Status == nil {
		var ret string
		return ret
	}
	return *o.Status
}

// GetStatusOk returns a tuple with the Status field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoEntity) GetStatusOk() (*string, bool) {
	if o == nil || o.Status == nil {
		return nil, false
	}
	return o.Status, true
}

// HasStatus returns a boolean if a field has been set.
func (o *UserInfoEntity) HasStatus() bool {
	if o != nil && o.Status != nil {
		return true
	}

	return false
}

// SetStatus gets a reference to the given string and assigns it to the Status field.
func (o *UserInfoEntity) SetStatus(v string) {
	o.Status = &v
}

// GetTelephone returns the Telephone field value if set, zero value otherwise.
func (o *UserInfoEntity) GetTelephone() string {
	if o == nil || o.Telephone == nil {
		var ret string
		return ret
	}
	return *o.Telephone
}

// GetTelephoneOk returns a tuple with the Telephone field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoEntity) GetTelephoneOk() (*string, bool) {
	if o == nil || o.Telephone == nil {
		return nil, false
	}
	return o.Telephone, true
}

// HasTelephone returns a boolean if a field has been set.
func (o *UserInfoEntity) HasTelephone() bool {
	if o != nil && o.Telephone != nil {
		return true
	}

	return false
}

// SetTelephone gets a reference to the given string and assigns it to the Telephone field.
func (o *UserInfoEntity) SetTelephone(v string) {
	o.Telephone = &v
}

// GetUpdateTime returns the UpdateTime field value if set, zero value otherwise.
func (o *UserInfoEntity) GetUpdateTime() time.Time {
	if o == nil || o.UpdateTime == nil {
		var ret time.Time
		return ret
	}
	return *o.UpdateTime
}

// GetUpdateTimeOk returns a tuple with the UpdateTime field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoEntity) GetUpdateTimeOk() (*time.Time, bool) {
	if o == nil || o.UpdateTime == nil {
		return nil, false
	}
	return o.UpdateTime, true
}

// HasUpdateTime returns a boolean if a field has been set.
func (o *UserInfoEntity) HasUpdateTime() bool {
	if o != nil && o.UpdateTime != nil {
		return true
	}

	return false
}

// SetUpdateTime gets a reference to the given time.Time and assigns it to the UpdateTime field.
func (o *UserInfoEntity) SetUpdateTime(v time.Time) {
	o.UpdateTime = &v
}

// GetUpdateUser returns the UpdateUser field value if set, zero value otherwise.
func (o *UserInfoEntity) GetUpdateUser() string {
	if o == nil || o.UpdateUser == nil {
		var ret string
		return ret
	}
	return *o.UpdateUser
}

// GetUpdateUserOk returns a tuple with the UpdateUser field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoEntity) GetUpdateUserOk() (*string, bool) {
	if o == nil || o.UpdateUser == nil {
		return nil, false
	}
	return o.UpdateUser, true
}

// HasUpdateUser returns a boolean if a field has been set.
func (o *UserInfoEntity) HasUpdateUser() bool {
	if o != nil && o.UpdateUser != nil {
		return true
	}

	return false
}

// SetUpdateUser gets a reference to the given string and assigns it to the UpdateUser field.
func (o *UserInfoEntity) SetUpdateUser(v string) {
	o.UpdateUser = &v
}

// GetUsername returns the Username field value if set, zero value otherwise.
func (o *UserInfoEntity) GetUsername() string {
	if o == nil || o.Username == nil {
		var ret string
		return ret
	}
	return *o.Username
}

// GetUsernameOk returns a tuple with the Username field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoEntity) GetUsernameOk() (*string, bool) {
	if o == nil || o.Username == nil {
		return nil, false
	}
	return o.Username, true
}

// HasUsername returns a boolean if a field has been set.
func (o *UserInfoEntity) HasUsername() bool {
	if o != nil && o.Username != nil {
		return true
	}

	return false
}

// SetUsername gets a reference to the given string and assigns it to the Username field.
func (o *UserInfoEntity) SetUsername(v string) {
	o.Username = &v
}

// GetWorkspaceName returns the WorkspaceName field value if set, zero value otherwise.
func (o *UserInfoEntity) GetWorkspaceName() string {
	if o == nil || o.WorkspaceName == nil {
		var ret string
		return ret
	}
	return *o.WorkspaceName
}

// GetWorkspaceNameOk returns a tuple with the WorkspaceName field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoEntity) GetWorkspaceNameOk() (*string, bool) {
	if o == nil || o.WorkspaceName == nil {
		return nil, false
	}
	return o.WorkspaceName, true
}

// HasWorkspaceName returns a boolean if a field has been set.
func (o *UserInfoEntity) HasWorkspaceName() bool {
	if o != nil && o.WorkspaceName != nil {
		return true
	}

	return false
}

// SetWorkspaceName gets a reference to the given string and assigns it to the WorkspaceName field.
func (o *UserInfoEntity) SetWorkspaceName(v string) {
	o.WorkspaceName = &v
}

// GetWorkspaceRoleIds returns the WorkspaceRoleIds field value if set, zero value otherwise.
func (o *UserInfoEntity) GetWorkspaceRoleIds() map[string][]string {
	if o == nil || o.WorkspaceRoleIds == nil {
		var ret map[string][]string
		return ret
	}
	return *o.WorkspaceRoleIds
}

// GetWorkspaceRoleIdsOk returns a tuple with the WorkspaceRoleIds field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserInfoEntity) GetWorkspaceRoleIdsOk() (*map[string][]string, bool) {
	if o == nil || o.WorkspaceRoleIds == nil {
		return nil, false
	}
	return o.WorkspaceRoleIds, true
}

// HasWorkspaceRoleIds returns a boolean if a field has been set.
func (o *UserInfoEntity) HasWorkspaceRoleIds() bool {
	if o != nil && o.WorkspaceRoleIds != nil {
		return true
	}

	return false
}

// SetWorkspaceRoleIds gets a reference to the given map[string][]string and assigns it to the WorkspaceRoleIds field.
func (o *UserInfoEntity) SetWorkspaceRoleIds(v map[string][]string) {
	o.WorkspaceRoleIds = &v
}

func (o UserInfoEntity) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.CreateTime != nil {
		toSerialize["createTime"] = o.CreateTime
	}
	if o.CreateUser != nil {
		toSerialize["createUser"] = o.CreateUser
	}
	if o.DisplayWorkspace != nil {
		toSerialize["displayWorkspace"] = o.DisplayWorkspace
	}
	if o.Email != nil {
		toSerialize["email"] = o.Email
	}
	if o.Id != nil {
		toSerialize["id"] = o.Id
	}
	if o.IsAdmin != nil {
		toSerialize["isAdmin"] = o.IsAdmin
	}
	if o.IsWorkspaceAdmin != nil {
		toSerialize["isWorkspaceAdmin"] = o.IsWorkspaceAdmin
	}
	if o.Locale != nil {
		toSerialize["locale"] = o.Locale
	}
	if o.OrganId != nil {
		toSerialize["organId"] = o.OrganId
	}
	if o.OrganName != nil {
		toSerialize["organName"] = o.OrganName
	}
	if o.OwnGroupNames != nil {
		toSerialize["ownGroupNames"] = o.OwnGroupNames
	}
	if o.OwnWorkspaceIds != nil {
		toSerialize["ownWorkspaceIds"] = o.OwnWorkspaceIds
	}
	if o.Password != nil {
		toSerialize["password"] = o.Password
	}
	if o.RealName != nil {
		toSerialize["realName"] = o.RealName
	}
	if o.RoleIds != nil {
		toSerialize["roleIds"] = o.RoleIds
	}
	if o.RoleInfoMap != nil {
		toSerialize["roleInfoMap"] = o.RoleInfoMap
	}
	if o.Status != nil {
		toSerialize["status"] = o.Status
	}
	if o.Telephone != nil {
		toSerialize["telephone"] = o.Telephone
	}
	if o.UpdateTime != nil {
		toSerialize["updateTime"] = o.UpdateTime
	}
	if o.UpdateUser != nil {
		toSerialize["updateUser"] = o.UpdateUser
	}
	if o.Username != nil {
		toSerialize["username"] = o.Username
	}
	if o.WorkspaceName != nil {
		toSerialize["workspaceName"] = o.WorkspaceName
	}
	if o.WorkspaceRoleIds != nil {
		toSerialize["workspaceRoleIds"] = o.WorkspaceRoleIds
	}
	return json.Marshal(toSerialize)
}

type NullableUserInfoEntity struct {
	value *UserInfoEntity
	isSet bool
}

func (v NullableUserInfoEntity) Get() *UserInfoEntity {
	return v.value
}

func (v *NullableUserInfoEntity) Set(val *UserInfoEntity) {
	v.value = val
	v.isSet = true
}

func (v NullableUserInfoEntity) IsSet() bool {
	return v.isSet
}

func (v *NullableUserInfoEntity) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableUserInfoEntity(val *UserInfoEntity) *NullableUserInfoEntity {
	return &NullableUserInfoEntity{value: val, isSet: true}
}

func (v NullableUserInfoEntity) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableUserInfoEntity) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
