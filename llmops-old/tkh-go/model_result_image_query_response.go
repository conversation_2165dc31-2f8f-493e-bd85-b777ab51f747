/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// ResultImageQueryResponse Result«ImageQueryResponse»
type ResultImageQueryResponse struct {
	Code *int32 `json:"code,omitempty"`
	Data *ImageQueryResponse `json:"data,omitempty"`
	Message *string `json:"message,omitempty"`
}

// NewResultImageQueryResponse instantiates a new ResultImageQueryResponse object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewResultImageQueryResponse() *ResultImageQueryResponse {
	this := ResultImageQueryResponse{}
	return &this
}

// NewResultImageQueryResponseWithDefaults instantiates a new ResultImageQueryResponse object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewResultImageQueryResponseWithDefaults() *ResultImageQueryResponse {
	this := ResultImageQueryResponse{}
	return &this
}

// GetCode returns the Code field value if set, zero value otherwise.
func (o *ResultImageQueryResponse) GetCode() int32 {
	if o == nil || o.Code == nil {
		var ret int32
		return ret
	}
	return *o.Code
}

// GetCodeOk returns a tuple with the Code field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ResultImageQueryResponse) GetCodeOk() (*int32, bool) {
	if o == nil || o.Code == nil {
		return nil, false
	}
	return o.Code, true
}

// HasCode returns a boolean if a field has been set.
func (o *ResultImageQueryResponse) HasCode() bool {
	if o != nil && o.Code != nil {
		return true
	}

	return false
}

// SetCode gets a reference to the given int32 and assigns it to the Code field.
func (o *ResultImageQueryResponse) SetCode(v int32) {
	o.Code = &v
}

// GetData returns the Data field value if set, zero value otherwise.
func (o *ResultImageQueryResponse) GetData() ImageQueryResponse {
	if o == nil || o.Data == nil {
		var ret ImageQueryResponse
		return ret
	}
	return *o.Data
}

// GetDataOk returns a tuple with the Data field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ResultImageQueryResponse) GetDataOk() (*ImageQueryResponse, bool) {
	if o == nil || o.Data == nil {
		return nil, false
	}
	return o.Data, true
}

// HasData returns a boolean if a field has been set.
func (o *ResultImageQueryResponse) HasData() bool {
	if o != nil && o.Data != nil {
		return true
	}

	return false
}

// SetData gets a reference to the given ImageQueryResponse and assigns it to the Data field.
func (o *ResultImageQueryResponse) SetData(v ImageQueryResponse) {
	o.Data = &v
}

// GetMessage returns the Message field value if set, zero value otherwise.
func (o *ResultImageQueryResponse) GetMessage() string {
	if o == nil || o.Message == nil {
		var ret string
		return ret
	}
	return *o.Message
}

// GetMessageOk returns a tuple with the Message field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ResultImageQueryResponse) GetMessageOk() (*string, bool) {
	if o == nil || o.Message == nil {
		return nil, false
	}
	return o.Message, true
}

// HasMessage returns a boolean if a field has been set.
func (o *ResultImageQueryResponse) HasMessage() bool {
	if o != nil && o.Message != nil {
		return true
	}

	return false
}

// SetMessage gets a reference to the given string and assigns it to the Message field.
func (o *ResultImageQueryResponse) SetMessage(v string) {
	o.Message = &v
}

func (o ResultImageQueryResponse) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Code != nil {
		toSerialize["code"] = o.Code
	}
	if o.Data != nil {
		toSerialize["data"] = o.Data
	}
	if o.Message != nil {
		toSerialize["message"] = o.Message
	}
	return json.Marshal(toSerialize)
}

type NullableResultImageQueryResponse struct {
	value *ResultImageQueryResponse
	isSet bool
}

func (v NullableResultImageQueryResponse) Get() *ResultImageQueryResponse {
	return v.value
}

func (v *NullableResultImageQueryResponse) Set(val *ResultImageQueryResponse) {
	v.value = val
	v.isSet = true
}

func (v NullableResultImageQueryResponse) IsSet() bool {
	return v.isSet
}

func (v *NullableResultImageQueryResponse) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableResultImageQueryResponse(val *ResultImageQueryResponse) *NullableResultImageQueryResponse {
	return &NullableResultImageQueryResponse{value: val, isSet: true}
}

func (v NullableResultImageQueryResponse) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableResultImageQueryResponse) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
