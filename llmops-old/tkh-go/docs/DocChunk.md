# DocChunk

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Content** | Pointer to **string** |  | [optional] 
**Metadata** | Pointer to [**ChunkMetadata**](ChunkMetadata.md) |  | [optional] 

## Methods

### NewDocChunk

`func NewDocChunk() *DocChunk`

NewDocChunk instantiates a new DocChunk object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewDocChunkWithDefaults

`func NewDocChunkWithDefaults() *DocChunk`

NewDocChunkWithDefaults instantiates a new DocChunk object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetContent

`func (o *DocChunk) GetContent() string`

GetContent returns the Content field if non-nil, zero value otherwise.

### GetContentOk

`func (o *DocChunk) GetContentOk() (*string, bool)`

GetContentOk returns a tuple with the Content field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetContent

`func (o *DocChunk) SetContent(v string)`

SetContent sets Content field to given value.

### HasContent

`func (o *DocChunk) HasContent() bool`

HasContent returns a boolean if a field has been set.

### GetMetadata

`func (o *DocChunk) GetMetadata() ChunkMetadata`

GetMetadata returns the Metadata field if non-nil, zero value otherwise.

### GetMetadataOk

`func (o *DocChunk) GetMetadataOk() (*ChunkMetadata, bool)`

GetMetadataOk returns a tuple with the Metadata field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetMetadata

`func (o *DocChunk) SetMetadata(v ChunkMetadata)`

SetMetadata sets Metadata field to given value.

### HasMetadata

`func (o *DocChunk) HasMetadata() bool`

HasMetadata returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


