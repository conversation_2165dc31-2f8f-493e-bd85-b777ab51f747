# DocumentUploadTask

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Documents** | Pointer to [**[]Document**](Document.md) |  | [optional] 
**EndTime** | Pointer to **string** | 创建时间 | [optional] 
**Id** | Pointer to **int64** | ID | [optional] 
**Percent** | Pointer to **string** |  | [optional] 
**StartTime** | Pointer to **string** | 创建时间 | [optional] 
**SuccessDocs** | Pointer to **int32** |  | [optional] 
**TotalDocs** | Pointer to **int32** |  | [optional] 
**UploadTaskStatus** | Pointer to **int32** | 任务状态 1&#x3D; 任务创建 2&#x3D;上传中 3&#x3D;上传成功  4&#x3D;上传失败 | [optional] 
**UploadType** | Pointer to **int32** | 上传类型 1 &#x3D;本地上传 2 知识库上传 default &#x3D; 1 | [optional] 

## Methods

### NewDocumentUploadTask

`func NewDocumentUploadTask() *DocumentUploadTask`

NewDocumentUploadTask instantiates a new DocumentUploadTask object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewDocumentUploadTaskWithDefaults

`func NewDocumentUploadTaskWithDefaults() *DocumentUploadTask`

NewDocumentUploadTaskWithDefaults instantiates a new DocumentUploadTask object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetDocuments

`func (o *DocumentUploadTask) GetDocuments() []Document`

GetDocuments returns the Documents field if non-nil, zero value otherwise.

### GetDocumentsOk

`func (o *DocumentUploadTask) GetDocumentsOk() (*[]Document, bool)`

GetDocumentsOk returns a tuple with the Documents field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDocuments

`func (o *DocumentUploadTask) SetDocuments(v []Document)`

SetDocuments sets Documents field to given value.

### HasDocuments

`func (o *DocumentUploadTask) HasDocuments() bool`

HasDocuments returns a boolean if a field has been set.

### GetEndTime

`func (o *DocumentUploadTask) GetEndTime() string`

GetEndTime returns the EndTime field if non-nil, zero value otherwise.

### GetEndTimeOk

`func (o *DocumentUploadTask) GetEndTimeOk() (*string, bool)`

GetEndTimeOk returns a tuple with the EndTime field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetEndTime

`func (o *DocumentUploadTask) SetEndTime(v string)`

SetEndTime sets EndTime field to given value.

### HasEndTime

`func (o *DocumentUploadTask) HasEndTime() bool`

HasEndTime returns a boolean if a field has been set.

### GetId

`func (o *DocumentUploadTask) GetId() int64`

GetId returns the Id field if non-nil, zero value otherwise.

### GetIdOk

`func (o *DocumentUploadTask) GetIdOk() (*int64, bool)`

GetIdOk returns a tuple with the Id field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetId

`func (o *DocumentUploadTask) SetId(v int64)`

SetId sets Id field to given value.

### HasId

`func (o *DocumentUploadTask) HasId() bool`

HasId returns a boolean if a field has been set.

### GetPercent

`func (o *DocumentUploadTask) GetPercent() string`

GetPercent returns the Percent field if non-nil, zero value otherwise.

### GetPercentOk

`func (o *DocumentUploadTask) GetPercentOk() (*string, bool)`

GetPercentOk returns a tuple with the Percent field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetPercent

`func (o *DocumentUploadTask) SetPercent(v string)`

SetPercent sets Percent field to given value.

### HasPercent

`func (o *DocumentUploadTask) HasPercent() bool`

HasPercent returns a boolean if a field has been set.

### GetStartTime

`func (o *DocumentUploadTask) GetStartTime() string`

GetStartTime returns the StartTime field if non-nil, zero value otherwise.

### GetStartTimeOk

`func (o *DocumentUploadTask) GetStartTimeOk() (*string, bool)`

GetStartTimeOk returns a tuple with the StartTime field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetStartTime

`func (o *DocumentUploadTask) SetStartTime(v string)`

SetStartTime sets StartTime field to given value.

### HasStartTime

`func (o *DocumentUploadTask) HasStartTime() bool`

HasStartTime returns a boolean if a field has been set.

### GetSuccessDocs

`func (o *DocumentUploadTask) GetSuccessDocs() int32`

GetSuccessDocs returns the SuccessDocs field if non-nil, zero value otherwise.

### GetSuccessDocsOk

`func (o *DocumentUploadTask) GetSuccessDocsOk() (*int32, bool)`

GetSuccessDocsOk returns a tuple with the SuccessDocs field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetSuccessDocs

`func (o *DocumentUploadTask) SetSuccessDocs(v int32)`

SetSuccessDocs sets SuccessDocs field to given value.

### HasSuccessDocs

`func (o *DocumentUploadTask) HasSuccessDocs() bool`

HasSuccessDocs returns a boolean if a field has been set.

### GetTotalDocs

`func (o *DocumentUploadTask) GetTotalDocs() int32`

GetTotalDocs returns the TotalDocs field if non-nil, zero value otherwise.

### GetTotalDocsOk

`func (o *DocumentUploadTask) GetTotalDocsOk() (*int32, bool)`

GetTotalDocsOk returns a tuple with the TotalDocs field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetTotalDocs

`func (o *DocumentUploadTask) SetTotalDocs(v int32)`

SetTotalDocs sets TotalDocs field to given value.

### HasTotalDocs

`func (o *DocumentUploadTask) HasTotalDocs() bool`

HasTotalDocs returns a boolean if a field has been set.

### GetUploadTaskStatus

`func (o *DocumentUploadTask) GetUploadTaskStatus() int32`

GetUploadTaskStatus returns the UploadTaskStatus field if non-nil, zero value otherwise.

### GetUploadTaskStatusOk

`func (o *DocumentUploadTask) GetUploadTaskStatusOk() (*int32, bool)`

GetUploadTaskStatusOk returns a tuple with the UploadTaskStatus field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUploadTaskStatus

`func (o *DocumentUploadTask) SetUploadTaskStatus(v int32)`

SetUploadTaskStatus sets UploadTaskStatus field to given value.

### HasUploadTaskStatus

`func (o *DocumentUploadTask) HasUploadTaskStatus() bool`

HasUploadTaskStatus returns a boolean if a field has been set.

### GetUploadType

`func (o *DocumentUploadTask) GetUploadType() int32`

GetUploadType returns the UploadType field if non-nil, zero value otherwise.

### GetUploadTypeOk

`func (o *DocumentUploadTask) GetUploadTypeOk() (*int32, bool)`

GetUploadTypeOk returns a tuple with the UploadType field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUploadType

`func (o *DocumentUploadTask) SetUploadType(v int32)`

SetUploadType sets UploadType field to given value.

### HasUploadType

`func (o *DocumentUploadTask) HasUploadType() bool`

HasUploadType returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


