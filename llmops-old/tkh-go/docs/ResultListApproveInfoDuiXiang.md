# ResultListApproveInfoDuiXiang

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Code** | Pointer to **int32** |  | [optional] 
**Data** | Pointer to [**[]ApproveInfoDuiXiang**](ApproveInfoDuiXiang.md) |  | [optional] 
**Message** | Pointer to **string** |  | [optional] 

## Methods

### NewResultListApproveInfoDuiXiang

`func NewResultListApproveInfoDuiXiang() *ResultListApproveInfoDuiXiang`

NewResultListApproveInfoDuiXiang instantiates a new ResultListApproveInfoDuiXiang object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewResultListApproveInfoDuiXiangWithDefaults

`func NewResultListApproveInfoDuiXiangWithDefaults() *ResultListApproveInfoDuiXiang`

NewResultListApproveInfoDuiXiangWithDefaults instantiates a new ResultListApproveInfoDuiXiang object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetCode

`func (o *ResultListApproveInfoDuiXiang) GetCode() int32`

GetCode returns the Code field if non-nil, zero value otherwise.

### GetCodeOk

`func (o *ResultListApproveInfoDuiXiang) GetCodeOk() (*int32, bool)`

GetCodeOk returns a tuple with the Code field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCode

`func (o *ResultListApproveInfoDuiXiang) SetCode(v int32)`

SetCode sets Code field to given value.

### HasCode

`func (o *ResultListApproveInfoDuiXiang) HasCode() bool`

HasCode returns a boolean if a field has been set.

### GetData

`func (o *ResultListApproveInfoDuiXiang) GetData() []ApproveInfoDuiXiang`

GetData returns the Data field if non-nil, zero value otherwise.

### GetDataOk

`func (o *ResultListApproveInfoDuiXiang) GetDataOk() (*[]ApproveInfoDuiXiang, bool)`

GetDataOk returns a tuple with the Data field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetData

`func (o *ResultListApproveInfoDuiXiang) SetData(v []ApproveInfoDuiXiang)`

SetData sets Data field to given value.

### HasData

`func (o *ResultListApproveInfoDuiXiang) HasData() bool`

HasData returns a boolean if a field has been set.

### GetMessage

`func (o *ResultListApproveInfoDuiXiang) GetMessage() string`

GetMessage returns the Message field if non-nil, zero value otherwise.

### GetMessageOk

`func (o *ResultListApproveInfoDuiXiang) GetMessageOk() (*string, bool)`

GetMessageOk returns a tuple with the Message field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetMessage

`func (o *ResultListApproveInfoDuiXiang) SetMessage(v string)`

SetMessage sets Message field to given value.

### HasMessage

`func (o *ResultListApproveInfoDuiXiang) HasMessage() bool`

HasMessage returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


