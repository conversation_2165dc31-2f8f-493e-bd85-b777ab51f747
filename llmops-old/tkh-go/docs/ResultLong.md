# ResultLong

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Code** | Pointer to **int32** |  | [optional] 
**Data** | Pointer to **int64** |  | [optional] 
**Message** | Pointer to **string** |  | [optional] 

## Methods

### NewResultLong

`func NewResultLong() *ResultLong`

NewResultLong instantiates a new ResultLong object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewResultLongWithDefaults

`func NewResultLongWithDefaults() *ResultLong`

NewResultLongWithDefaults instantiates a new ResultLong object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetCode

`func (o *ResultLong) GetCode() int32`

GetCode returns the Code field if non-nil, zero value otherwise.

### GetCodeOk

`func (o *ResultLong) GetCodeOk() (*int32, bool)`

GetCodeOk returns a tuple with the Code field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCode

`func (o *ResultLong) SetCode(v int32)`

SetCode sets Code field to given value.

### HasCode

`func (o *ResultLong) HasCode() bool`

HasCode returns a boolean if a field has been set.

### GetData

`func (o *ResultLong) GetData() int64`

GetData returns the Data field if non-nil, zero value otherwise.

### GetDataOk

`func (o *ResultLong) GetDataOk() (*int64, bool)`

GetDataOk returns a tuple with the Data field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetData

`func (o *ResultLong) SetData(v int64)`

SetData sets Data field to given value.

### HasData

`func (o *ResultLong) HasData() bool`

HasData returns a boolean if a field has been set.

### GetMessage

`func (o *ResultLong) GetMessage() string`

GetMessage returns the Message field if non-nil, zero value otherwise.

### GetMessageOk

`func (o *ResultLong) GetMessageOk() (*string, bool)`

GetMessageOk returns a tuple with the Message field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetMessage

`func (o *ResultLong) SetMessage(v string)`

SetMessage sets Message field to given value.

### HasMessage

`func (o *ResultLong) HasMessage() bool`

HasMessage returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


