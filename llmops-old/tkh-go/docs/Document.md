# Document

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**CreateTime** | Pointer to **string** | 创建时间 | [optional] 
**Creator** | Pointer to **string** | 创建者 | [optional] 
**DocumentTreeId** | Pointer to **int64** | 文档树ID | [optional] 
**FileBusinessType** | Pointer to **int32** | 上传文档业务归类 PDF&#x3D;1, IMG&#x3D;2, DOCX&#x3D;3, PPTX&#x3D;4, TABLE&#x3D;5, TXT&#x3D;6, AUDIO&#x3D;7, VIDEO&#x3D;8, NONE&#x3D;9  | [optional] 
**FileContentType** | Pointer to **int32** | 上传文档contentType枚举 TYPE_PDF&#x3D;1, TYPE_IMG_JPEG&#x3D;21, TYPE_IMG_PNG&#x3D;22, TYPE_IMG_GIF&#x3D;23, TYPE_IMG_GMP&#x3D;24, TYPE_DOCX_VND&#x3D;31, TYPE_DOCX_MS&#x3D;32, TYPE_PPT_OPENXML&#x3D;41, TYPE_PPT_MS&#x3D;42, TYPE_TABLE_VND&#x3D;51, TYPE_TABLE_CSV&#x3D;52, TYPE_TEXT_PLAIN&#x3D;61, TYPE_AUDIO_MEPG&#x3D;71, TYPE_AUDIO_WAV&#x3D;72, TYPE_AUDIO_WAVE&#x3D;73, TYPE_AUDIO_XWAV&#x3D;74, TYPE_AUDIO_OGG&#x3D;75, TYPE_AUDIO_AAC&#x3D;76, TYPE_AUDIO_FLAC&#x3D;77, TYPE_AUDIO_MP4&#x3D;78, TYPE_VIDEO_AVI&#x3D;81, TYPE_VIDEO_MP4&#x3D;82, TYPE_VIDEO_FLV&#x3D;83, TYPE_VIDEO_WMV&#x3D;84, TYPE_VIDEO_MKV&#x3D;85, TYPE_NONE&#x3D;91 | [optional] 
**FileMd5** | Pointer to **string** | 文件MD5值 | [optional] 
**FilePath** | Pointer to **string** |  | [optional] 
**FileSize** | Pointer to **int64** | 上传文档大小-bytes | [optional] 
**FileStatus** | Pointer to **int32** | 枚举 1&#x3D; waiting 2&#x3D;uploading 3&#x3D;upload_Failed  4&#x3D; uploaded 5&#x3D;parsing、6&#x3D;parse_failed 、7&#x3D; parse_success 、8&#x3D; edited  9&#x3D;saved_failed 10 saved | [optional] 
**FileSuffix** | Pointer to **int32** | 上传文档后缀枚举值 PDF&#x3D;1, PNG&#x3D;21, JPEG&#x3D;22, JPG&#x3D;23, GIF&#x3D;24, BMP&#x3D;25, DOC&#x3D;31, DOCX&#x3D;32, PPT&#x3D;41, PPTX&#x3D;42, XLSX&#x3D;51, CSV&#x3D;52, TXT&#x3D;61, MP3&#x3D;71, WAV&#x3D;72, OGG&#x3D;73, AAC&#x3D;74, FLAC&#x3D;75, M4A&#x3D;76, AVI&#x3D;81, MP4&#x3D;82, FLV&#x3D;83, WMV&#x3D;84, MKV&#x3D;85, NONE&#x3D;91 | [optional] 
**FileUploadTaskId** | Pointer to **int64** | 关联上传任务id | [optional] 
**FileUuid** | Pointer to **string** | 文件uuid | [optional] 
**Id** | Pointer to **int64** | ID | [optional] 
**Name** | Pointer to **string** | 名称 | [optional] 
**UpdateTime** | Pointer to **string** | 更新时间 | [optional] 

## Methods

### NewDocument

`func NewDocument() *Document`

NewDocument instantiates a new Document object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewDocumentWithDefaults

`func NewDocumentWithDefaults() *Document`

NewDocumentWithDefaults instantiates a new Document object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetCreateTime

`func (o *Document) GetCreateTime() string`

GetCreateTime returns the CreateTime field if non-nil, zero value otherwise.

### GetCreateTimeOk

`func (o *Document) GetCreateTimeOk() (*string, bool)`

GetCreateTimeOk returns a tuple with the CreateTime field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCreateTime

`func (o *Document) SetCreateTime(v string)`

SetCreateTime sets CreateTime field to given value.

### HasCreateTime

`func (o *Document) HasCreateTime() bool`

HasCreateTime returns a boolean if a field has been set.

### GetCreator

`func (o *Document) GetCreator() string`

GetCreator returns the Creator field if non-nil, zero value otherwise.

### GetCreatorOk

`func (o *Document) GetCreatorOk() (*string, bool)`

GetCreatorOk returns a tuple with the Creator field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCreator

`func (o *Document) SetCreator(v string)`

SetCreator sets Creator field to given value.

### HasCreator

`func (o *Document) HasCreator() bool`

HasCreator returns a boolean if a field has been set.

### GetDocumentTreeId

`func (o *Document) GetDocumentTreeId() int64`

GetDocumentTreeId returns the DocumentTreeId field if non-nil, zero value otherwise.

### GetDocumentTreeIdOk

`func (o *Document) GetDocumentTreeIdOk() (*int64, bool)`

GetDocumentTreeIdOk returns a tuple with the DocumentTreeId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDocumentTreeId

`func (o *Document) SetDocumentTreeId(v int64)`

SetDocumentTreeId sets DocumentTreeId field to given value.

### HasDocumentTreeId

`func (o *Document) HasDocumentTreeId() bool`

HasDocumentTreeId returns a boolean if a field has been set.

### GetFileBusinessType

`func (o *Document) GetFileBusinessType() int32`

GetFileBusinessType returns the FileBusinessType field if non-nil, zero value otherwise.

### GetFileBusinessTypeOk

`func (o *Document) GetFileBusinessTypeOk() (*int32, bool)`

GetFileBusinessTypeOk returns a tuple with the FileBusinessType field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFileBusinessType

`func (o *Document) SetFileBusinessType(v int32)`

SetFileBusinessType sets FileBusinessType field to given value.

### HasFileBusinessType

`func (o *Document) HasFileBusinessType() bool`

HasFileBusinessType returns a boolean if a field has been set.

### GetFileContentType

`func (o *Document) GetFileContentType() int32`

GetFileContentType returns the FileContentType field if non-nil, zero value otherwise.

### GetFileContentTypeOk

`func (o *Document) GetFileContentTypeOk() (*int32, bool)`

GetFileContentTypeOk returns a tuple with the FileContentType field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFileContentType

`func (o *Document) SetFileContentType(v int32)`

SetFileContentType sets FileContentType field to given value.

### HasFileContentType

`func (o *Document) HasFileContentType() bool`

HasFileContentType returns a boolean if a field has been set.

### GetFileMd5

`func (o *Document) GetFileMd5() string`

GetFileMd5 returns the FileMd5 field if non-nil, zero value otherwise.

### GetFileMd5Ok

`func (o *Document) GetFileMd5Ok() (*string, bool)`

GetFileMd5Ok returns a tuple with the FileMd5 field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFileMd5

`func (o *Document) SetFileMd5(v string)`

SetFileMd5 sets FileMd5 field to given value.

### HasFileMd5

`func (o *Document) HasFileMd5() bool`

HasFileMd5 returns a boolean if a field has been set.

### GetFilePath

`func (o *Document) GetFilePath() string`

GetFilePath returns the FilePath field if non-nil, zero value otherwise.

### GetFilePathOk

`func (o *Document) GetFilePathOk() (*string, bool)`

GetFilePathOk returns a tuple with the FilePath field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFilePath

`func (o *Document) SetFilePath(v string)`

SetFilePath sets FilePath field to given value.

### HasFilePath

`func (o *Document) HasFilePath() bool`

HasFilePath returns a boolean if a field has been set.

### GetFileSize

`func (o *Document) GetFileSize() int64`

GetFileSize returns the FileSize field if non-nil, zero value otherwise.

### GetFileSizeOk

`func (o *Document) GetFileSizeOk() (*int64, bool)`

GetFileSizeOk returns a tuple with the FileSize field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFileSize

`func (o *Document) SetFileSize(v int64)`

SetFileSize sets FileSize field to given value.

### HasFileSize

`func (o *Document) HasFileSize() bool`

HasFileSize returns a boolean if a field has been set.

### GetFileStatus

`func (o *Document) GetFileStatus() int32`

GetFileStatus returns the FileStatus field if non-nil, zero value otherwise.

### GetFileStatusOk

`func (o *Document) GetFileStatusOk() (*int32, bool)`

GetFileStatusOk returns a tuple with the FileStatus field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFileStatus

`func (o *Document) SetFileStatus(v int32)`

SetFileStatus sets FileStatus field to given value.

### HasFileStatus

`func (o *Document) HasFileStatus() bool`

HasFileStatus returns a boolean if a field has been set.

### GetFileSuffix

`func (o *Document) GetFileSuffix() int32`

GetFileSuffix returns the FileSuffix field if non-nil, zero value otherwise.

### GetFileSuffixOk

`func (o *Document) GetFileSuffixOk() (*int32, bool)`

GetFileSuffixOk returns a tuple with the FileSuffix field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFileSuffix

`func (o *Document) SetFileSuffix(v int32)`

SetFileSuffix sets FileSuffix field to given value.

### HasFileSuffix

`func (o *Document) HasFileSuffix() bool`

HasFileSuffix returns a boolean if a field has been set.

### GetFileUploadTaskId

`func (o *Document) GetFileUploadTaskId() int64`

GetFileUploadTaskId returns the FileUploadTaskId field if non-nil, zero value otherwise.

### GetFileUploadTaskIdOk

`func (o *Document) GetFileUploadTaskIdOk() (*int64, bool)`

GetFileUploadTaskIdOk returns a tuple with the FileUploadTaskId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFileUploadTaskId

`func (o *Document) SetFileUploadTaskId(v int64)`

SetFileUploadTaskId sets FileUploadTaskId field to given value.

### HasFileUploadTaskId

`func (o *Document) HasFileUploadTaskId() bool`

HasFileUploadTaskId returns a boolean if a field has been set.

### GetFileUuid

`func (o *Document) GetFileUuid() string`

GetFileUuid returns the FileUuid field if non-nil, zero value otherwise.

### GetFileUuidOk

`func (o *Document) GetFileUuidOk() (*string, bool)`

GetFileUuidOk returns a tuple with the FileUuid field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFileUuid

`func (o *Document) SetFileUuid(v string)`

SetFileUuid sets FileUuid field to given value.

### HasFileUuid

`func (o *Document) HasFileUuid() bool`

HasFileUuid returns a boolean if a field has been set.

### GetId

`func (o *Document) GetId() int64`

GetId returns the Id field if non-nil, zero value otherwise.

### GetIdOk

`func (o *Document) GetIdOk() (*int64, bool)`

GetIdOk returns a tuple with the Id field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetId

`func (o *Document) SetId(v int64)`

SetId sets Id field to given value.

### HasId

`func (o *Document) HasId() bool`

HasId returns a boolean if a field has been set.

### GetName

`func (o *Document) GetName() string`

GetName returns the Name field if non-nil, zero value otherwise.

### GetNameOk

`func (o *Document) GetNameOk() (*string, bool)`

GetNameOk returns a tuple with the Name field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetName

`func (o *Document) SetName(v string)`

SetName sets Name field to given value.

### HasName

`func (o *Document) HasName() bool`

HasName returns a boolean if a field has been set.

### GetUpdateTime

`func (o *Document) GetUpdateTime() string`

GetUpdateTime returns the UpdateTime field if non-nil, zero value otherwise.

### GetUpdateTimeOk

`func (o *Document) GetUpdateTimeOk() (*string, bool)`

GetUpdateTimeOk returns a tuple with the UpdateTime field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUpdateTime

`func (o *Document) SetUpdateTime(v string)`

SetUpdateTime sets UpdateTime field to given value.

### HasUpdateTime

`func (o *Document) HasUpdateTime() bool`

HasUpdateTime returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


