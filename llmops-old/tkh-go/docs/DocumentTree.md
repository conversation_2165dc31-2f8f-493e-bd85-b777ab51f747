# DocumentTree

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Category** | Pointer to **int32** | 类别 枚举:1&#x3D;目录 2&#x3D;文件 | [optional] 
**CreateTime** | Pointer to **string** | 创建时间 | [optional] 
**FileBusinessType** | Pointer to **int32** | 上传文档业务归类 1&#x3D;PDF 2&#x3D;IMG 3&#x3D;DOCX 4&#x3D;PPTX  | [optional] 
**FileContentType** | Pointer to **int32** | 上传文档contentType枚举 | [optional] 
**FileMd5** | Pointer to **string** | 文件MD5值 | [optional] 
**FileSize** | Pointer to **int64** | 上传文档大小-byte | [optional] 
**FileStatus** | Pointer to **int32** | 枚举 1&#x3D; waiting 2&#x3D;uploading 3&#x3D;upload_Failed  4&#x3D; uploaded 5&#x3D;parsing、6&#x3D;parse_failed 、7&#x3D; parse_success 、8&#x3D; edited  9&#x3D;saved_failed 10 saved | [optional] 
**FileSuffix** | Pointer to **int32** | 上传文档后缀枚举值 1&#x3D;PDF 2&#x3D;PNG 3&#x3D;JPEG 4&#x3D;GIF 5&#x3D;BMP 6&#x3D;PPT 7&#x3D;PPTX 8&#x3D;DOC 9&#x3D;DOCX 10&#x3D;XLSX 11&#x3D;CSV 12&#x3D;NONE | [optional] 
**FileUploadTaskId** | Pointer to **int64** | 关联上传任务id | [optional] 
**FileUuid** | Pointer to **string** | 文件uuid | [optional] 
**Id** | Pointer to **int64** | ID | [optional] 
**Name** | Pointer to **string** | 文件夹或者节点name | [optional] 
**ParentId** | Pointer to **int64** | 父级ID | [optional] 
**Path** | Pointer to **string** | 路径 | [optional] 
**RepositoryId** | Pointer to **int64** | 仓库ID | [optional] 
**UpdateTime** | Pointer to **string** | 更新时间 | [optional] 

## Methods

### NewDocumentTree

`func NewDocumentTree() *DocumentTree`

NewDocumentTree instantiates a new DocumentTree object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewDocumentTreeWithDefaults

`func NewDocumentTreeWithDefaults() *DocumentTree`

NewDocumentTreeWithDefaults instantiates a new DocumentTree object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetCategory

`func (o *DocumentTree) GetCategory() int32`

GetCategory returns the Category field if non-nil, zero value otherwise.

### GetCategoryOk

`func (o *DocumentTree) GetCategoryOk() (*int32, bool)`

GetCategoryOk returns a tuple with the Category field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCategory

`func (o *DocumentTree) SetCategory(v int32)`

SetCategory sets Category field to given value.

### HasCategory

`func (o *DocumentTree) HasCategory() bool`

HasCategory returns a boolean if a field has been set.

### GetCreateTime

`func (o *DocumentTree) GetCreateTime() string`

GetCreateTime returns the CreateTime field if non-nil, zero value otherwise.

### GetCreateTimeOk

`func (o *DocumentTree) GetCreateTimeOk() (*string, bool)`

GetCreateTimeOk returns a tuple with the CreateTime field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCreateTime

`func (o *DocumentTree) SetCreateTime(v string)`

SetCreateTime sets CreateTime field to given value.

### HasCreateTime

`func (o *DocumentTree) HasCreateTime() bool`

HasCreateTime returns a boolean if a field has been set.

### GetFileBusinessType

`func (o *DocumentTree) GetFileBusinessType() int32`

GetFileBusinessType returns the FileBusinessType field if non-nil, zero value otherwise.

### GetFileBusinessTypeOk

`func (o *DocumentTree) GetFileBusinessTypeOk() (*int32, bool)`

GetFileBusinessTypeOk returns a tuple with the FileBusinessType field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFileBusinessType

`func (o *DocumentTree) SetFileBusinessType(v int32)`

SetFileBusinessType sets FileBusinessType field to given value.

### HasFileBusinessType

`func (o *DocumentTree) HasFileBusinessType() bool`

HasFileBusinessType returns a boolean if a field has been set.

### GetFileContentType

`func (o *DocumentTree) GetFileContentType() int32`

GetFileContentType returns the FileContentType field if non-nil, zero value otherwise.

### GetFileContentTypeOk

`func (o *DocumentTree) GetFileContentTypeOk() (*int32, bool)`

GetFileContentTypeOk returns a tuple with the FileContentType field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFileContentType

`func (o *DocumentTree) SetFileContentType(v int32)`

SetFileContentType sets FileContentType field to given value.

### HasFileContentType

`func (o *DocumentTree) HasFileContentType() bool`

HasFileContentType returns a boolean if a field has been set.

### GetFileMd5

`func (o *DocumentTree) GetFileMd5() string`

GetFileMd5 returns the FileMd5 field if non-nil, zero value otherwise.

### GetFileMd5Ok

`func (o *DocumentTree) GetFileMd5Ok() (*string, bool)`

GetFileMd5Ok returns a tuple with the FileMd5 field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFileMd5

`func (o *DocumentTree) SetFileMd5(v string)`

SetFileMd5 sets FileMd5 field to given value.

### HasFileMd5

`func (o *DocumentTree) HasFileMd5() bool`

HasFileMd5 returns a boolean if a field has been set.

### GetFileSize

`func (o *DocumentTree) GetFileSize() int64`

GetFileSize returns the FileSize field if non-nil, zero value otherwise.

### GetFileSizeOk

`func (o *DocumentTree) GetFileSizeOk() (*int64, bool)`

GetFileSizeOk returns a tuple with the FileSize field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFileSize

`func (o *DocumentTree) SetFileSize(v int64)`

SetFileSize sets FileSize field to given value.

### HasFileSize

`func (o *DocumentTree) HasFileSize() bool`

HasFileSize returns a boolean if a field has been set.

### GetFileStatus

`func (o *DocumentTree) GetFileStatus() int32`

GetFileStatus returns the FileStatus field if non-nil, zero value otherwise.

### GetFileStatusOk

`func (o *DocumentTree) GetFileStatusOk() (*int32, bool)`

GetFileStatusOk returns a tuple with the FileStatus field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFileStatus

`func (o *DocumentTree) SetFileStatus(v int32)`

SetFileStatus sets FileStatus field to given value.

### HasFileStatus

`func (o *DocumentTree) HasFileStatus() bool`

HasFileStatus returns a boolean if a field has been set.

### GetFileSuffix

`func (o *DocumentTree) GetFileSuffix() int32`

GetFileSuffix returns the FileSuffix field if non-nil, zero value otherwise.

### GetFileSuffixOk

`func (o *DocumentTree) GetFileSuffixOk() (*int32, bool)`

GetFileSuffixOk returns a tuple with the FileSuffix field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFileSuffix

`func (o *DocumentTree) SetFileSuffix(v int32)`

SetFileSuffix sets FileSuffix field to given value.

### HasFileSuffix

`func (o *DocumentTree) HasFileSuffix() bool`

HasFileSuffix returns a boolean if a field has been set.

### GetFileUploadTaskId

`func (o *DocumentTree) GetFileUploadTaskId() int64`

GetFileUploadTaskId returns the FileUploadTaskId field if non-nil, zero value otherwise.

### GetFileUploadTaskIdOk

`func (o *DocumentTree) GetFileUploadTaskIdOk() (*int64, bool)`

GetFileUploadTaskIdOk returns a tuple with the FileUploadTaskId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFileUploadTaskId

`func (o *DocumentTree) SetFileUploadTaskId(v int64)`

SetFileUploadTaskId sets FileUploadTaskId field to given value.

### HasFileUploadTaskId

`func (o *DocumentTree) HasFileUploadTaskId() bool`

HasFileUploadTaskId returns a boolean if a field has been set.

### GetFileUuid

`func (o *DocumentTree) GetFileUuid() string`

GetFileUuid returns the FileUuid field if non-nil, zero value otherwise.

### GetFileUuidOk

`func (o *DocumentTree) GetFileUuidOk() (*string, bool)`

GetFileUuidOk returns a tuple with the FileUuid field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFileUuid

`func (o *DocumentTree) SetFileUuid(v string)`

SetFileUuid sets FileUuid field to given value.

### HasFileUuid

`func (o *DocumentTree) HasFileUuid() bool`

HasFileUuid returns a boolean if a field has been set.

### GetId

`func (o *DocumentTree) GetId() int64`

GetId returns the Id field if non-nil, zero value otherwise.

### GetIdOk

`func (o *DocumentTree) GetIdOk() (*int64, bool)`

GetIdOk returns a tuple with the Id field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetId

`func (o *DocumentTree) SetId(v int64)`

SetId sets Id field to given value.

### HasId

`func (o *DocumentTree) HasId() bool`

HasId returns a boolean if a field has been set.

### GetName

`func (o *DocumentTree) GetName() string`

GetName returns the Name field if non-nil, zero value otherwise.

### GetNameOk

`func (o *DocumentTree) GetNameOk() (*string, bool)`

GetNameOk returns a tuple with the Name field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetName

`func (o *DocumentTree) SetName(v string)`

SetName sets Name field to given value.

### HasName

`func (o *DocumentTree) HasName() bool`

HasName returns a boolean if a field has been set.

### GetParentId

`func (o *DocumentTree) GetParentId() int64`

GetParentId returns the ParentId field if non-nil, zero value otherwise.

### GetParentIdOk

`func (o *DocumentTree) GetParentIdOk() (*int64, bool)`

GetParentIdOk returns a tuple with the ParentId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetParentId

`func (o *DocumentTree) SetParentId(v int64)`

SetParentId sets ParentId field to given value.

### HasParentId

`func (o *DocumentTree) HasParentId() bool`

HasParentId returns a boolean if a field has been set.

### GetPath

`func (o *DocumentTree) GetPath() string`

GetPath returns the Path field if non-nil, zero value otherwise.

### GetPathOk

`func (o *DocumentTree) GetPathOk() (*string, bool)`

GetPathOk returns a tuple with the Path field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetPath

`func (o *DocumentTree) SetPath(v string)`

SetPath sets Path field to given value.

### HasPath

`func (o *DocumentTree) HasPath() bool`

HasPath returns a boolean if a field has been set.

### GetRepositoryId

`func (o *DocumentTree) GetRepositoryId() int64`

GetRepositoryId returns the RepositoryId field if non-nil, zero value otherwise.

### GetRepositoryIdOk

`func (o *DocumentTree) GetRepositoryIdOk() (*int64, bool)`

GetRepositoryIdOk returns a tuple with the RepositoryId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetRepositoryId

`func (o *DocumentTree) SetRepositoryId(v int64)`

SetRepositoryId sets RepositoryId field to given value.

### HasRepositoryId

`func (o *DocumentTree) HasRepositoryId() bool`

HasRepositoryId returns a boolean if a field has been set.

### GetUpdateTime

`func (o *DocumentTree) GetUpdateTime() string`

GetUpdateTime returns the UpdateTime field if non-nil, zero value otherwise.

### GetUpdateTimeOk

`func (o *DocumentTree) GetUpdateTimeOk() (*string, bool)`

GetUpdateTimeOk returns a tuple with the UpdateTime field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUpdateTime

`func (o *DocumentTree) SetUpdateTime(v string)`

SetUpdateTime sets UpdateTime field to given value.

### HasUpdateTime

`func (o *DocumentTree) HasUpdateTime() bool`

HasUpdateTime returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


