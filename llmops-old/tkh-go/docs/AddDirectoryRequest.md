# AddDirectoryRequest

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Name** | Pointer to **string** | 文件及名称 | [optional] 
**ParentId** | Pointer to **int64** | 父级ID | [optional] 

## Methods

### NewAddDirectoryRequest

`func NewAddDirectoryRequest() *AddDirectoryRequest`

NewAddDirectoryRequest instantiates a new AddDirectoryRequest object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewAddDirectoryRequestWithDefaults

`func NewAddDirectoryRequestWithDefaults() *AddDirectoryRequest`

NewAddDirectoryRequestWithDefaults instantiates a new AddDirectoryRequest object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetName

`func (o *AddDirectoryRequest) GetName() string`

GetName returns the Name field if non-nil, zero value otherwise.

### GetNameOk

`func (o *AddDirectoryRequest) GetNameOk() (*string, bool)`

GetNameOk returns a tuple with the Name field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetName

`func (o *AddDirectoryRequest) SetName(v string)`

SetName sets Name field to given value.

### HasName

`func (o *AddDirectoryRequest) HasName() bool`

HasName returns a boolean if a field has been set.

### GetParentId

`func (o *AddDirectoryRequest) GetParentId() int64`

GetParentId returns the ParentId field if non-nil, zero value otherwise.

### GetParentIdOk

`func (o *AddDirectoryRequest) GetParentIdOk() (*int64, bool)`

GetParentIdOk returns a tuple with the ParentId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetParentId

`func (o *AddDirectoryRequest) SetParentId(v int64)`

SetParentId sets ParentId field to given value.

### HasParentId

`func (o *AddDirectoryRequest) HasParentId() bool`

HasParentId returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


