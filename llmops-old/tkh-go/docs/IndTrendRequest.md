# IndTrendRequest

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**EndDate** | Pointer to **string** |  | [optional] 
**IndName** | Pointer to **string** |  | [optional] 
**IndType** | Pointer to **string** |  | [optional] 
**StartDate** | Pointer to **string** |  | [optional] 

## Methods

### NewIndTrendRequest

`func NewIndTrendRequest() *IndTrendRequest`

NewIndTrendRequest instantiates a new IndTrendRequest object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewIndTrendRequestWithDefaults

`func NewIndTrendRequestWithDefaults() *IndTrendRequest`

NewIndTrendRequestWithDefaults instantiates a new IndTrendRequest object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetEndDate

`func (o *IndTrendRequest) GetEndDate() string`

GetEndDate returns the EndDate field if non-nil, zero value otherwise.

### GetEndDateOk

`func (o *IndTrendRequest) GetEndDateOk() (*string, bool)`

GetEndDateOk returns a tuple with the EndDate field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetEndDate

`func (o *IndTrendRequest) SetEndDate(v string)`

SetEndDate sets EndDate field to given value.

### HasEndDate

`func (o *IndTrendRequest) HasEndDate() bool`

HasEndDate returns a boolean if a field has been set.

### GetIndName

`func (o *IndTrendRequest) GetIndName() string`

GetIndName returns the IndName field if non-nil, zero value otherwise.

### GetIndNameOk

`func (o *IndTrendRequest) GetIndNameOk() (*string, bool)`

GetIndNameOk returns a tuple with the IndName field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetIndName

`func (o *IndTrendRequest) SetIndName(v string)`

SetIndName sets IndName field to given value.

### HasIndName

`func (o *IndTrendRequest) HasIndName() bool`

HasIndName returns a boolean if a field has been set.

### GetIndType

`func (o *IndTrendRequest) GetIndType() string`

GetIndType returns the IndType field if non-nil, zero value otherwise.

### GetIndTypeOk

`func (o *IndTrendRequest) GetIndTypeOk() (*string, bool)`

GetIndTypeOk returns a tuple with the IndType field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetIndType

`func (o *IndTrendRequest) SetIndType(v string)`

SetIndType sets IndType field to given value.

### HasIndType

`func (o *IndTrendRequest) HasIndType() bool`

HasIndType returns a boolean if a field has been set.

### GetStartDate

`func (o *IndTrendRequest) GetStartDate() string`

GetStartDate returns the StartDate field if non-nil, zero value otherwise.

### GetStartDateOk

`func (o *IndTrendRequest) GetStartDateOk() (*string, bool)`

GetStartDateOk returns a tuple with the StartDate field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetStartDate

`func (o *IndTrendRequest) SetStartDate(v string)`

SetStartDate sets StartDate field to given value.

### HasStartDate

`func (o *IndTrendRequest) HasStartDate() bool`

HasStartDate returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


