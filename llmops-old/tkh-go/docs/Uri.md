# Uri

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Absolute** | Pointer to **bool** |  | [optional] 
**Authority** | Pointer to **string** |  | [optional] 
**Fragment** | Pointer to **string** |  | [optional] 
**Host** | Pointer to **string** |  | [optional] 
**Opaque** | Pointer to **bool** |  | [optional] 
**Path** | Pointer to **string** |  | [optional] 
**Port** | Pointer to **int32** |  | [optional] 
**Query** | Pointer to **string** |  | [optional] 
**RawAuthority** | Pointer to **string** |  | [optional] 
**RawFragment** | Pointer to **string** |  | [optional] 
**RawPath** | Pointer to **string** |  | [optional] 
**RawQuery** | Pointer to **string** |  | [optional] 
**RawSchemeSpecificPart** | Pointer to **string** |  | [optional] 
**RawUserInfo** | Pointer to **string** |  | [optional] 
**Scheme** | Pointer to **string** |  | [optional] 
**SchemeSpecificPart** | Pointer to **string** |  | [optional] 
**UserInfo** | Pointer to **string** |  | [optional] 

## Methods

### NewUri

`func NewUri() *Uri`

NewUri instantiates a new Uri object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewUriWithDefaults

`func NewUriWithDefaults() *Uri`

NewUriWithDefaults instantiates a new Uri object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetAbsolute

`func (o *Uri) GetAbsolute() bool`

GetAbsolute returns the Absolute field if non-nil, zero value otherwise.

### GetAbsoluteOk

`func (o *Uri) GetAbsoluteOk() (*bool, bool)`

GetAbsoluteOk returns a tuple with the Absolute field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetAbsolute

`func (o *Uri) SetAbsolute(v bool)`

SetAbsolute sets Absolute field to given value.

### HasAbsolute

`func (o *Uri) HasAbsolute() bool`

HasAbsolute returns a boolean if a field has been set.

### GetAuthority

`func (o *Uri) GetAuthority() string`

GetAuthority returns the Authority field if non-nil, zero value otherwise.

### GetAuthorityOk

`func (o *Uri) GetAuthorityOk() (*string, bool)`

GetAuthorityOk returns a tuple with the Authority field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetAuthority

`func (o *Uri) SetAuthority(v string)`

SetAuthority sets Authority field to given value.

### HasAuthority

`func (o *Uri) HasAuthority() bool`

HasAuthority returns a boolean if a field has been set.

### GetFragment

`func (o *Uri) GetFragment() string`

GetFragment returns the Fragment field if non-nil, zero value otherwise.

### GetFragmentOk

`func (o *Uri) GetFragmentOk() (*string, bool)`

GetFragmentOk returns a tuple with the Fragment field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFragment

`func (o *Uri) SetFragment(v string)`

SetFragment sets Fragment field to given value.

### HasFragment

`func (o *Uri) HasFragment() bool`

HasFragment returns a boolean if a field has been set.

### GetHost

`func (o *Uri) GetHost() string`

GetHost returns the Host field if non-nil, zero value otherwise.

### GetHostOk

`func (o *Uri) GetHostOk() (*string, bool)`

GetHostOk returns a tuple with the Host field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetHost

`func (o *Uri) SetHost(v string)`

SetHost sets Host field to given value.

### HasHost

`func (o *Uri) HasHost() bool`

HasHost returns a boolean if a field has been set.

### GetOpaque

`func (o *Uri) GetOpaque() bool`

GetOpaque returns the Opaque field if non-nil, zero value otherwise.

### GetOpaqueOk

`func (o *Uri) GetOpaqueOk() (*bool, bool)`

GetOpaqueOk returns a tuple with the Opaque field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetOpaque

`func (o *Uri) SetOpaque(v bool)`

SetOpaque sets Opaque field to given value.

### HasOpaque

`func (o *Uri) HasOpaque() bool`

HasOpaque returns a boolean if a field has been set.

### GetPath

`func (o *Uri) GetPath() string`

GetPath returns the Path field if non-nil, zero value otherwise.

### GetPathOk

`func (o *Uri) GetPathOk() (*string, bool)`

GetPathOk returns a tuple with the Path field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetPath

`func (o *Uri) SetPath(v string)`

SetPath sets Path field to given value.

### HasPath

`func (o *Uri) HasPath() bool`

HasPath returns a boolean if a field has been set.

### GetPort

`func (o *Uri) GetPort() int32`

GetPort returns the Port field if non-nil, zero value otherwise.

### GetPortOk

`func (o *Uri) GetPortOk() (*int32, bool)`

GetPortOk returns a tuple with the Port field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetPort

`func (o *Uri) SetPort(v int32)`

SetPort sets Port field to given value.

### HasPort

`func (o *Uri) HasPort() bool`

HasPort returns a boolean if a field has been set.

### GetQuery

`func (o *Uri) GetQuery() string`

GetQuery returns the Query field if non-nil, zero value otherwise.

### GetQueryOk

`func (o *Uri) GetQueryOk() (*string, bool)`

GetQueryOk returns a tuple with the Query field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetQuery

`func (o *Uri) SetQuery(v string)`

SetQuery sets Query field to given value.

### HasQuery

`func (o *Uri) HasQuery() bool`

HasQuery returns a boolean if a field has been set.

### GetRawAuthority

`func (o *Uri) GetRawAuthority() string`

GetRawAuthority returns the RawAuthority field if non-nil, zero value otherwise.

### GetRawAuthorityOk

`func (o *Uri) GetRawAuthorityOk() (*string, bool)`

GetRawAuthorityOk returns a tuple with the RawAuthority field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetRawAuthority

`func (o *Uri) SetRawAuthority(v string)`

SetRawAuthority sets RawAuthority field to given value.

### HasRawAuthority

`func (o *Uri) HasRawAuthority() bool`

HasRawAuthority returns a boolean if a field has been set.

### GetRawFragment

`func (o *Uri) GetRawFragment() string`

GetRawFragment returns the RawFragment field if non-nil, zero value otherwise.

### GetRawFragmentOk

`func (o *Uri) GetRawFragmentOk() (*string, bool)`

GetRawFragmentOk returns a tuple with the RawFragment field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetRawFragment

`func (o *Uri) SetRawFragment(v string)`

SetRawFragment sets RawFragment field to given value.

### HasRawFragment

`func (o *Uri) HasRawFragment() bool`

HasRawFragment returns a boolean if a field has been set.

### GetRawPath

`func (o *Uri) GetRawPath() string`

GetRawPath returns the RawPath field if non-nil, zero value otherwise.

### GetRawPathOk

`func (o *Uri) GetRawPathOk() (*string, bool)`

GetRawPathOk returns a tuple with the RawPath field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetRawPath

`func (o *Uri) SetRawPath(v string)`

SetRawPath sets RawPath field to given value.

### HasRawPath

`func (o *Uri) HasRawPath() bool`

HasRawPath returns a boolean if a field has been set.

### GetRawQuery

`func (o *Uri) GetRawQuery() string`

GetRawQuery returns the RawQuery field if non-nil, zero value otherwise.

### GetRawQueryOk

`func (o *Uri) GetRawQueryOk() (*string, bool)`

GetRawQueryOk returns a tuple with the RawQuery field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetRawQuery

`func (o *Uri) SetRawQuery(v string)`

SetRawQuery sets RawQuery field to given value.

### HasRawQuery

`func (o *Uri) HasRawQuery() bool`

HasRawQuery returns a boolean if a field has been set.

### GetRawSchemeSpecificPart

`func (o *Uri) GetRawSchemeSpecificPart() string`

GetRawSchemeSpecificPart returns the RawSchemeSpecificPart field if non-nil, zero value otherwise.

### GetRawSchemeSpecificPartOk

`func (o *Uri) GetRawSchemeSpecificPartOk() (*string, bool)`

GetRawSchemeSpecificPartOk returns a tuple with the RawSchemeSpecificPart field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetRawSchemeSpecificPart

`func (o *Uri) SetRawSchemeSpecificPart(v string)`

SetRawSchemeSpecificPart sets RawSchemeSpecificPart field to given value.

### HasRawSchemeSpecificPart

`func (o *Uri) HasRawSchemeSpecificPart() bool`

HasRawSchemeSpecificPart returns a boolean if a field has been set.

### GetRawUserInfo

`func (o *Uri) GetRawUserInfo() string`

GetRawUserInfo returns the RawUserInfo field if non-nil, zero value otherwise.

### GetRawUserInfoOk

`func (o *Uri) GetRawUserInfoOk() (*string, bool)`

GetRawUserInfoOk returns a tuple with the RawUserInfo field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetRawUserInfo

`func (o *Uri) SetRawUserInfo(v string)`

SetRawUserInfo sets RawUserInfo field to given value.

### HasRawUserInfo

`func (o *Uri) HasRawUserInfo() bool`

HasRawUserInfo returns a boolean if a field has been set.

### GetScheme

`func (o *Uri) GetScheme() string`

GetScheme returns the Scheme field if non-nil, zero value otherwise.

### GetSchemeOk

`func (o *Uri) GetSchemeOk() (*string, bool)`

GetSchemeOk returns a tuple with the Scheme field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetScheme

`func (o *Uri) SetScheme(v string)`

SetScheme sets Scheme field to given value.

### HasScheme

`func (o *Uri) HasScheme() bool`

HasScheme returns a boolean if a field has been set.

### GetSchemeSpecificPart

`func (o *Uri) GetSchemeSpecificPart() string`

GetSchemeSpecificPart returns the SchemeSpecificPart field if non-nil, zero value otherwise.

### GetSchemeSpecificPartOk

`func (o *Uri) GetSchemeSpecificPartOk() (*string, bool)`

GetSchemeSpecificPartOk returns a tuple with the SchemeSpecificPart field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetSchemeSpecificPart

`func (o *Uri) SetSchemeSpecificPart(v string)`

SetSchemeSpecificPart sets SchemeSpecificPart field to given value.

### HasSchemeSpecificPart

`func (o *Uri) HasSchemeSpecificPart() bool`

HasSchemeSpecificPart returns a boolean if a field has been set.

### GetUserInfo

`func (o *Uri) GetUserInfo() string`

GetUserInfo returns the UserInfo field if non-nil, zero value otherwise.

### GetUserInfoOk

`func (o *Uri) GetUserInfoOk() (*string, bool)`

GetUserInfoOk returns a tuple with the UserInfo field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUserInfo

`func (o *Uri) SetUserInfo(v string)`

SetUserInfo sets UserInfo field to given value.

### HasUserInfo

`func (o *Uri) HasUserInfo() bool`

HasUserInfo returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


