# ApproveInfoDuiXiang

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Apikey** | Pointer to **string** |  | [optional] 
**EndTime** | Pointer to **string** |  | [optional] 
**Id** | Pointer to **int32** |  | [optional] 
**Purpose** | Pointer to **string** |  | [optional] 
**StartTime** | Pointer to **string** |  | [optional] 
**Status** | Pointer to **int32** |  | [optional] 
**Username** | Pointer to **string** |  | [optional] 

## Methods

### NewApproveInfoDuiXiang

`func NewApproveInfoDuiXiang() *ApproveInfoDuiXiang`

NewApproveInfoDuiXiang instantiates a new ApproveInfoDuiXiang object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewApproveInfoDuiXiangWithDefaults

`func NewApproveInfoDuiXiangWithDefaults() *ApproveInfoDuiXiang`

NewApproveInfoDuiXiangWithDefaults instantiates a new ApproveInfoDuiXiang object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetApikey

`func (o *ApproveInfoDuiXiang) GetApikey() string`

GetApikey returns the Apikey field if non-nil, zero value otherwise.

### GetApikeyOk

`func (o *ApproveInfoDuiXiang) GetApikeyOk() (*string, bool)`

GetApikeyOk returns a tuple with the Apikey field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetApikey

`func (o *ApproveInfoDuiXiang) SetApikey(v string)`

SetApikey sets Apikey field to given value.

### HasApikey

`func (o *ApproveInfoDuiXiang) HasApikey() bool`

HasApikey returns a boolean if a field has been set.

### GetEndTime

`func (o *ApproveInfoDuiXiang) GetEndTime() string`

GetEndTime returns the EndTime field if non-nil, zero value otherwise.

### GetEndTimeOk

`func (o *ApproveInfoDuiXiang) GetEndTimeOk() (*string, bool)`

GetEndTimeOk returns a tuple with the EndTime field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetEndTime

`func (o *ApproveInfoDuiXiang) SetEndTime(v string)`

SetEndTime sets EndTime field to given value.

### HasEndTime

`func (o *ApproveInfoDuiXiang) HasEndTime() bool`

HasEndTime returns a boolean if a field has been set.

### GetId

`func (o *ApproveInfoDuiXiang) GetId() int32`

GetId returns the Id field if non-nil, zero value otherwise.

### GetIdOk

`func (o *ApproveInfoDuiXiang) GetIdOk() (*int32, bool)`

GetIdOk returns a tuple with the Id field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetId

`func (o *ApproveInfoDuiXiang) SetId(v int32)`

SetId sets Id field to given value.

### HasId

`func (o *ApproveInfoDuiXiang) HasId() bool`

HasId returns a boolean if a field has been set.

### GetPurpose

`func (o *ApproveInfoDuiXiang) GetPurpose() string`

GetPurpose returns the Purpose field if non-nil, zero value otherwise.

### GetPurposeOk

`func (o *ApproveInfoDuiXiang) GetPurposeOk() (*string, bool)`

GetPurposeOk returns a tuple with the Purpose field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetPurpose

`func (o *ApproveInfoDuiXiang) SetPurpose(v string)`

SetPurpose sets Purpose field to given value.

### HasPurpose

`func (o *ApproveInfoDuiXiang) HasPurpose() bool`

HasPurpose returns a boolean if a field has been set.

### GetStartTime

`func (o *ApproveInfoDuiXiang) GetStartTime() string`

GetStartTime returns the StartTime field if non-nil, zero value otherwise.

### GetStartTimeOk

`func (o *ApproveInfoDuiXiang) GetStartTimeOk() (*string, bool)`

GetStartTimeOk returns a tuple with the StartTime field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetStartTime

`func (o *ApproveInfoDuiXiang) SetStartTime(v string)`

SetStartTime sets StartTime field to given value.

### HasStartTime

`func (o *ApproveInfoDuiXiang) HasStartTime() bool`

HasStartTime returns a boolean if a field has been set.

### GetStatus

`func (o *ApproveInfoDuiXiang) GetStatus() int32`

GetStatus returns the Status field if non-nil, zero value otherwise.

### GetStatusOk

`func (o *ApproveInfoDuiXiang) GetStatusOk() (*int32, bool)`

GetStatusOk returns a tuple with the Status field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetStatus

`func (o *ApproveInfoDuiXiang) SetStatus(v int32)`

SetStatus sets Status field to given value.

### HasStatus

`func (o *ApproveInfoDuiXiang) HasStatus() bool`

HasStatus returns a boolean if a field has been set.

### GetUsername

`func (o *ApproveInfoDuiXiang) GetUsername() string`

GetUsername returns the Username field if non-nil, zero value otherwise.

### GetUsernameOk

`func (o *ApproveInfoDuiXiang) GetUsernameOk() (*string, bool)`

GetUsernameOk returns a tuple with the Username field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUsername

`func (o *ApproveInfoDuiXiang) SetUsername(v string)`

SetUsername sets Username field to given value.

### HasUsername

`func (o *ApproveInfoDuiXiang) HasUsername() bool`

HasUsername returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


