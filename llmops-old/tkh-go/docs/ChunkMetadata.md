# ChunkMetadata

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Page** | Pointer to **string** |  | [optional] 
**TotalPages** | Pointer to **int32** |  | [optional] 

## Methods

### NewChunkMetadata

`func NewChunkMetadata() *ChunkMetadata`

NewChunkMetadata instantiates a new ChunkMetadata object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewChunkMetadataWithDefaults

`func NewChunkMetadataWithDefaults() *ChunkMetadata`

NewChunkMetadataWithDefaults instantiates a new ChunkMetadata object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetPage

`func (o *ChunkMetadata) GetPage() string`

GetPage returns the Page field if non-nil, zero value otherwise.

### GetPageOk

`func (o *ChunkMetadata) GetPageOk() (*string, bool)`

GetPageOk returns a tuple with the Page field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetPage

`func (o *ChunkMetadata) SetPage(v string)`

SetPage sets Page field to given value.

### HasPage

`func (o *ChunkMetadata) HasPage() bool`

HasPage returns a boolean if a field has been set.

### GetTotalPages

`func (o *ChunkMetadata) GetTotalPages() int32`

GetTotalPages returns the TotalPages field if non-nil, zero value otherwise.

### GetTotalPagesOk

`func (o *ChunkMetadata) GetTotalPagesOk() (*int32, bool)`

GetTotalPagesOk returns a tuple with the TotalPages field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetTotalPages

`func (o *ChunkMetadata) SetTotalPages(v int32)`

SetTotalPages sets TotalPages field to given value.

### HasTotalPages

`func (o *ChunkMetadata) HasTotalPages() bool`

HasTotalPages returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


