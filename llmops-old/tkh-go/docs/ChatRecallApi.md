# \ChatRecallApi

All URIs are relative to *http://localhost*

Method | HTTP request | Description
------------- | ------------- | -------------
[**RecallInfinityChatRecallPost**](ChatRecallApi.md#RecallInfinityChatRecallPost) | **Post** /infinity/chat/recall | Recall



## RecallInfinityChatRecallPost

> string RecallInfinityChatRecallPost(ctx).RecallServiceRequest(recallServiceRequest).Execute()

Recall

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    recallServiceRequest := openapiclient.RecallServiceRequest{UserQueries: []string{"UserQueries_example"), Sources: []KnowledgeSource{openapiclient.KnowledgeSource{KbId: "KbId_example", DocId: "DocId_example", DirId: "DirId_example"}), HippoDataSources: []string{"HippoDataSources_example"), ScopeDataSources: []string{"ScopeDataSources_example"), ReservedConditionCompany: []string{"ReservedConditionCompany_example"), ReservedConditionTime: []string{"ReservedConditionTime_example"), PdfName: "PdfName_example", DocId: "DocId_example", TopK: 123, FragmentTextFiler: false, Deduplicate: false, TableReformat: false, Rerank: false, CompactText: false} // RecallServiceRequest |  (optional)

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.ChatRecallApi.RecallInfinityChatRecallPost(context.Background(), ).RecallServiceRequest(recallServiceRequest).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `ChatRecallApi.RecallInfinityChatRecallPost``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `RecallInfinityChatRecallPost`: string
    fmt.Fprintf(os.Stdout, "Response from `ChatRecallApi.RecallInfinityChatRecallPost`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiRecallInfinityChatRecallPostRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **recallServiceRequest** | [**RecallServiceRequest**](RecallServiceRequest.md) |  | 

### Return type

**string**

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)

