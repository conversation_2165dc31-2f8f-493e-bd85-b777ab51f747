# ImageDetail

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**DirIds** | Pointer to **[]string** |  | [optional] 
**Distance** | Pointer to **string** |  | [optional] 
**DocId** | Pointer to **string** |  | [optional] 
**FileName** | Pointer to **string** |  | [optional] 
**ImageData** | Pointer to **string** |  | [optional] 
**KbId** | Pointer to **string** |  | [optional] 

## Methods

### NewImageDetail

`func NewImageDetail() *ImageDetail`

NewImageDetail instantiates a new ImageDetail object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewImageDetailWithDefaults

`func NewImageDetailWithDefaults() *ImageDetail`

NewImageDetailWithDefaults instantiates a new ImageDetail object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetDirIds

`func (o *ImageDetail) GetDirIds() []string`

GetDirIds returns the DirIds field if non-nil, zero value otherwise.

### GetDirIdsOk

`func (o *ImageDetail) GetDirIdsOk() (*[]string, bool)`

GetDirIdsOk returns a tuple with the DirIds field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDirIds

`func (o *ImageDetail) SetDirIds(v []string)`

SetDirIds sets DirIds field to given value.

### HasDirIds

`func (o *ImageDetail) HasDirIds() bool`

HasDirIds returns a boolean if a field has been set.

### GetDistance

`func (o *ImageDetail) GetDistance() string`

GetDistance returns the Distance field if non-nil, zero value otherwise.

### GetDistanceOk

`func (o *ImageDetail) GetDistanceOk() (*string, bool)`

GetDistanceOk returns a tuple with the Distance field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDistance

`func (o *ImageDetail) SetDistance(v string)`

SetDistance sets Distance field to given value.

### HasDistance

`func (o *ImageDetail) HasDistance() bool`

HasDistance returns a boolean if a field has been set.

### GetDocId

`func (o *ImageDetail) GetDocId() string`

GetDocId returns the DocId field if non-nil, zero value otherwise.

### GetDocIdOk

`func (o *ImageDetail) GetDocIdOk() (*string, bool)`

GetDocIdOk returns a tuple with the DocId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDocId

`func (o *ImageDetail) SetDocId(v string)`

SetDocId sets DocId field to given value.

### HasDocId

`func (o *ImageDetail) HasDocId() bool`

HasDocId returns a boolean if a field has been set.

### GetFileName

`func (o *ImageDetail) GetFileName() string`

GetFileName returns the FileName field if non-nil, zero value otherwise.

### GetFileNameOk

`func (o *ImageDetail) GetFileNameOk() (*string, bool)`

GetFileNameOk returns a tuple with the FileName field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFileName

`func (o *ImageDetail) SetFileName(v string)`

SetFileName sets FileName field to given value.

### HasFileName

`func (o *ImageDetail) HasFileName() bool`

HasFileName returns a boolean if a field has been set.

### GetImageData

`func (o *ImageDetail) GetImageData() string`

GetImageData returns the ImageData field if non-nil, zero value otherwise.

### GetImageDataOk

`func (o *ImageDetail) GetImageDataOk() (*string, bool)`

GetImageDataOk returns a tuple with the ImageData field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetImageData

`func (o *ImageDetail) SetImageData(v string)`

SetImageData sets ImageData field to given value.

### HasImageData

`func (o *ImageDetail) HasImageData() bool`

HasImageData returns a boolean if a field has been set.

### GetKbId

`func (o *ImageDetail) GetKbId() string`

GetKbId returns the KbId field if non-nil, zero value otherwise.

### GetKbIdOk

`func (o *ImageDetail) GetKbIdOk() (*string, bool)`

GetKbIdOk returns a tuple with the KbId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetKbId

`func (o *ImageDetail) SetKbId(v string)`

SetKbId sets KbId field to given value.

### HasKbId

`func (o *ImageDetail) HasKbId() bool`

HasKbId returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


