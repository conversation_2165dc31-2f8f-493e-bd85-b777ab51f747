# ResultListUserInfoDuiXiang

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Code** | Pointer to **int32** |  | [optional] 
**Data** | Pointer to [**[]UserInfoDuiXiang**](UserInfoDuiXiang.md) |  | [optional] 
**Message** | Pointer to **string** |  | [optional] 

## Methods

### NewResultListUserInfoDuiXiang

`func NewResultListUserInfoDuiXiang() *ResultListUserInfoDuiXiang`

NewResultListUserInfoDuiXiang instantiates a new ResultListUserInfoDuiXiang object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewResultListUserInfoDuiXiangWithDefaults

`func NewResultListUserInfoDuiXiangWithDefaults() *ResultListUserInfoDuiXiang`

NewResultListUserInfoDuiXiangWithDefaults instantiates a new ResultListUserInfoDuiXiang object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetCode

`func (o *ResultListUserInfoDuiXiang) GetCode() int32`

GetCode returns the Code field if non-nil, zero value otherwise.

### GetCodeOk

`func (o *ResultListUserInfoDuiXiang) GetCodeOk() (*int32, bool)`

GetCodeOk returns a tuple with the Code field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCode

`func (o *ResultListUserInfoDuiXiang) SetCode(v int32)`

SetCode sets Code field to given value.

### HasCode

`func (o *ResultListUserInfoDuiXiang) HasCode() bool`

HasCode returns a boolean if a field has been set.

### GetData

`func (o *ResultListUserInfoDuiXiang) GetData() []UserInfoDuiXiang`

GetData returns the Data field if non-nil, zero value otherwise.

### GetDataOk

`func (o *ResultListUserInfoDuiXiang) GetDataOk() (*[]UserInfoDuiXiang, bool)`

GetDataOk returns a tuple with the Data field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetData

`func (o *ResultListUserInfoDuiXiang) SetData(v []UserInfoDuiXiang)`

SetData sets Data field to given value.

### HasData

`func (o *ResultListUserInfoDuiXiang) HasData() bool`

HasData returns a boolean if a field has been set.

### GetMessage

`func (o *ResultListUserInfoDuiXiang) GetMessage() string`

GetMessage returns the Message field if non-nil, zero value otherwise.

### GetMessageOk

`func (o *ResultListUserInfoDuiXiang) GetMessageOk() (*string, bool)`

GetMessageOk returns a tuple with the Message field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetMessage

`func (o *ResultListUserInfoDuiXiang) SetMessage(v string)`

SetMessage sets Message field to given value.

### HasMessage

`func (o *ResultListUserInfoDuiXiang) HasMessage() bool`

HasMessage returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


