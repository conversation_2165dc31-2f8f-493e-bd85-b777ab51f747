# ImageMatchRequest

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**ImageData** | Pointer to **string** |  | [optional] 
**KbIdParams** | Pointer to [**[]KnowledgeBaseDirParams**](KnowledgeBaseDirParams.md) |  | [optional] 
**ReturnImage** | Pointer to **bool** |  | [optional] 
**TopK** | Pointer to **int32** |  | [optional] 

## Methods

### NewImageMatchRequest

`func NewImageMatchRequest() *ImageMatchRequest`

NewImageMatchRequest instantiates a new ImageMatchRequest object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewImageMatchRequestWithDefaults

`func NewImageMatchRequestWithDefaults() *ImageMatchRequest`

NewImageMatchRequestWithDefaults instantiates a new ImageMatchRequest object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetImageData

`func (o *ImageMatchRequest) GetImageData() string`

GetImageData returns the ImageData field if non-nil, zero value otherwise.

### GetImageDataOk

`func (o *ImageMatchRequest) GetImageDataOk() (*string, bool)`

GetImageDataOk returns a tuple with the ImageData field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetImageData

`func (o *ImageMatchRequest) SetImageData(v string)`

SetImageData sets ImageData field to given value.

### HasImageData

`func (o *ImageMatchRequest) HasImageData() bool`

HasImageData returns a boolean if a field has been set.

### GetKbIdParams

`func (o *ImageMatchRequest) GetKbIdParams() []KnowledgeBaseDirParams`

GetKbIdParams returns the KbIdParams field if non-nil, zero value otherwise.

### GetKbIdParamsOk

`func (o *ImageMatchRequest) GetKbIdParamsOk() (*[]KnowledgeBaseDirParams, bool)`

GetKbIdParamsOk returns a tuple with the KbIdParams field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetKbIdParams

`func (o *ImageMatchRequest) SetKbIdParams(v []KnowledgeBaseDirParams)`

SetKbIdParams sets KbIdParams field to given value.

### HasKbIdParams

`func (o *ImageMatchRequest) HasKbIdParams() bool`

HasKbIdParams returns a boolean if a field has been set.

### GetReturnImage

`func (o *ImageMatchRequest) GetReturnImage() bool`

GetReturnImage returns the ReturnImage field if non-nil, zero value otherwise.

### GetReturnImageOk

`func (o *ImageMatchRequest) GetReturnImageOk() (*bool, bool)`

GetReturnImageOk returns a tuple with the ReturnImage field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetReturnImage

`func (o *ImageMatchRequest) SetReturnImage(v bool)`

SetReturnImage sets ReturnImage field to given value.

### HasReturnImage

`func (o *ImageMatchRequest) HasReturnImage() bool`

HasReturnImage returns a boolean if a field has been set.

### GetTopK

`func (o *ImageMatchRequest) GetTopK() int32`

GetTopK returns the TopK field if non-nil, zero value otherwise.

### GetTopKOk

`func (o *ImageMatchRequest) GetTopKOk() (*int32, bool)`

GetTopKOk returns a tuple with the TopK field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetTopK

`func (o *ImageMatchRequest) SetTopK(v int32)`

SetTopK sets TopK field to given value.

### HasTopK

`func (o *ImageMatchRequest) HasTopK() bool`

HasTopK returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


