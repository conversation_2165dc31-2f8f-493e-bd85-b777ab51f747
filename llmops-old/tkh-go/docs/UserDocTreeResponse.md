# UserDocTreeResponse

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**FinanceTree** | Pointer to [**[]Repository**](Repository.md) |  | [optional] 
**InternetTree** | Pointer to [**[]Repository**](Repository.md) |  | [optional] 
**LawTree** | Pointer to [**[]Repository**](Repository.md) |  | [optional] 
**PrivateTree** | Pointer to [**[]Repository**](Repository.md) |  | [optional] 
**PublicTree** | Pointer to [**[]Repository**](Repository.md) |  | [optional] 

## Methods

### NewUserDocTreeResponse

`func NewUserDocTreeResponse() *UserDocTreeResponse`

NewUserDocTreeResponse instantiates a new UserDocTreeResponse object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewUserDocTreeResponseWithDefaults

`func NewUserDocTreeResponseWithDefaults() *UserDocTreeResponse`

NewUserDocTreeResponseWithDefaults instantiates a new UserDocTreeResponse object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetFinanceTree

`func (o *UserDocTreeResponse) GetFinanceTree() []Repository`

GetFinanceTree returns the FinanceTree field if non-nil, zero value otherwise.

### GetFinanceTreeOk

`func (o *UserDocTreeResponse) GetFinanceTreeOk() (*[]Repository, bool)`

GetFinanceTreeOk returns a tuple with the FinanceTree field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFinanceTree

`func (o *UserDocTreeResponse) SetFinanceTree(v []Repository)`

SetFinanceTree sets FinanceTree field to given value.

### HasFinanceTree

`func (o *UserDocTreeResponse) HasFinanceTree() bool`

HasFinanceTree returns a boolean if a field has been set.

### GetInternetTree

`func (o *UserDocTreeResponse) GetInternetTree() []Repository`

GetInternetTree returns the InternetTree field if non-nil, zero value otherwise.

### GetInternetTreeOk

`func (o *UserDocTreeResponse) GetInternetTreeOk() (*[]Repository, bool)`

GetInternetTreeOk returns a tuple with the InternetTree field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetInternetTree

`func (o *UserDocTreeResponse) SetInternetTree(v []Repository)`

SetInternetTree sets InternetTree field to given value.

### HasInternetTree

`func (o *UserDocTreeResponse) HasInternetTree() bool`

HasInternetTree returns a boolean if a field has been set.

### GetLawTree

`func (o *UserDocTreeResponse) GetLawTree() []Repository`

GetLawTree returns the LawTree field if non-nil, zero value otherwise.

### GetLawTreeOk

`func (o *UserDocTreeResponse) GetLawTreeOk() (*[]Repository, bool)`

GetLawTreeOk returns a tuple with the LawTree field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetLawTree

`func (o *UserDocTreeResponse) SetLawTree(v []Repository)`

SetLawTree sets LawTree field to given value.

### HasLawTree

`func (o *UserDocTreeResponse) HasLawTree() bool`

HasLawTree returns a boolean if a field has been set.

### GetPrivateTree

`func (o *UserDocTreeResponse) GetPrivateTree() []Repository`

GetPrivateTree returns the PrivateTree field if non-nil, zero value otherwise.

### GetPrivateTreeOk

`func (o *UserDocTreeResponse) GetPrivateTreeOk() (*[]Repository, bool)`

GetPrivateTreeOk returns a tuple with the PrivateTree field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetPrivateTree

`func (o *UserDocTreeResponse) SetPrivateTree(v []Repository)`

SetPrivateTree sets PrivateTree field to given value.

### HasPrivateTree

`func (o *UserDocTreeResponse) HasPrivateTree() bool`

HasPrivateTree returns a boolean if a field has been set.

### GetPublicTree

`func (o *UserDocTreeResponse) GetPublicTree() []Repository`

GetPublicTree returns the PublicTree field if non-nil, zero value otherwise.

### GetPublicTreeOk

`func (o *UserDocTreeResponse) GetPublicTreeOk() (*[]Repository, bool)`

GetPublicTreeOk returns a tuple with the PublicTree field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetPublicTree

`func (o *UserDocTreeResponse) SetPublicTree(v []Repository)`

SetPublicTree sets PublicTree field to given value.

### HasPublicTree

`func (o *UserDocTreeResponse) HasPublicTree() bool`

HasPublicTree returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


