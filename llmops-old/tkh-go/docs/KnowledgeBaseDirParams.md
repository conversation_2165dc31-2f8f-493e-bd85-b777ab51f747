# KnowledgeBaseDirParams

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**DirIds** | Pointer to **[]string** |  | [optional] 
**KbId** | Pointer to **string** |  | [optional] 

## Methods

### NewKnowledgeBaseDirParams

`func NewKnowledgeBaseDirParams() *KnowledgeBaseDirParams`

NewKnowledgeBaseDirParams instantiates a new KnowledgeBaseDirParams object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewKnowledgeBaseDirParamsWithDefaults

`func NewKnowledgeBaseDirParamsWithDefaults() *KnowledgeBaseDirParams`

NewKnowledgeBaseDirParamsWithDefaults instantiates a new KnowledgeBaseDirParams object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetDirIds

`func (o *KnowledgeBaseDirParams) GetDirIds() []string`

GetDirIds returns the DirIds field if non-nil, zero value otherwise.

### GetDirIdsOk

`func (o *KnowledgeBaseDirParams) GetDirIdsOk() (*[]string, bool)`

GetDirIdsOk returns a tuple with the DirIds field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDirIds

`func (o *KnowledgeBaseDirParams) SetDirIds(v []string)`

SetDirIds sets DirIds field to given value.

### HasDirIds

`func (o *KnowledgeBaseDirParams) HasDirIds() bool`

HasDirIds returns a boolean if a field has been set.

### GetKbId

`func (o *KnowledgeBaseDirParams) GetKbId() string`

GetKbId returns the KbId field if non-nil, zero value otherwise.

### GetKbIdOk

`func (o *KnowledgeBaseDirParams) GetKbIdOk() (*string, bool)`

GetKbIdOk returns a tuple with the KbId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetKbId

`func (o *KnowledgeBaseDirParams) SetKbId(v string)`

SetKbId sets KbId field to given value.

### HasKbId

`func (o *KnowledgeBaseDirParams) HasKbId() bool`

HasKbId returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


