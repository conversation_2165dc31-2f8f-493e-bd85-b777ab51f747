# DocPreviewRequest

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**DocUuid** | Pointer to **string** |  | [optional] 
**EndPage** | Pointer to **int32** |  | [optional] 
**StartPage** | Pointer to **int32** |  | [optional] 

## Methods

### NewDocPreviewRequest

`func NewDocPreviewRequest() *DocPreviewRequest`

NewDocPreviewRequest instantiates a new DocPreviewRequest object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewDocPreviewRequestWithDefaults

`func NewDocPreviewRequestWithDefaults() *DocPreviewRequest`

NewDocPreviewRequestWithDefaults instantiates a new DocPreviewRequest object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetDocUuid

`func (o *DocPreviewRequest) GetDocUuid() string`

GetDocUuid returns the DocUuid field if non-nil, zero value otherwise.

### GetDocUuidOk

`func (o *DocPreviewRequest) GetDocUuidOk() (*string, bool)`

GetDocUuidOk returns a tuple with the DocUuid field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDocUuid

`func (o *DocPreviewRequest) SetDocUuid(v string)`

SetDocUuid sets DocUuid field to given value.

### HasDocUuid

`func (o *DocPreviewRequest) HasDocUuid() bool`

HasDocUuid returns a boolean if a field has been set.

### GetEndPage

`func (o *DocPreviewRequest) GetEndPage() int32`

GetEndPage returns the EndPage field if non-nil, zero value otherwise.

### GetEndPageOk

`func (o *DocPreviewRequest) GetEndPageOk() (*int32, bool)`

GetEndPageOk returns a tuple with the EndPage field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetEndPage

`func (o *DocPreviewRequest) SetEndPage(v int32)`

SetEndPage sets EndPage field to given value.

### HasEndPage

`func (o *DocPreviewRequest) HasEndPage() bool`

HasEndPage returns a boolean if a field has been set.

### GetStartPage

`func (o *DocPreviewRequest) GetStartPage() int32`

GetStartPage returns the StartPage field if non-nil, zero value otherwise.

### GetStartPageOk

`func (o *DocPreviewRequest) GetStartPageOk() (*int32, bool)`

GetStartPageOk returns a tuple with the StartPage field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetStartPage

`func (o *DocPreviewRequest) SetStartPage(v int32)`

SetStartPage sets StartPage field to given value.

### HasStartPage

`func (o *DocPreviewRequest) HasStartPage() bool`

HasStartPage returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


