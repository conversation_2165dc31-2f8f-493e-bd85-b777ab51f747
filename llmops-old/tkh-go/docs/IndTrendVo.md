# IndTrendVo

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Index** | Pointer to **float64** |  | [optional] 
**TradeDate** | Pointer to **string** |  | [optional] 

## Methods

### NewIndTrendVo

`func NewIndTrendVo() *IndTrendVo`

NewIndTrendVo instantiates a new IndTrendVo object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewIndTrendVoWithDefaults

`func NewIndTrendVoWithDefaults() *IndTrendVo`

NewIndTrendVoWithDefaults instantiates a new IndTrendVo object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetIndex

`func (o *IndTrendVo) GetIndex() float64`

GetIndex returns the Index field if non-nil, zero value otherwise.

### GetIndexOk

`func (o *IndTrendVo) GetIndexOk() (*float64, bool)`

GetIndexOk returns a tuple with the Index field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetIndex

`func (o *IndTrendVo) SetIndex(v float64)`

SetIndex sets Index field to given value.

### HasIndex

`func (o *IndTrendVo) HasIndex() bool`

HasIndex returns a boolean if a field has been set.

### GetTradeDate

`func (o *IndTrendVo) GetTradeDate() string`

GetTradeDate returns the TradeDate field if non-nil, zero value otherwise.

### GetTradeDateOk

`func (o *IndTrendVo) GetTradeDateOk() (*string, bool)`

GetTradeDateOk returns a tuple with the TradeDate field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetTradeDate

`func (o *IndTrendVo) SetTradeDate(v string)`

SetTradeDate sets TradeDate field to given value.

### HasTradeDate

`func (o *IndTrendVo) HasTradeDate() bool`

HasTradeDate returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


