# TokenRequestInfo

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**ClientId** | Pointer to **string** | app | [optional] 
**ClientSecret** | Pointer to **string** | secret | [optional] 
**Password** | Pointer to **string** |  | [optional] 
**UserName** | Pointer to **string** |  | [optional] 

## Methods

### NewTokenRequestInfo

`func NewTokenRequestInfo() *TokenRequestInfo`

NewTokenRequestInfo instantiates a new TokenRequestInfo object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewTokenRequestInfoWithDefaults

`func NewTokenRequestInfoWithDefaults() *TokenRequestInfo`

NewTokenRequestInfoWithDefaults instantiates a new TokenRequestInfo object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetClientId

`func (o *TokenRequestInfo) GetClientId() string`

GetClientId returns the ClientId field if non-nil, zero value otherwise.

### GetClientIdOk

`func (o *TokenRequestInfo) GetClientIdOk() (*string, bool)`

GetClientIdOk returns a tuple with the ClientId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetClientId

`func (o *TokenRequestInfo) SetClientId(v string)`

SetClientId sets ClientId field to given value.

### HasClientId

`func (o *TokenRequestInfo) HasClientId() bool`

HasClientId returns a boolean if a field has been set.

### GetClientSecret

`func (o *TokenRequestInfo) GetClientSecret() string`

GetClientSecret returns the ClientSecret field if non-nil, zero value otherwise.

### GetClientSecretOk

`func (o *TokenRequestInfo) GetClientSecretOk() (*string, bool)`

GetClientSecretOk returns a tuple with the ClientSecret field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetClientSecret

`func (o *TokenRequestInfo) SetClientSecret(v string)`

SetClientSecret sets ClientSecret field to given value.

### HasClientSecret

`func (o *TokenRequestInfo) HasClientSecret() bool`

HasClientSecret returns a boolean if a field has been set.

### GetPassword

`func (o *TokenRequestInfo) GetPassword() string`

GetPassword returns the Password field if non-nil, zero value otherwise.

### GetPasswordOk

`func (o *TokenRequestInfo) GetPasswordOk() (*string, bool)`

GetPasswordOk returns a tuple with the Password field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetPassword

`func (o *TokenRequestInfo) SetPassword(v string)`

SetPassword sets Password field to given value.

### HasPassword

`func (o *TokenRequestInfo) HasPassword() bool`

HasPassword returns a boolean if a field has been set.

### GetUserName

`func (o *TokenRequestInfo) GetUserName() string`

GetUserName returns the UserName field if non-nil, zero value otherwise.

### GetUserNameOk

`func (o *TokenRequestInfo) GetUserNameOk() (*string, bool)`

GetUserNameOk returns a tuple with the UserName field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUserName

`func (o *TokenRequestInfo) SetUserName(v string)`

SetUserName sets UserName field to given value.

### HasUserName

`func (o *TokenRequestInfo) HasUserName() bool`

HasUserName returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


