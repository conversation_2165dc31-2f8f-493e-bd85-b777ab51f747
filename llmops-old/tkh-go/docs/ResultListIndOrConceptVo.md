# ResultListIndOrConceptVo

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Code** | Pointer to **int32** |  | [optional] 
**Data** | Pointer to [**[]IndOrConceptVo**](IndOrConceptVo.md) |  | [optional] 
**Message** | Pointer to **string** |  | [optional] 

## Methods

### NewResultListIndOrConceptVo

`func NewResultListIndOrConceptVo() *ResultListIndOrConceptVo`

NewResultListIndOrConceptVo instantiates a new ResultListIndOrConceptVo object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewResultListIndOrConceptVoWithDefaults

`func NewResultListIndOrConceptVoWithDefaults() *ResultListIndOrConceptVo`

NewResultListIndOrConceptVoWithDefaults instantiates a new ResultListIndOrConceptVo object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetCode

`func (o *ResultListIndOrConceptVo) GetCode() int32`

GetCode returns the Code field if non-nil, zero value otherwise.

### GetCodeOk

`func (o *ResultListIndOrConceptVo) GetCodeOk() (*int32, bool)`

GetCodeOk returns a tuple with the Code field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCode

`func (o *ResultListIndOrConceptVo) SetCode(v int32)`

SetCode sets Code field to given value.

### HasCode

`func (o *ResultListIndOrConceptVo) HasCode() bool`

HasCode returns a boolean if a field has been set.

### GetData

`func (o *ResultListIndOrConceptVo) GetData() []IndOrConceptVo`

GetData returns the Data field if non-nil, zero value otherwise.

### GetDataOk

`func (o *ResultListIndOrConceptVo) GetDataOk() (*[]IndOrConceptVo, bool)`

GetDataOk returns a tuple with the Data field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetData

`func (o *ResultListIndOrConceptVo) SetData(v []IndOrConceptVo)`

SetData sets Data field to given value.

### HasData

`func (o *ResultListIndOrConceptVo) HasData() bool`

HasData returns a boolean if a field has been set.

### GetMessage

`func (o *ResultListIndOrConceptVo) GetMessage() string`

GetMessage returns the Message field if non-nil, zero value otherwise.

### GetMessageOk

`func (o *ResultListIndOrConceptVo) GetMessageOk() (*string, bool)`

GetMessageOk returns a tuple with the Message field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetMessage

`func (o *ResultListIndOrConceptVo) SetMessage(v string)`

SetMessage sets Message field to given value.

### HasMessage

`func (o *ResultListIndOrConceptVo) HasMessage() bool`

HasMessage returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


