# TreeNodeListVo

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Children** | Pointer to [**[]DocTreeDetail**](DocTreeDetail.md) |  | [optional] 
**ParentId** | Pointer to **int64** |  | [optional] 
**RepositoryId** | Pointer to **int64** |  | [optional] 

## Methods

### NewTreeNodeListVo

`func NewTreeNodeListVo() *TreeNodeListVo`

NewTreeNodeListVo instantiates a new TreeNodeListVo object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewTreeNodeListVoWithDefaults

`func NewTreeNodeListVoWithDefaults() *TreeNodeListVo`

NewTreeNodeListVoWithDefaults instantiates a new TreeNodeListVo object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetChildren

`func (o *TreeNodeListVo) GetChildren() []DocTreeDetail`

GetChildren returns the Children field if non-nil, zero value otherwise.

### GetChildrenOk

`func (o *TreeNodeListVo) GetChildrenOk() (*[]DocTreeDetail, bool)`

GetChildrenOk returns a tuple with the Children field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetChildren

`func (o *TreeNodeListVo) SetChildren(v []DocTreeDetail)`

SetChildren sets Children field to given value.

### HasChildren

`func (o *TreeNodeListVo) HasChildren() bool`

HasChildren returns a boolean if a field has been set.

### GetParentId

`func (o *TreeNodeListVo) GetParentId() int64`

GetParentId returns the ParentId field if non-nil, zero value otherwise.

### GetParentIdOk

`func (o *TreeNodeListVo) GetParentIdOk() (*int64, bool)`

GetParentIdOk returns a tuple with the ParentId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetParentId

`func (o *TreeNodeListVo) SetParentId(v int64)`

SetParentId sets ParentId field to given value.

### HasParentId

`func (o *TreeNodeListVo) HasParentId() bool`

HasParentId returns a boolean if a field has been set.

### GetRepositoryId

`func (o *TreeNodeListVo) GetRepositoryId() int64`

GetRepositoryId returns the RepositoryId field if non-nil, zero value otherwise.

### GetRepositoryIdOk

`func (o *TreeNodeListVo) GetRepositoryIdOk() (*int64, bool)`

GetRepositoryIdOk returns a tuple with the RepositoryId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetRepositoryId

`func (o *TreeNodeListVo) SetRepositoryId(v int64)`

SetRepositoryId sets RepositoryId field to given value.

### HasRepositoryId

`func (o *TreeNodeListVo) HasRepositoryId() bool`

HasRepositoryId returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


