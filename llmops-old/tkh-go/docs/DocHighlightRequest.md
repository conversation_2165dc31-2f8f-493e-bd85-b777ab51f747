# DocHighlightRequest

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**DocUuid** | Pointer to **string** |  | [optional] 
**EndPage** | Pointer to **int32** |  | [optional] 
**Image** | Pointer to **bool** |  | [optional] 
**Rectangles** | Pointer to **[]float64** |  | [optional] 
**StartPage** | Pointer to **int32** |  | [optional] 
**Text** | Pointer to **string** |  | [optional] 

## Methods

### NewDocHighlightRequest

`func NewDocHighlightRequest() *DocHighlightRequest`

NewDocHighlightRequest instantiates a new DocHighlightRequest object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewDocHighlightRequestWithDefaults

`func NewDocHighlightRequestWithDefaults() *DocHighlightRequest`

NewDocHighlightRequestWithDefaults instantiates a new DocHighlightRequest object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetDocUuid

`func (o *DocHighlightRequest) GetDocUuid() string`

GetDocUuid returns the DocUuid field if non-nil, zero value otherwise.

### GetDocUuidOk

`func (o *DocHighlightRequest) GetDocUuidOk() (*string, bool)`

GetDocUuidOk returns a tuple with the DocUuid field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDocUuid

`func (o *DocHighlightRequest) SetDocUuid(v string)`

SetDocUuid sets DocUuid field to given value.

### HasDocUuid

`func (o *DocHighlightRequest) HasDocUuid() bool`

HasDocUuid returns a boolean if a field has been set.

### GetEndPage

`func (o *DocHighlightRequest) GetEndPage() int32`

GetEndPage returns the EndPage field if non-nil, zero value otherwise.

### GetEndPageOk

`func (o *DocHighlightRequest) GetEndPageOk() (*int32, bool)`

GetEndPageOk returns a tuple with the EndPage field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetEndPage

`func (o *DocHighlightRequest) SetEndPage(v int32)`

SetEndPage sets EndPage field to given value.

### HasEndPage

`func (o *DocHighlightRequest) HasEndPage() bool`

HasEndPage returns a boolean if a field has been set.

### GetImage

`func (o *DocHighlightRequest) GetImage() bool`

GetImage returns the Image field if non-nil, zero value otherwise.

### GetImageOk

`func (o *DocHighlightRequest) GetImageOk() (*bool, bool)`

GetImageOk returns a tuple with the Image field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetImage

`func (o *DocHighlightRequest) SetImage(v bool)`

SetImage sets Image field to given value.

### HasImage

`func (o *DocHighlightRequest) HasImage() bool`

HasImage returns a boolean if a field has been set.

### GetRectangles

`func (o *DocHighlightRequest) GetRectangles() []float64`

GetRectangles returns the Rectangles field if non-nil, zero value otherwise.

### GetRectanglesOk

`func (o *DocHighlightRequest) GetRectanglesOk() (*[]float64, bool)`

GetRectanglesOk returns a tuple with the Rectangles field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetRectangles

`func (o *DocHighlightRequest) SetRectangles(v []float64)`

SetRectangles sets Rectangles field to given value.

### HasRectangles

`func (o *DocHighlightRequest) HasRectangles() bool`

HasRectangles returns a boolean if a field has been set.

### GetStartPage

`func (o *DocHighlightRequest) GetStartPage() int32`

GetStartPage returns the StartPage field if non-nil, zero value otherwise.

### GetStartPageOk

`func (o *DocHighlightRequest) GetStartPageOk() (*int32, bool)`

GetStartPageOk returns a tuple with the StartPage field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetStartPage

`func (o *DocHighlightRequest) SetStartPage(v int32)`

SetStartPage sets StartPage field to given value.

### HasStartPage

`func (o *DocHighlightRequest) HasStartPage() bool`

HasStartPage returns a boolean if a field has been set.

### GetText

`func (o *DocHighlightRequest) GetText() string`

GetText returns the Text field if non-nil, zero value otherwise.

### GetTextOk

`func (o *DocHighlightRequest) GetTextOk() (*string, bool)`

GetTextOk returns a tuple with the Text field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetText

`func (o *DocHighlightRequest) SetText(v string)`

SetText sets Text field to given value.

### HasText

`func (o *DocHighlightRequest) HasText() bool`

HasText returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


