# HotStockVo

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**StockCode** | Pointer to **string** |  | [optional] 
**StockName** | Pointer to **string** |  | [optional] 

## Methods

### NewHotStockVo

`func NewHotStockVo() *HotStockVo`

NewHotStockVo instantiates a new HotStockVo object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewHotStockVoWithDefaults

`func NewHotStockVoWithDefaults() *HotStockVo`

NewHotStockVoWithDefaults instantiates a new HotStockVo object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetStockCode

`func (o *HotStockVo) GetStockCode() string`

GetStockCode returns the StockCode field if non-nil, zero value otherwise.

### GetStockCodeOk

`func (o *HotStockVo) GetStockCodeOk() (*string, bool)`

GetStockCodeOk returns a tuple with the StockCode field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetStockCode

`func (o *HotStockVo) SetStockCode(v string)`

SetStockCode sets StockCode field to given value.

### HasStockCode

`func (o *HotStockVo) HasStockCode() bool`

HasStockCode returns a boolean if a field has been set.

### GetStockName

`func (o *HotStockVo) GetStockName() string`

GetStockName returns the StockName field if non-nil, zero value otherwise.

### GetStockNameOk

`func (o *HotStockVo) GetStockNameOk() (*string, bool)`

GetStockNameOk returns a tuple with the StockName field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetStockName

`func (o *HotStockVo) SetStockName(v string)`

SetStockName sets StockName field to given value.

### HasStockName

`func (o *HotStockVo) HasStockName() bool`

HasStockName returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


