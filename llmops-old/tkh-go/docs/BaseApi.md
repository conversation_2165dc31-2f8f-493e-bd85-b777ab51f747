# \BaseApi

All URIs are relative to *http://localhost*

Method | HTTP request | Description
------------- | ------------- | -------------
[**AddConversationUsingPOST**](BaseApi.md#AddConversationUsingPOST) | **Post** /infinity/base/qa/add-conversation | 会话新增
[**AddDirectoryUsingPOST**](BaseApi.md#AddDirectoryUsingPOST) | **Post** /infinity/base/documents/add-directory | 知识库下创建一个文件夹
[**AddQAUsingPOST**](BaseApi.md#AddQAUsingPOST) | **Post** /infinity/base/qa/add-QA | 问答记录新增
[**AddRepositoryUsingPOST**](BaseApi.md#AddRepositoryUsingPOST) | **Post** /infinity/base/documents/add-repository | 创建一个基础知识库
[**AddUsersUsingPOST**](BaseApi.md#AddUsersUsingPOST) | **Post** /infinity/base/user/admin/add | admin-创建用户
[**ApplyForApikeyUsingPOST**](BaseApi.md#ApplyForApikeyUsingPOST) | **Post** /infinity/base/approve/apikey/apply | 申请apikey
[**ApprovalForApikeyUsingGET**](BaseApi.md#ApprovalForApikeyUsingGET) | **Get** /infinity/base/approve/admin/apikey/approval | 审批apikey
[**DelUsersUsingGET**](BaseApi.md#DelUsersUsingGET) | **Get** /infinity/base/user/admin/del | admin-删除用户
[**DeleteByIdUsingDELETE**](BaseApi.md#DeleteByIdUsingDELETE) | **Delete** /infinity/base/approve/apikey/approval | 删除申请记录 根据id
[**DeleteConversationUsingDELETE**](BaseApi.md#DeleteConversationUsingDELETE) | **Delete** /infinity/base/qa/delete-conversation | 会话删除
[**DocumentsSplitUsingPOST**](BaseApi.md#DocumentsSplitUsingPOST) | **Post** /infinity/base/documents/split/documents | 批量文档解析
[**FullProcessingFilesUsingPOST**](BaseApi.md#FullProcessingFilesUsingPOST) | **Post** /infinity/base/documents/full-processing-files | 批量上传解析存储文件--全同步
[**FullProcessingSingleFileUsingPOST**](BaseApi.md#FullProcessingSingleFileUsingPOST) | **Post** /infinity/base/documents/full-processing-single-file | 上传解析存储单个文件--同步
[**GetCaptchaUsingGET**](BaseApi.md#GetCaptchaUsingGET) | **Get** /infinity/base/auth/any/captcha | 获取验证码
[**GetConversationDetailUsingGET**](BaseApi.md#GetConversationDetailUsingGET) | **Get** /infinity/base/qa/get-conversation-detail | 会话详情查询
[**GetConversationListUsingGET**](BaseApi.md#GetConversationListUsingGET) | **Get** /infinity/base/qa/get-conversation | 会话列表查询
[**GetDatasourceByIdUsingPOST**](BaseApi.md#GetDatasourceByIdUsingPOST) | **Post** /infinity/base/documents/get-datasource | 获取datasource连接信息
[**GetDocTreeByIdUsingGET**](BaseApi.md#GetDocTreeByIdUsingGET) | **Get** /infinity/base/documents/{id}/list | 根据id获取文档目录
[**GetFullTreeByIdUsingPOST**](BaseApi.md#GetFullTreeByIdUsingPOST) | **Post** /infinity/base/documents/get-full-tree | 根据id获取完整树
[**GetIndTrendListUsingPOST**](BaseApi.md#GetIndTrendListUsingPOST) | **Post** /infinity/base/qa/get-ind-trend | 行业走势
[**GetStockListUsingGET**](BaseApi.md#GetStockListUsingGET) | **Get** /infinity/base/qa/get-stock | 根据行业或概念名称获取个股列表
[**GetStockTrendListUsingPOST**](BaseApi.md#GetStockTrendListUsingPOST) | **Post** /infinity/base/qa/get-stock-trend | 上市公司/股票代码股价走势
[**GetTagStockListUsingPOST**](BaseApi.md#GetTagStockListUsingPOST) | **Post** /infinity/base/qa/get-ind | 根据股票名称list获取行业或概念
[**GetTdsUserInfoUsingGET**](BaseApi.md#GetTdsUserInfoUsingGET) | **Get** /infinity/base/auth/tds-user-info | 获取tds用户详情
[**GetUploadTaskDetailUsingPOST**](BaseApi.md#GetUploadTaskDetailUsingPOST) | **Post** /infinity/base/documents/get-upload-task-detail | 获取上传任务详情，包括任务状况，各文件状态
[**GetUserListUsingGET**](BaseApi.md#GetUserListUsingGET) | **Get** /infinity/base/user/admin/user-list | admin-根据用户名模糊查找用户list
[**GetUserUsingGET**](BaseApi.md#GetUserUsingGET) | **Get** /infinity/base/auth/info | 获取当前登录用户个人信息
[**HighlightUsingGET**](BaseApi.md#HighlightUsingGET) | **Get** /infinity/base/documents/highlight | 根据文本片段ID对文档高亮
[**HighlightUsingPOST**](BaseApi.md#HighlightUsingPOST) | **Post** /infinity/base/documents/highlight/online | 在线根据坐标对文档高亮
[**HighlightUsingPOST1**](BaseApi.md#HighlightUsingPOST1) | **Post** /infinity/base/documents/highlight | 根据坐标或文本对文档高亮
[**ListAllDocTreesUsingGET**](BaseApi.md#ListAllDocTreesUsingGET) | **Get** /infinity/base/documents/list | 获取所有文本类型知识库列表
[**ListApproveInfoUsingGET**](BaseApi.md#ListApproveInfoUsingGET) | **Get** /infinity/base/approve/apikey/approval | 获取apikey申请列表
[**ListFilesByBusinessTypeUsingPOST**](BaseApi.md#ListFilesByBusinessTypeUsingPOST) | **Post** /infinity/base/documents/list-files-business-type | id获取文件夹下按照业务类型分组的所有文档(树展开状态)
[**ListUserDocTreesUsingPOST**](BaseApi.md#ListUserDocTreesUsingPOST) | **Post** /infinity/base/documents/list-user-trees | 获取用户有权限的知识库列表,包含私库+sdb
[**LoginTDSUsingPOST**](BaseApi.md#LoginTDSUsingPOST) | **Post** /infinity/base/auth/any/login-tds | 登录TDS
[**LoginUsingPOST**](BaseApi.md#LoginUsingPOST) | **Post** /infinity/base/auth/any/login | 登录
[**MatchImagesUsingGET**](BaseApi.md#MatchImagesUsingGET) | **Get** /infinity/base/image/match | 根据图片检索图片
[**ModifyPermissionUsingGET**](BaseApi.md#ModifyPermissionUsingGET) | **Get** /infinity/base/user/admin/permission | admin-修改用户权限
[**PreviewDocUsingPOST**](BaseApi.md#PreviewDocUsingPOST) | **Post** /infinity/base/documents/preview | 预览指定文档
[**PrintServicesUsingGET**](BaseApi.md#PrintServicesUsingGET) | **Get** /infinity/base/documents/print-services | printServices
[**QueryImagesUsingPOST**](BaseApi.md#QueryImagesUsingPOST) | **Post** /infinity/base/image/query | 根据文本检索图片
[**ResetUserUsingGET**](BaseApi.md#ResetUserUsingGET) | **Get** /infinity/base/user/admin/reset | admin-重置用户
[**SingleDocumentSplitUsingPOST**](BaseApi.md#SingleDocumentSplitUsingPOST) | **Post** /infinity/base/documents/split/batch/save | 批量文档保存
[**SingleDocumentSplitUsingPOST1**](BaseApi.md#SingleDocumentSplitUsingPOST1) | **Post** /infinity/base/documents/split/cache/save | 单文档编辑确定
[**SplitDocReadUsingPOST**](BaseApi.md#SplitDocReadUsingPOST) | **Post** /infinity/base/documents/split/doc/read | 编辑文档查看
[**SplitDocumentUsingPOST**](BaseApi.md#SplitDocumentUsingPOST) | **Post** /infinity/base/documents/split/document | 单个文件的解析服务
[**UpdateConversationUsingPOST**](BaseApi.md#UpdateConversationUsingPOST) | **Post** /infinity/base/qa/update-conversation | 会话标题修改
[**UpdatePasswordUsingPOST**](BaseApi.md#UpdatePasswordUsingPOST) | **Post** /infinity/base/user/password | 修改密码
[**UploadLocalUsingPOST**](BaseApi.md#UploadLocalUsingPOST) | **Post** /infinity/base/documents/upload/local | 批量上传文件
[**UploadRepositoryUsingPOST**](BaseApi.md#UploadRepositoryUsingPOST) | **Post** /infinity/base/documents/upload/repository | 获取知识库文件详情



## AddConversationUsingPOST

> ResultString AddConversationUsingPOST(ctx).ConversationRequest(conversationRequest).Execute()

会话新增

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    conversationRequest := openapiclient.ConversationRequest{FirstAnswer: "FirstAnswer_example", FirstQuestion: "FirstQuestion_example", SourceIds: "SourceIds_example", Type: 123} // ConversationRequest |  (optional)

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.AddConversationUsingPOST(context.Background(), ).ConversationRequest(conversationRequest).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.AddConversationUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `AddConversationUsingPOST`: ResultString
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.AddConversationUsingPOST`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiAddConversationUsingPOSTRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **conversationRequest** | [**ConversationRequest**](ConversationRequest.md) |  | 

### Return type

[**ResultString**](ResultString.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## AddDirectoryUsingPOST

> ResultDocumentTree AddDirectoryUsingPOST(ctx).AddDirectoryRequest(addDirectoryRequest).Execute()

知识库下创建一个文件夹

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    addDirectoryRequest := openapiclient.AddDirectoryRequest{Name: "Name_example", ParentId: int64(123)} // AddDirectoryRequest |  (optional)

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.AddDirectoryUsingPOST(context.Background(), ).AddDirectoryRequest(addDirectoryRequest).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.AddDirectoryUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `AddDirectoryUsingPOST`: ResultDocumentTree
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.AddDirectoryUsingPOST`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiAddDirectoryUsingPOSTRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **addDirectoryRequest** | [**AddDirectoryRequest**](AddDirectoryRequest.md) |  | 

### Return type

[**ResultDocumentTree**](ResultDocumentTree.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## AddQAUsingPOST

> ResultString AddQAUsingPOST(ctx).QaRequest(qaRequest).Execute()

问答记录新增

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    qaRequest := openapiclient.QaRequest{Answer: "Answer_example", ConversationId: "ConversationId_example", Question: "Question_example"} // QaRequest |  (optional)

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.AddQAUsingPOST(context.Background(), ).QaRequest(qaRequest).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.AddQAUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `AddQAUsingPOST`: ResultString
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.AddQAUsingPOST`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiAddQAUsingPOSTRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **qaRequest** | [**QaRequest**](QaRequest.md) |  | 

### Return type

[**ResultString**](ResultString.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## AddRepositoryUsingPOST

> ResultRepository AddRepositoryUsingPOST(ctx).AddRepoRequest(addRepoRequest).Execute()

创建一个基础知识库

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    addRepoRequest := openapiclient.AddRepoRequest{Description: "Description_example", Name: "Name_example", Password: "Password_example", RepositoryType: 123, SdbConfigureType: 123, TableName: "TableName_example", Url: "Url_example", Username: "Username_example"} // AddRepoRequest |  (optional)

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.AddRepositoryUsingPOST(context.Background(), ).AddRepoRequest(addRepoRequest).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.AddRepositoryUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `AddRepositoryUsingPOST`: ResultRepository
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.AddRepositoryUsingPOST`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiAddRepositoryUsingPOSTRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **addRepoRequest** | [**AddRepoRequest**](AddRepoRequest.md) |  | 

### Return type

[**ResultRepository**](ResultRepository.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## AddUsersUsingPOST

> ResultBoolean AddUsersUsingPOST(ctx).UserInfoDuiXiang(userInfoDuiXiang).Execute()

admin-创建用户

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    userInfoDuiXiang := []UserInfoDuiXiang{openapiclient.UserInfoDuiXiang{Admin: false, CreateTime: "CreateTime_example", Id: 123, Password: "Password_example", Status: 123, UpdateTime: "UpdateTime_example", Username: "Username_example"}} // []UserInfoDuiXiang |  (optional)

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.AddUsersUsingPOST(context.Background(), ).UserInfoDuiXiang(userInfoDuiXiang).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.AddUsersUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `AddUsersUsingPOST`: ResultBoolean
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.AddUsersUsingPOST`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiAddUsersUsingPOSTRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **userInfoDuiXiang** | [**[]UserInfoDuiXiang**](UserInfoDuiXiang.md) |  | 

### Return type

[**ResultBoolean**](ResultBoolean.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## ApplyForApikeyUsingPOST

> ResultApproveInfoDuiXiang ApplyForApikeyUsingPOST(ctx).DtoParamApply(dtoParamApply).Execute()

申请apikey

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    dtoParamApply := openapiclient.DtoParamApply{EndTime: "EndTime_example", Purpose: "Purpose_example"} // DtoParamApply |  (optional)

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.ApplyForApikeyUsingPOST(context.Background(), ).DtoParamApply(dtoParamApply).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.ApplyForApikeyUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `ApplyForApikeyUsingPOST`: ResultApproveInfoDuiXiang
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.ApplyForApikeyUsingPOST`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiApplyForApikeyUsingPOSTRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **dtoParamApply** | [**DtoParamApply**](DtoParamApply.md) |  | 

### Return type

[**ResultApproveInfoDuiXiang**](ResultApproveInfoDuiXiang.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## ApprovalForApikeyUsingGET

> ResultBoolean ApprovalForApikeyUsingGET(ctx).Id(id).Status(status).Execute()

审批apikey

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    id := 987 // int32 | id
    status := 987 // int32 | status

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.ApprovalForApikeyUsingGET(context.Background(), id, status).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.ApprovalForApikeyUsingGET``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `ApprovalForApikeyUsingGET`: ResultBoolean
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.ApprovalForApikeyUsingGET`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiApprovalForApikeyUsingGETRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **int32** | id | 
 **status** | **int32** | status | 

### Return type

[**ResultBoolean**](ResultBoolean.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## DelUsersUsingGET

> ResultBoolean DelUsersUsingGET(ctx).Id(id).Execute()

admin-删除用户

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    id := 987 // int32 | id

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.DelUsersUsingGET(context.Background(), id).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.DelUsersUsingGET``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `DelUsersUsingGET`: ResultBoolean
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.DelUsersUsingGET`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiDelUsersUsingGETRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **int32** | id | 

### Return type

[**ResultBoolean**](ResultBoolean.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## DeleteByIdUsingDELETE

> ResultBoolean DeleteByIdUsingDELETE(ctx).Id(id).Execute()

删除申请记录 根据id

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    id := 987 // int32 | id

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.DeleteByIdUsingDELETE(context.Background(), id).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.DeleteByIdUsingDELETE``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `DeleteByIdUsingDELETE`: ResultBoolean
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.DeleteByIdUsingDELETE`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiDeleteByIdUsingDELETERequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **int32** | id | 

### Return type

[**ResultBoolean**](ResultBoolean.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## DeleteConversationUsingDELETE

> ResultString DeleteConversationUsingDELETE(ctx).ConversationId(conversationId).Execute()

会话删除

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    conversationId := "conversationId_example" // string | conversationId

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.DeleteConversationUsingDELETE(context.Background(), conversationId).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.DeleteConversationUsingDELETE``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `DeleteConversationUsingDELETE`: ResultString
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.DeleteConversationUsingDELETE`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiDeleteConversationUsingDELETERequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **conversationId** | **string** | conversationId | 

### Return type

[**ResultString**](ResultString.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## DocumentsSplitUsingPOST

> ResultDocumentUploadTask DocumentsSplitUsingPOST(ctx).SingleDocSplitParam(singleDocSplitParam).Execute()

批量文档解析

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    singleDocSplitParam := []SingleDocSplitParam{openapiclient.SingleDocSplitParam{DocId: "DocId_example", DocumentParser: "DocumentParser_example", FilePath: "FilePath_example", Params: "TODO"}} // []SingleDocSplitParam |  (optional)

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.DocumentsSplitUsingPOST(context.Background(), ).SingleDocSplitParam(singleDocSplitParam).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.DocumentsSplitUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `DocumentsSplitUsingPOST`: ResultDocumentUploadTask
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.DocumentsSplitUsingPOST`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiDocumentsSplitUsingPOSTRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **singleDocSplitParam** | [**[]SingleDocSplitParam**](SingleDocSplitParam.md) |  | 

### Return type

[**ResultDocumentUploadTask**](ResultDocumentUploadTask.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## FullProcessingFilesUsingPOST

> ResultLong FullProcessingFilesUsingPOST(ctx).Execute()

批量上传解析存储文件--全同步



### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.FullProcessingFilesUsingPOST(context.Background(), ).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.FullProcessingFilesUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `FullProcessingFilesUsingPOST`: ResultLong
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.FullProcessingFilesUsingPOST`: %v\n", resp)
}
```

### Path Parameters

This endpoint does not need any parameter.

### Other Parameters

Other parameters are passed through a pointer to a apiFullProcessingFilesUsingPOSTRequest struct via the builder pattern


### Return type

[**ResultLong**](ResultLong.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/x-www-form-urlencoded
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## FullProcessingSingleFileUsingPOST

> ResultDocument FullProcessingSingleFileUsingPOST(ctx).Execute()

上传解析存储单个文件--同步



### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.FullProcessingSingleFileUsingPOST(context.Background(), ).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.FullProcessingSingleFileUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `FullProcessingSingleFileUsingPOST`: ResultDocument
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.FullProcessingSingleFileUsingPOST`: %v\n", resp)
}
```

### Path Parameters

This endpoint does not need any parameter.

### Other Parameters

Other parameters are passed through a pointer to a apiFullProcessingSingleFileUsingPOSTRequest struct via the builder pattern


### Return type

[**ResultDocument**](ResultDocument.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: multipart/form-data
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## GetCaptchaUsingGET

> ResultDtoResultCaptcha GetCaptchaUsingGET(ctx).Execute()

获取验证码

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.GetCaptchaUsingGET(context.Background(), ).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.GetCaptchaUsingGET``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `GetCaptchaUsingGET`: ResultDtoResultCaptcha
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.GetCaptchaUsingGET`: %v\n", resp)
}
```

### Path Parameters

This endpoint does not need any parameter.

### Other Parameters

Other parameters are passed through a pointer to a apiGetCaptchaUsingGETRequest struct via the builder pattern


### Return type

[**ResultDtoResultCaptcha**](ResultDtoResultCaptcha.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## GetConversationDetailUsingGET

> ResultConversationDetailVo GetConversationDetailUsingGET(ctx).ConversationId(conversationId).Execute()

会话详情查询

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    conversationId := "conversationId_example" // string | conversationId

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.GetConversationDetailUsingGET(context.Background(), conversationId).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.GetConversationDetailUsingGET``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `GetConversationDetailUsingGET`: ResultConversationDetailVo
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.GetConversationDetailUsingGET`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiGetConversationDetailUsingGETRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **conversationId** | **string** | conversationId | 

### Return type

[**ResultConversationDetailVo**](ResultConversationDetailVo.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## GetConversationListUsingGET

> ResultPageConversationVo GetConversationListUsingGET(ctx).PageNum(pageNum).PageSize(pageSize).Type_(type_).Execute()

会话列表查询

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    pageNum := 987 // int32 | pageNum
    pageSize := 987 // int32 | pageSize
    type_ := 987 // int32 | type (optional)

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.GetConversationListUsingGET(context.Background(), pageNum, pageSize).Type_(type_).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.GetConversationListUsingGET``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `GetConversationListUsingGET`: ResultPageConversationVo
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.GetConversationListUsingGET`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiGetConversationListUsingGETRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **pageNum** | **int32** | pageNum | 
 **pageSize** | **int32** | pageSize | 
 **type_** | **int32** | type | 

### Return type

[**ResultPageConversationVo**](ResultPageConversationVo.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## GetDatasourceByIdUsingPOST

> ResultListDocument GetDatasourceByIdUsingPOST(ctx).DatasourceId(datasourceId).Execute()

获取datasource连接信息

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    datasourceId := 987 // int32 | datasourceId

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.GetDatasourceByIdUsingPOST(context.Background(), datasourceId).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.GetDatasourceByIdUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `GetDatasourceByIdUsingPOST`: ResultListDocument
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.GetDatasourceByIdUsingPOST`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiGetDatasourceByIdUsingPOSTRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **datasourceId** | **int32** | datasourceId | 

### Return type

[**ResultListDocument**](ResultListDocument.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## GetDocTreeByIdUsingGET

> ResultTreeNodeListVo GetDocTreeByIdUsingGET(ctx, id).Execute()

根据id获取文档目录

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    id := 987 // int32 | id

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.GetDocTreeByIdUsingGET(context.Background(), id).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.GetDocTreeByIdUsingGET``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `GetDocTreeByIdUsingGET`: ResultTreeNodeListVo
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.GetDocTreeByIdUsingGET`: %v\n", resp)
}
```

### Path Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
**ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
**id** | **int32** | id | 

### Other Parameters

Other parameters are passed through a pointer to a apiGetDocTreeByIdUsingGETRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


### Return type

[**ResultTreeNodeListVo**](ResultTreeNodeListVo.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## GetFullTreeByIdUsingPOST

> ResultFullTree GetFullTreeByIdUsingPOST(ctx).Id(id).Execute()

根据id获取完整树

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    id := 987 // int32 | id

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.GetFullTreeByIdUsingPOST(context.Background(), id).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.GetFullTreeByIdUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `GetFullTreeByIdUsingPOST`: ResultFullTree
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.GetFullTreeByIdUsingPOST`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiGetFullTreeByIdUsingPOSTRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **int32** | id | 

### Return type

[**ResultFullTree**](ResultFullTree.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## GetIndTrendListUsingPOST

> ResultListIndTrendVo GetIndTrendListUsingPOST(ctx).IndTrendRequest(indTrendRequest).Execute()

行业走势

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    indTrendRequest := openapiclient.IndTrendRequest{EndDate: "EndDate_example", IndName: "IndName_example", IndType: "IndType_example", StartDate: "StartDate_example"} // IndTrendRequest |  (optional)

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.GetIndTrendListUsingPOST(context.Background(), ).IndTrendRequest(indTrendRequest).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.GetIndTrendListUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `GetIndTrendListUsingPOST`: ResultListIndTrendVo
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.GetIndTrendListUsingPOST`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiGetIndTrendListUsingPOSTRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **indTrendRequest** | [**IndTrendRequest**](IndTrendRequest.md) |  | 

### Return type

[**ResultListIndTrendVo**](ResultListIndTrendVo.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## GetStockListUsingGET

> ResultListHotStockVo GetStockListUsingGET(ctx).IndName(indName).Execute()

根据行业或概念名称获取个股列表

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    indName := "indName_example" // string | indName

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.GetStockListUsingGET(context.Background(), indName).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.GetStockListUsingGET``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `GetStockListUsingGET`: ResultListHotStockVo
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.GetStockListUsingGET`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiGetStockListUsingGETRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **indName** | **string** | indName | 

### Return type

[**ResultListHotStockVo**](ResultListHotStockVo.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## GetStockTrendListUsingPOST

> ResultStockTrendVo GetStockTrendListUsingPOST(ctx).StockTrendRequest(stockTrendRequest).Execute()

上市公司/股票代码股价走势

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    stockTrendRequest := openapiclient.StockTrendRequest{DateCode: 123, StockCode: "StockCode_example"} // StockTrendRequest |  (optional)

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.GetStockTrendListUsingPOST(context.Background(), ).StockTrendRequest(stockTrendRequest).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.GetStockTrendListUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `GetStockTrendListUsingPOST`: ResultStockTrendVo
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.GetStockTrendListUsingPOST`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiGetStockTrendListUsingPOSTRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **stockTrendRequest** | [**StockTrendRequest**](StockTrendRequest.md) |  | 

### Return type

[**ResultStockTrendVo**](ResultStockTrendVo.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## GetTagStockListUsingPOST

> ResultListIndOrConceptVo GetTagStockListUsingPOST(ctx).TagHotStockRequest(tagHotStockRequest).Execute()

根据股票名称list获取行业或概念

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    tagHotStockRequest := openapiclient.TagHotStockRequest{StockName: []string{"StockName_example")} // TagHotStockRequest |  (optional)

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.GetTagStockListUsingPOST(context.Background(), ).TagHotStockRequest(tagHotStockRequest).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.GetTagStockListUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `GetTagStockListUsingPOST`: ResultListIndOrConceptVo
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.GetTagStockListUsingPOST`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiGetTagStockListUsingPOSTRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **tagHotStockRequest** | [**TagHotStockRequest**](TagHotStockRequest.md) |  | 

### Return type

[**ResultListIndOrConceptVo**](ResultListIndOrConceptVo.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## GetTdsUserInfoUsingGET

> ResultUserInfoEntity GetTdsUserInfoUsingGET(ctx).Authorization(authorization).Execute()

获取tds用户详情

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    authorization := "authorization_example" // string | Authorization

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.GetTdsUserInfoUsingGET(context.Background(), authorization).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.GetTdsUserInfoUsingGET``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `GetTdsUserInfoUsingGET`: ResultUserInfoEntity
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.GetTdsUserInfoUsingGET`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiGetTdsUserInfoUsingGETRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **authorization** | **string** | Authorization | 

### Return type

[**ResultUserInfoEntity**](ResultUserInfoEntity.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## GetUploadTaskDetailUsingPOST

> ResultDocumentUploadTask GetUploadTaskDetailUsingPOST(ctx).Id(id).Execute()

获取上传任务详情，包括任务状况，各文件状态

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    id := 987 // int32 | id

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.GetUploadTaskDetailUsingPOST(context.Background(), id).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.GetUploadTaskDetailUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `GetUploadTaskDetailUsingPOST`: ResultDocumentUploadTask
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.GetUploadTaskDetailUsingPOST`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiGetUploadTaskDetailUsingPOSTRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **int32** | id | 

### Return type

[**ResultDocumentUploadTask**](ResultDocumentUploadTask.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## GetUserListUsingGET

> ResultListUserInfoDuiXiang GetUserListUsingGET(ctx).LikeName(likeName).Execute()

admin-根据用户名模糊查找用户list

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    likeName := "likeName_example" // string | likeName (optional)

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.GetUserListUsingGET(context.Background(), ).LikeName(likeName).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.GetUserListUsingGET``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `GetUserListUsingGET`: ResultListUserInfoDuiXiang
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.GetUserListUsingGET`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiGetUserListUsingGETRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **likeName** | **string** | likeName | 

### Return type

[**ResultListUserInfoDuiXiang**](ResultListUserInfoDuiXiang.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## GetUserUsingGET

> ResultUserInfoDuiXiang GetUserUsingGET(ctx).Execute()

获取当前登录用户个人信息

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.GetUserUsingGET(context.Background(), ).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.GetUserUsingGET``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `GetUserUsingGET`: ResultUserInfoDuiXiang
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.GetUserUsingGET`: %v\n", resp)
}
```

### Path Parameters

This endpoint does not need any parameter.

### Other Parameters

Other parameters are passed through a pointer to a apiGetUserUsingGETRequest struct via the builder pattern


### Return type

[**ResultUserInfoDuiXiang**](ResultUserInfoDuiXiang.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## HighlightUsingGET

> map[string]interface{} HighlightUsingGET(ctx).ItemId(itemId).Image(image).Execute()

根据文本片段ID对文档高亮

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    itemId := "itemId_example" // string | item_id
    image := true // bool | image (optional)

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.HighlightUsingGET(context.Background(), itemId).Image(image).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.HighlightUsingGET``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `HighlightUsingGET`: map[string]interface{}
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.HighlightUsingGET`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiHighlightUsingGETRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **itemId** | **string** | item_id | 
 **image** | **bool** | image | 

### Return type

**map[string]interface{}**

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## HighlightUsingPOST

> map[string]interface{} HighlightUsingPOST(ctx).EndPage(endPage).Rectangles(rectangles).StartPage(startPage).File(file).Execute()

在线根据坐标对文档高亮

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    endPage := 987 // int32 | endPage
    rectangles := []string{"Inner_example"} // []string | rectangles
    startPage := 987 // int32 | startPage
    file := 987 // *os.File | file

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.HighlightUsingPOST(context.Background(), endPage, rectangles, startPage, file).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.HighlightUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `HighlightUsingPOST`: map[string]interface{}
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.HighlightUsingPOST`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiHighlightUsingPOSTRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **endPage** | **int32** | endPage | 
 **rectangles** | [**[]string**](string.md) | rectangles | 
 **startPage** | **int32** | startPage | 
 **file** | ***os.File** | file | 

### Return type

**map[string]interface{}**

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: multipart/form-data
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## HighlightUsingPOST1

> map[string]interface{} HighlightUsingPOST1(ctx).DocHighlightRequest(docHighlightRequest).Execute()

根据坐标或文本对文档高亮

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    docHighlightRequest := openapiclient.DocHighlightRequest{DocUuid: "DocUuid_example", EndPage: 123, Image: false, Rectangles: []float64{123), StartPage: 123, Text: "Text_example"} // DocHighlightRequest |  (optional)

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.HighlightUsingPOST1(context.Background(), ).DocHighlightRequest(docHighlightRequest).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.HighlightUsingPOST1``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `HighlightUsingPOST1`: map[string]interface{}
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.HighlightUsingPOST1`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiHighlightUsingPOST1Request struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **docHighlightRequest** | [**DocHighlightRequest**](DocHighlightRequest.md) |  | 

### Return type

**map[string]interface{}**

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## ListAllDocTreesUsingGET

> ResultListRepository ListAllDocTreesUsingGET(ctx).Execute()

获取所有文本类型知识库列表

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.ListAllDocTreesUsingGET(context.Background(), ).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.ListAllDocTreesUsingGET``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `ListAllDocTreesUsingGET`: ResultListRepository
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.ListAllDocTreesUsingGET`: %v\n", resp)
}
```

### Path Parameters

This endpoint does not need any parameter.

### Other Parameters

Other parameters are passed through a pointer to a apiListAllDocTreesUsingGETRequest struct via the builder pattern


### Return type

[**ResultListRepository**](ResultListRepository.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## ListApproveInfoUsingGET

> ResultListApproveInfoDuiXiang ListApproveInfoUsingGET(ctx).Execute()

获取apikey申请列表

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.ListApproveInfoUsingGET(context.Background(), ).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.ListApproveInfoUsingGET``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `ListApproveInfoUsingGET`: ResultListApproveInfoDuiXiang
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.ListApproveInfoUsingGET`: %v\n", resp)
}
```

### Path Parameters

This endpoint does not need any parameter.

### Other Parameters

Other parameters are passed through a pointer to a apiListApproveInfoUsingGETRequest struct via the builder pattern


### Return type

[**ResultListApproveInfoDuiXiang**](ResultListApproveInfoDuiXiang.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## ListFilesByBusinessTypeUsingPOST

> ResultDocBusinessTypeVo ListFilesByBusinessTypeUsingPOST(ctx).Id(id).Execute()

id获取文件夹下按照业务类型分组的所有文档(树展开状态)

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    id := 987 // int32 | id

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.ListFilesByBusinessTypeUsingPOST(context.Background(), id).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.ListFilesByBusinessTypeUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `ListFilesByBusinessTypeUsingPOST`: ResultDocBusinessTypeVo
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.ListFilesByBusinessTypeUsingPOST`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiListFilesByBusinessTypeUsingPOSTRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **int32** | id | 

### Return type

[**ResultDocBusinessTypeVo**](ResultDocBusinessTypeVo.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## ListUserDocTreesUsingPOST

> ResultUserDocTreeResponse ListUserDocTreesUsingPOST(ctx).Execute()

获取用户有权限的知识库列表,包含私库+sdb

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.ListUserDocTreesUsingPOST(context.Background(), ).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.ListUserDocTreesUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `ListUserDocTreesUsingPOST`: ResultUserDocTreeResponse
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.ListUserDocTreesUsingPOST`: %v\n", resp)
}
```

### Path Parameters

This endpoint does not need any parameter.

### Other Parameters

Other parameters are passed through a pointer to a apiListUserDocTreesUsingPOSTRequest struct via the builder pattern


### Return type

[**ResultUserDocTreeResponse**](ResultUserDocTreeResponse.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## LoginTDSUsingPOST

> ResultUserInfoEntity LoginTDSUsingPOST(ctx).TokenRequestInfo(tokenRequestInfo).Execute()

登录TDS

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    tokenRequestInfo := openapiclient.TokenRequestInfo{ClientId: "ClientId_example", ClientSecret: "ClientSecret_example", Password: "Password_example", UserName: "UserName_example"} // TokenRequestInfo |  (optional)

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.LoginTDSUsingPOST(context.Background(), ).TokenRequestInfo(tokenRequestInfo).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.LoginTDSUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `LoginTDSUsingPOST`: ResultUserInfoEntity
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.LoginTDSUsingPOST`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiLoginTDSUsingPOSTRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **tokenRequestInfo** | [**TokenRequestInfo**](TokenRequestInfo.md) |  | 

### Return type

[**ResultUserInfoEntity**](ResultUserInfoEntity.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## LoginUsingPOST

> ResultUserInfoDuiXiang LoginUsingPOST(ctx).DtoParamLogin(dtoParamLogin).Execute()

登录

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    dtoParamLogin := openapiclient.DtoParamLogin{Code: "Code_example", Key: "Key_example", Password: "Password_example", Username: "Username_example"} // DtoParamLogin |  (optional)

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.LoginUsingPOST(context.Background(), ).DtoParamLogin(dtoParamLogin).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.LoginUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `LoginUsingPOST`: ResultUserInfoDuiXiang
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.LoginUsingPOST`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiLoginUsingPOSTRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **dtoParamLogin** | [**DtoParamLogin**](DtoParamLogin.md) |  | 

### Return type

[**ResultUserInfoDuiXiang**](ResultUserInfoDuiXiang.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## MatchImagesUsingGET

> ResultImageQueryResponse MatchImagesUsingGET(ctx).ImageMatchRequest(imageMatchRequest).Execute()

根据图片检索图片

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    imageMatchRequest := openapiclient.ImageMatchRequest{ImageData: "ImageData_example", KbIdParams: []KnowledgeBaseDirParams{openapiclient.KnowledgeBaseDirParams{DirIds: []string{"DirIds_example"), KbId: "KbId_example"}), ReturnImage: false, TopK: 123} // ImageMatchRequest |  (optional)

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.MatchImagesUsingGET(context.Background(), ).ImageMatchRequest(imageMatchRequest).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.MatchImagesUsingGET``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `MatchImagesUsingGET`: ResultImageQueryResponse
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.MatchImagesUsingGET`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiMatchImagesUsingGETRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **imageMatchRequest** | [**ImageMatchRequest**](ImageMatchRequest.md) |  | 

### Return type

[**ResultImageQueryResponse**](ResultImageQueryResponse.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## ModifyPermissionUsingGET

> ResultBoolean ModifyPermissionUsingGET(ctx).IsAdmin(isAdmin).Uid(uid).Execute()

admin-修改用户权限

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    isAdmin := true // bool | isAdmin
    uid := 987 // int32 | uid

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.ModifyPermissionUsingGET(context.Background(), isAdmin, uid).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.ModifyPermissionUsingGET``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `ModifyPermissionUsingGET`: ResultBoolean
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.ModifyPermissionUsingGET`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiModifyPermissionUsingGETRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **isAdmin** | **bool** | isAdmin | 
 **uid** | **int32** | uid | 

### Return type

[**ResultBoolean**](ResultBoolean.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## PreviewDocUsingPOST

> map[string]interface{} PreviewDocUsingPOST(ctx).DocPreviewRequest(docPreviewRequest).Execute()

预览指定文档

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    docPreviewRequest := openapiclient.DocPreviewRequest{DocUuid: "DocUuid_example", EndPage: 123, StartPage: 123} // DocPreviewRequest |  (optional)

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.PreviewDocUsingPOST(context.Background(), ).DocPreviewRequest(docPreviewRequest).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.PreviewDocUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `PreviewDocUsingPOST`: map[string]interface{}
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.PreviewDocUsingPOST`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiPreviewDocUsingPOSTRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **docPreviewRequest** | [**DocPreviewRequest**](DocPreviewRequest.md) |  | 

### Return type

**map[string]interface{}**

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## PrintServicesUsingGET

> map[string]interface{} PrintServicesUsingGET(ctx).Execute()

printServices

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.PrintServicesUsingGET(context.Background(), ).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.PrintServicesUsingGET``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `PrintServicesUsingGET`: map[string]interface{}
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.PrintServicesUsingGET`: %v\n", resp)
}
```

### Path Parameters

This endpoint does not need any parameter.

### Other Parameters

Other parameters are passed through a pointer to a apiPrintServicesUsingGETRequest struct via the builder pattern


### Return type

**map[string]interface{}**

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## QueryImagesUsingPOST

> ResultImageQueryResponse QueryImagesUsingPOST(ctx).ImageQueryRequest(imageQueryRequest).Execute()

根据文本检索图片

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    imageQueryRequest := openapiclient.ImageQueryRequest{KbIdParams: []KnowledgeBaseDirParams{openapiclient.KnowledgeBaseDirParams{DirIds: []string{"DirIds_example"), KbId: "KbId_example"}), Mode: "Mode_example", ReturnImage: false, Text: "Text_example", TopK: 123} // ImageQueryRequest |  (optional)

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.QueryImagesUsingPOST(context.Background(), ).ImageQueryRequest(imageQueryRequest).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.QueryImagesUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `QueryImagesUsingPOST`: ResultImageQueryResponse
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.QueryImagesUsingPOST`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiQueryImagesUsingPOSTRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **imageQueryRequest** | [**ImageQueryRequest**](ImageQueryRequest.md) |  | 

### Return type

[**ResultImageQueryResponse**](ResultImageQueryResponse.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## ResetUserUsingGET

> ResultBoolean ResetUserUsingGET(ctx).Uid(uid).Execute()

admin-重置用户

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    uid := 987 // int32 | uid

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.ResetUserUsingGET(context.Background(), uid).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.ResetUserUsingGET``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `ResetUserUsingGET`: ResultBoolean
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.ResetUserUsingGET`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiResetUserUsingGETRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **uid** | **int32** | uid | 

### Return type

[**ResultBoolean**](ResultBoolean.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## SingleDocumentSplitUsingPOST

> ResultString SingleDocumentSplitUsingPOST(ctx).UploadTaskId(uploadTaskId).Execute()

批量文档保存

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    uploadTaskId := 987 // int32 | uploadTaskId

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.SingleDocumentSplitUsingPOST(context.Background(), uploadTaskId).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.SingleDocumentSplitUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `SingleDocumentSplitUsingPOST`: ResultString
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.SingleDocumentSplitUsingPOST`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiSingleDocumentSplitUsingPOSTRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **uploadTaskId** | **int32** | uploadTaskId | 

### Return type

[**ResultString**](ResultString.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## SingleDocumentSplitUsingPOST1

> ResultString SingleDocumentSplitUsingPOST1(ctx).DocCacheRequest(docCacheRequest).Execute()

单文档编辑确定

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    docCacheRequest := openapiclient.DocCacheRequest{ChunkList: []DocChunk{openapiclient.DocChunk{Content: "Content_example", Metadata: openapiclient.ChunkMetadata{Page: "Page_example", TotalPages: 123}}), FileUuid: "FileUuid_example"} // DocCacheRequest |  (optional)

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.SingleDocumentSplitUsingPOST1(context.Background(), ).DocCacheRequest(docCacheRequest).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.SingleDocumentSplitUsingPOST1``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `SingleDocumentSplitUsingPOST1`: ResultString
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.SingleDocumentSplitUsingPOST1`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiSingleDocumentSplitUsingPOST1Request struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **docCacheRequest** | [**DocCacheRequest**](DocCacheRequest.md) |  | 

### Return type

[**ResultString**](ResultString.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## SplitDocReadUsingPOST

> ResultListDocChunk SplitDocReadUsingPOST(ctx).FileUuid(fileUuid).Execute()

编辑文档查看

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    fileUuid := "fileUuid_example" // string | fileUuid

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.SplitDocReadUsingPOST(context.Background(), fileUuid).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.SplitDocReadUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `SplitDocReadUsingPOST`: ResultListDocChunk
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.SplitDocReadUsingPOST`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiSplitDocReadUsingPOSTRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **fileUuid** | **string** | fileUuid | 

### Return type

[**ResultListDocChunk**](ResultListDocChunk.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## SplitDocumentUsingPOST

> ResultDocumentUploadTask SplitDocumentUsingPOST(ctx).SingleDocSplitParam(singleDocSplitParam).Execute()

单个文件的解析服务

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    singleDocSplitParam := openapiclient.SingleDocSplitParam{DocId: "DocId_example", DocumentParser: "DocumentParser_example", FilePath: "FilePath_example", Params: "TODO"} // SingleDocSplitParam |  (optional)

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.SplitDocumentUsingPOST(context.Background(), ).SingleDocSplitParam(singleDocSplitParam).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.SplitDocumentUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `SplitDocumentUsingPOST`: ResultDocumentUploadTask
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.SplitDocumentUsingPOST`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiSplitDocumentUsingPOSTRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **singleDocSplitParam** | [**SingleDocSplitParam**](SingleDocSplitParam.md) |  | 

### Return type

[**ResultDocumentUploadTask**](ResultDocumentUploadTask.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## UpdateConversationUsingPOST

> ResultString UpdateConversationUsingPOST(ctx).ConversationUpdateRequest(conversationUpdateRequest).Execute()

会话标题修改

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    conversationUpdateRequest := openapiclient.ConversationUpdateRequest{ConversationId: "ConversationId_example", Title: "Title_example"} // ConversationUpdateRequest |  (optional)

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.UpdateConversationUsingPOST(context.Background(), ).ConversationUpdateRequest(conversationUpdateRequest).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.UpdateConversationUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `UpdateConversationUsingPOST`: ResultString
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.UpdateConversationUsingPOST`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiUpdateConversationUsingPOSTRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **conversationUpdateRequest** | [**ConversationUpdateRequest**](ConversationUpdateRequest.md) |  | 

### Return type

[**ResultString**](ResultString.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## UpdatePasswordUsingPOST

> ResultBoolean UpdatePasswordUsingPOST(ctx).DtoParamUpdatePassword(dtoParamUpdatePassword).Execute()

修改密码

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    dtoParamUpdatePassword := openapiclient.DtoParamUpdatePassword{NewPassword: "NewPassword_example", OldPassword: "OldPassword_example"} // DtoParamUpdatePassword |  (optional)

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.UpdatePasswordUsingPOST(context.Background(), ).DtoParamUpdatePassword(dtoParamUpdatePassword).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.UpdatePasswordUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `UpdatePasswordUsingPOST`: ResultBoolean
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.UpdatePasswordUsingPOST`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiUpdatePasswordUsingPOSTRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **dtoParamUpdatePassword** | [**DtoParamUpdatePassword**](DtoParamUpdatePassword.md) |  | 

### Return type

[**ResultBoolean**](ResultBoolean.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## UploadLocalUsingPOST

> ResultLong UploadLocalUsingPOST(ctx).Execute()

批量上传文件

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.UploadLocalUsingPOST(context.Background(), ).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.UploadLocalUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `UploadLocalUsingPOST`: ResultLong
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.UploadLocalUsingPOST`: %v\n", resp)
}
```

### Path Parameters

This endpoint does not need any parameter.

### Other Parameters

Other parameters are passed through a pointer to a apiUploadLocalUsingPOSTRequest struct via the builder pattern


### Return type

[**ResultLong**](ResultLong.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/x-www-form-urlencoded
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## UploadRepositoryUsingPOST

> ResultListDocument UploadRepositoryUsingPOST(ctx).RequestBody(requestBody).Execute()

获取知识库文件详情

### Example

```go
package main

import (
    "context"
    "fmt"
    "os"
    openapiclient "./openapi"
)

func main() {
    requestBody := []int64{int64(123)} // []int64 |  (optional)

    configuration := openapiclient.NewConfiguration()
    api_client := openapiclient.NewAPIClient(configuration)
    resp, r, err := api_client.BaseApi.UploadRepositoryUsingPOST(context.Background(), ).RequestBody(requestBody).Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `BaseApi.UploadRepositoryUsingPOST``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // response from `UploadRepositoryUsingPOST`: ResultListDocument
    fmt.Fprintf(os.Stdout, "Response from `BaseApi.UploadRepositoryUsingPOST`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiUploadRepositoryUsingPOSTRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **requestBody** | [**[]int64**](int64.md) |  | 

### Return type

[**ResultListDocument**](ResultListDocument.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)

