# DocBusinessTypeVo

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**DocsList** | Pointer to [**[]DocumentTree**](DocumentTree.md) |  | [optional] 
**ImgList** | Pointer to [**[]DocumentTree**](DocumentTree.md) |  | [optional] 
**NoneList** | Pointer to [**[]DocumentTree**](DocumentTree.md) |  | [optional] 
**PdfList** | Pointer to [**[]DocumentTree**](DocumentTree.md) |  | [optional] 
**PptxList** | Pointer to [**[]DocumentTree**](DocumentTree.md) |  | [optional] 
**TableList** | Pointer to [**[]DocumentTree**](DocumentTree.md) |  | [optional] 
**TxtList** | Pointer to [**[]DocumentTree**](DocumentTree.md) |  | [optional] 

## Methods

### NewDocBusinessTypeVo

`func NewDocBusinessTypeVo() *DocBusinessTypeVo`

NewDocBusinessTypeVo instantiates a new DocBusinessTypeVo object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewDocBusinessTypeVoWithDefaults

`func NewDocBusinessTypeVoWithDefaults() *DocBusinessTypeVo`

NewDocBusinessTypeVoWithDefaults instantiates a new DocBusinessTypeVo object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetDocsList

`func (o *DocBusinessTypeVo) GetDocsList() []DocumentTree`

GetDocsList returns the DocsList field if non-nil, zero value otherwise.

### GetDocsListOk

`func (o *DocBusinessTypeVo) GetDocsListOk() (*[]DocumentTree, bool)`

GetDocsListOk returns a tuple with the DocsList field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDocsList

`func (o *DocBusinessTypeVo) SetDocsList(v []DocumentTree)`

SetDocsList sets DocsList field to given value.

### HasDocsList

`func (o *DocBusinessTypeVo) HasDocsList() bool`

HasDocsList returns a boolean if a field has been set.

### GetImgList

`func (o *DocBusinessTypeVo) GetImgList() []DocumentTree`

GetImgList returns the ImgList field if non-nil, zero value otherwise.

### GetImgListOk

`func (o *DocBusinessTypeVo) GetImgListOk() (*[]DocumentTree, bool)`

GetImgListOk returns a tuple with the ImgList field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetImgList

`func (o *DocBusinessTypeVo) SetImgList(v []DocumentTree)`

SetImgList sets ImgList field to given value.

### HasImgList

`func (o *DocBusinessTypeVo) HasImgList() bool`

HasImgList returns a boolean if a field has been set.

### GetNoneList

`func (o *DocBusinessTypeVo) GetNoneList() []DocumentTree`

GetNoneList returns the NoneList field if non-nil, zero value otherwise.

### GetNoneListOk

`func (o *DocBusinessTypeVo) GetNoneListOk() (*[]DocumentTree, bool)`

GetNoneListOk returns a tuple with the NoneList field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetNoneList

`func (o *DocBusinessTypeVo) SetNoneList(v []DocumentTree)`

SetNoneList sets NoneList field to given value.

### HasNoneList

`func (o *DocBusinessTypeVo) HasNoneList() bool`

HasNoneList returns a boolean if a field has been set.

### GetPdfList

`func (o *DocBusinessTypeVo) GetPdfList() []DocumentTree`

GetPdfList returns the PdfList field if non-nil, zero value otherwise.

### GetPdfListOk

`func (o *DocBusinessTypeVo) GetPdfListOk() (*[]DocumentTree, bool)`

GetPdfListOk returns a tuple with the PdfList field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetPdfList

`func (o *DocBusinessTypeVo) SetPdfList(v []DocumentTree)`

SetPdfList sets PdfList field to given value.

### HasPdfList

`func (o *DocBusinessTypeVo) HasPdfList() bool`

HasPdfList returns a boolean if a field has been set.

### GetPptxList

`func (o *DocBusinessTypeVo) GetPptxList() []DocumentTree`

GetPptxList returns the PptxList field if non-nil, zero value otherwise.

### GetPptxListOk

`func (o *DocBusinessTypeVo) GetPptxListOk() (*[]DocumentTree, bool)`

GetPptxListOk returns a tuple with the PptxList field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetPptxList

`func (o *DocBusinessTypeVo) SetPptxList(v []DocumentTree)`

SetPptxList sets PptxList field to given value.

### HasPptxList

`func (o *DocBusinessTypeVo) HasPptxList() bool`

HasPptxList returns a boolean if a field has been set.

### GetTableList

`func (o *DocBusinessTypeVo) GetTableList() []DocumentTree`

GetTableList returns the TableList field if non-nil, zero value otherwise.

### GetTableListOk

`func (o *DocBusinessTypeVo) GetTableListOk() (*[]DocumentTree, bool)`

GetTableListOk returns a tuple with the TableList field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetTableList

`func (o *DocBusinessTypeVo) SetTableList(v []DocumentTree)`

SetTableList sets TableList field to given value.

### HasTableList

`func (o *DocBusinessTypeVo) HasTableList() bool`

HasTableList returns a boolean if a field has been set.

### GetTxtList

`func (o *DocBusinessTypeVo) GetTxtList() []DocumentTree`

GetTxtList returns the TxtList field if non-nil, zero value otherwise.

### GetTxtListOk

`func (o *DocBusinessTypeVo) GetTxtListOk() (*[]DocumentTree, bool)`

GetTxtListOk returns a tuple with the TxtList field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetTxtList

`func (o *DocBusinessTypeVo) SetTxtList(v []DocumentTree)`

SetTxtList sets TxtList field to given value.

### HasTxtList

`func (o *DocBusinessTypeVo) HasTxtList() bool`

HasTxtList returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


