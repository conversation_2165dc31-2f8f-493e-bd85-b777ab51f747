# DocCacheRequest

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**ChunkList** | Pointer to [**[]DocChunk**](DocChunk.md) |  | [optional] 
**FileUuid** | Pointer to **string** |  | [optional] 

## Methods

### NewDocCacheRequest

`func NewDocCacheRequest() *DocCacheRequest`

NewDocCacheRequest instantiates a new DocCacheRequest object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewDocCacheRequestWithDefaults

`func NewDocCacheRequestWithDefaults() *DocCacheRequest`

NewDocCacheRequestWithDefaults instantiates a new DocCacheRequest object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetChunkList

`func (o *DocCacheRequest) GetChunkList() []DocChunk`

GetChunkList returns the ChunkList field if non-nil, zero value otherwise.

### GetChunkListOk

`func (o *DocCacheRequest) GetChunkListOk() (*[]DocChunk, bool)`

GetChunkListOk returns a tuple with the ChunkList field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetChunkList

`func (o *DocCacheRequest) SetChunkList(v []DocChunk)`

SetChunkList sets ChunkList field to given value.

### HasChunkList

`func (o *DocCacheRequest) HasChunkList() bool`

HasChunkList returns a boolean if a field has been set.

### GetFileUuid

`func (o *DocCacheRequest) GetFileUuid() string`

GetFileUuid returns the FileUuid field if non-nil, zero value otherwise.

### GetFileUuidOk

`func (o *DocCacheRequest) GetFileUuidOk() (*string, bool)`

GetFileUuidOk returns a tuple with the FileUuid field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFileUuid

`func (o *DocCacheRequest) SetFileUuid(v string)`

SetFileUuid sets FileUuid field to given value.

### HasFileUuid

`func (o *DocCacheRequest) HasFileUuid() bool`

HasFileUuid returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


