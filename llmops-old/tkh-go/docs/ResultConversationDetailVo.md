# ResultConversationDetailVo

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Code** | Pointer to **int32** |  | [optional] 
**Data** | Pointer to [**ConversationDetailVo**](ConversationDetailVo.md) |  | [optional] 
**Message** | Pointer to **string** |  | [optional] 

## Methods

### NewResultConversationDetailVo

`func NewResultConversationDetailVo() *ResultConversationDetailVo`

NewResultConversationDetailVo instantiates a new ResultConversationDetailVo object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewResultConversationDetailVoWithDefaults

`func NewResultConversationDetailVoWithDefaults() *ResultConversationDetailVo`

NewResultConversationDetailVoWithDefaults instantiates a new ResultConversationDetailVo object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetCode

`func (o *ResultConversationDetailVo) GetCode() int32`

GetCode returns the Code field if non-nil, zero value otherwise.

### GetCodeOk

`func (o *ResultConversationDetailVo) GetCodeOk() (*int32, bool)`

GetCodeOk returns a tuple with the Code field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCode

`func (o *ResultConversationDetailVo) SetCode(v int32)`

SetCode sets Code field to given value.

### HasCode

`func (o *ResultConversationDetailVo) HasCode() bool`

HasCode returns a boolean if a field has been set.

### GetData

`func (o *ResultConversationDetailVo) GetData() ConversationDetailVo`

GetData returns the Data field if non-nil, zero value otherwise.

### GetDataOk

`func (o *ResultConversationDetailVo) GetDataOk() (*ConversationDetailVo, bool)`

GetDataOk returns a tuple with the Data field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetData

`func (o *ResultConversationDetailVo) SetData(v ConversationDetailVo)`

SetData sets Data field to given value.

### HasData

`func (o *ResultConversationDetailVo) HasData() bool`

HasData returns a boolean if a field has been set.

### GetMessage

`func (o *ResultConversationDetailVo) GetMessage() string`

GetMessage returns the Message field if non-nil, zero value otherwise.

### GetMessageOk

`func (o *ResultConversationDetailVo) GetMessageOk() (*string, bool)`

GetMessageOk returns a tuple with the Message field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetMessage

`func (o *ResultConversationDetailVo) SetMessage(v string)`

SetMessage sets Message field to given value.

### HasMessage

`func (o *ResultConversationDetailVo) HasMessage() bool`

HasMessage returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


