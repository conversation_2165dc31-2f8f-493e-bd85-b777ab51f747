# ConversationRequest

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**FirstAnswer** | Pointer to **string** |  | [optional] 
**FirstQuestion** | Pointer to **string** |  | [optional] 
**SourceIds** | Pointer to **string** | 保留字段，文档来源，逗号分割，仅文档问答生效 | [optional] 
**Type** | Pointer to **int32** | 文档类型:0 智能问答 1文档问答 2智能写作 | 

## Methods

### NewConversationRequest

`func NewConversationRequest(type_ int32, ) *ConversationRequest`

NewConversationRequest instantiates a new ConversationRequest object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewConversationRequestWithDefaults

`func NewConversationRequestWithDefaults() *ConversationRequest`

NewConversationRequestWithDefaults instantiates a new ConversationRequest object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetFirstAnswer

`func (o *ConversationRequest) GetFirstAnswer() string`

GetFirstAnswer returns the FirstAnswer field if non-nil, zero value otherwise.

### GetFirstAnswerOk

`func (o *ConversationRequest) GetFirstAnswerOk() (*string, bool)`

GetFirstAnswerOk returns a tuple with the FirstAnswer field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFirstAnswer

`func (o *ConversationRequest) SetFirstAnswer(v string)`

SetFirstAnswer sets FirstAnswer field to given value.

### HasFirstAnswer

`func (o *ConversationRequest) HasFirstAnswer() bool`

HasFirstAnswer returns a boolean if a field has been set.

### GetFirstQuestion

`func (o *ConversationRequest) GetFirstQuestion() string`

GetFirstQuestion returns the FirstQuestion field if non-nil, zero value otherwise.

### GetFirstQuestionOk

`func (o *ConversationRequest) GetFirstQuestionOk() (*string, bool)`

GetFirstQuestionOk returns a tuple with the FirstQuestion field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFirstQuestion

`func (o *ConversationRequest) SetFirstQuestion(v string)`

SetFirstQuestion sets FirstQuestion field to given value.

### HasFirstQuestion

`func (o *ConversationRequest) HasFirstQuestion() bool`

HasFirstQuestion returns a boolean if a field has been set.

### GetSourceIds

`func (o *ConversationRequest) GetSourceIds() string`

GetSourceIds returns the SourceIds field if non-nil, zero value otherwise.

### GetSourceIdsOk

`func (o *ConversationRequest) GetSourceIdsOk() (*string, bool)`

GetSourceIdsOk returns a tuple with the SourceIds field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetSourceIds

`func (o *ConversationRequest) SetSourceIds(v string)`

SetSourceIds sets SourceIds field to given value.

### HasSourceIds

`func (o *ConversationRequest) HasSourceIds() bool`

HasSourceIds returns a boolean if a field has been set.

### GetType

`func (o *ConversationRequest) GetType() int32`

GetType returns the Type field if non-nil, zero value otherwise.

### GetTypeOk

`func (o *ConversationRequest) GetTypeOk() (*int32, bool)`

GetTypeOk returns a tuple with the Type field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetType

`func (o *ConversationRequest) SetType(v int32)`

SetType sets Type field to given value.



[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


