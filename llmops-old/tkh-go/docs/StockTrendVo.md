# StockTrendVo

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Fluctuations** | Pointer to **float64** |  | [optional] 
**Price** | Pointer to **float64** |  | [optional] 
**StockTrendList** | Pointer to [**[]StockTrend**](StockTrend.md) |  | [optional] 

## Methods

### NewStockTrendVo

`func NewStockTrendVo() *StockTrendVo`

NewStockTrendVo instantiates a new StockTrendVo object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewStockTrendVoWithDefaults

`func NewStockTrendVoWithDefaults() *StockTrendVo`

NewStockTrendVoWithDefaults instantiates a new StockTrendVo object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetFluctuations

`func (o *StockTrendVo) GetFluctuations() float64`

GetFluctuations returns the Fluctuations field if non-nil, zero value otherwise.

### GetFluctuationsOk

`func (o *StockTrendVo) GetFluctuationsOk() (*float64, bool)`

GetFluctuationsOk returns a tuple with the Fluctuations field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFluctuations

`func (o *StockTrendVo) SetFluctuations(v float64)`

SetFluctuations sets Fluctuations field to given value.

### HasFluctuations

`func (o *StockTrendVo) HasFluctuations() bool`

HasFluctuations returns a boolean if a field has been set.

### GetPrice

`func (o *StockTrendVo) GetPrice() float64`

GetPrice returns the Price field if non-nil, zero value otherwise.

### GetPriceOk

`func (o *StockTrendVo) GetPriceOk() (*float64, bool)`

GetPriceOk returns a tuple with the Price field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetPrice

`func (o *StockTrendVo) SetPrice(v float64)`

SetPrice sets Price field to given value.

### HasPrice

`func (o *StockTrendVo) HasPrice() bool`

HasPrice returns a boolean if a field has been set.

### GetStockTrendList

`func (o *StockTrendVo) GetStockTrendList() []StockTrend`

GetStockTrendList returns the StockTrendList field if non-nil, zero value otherwise.

### GetStockTrendListOk

`func (o *StockTrendVo) GetStockTrendListOk() (*[]StockTrend, bool)`

GetStockTrendListOk returns a tuple with the StockTrendList field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetStockTrendList

`func (o *StockTrendVo) SetStockTrendList(v []StockTrend)`

SetStockTrendList sets StockTrendList field to given value.

### HasStockTrendList

`func (o *StockTrendVo) HasStockTrendList() bool`

HasStockTrendList returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


