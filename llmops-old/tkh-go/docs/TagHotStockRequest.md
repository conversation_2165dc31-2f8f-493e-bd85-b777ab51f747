# TagHotStockRequest

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**StockName** | Pointer to **[]string** |  | [optional] 

## Methods

### NewTagHotStockRequest

`func NewTagHotStockRequest() *TagHotStockRequest`

NewTagHotStockRequest instantiates a new TagHotStockRequest object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewTagHotStockRequestWithDefaults

`func NewTagHotStockRequestWithDefaults() *TagHotStockRequest`

NewTagHotStockRequestWithDefaults instantiates a new TagHotStockRequest object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetStockName

`func (o *TagHotStockRequest) GetStockName() []string`

GetStockName returns the StockName field if non-nil, zero value otherwise.

### GetStockNameOk

`func (o *TagHotStockRequest) GetStockNameOk() (*[]string, bool)`

GetStockNameOk returns a tuple with the StockName field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetStockName

`func (o *TagHotStockRequest) SetStockName(v []string)`

SetStockName sets StockName field to given value.

### HasStockName

`func (o *TagHotStockRequest) HasStockName() bool`

HasStockName returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


