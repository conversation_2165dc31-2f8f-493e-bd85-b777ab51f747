# ConversationDetailVo

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**ConversationId** | Pointer to **string** |  | [optional] 
**Qalist** | Pointer to [**[]ConversationDetail**](ConversationDetail.md) |  | [optional] 

## Methods

### NewConversationDetailVo

`func NewConversationDetailVo() *ConversationDetailVo`

NewConversationDetailVo instantiates a new ConversationDetailVo object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewConversationDetailVoWithDefaults

`func NewConversationDetailVoWithDefaults() *ConversationDetailVo`

NewConversationDetailVoWithDefaults instantiates a new ConversationDetailVo object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetConversationId

`func (o *ConversationDetailVo) GetConversationId() string`

GetConversationId returns the ConversationId field if non-nil, zero value otherwise.

### GetConversationIdOk

`func (o *ConversationDetailVo) GetConversationIdOk() (*string, bool)`

GetConversationIdOk returns a tuple with the ConversationId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetConversationId

`func (o *ConversationDetailVo) SetConversationId(v string)`

SetConversationId sets ConversationId field to given value.

### HasConversationId

`func (o *ConversationDetailVo) HasConversationId() bool`

HasConversationId returns a boolean if a field has been set.

### GetQalist

`func (o *ConversationDetailVo) GetQalist() []ConversationDetail`

GetQalist returns the Qalist field if non-nil, zero value otherwise.

### GetQalistOk

`func (o *ConversationDetailVo) GetQalistOk() (*[]ConversationDetail, bool)`

GetQalistOk returns a tuple with the Qalist field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetQalist

`func (o *ConversationDetailVo) SetQalist(v []ConversationDetail)`

SetQalist sets Qalist field to given value.

### HasQalist

`func (o *ConversationDetailVo) HasQalist() bool`

HasQalist returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


