# QaRequest

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Answer** | Pointer to **string** |  | [optional] 
**ConversationId** | Pointer to **string** | 会话id | 
**Question** | Pointer to **string** |  | [optional] 

## Methods

### NewQaRequest

`func NewQaRequest(conversationId string, ) *QaRequest`

NewQaRequest instantiates a new QaRequest object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewQaRequestWithDefaults

`func NewQaRequestWithDefaults() *QaRequest`

NewQaRequestWithDefaults instantiates a new QaRequest object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetAnswer

`func (o *QaRequest) GetAnswer() string`

GetAnswer returns the Answer field if non-nil, zero value otherwise.

### GetAnswerOk

`func (o *QaRequest) GetAnswerOk() (*string, bool)`

GetAnswerOk returns a tuple with the Answer field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetAnswer

`func (o *QaRequest) SetAnswer(v string)`

SetAnswer sets Answer field to given value.

### HasAnswer

`func (o *QaRequest) HasAnswer() bool`

HasAnswer returns a boolean if a field has been set.

### GetConversationId

`func (o *QaRequest) GetConversationId() string`

GetConversationId returns the ConversationId field if non-nil, zero value otherwise.

### GetConversationIdOk

`func (o *QaRequest) GetConversationIdOk() (*string, bool)`

GetConversationIdOk returns a tuple with the ConversationId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetConversationId

`func (o *QaRequest) SetConversationId(v string)`

SetConversationId sets ConversationId field to given value.


### GetQuestion

`func (o *QaRequest) GetQuestion() string`

GetQuestion returns the Question field if non-nil, zero value otherwise.

### GetQuestionOk

`func (o *QaRequest) GetQuestionOk() (*string, bool)`

GetQuestionOk returns a tuple with the Question field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetQuestion

`func (o *QaRequest) SetQuestion(v string)`

SetQuestion sets Question field to given value.

### HasQuestion

`func (o *QaRequest) HasQuestion() bool`

HasQuestion returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


