# UserInfoDuiXiang

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Admin** | Pointer to **bool** |  | [optional] 
**CreateTime** | Pointer to **string** |  | [optional] 
**Id** | Pointer to **int32** |  | [optional] 
**Password** | Pointer to **string** |  | [optional] 
**Status** | Pointer to **int32** |  | [optional] 
**UpdateTime** | Pointer to **string** |  | [optional] 
**Username** | Pointer to **string** |  | [optional] 

## Methods

### NewUserInfoDuiXiang

`func NewUserInfoDuiXiang() *UserInfoDuiXiang`

NewUserInfoDuiXiang instantiates a new UserInfoDuiXiang object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewUserInfoDuiXiangWithDefaults

`func NewUserInfoDuiXiangWithDefaults() *UserInfoDuiXiang`

NewUserInfoDuiXiangWithDefaults instantiates a new UserInfoDuiXiang object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetAdmin

`func (o *UserInfoDuiXiang) GetAdmin() bool`

GetAdmin returns the Admin field if non-nil, zero value otherwise.

### GetAdminOk

`func (o *UserInfoDuiXiang) GetAdminOk() (*bool, bool)`

GetAdminOk returns a tuple with the Admin field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetAdmin

`func (o *UserInfoDuiXiang) SetAdmin(v bool)`

SetAdmin sets Admin field to given value.

### HasAdmin

`func (o *UserInfoDuiXiang) HasAdmin() bool`

HasAdmin returns a boolean if a field has been set.

### GetCreateTime

`func (o *UserInfoDuiXiang) GetCreateTime() string`

GetCreateTime returns the CreateTime field if non-nil, zero value otherwise.

### GetCreateTimeOk

`func (o *UserInfoDuiXiang) GetCreateTimeOk() (*string, bool)`

GetCreateTimeOk returns a tuple with the CreateTime field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCreateTime

`func (o *UserInfoDuiXiang) SetCreateTime(v string)`

SetCreateTime sets CreateTime field to given value.

### HasCreateTime

`func (o *UserInfoDuiXiang) HasCreateTime() bool`

HasCreateTime returns a boolean if a field has been set.

### GetId

`func (o *UserInfoDuiXiang) GetId() int32`

GetId returns the Id field if non-nil, zero value otherwise.

### GetIdOk

`func (o *UserInfoDuiXiang) GetIdOk() (*int32, bool)`

GetIdOk returns a tuple with the Id field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetId

`func (o *UserInfoDuiXiang) SetId(v int32)`

SetId sets Id field to given value.

### HasId

`func (o *UserInfoDuiXiang) HasId() bool`

HasId returns a boolean if a field has been set.

### GetPassword

`func (o *UserInfoDuiXiang) GetPassword() string`

GetPassword returns the Password field if non-nil, zero value otherwise.

### GetPasswordOk

`func (o *UserInfoDuiXiang) GetPasswordOk() (*string, bool)`

GetPasswordOk returns a tuple with the Password field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetPassword

`func (o *UserInfoDuiXiang) SetPassword(v string)`

SetPassword sets Password field to given value.

### HasPassword

`func (o *UserInfoDuiXiang) HasPassword() bool`

HasPassword returns a boolean if a field has been set.

### GetStatus

`func (o *UserInfoDuiXiang) GetStatus() int32`

GetStatus returns the Status field if non-nil, zero value otherwise.

### GetStatusOk

`func (o *UserInfoDuiXiang) GetStatusOk() (*int32, bool)`

GetStatusOk returns a tuple with the Status field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetStatus

`func (o *UserInfoDuiXiang) SetStatus(v int32)`

SetStatus sets Status field to given value.

### HasStatus

`func (o *UserInfoDuiXiang) HasStatus() bool`

HasStatus returns a boolean if a field has been set.

### GetUpdateTime

`func (o *UserInfoDuiXiang) GetUpdateTime() string`

GetUpdateTime returns the UpdateTime field if non-nil, zero value otherwise.

### GetUpdateTimeOk

`func (o *UserInfoDuiXiang) GetUpdateTimeOk() (*string, bool)`

GetUpdateTimeOk returns a tuple with the UpdateTime field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUpdateTime

`func (o *UserInfoDuiXiang) SetUpdateTime(v string)`

SetUpdateTime sets UpdateTime field to given value.

### HasUpdateTime

`func (o *UserInfoDuiXiang) HasUpdateTime() bool`

HasUpdateTime returns a boolean if a field has been set.

### GetUsername

`func (o *UserInfoDuiXiang) GetUsername() string`

GetUsername returns the Username field if non-nil, zero value otherwise.

### GetUsernameOk

`func (o *UserInfoDuiXiang) GetUsernameOk() (*string, bool)`

GetUsernameOk returns a tuple with the Username field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUsername

`func (o *UserInfoDuiXiang) SetUsername(v string)`

SetUsername sets Username field to given value.

### HasUsername

`func (o *UserInfoDuiXiang) HasUsername() bool`

HasUsername returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


