# SingleDocSplitParam

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**DocId** | Pointer to **string** | 文档uuid | [optional] 
**DocumentParser** | Pointer to **string** | 解析器字符串 | [optional] 
**FilePath** | Pointer to **string** |  | [optional] 
**Params** | Pointer to [**map[string]interface{}**](.md) | map结构，包含 chunksize  | [optional] 

## Methods

### NewSingleDocSplitParam

`func NewSingleDocSplitParam() *SingleDocSplitParam`

NewSingleDocSplitParam instantiates a new SingleDocSplitParam object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewSingleDocSplitParamWithDefaults

`func NewSingleDocSplitParamWithDefaults() *SingleDocSplitParam`

NewSingleDocSplitParamWithDefaults instantiates a new SingleDocSplitParam object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetDocId

`func (o *SingleDocSplitParam) GetDocId() string`

GetDocId returns the DocId field if non-nil, zero value otherwise.

### GetDocIdOk

`func (o *SingleDocSplitParam) GetDocIdOk() (*string, bool)`

GetDocIdOk returns a tuple with the DocId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDocId

`func (o *SingleDocSplitParam) SetDocId(v string)`

SetDocId sets DocId field to given value.

### HasDocId

`func (o *SingleDocSplitParam) HasDocId() bool`

HasDocId returns a boolean if a field has been set.

### GetDocumentParser

`func (o *SingleDocSplitParam) GetDocumentParser() string`

GetDocumentParser returns the DocumentParser field if non-nil, zero value otherwise.

### GetDocumentParserOk

`func (o *SingleDocSplitParam) GetDocumentParserOk() (*string, bool)`

GetDocumentParserOk returns a tuple with the DocumentParser field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDocumentParser

`func (o *SingleDocSplitParam) SetDocumentParser(v string)`

SetDocumentParser sets DocumentParser field to given value.

### HasDocumentParser

`func (o *SingleDocSplitParam) HasDocumentParser() bool`

HasDocumentParser returns a boolean if a field has been set.

### GetFilePath

`func (o *SingleDocSplitParam) GetFilePath() string`

GetFilePath returns the FilePath field if non-nil, zero value otherwise.

### GetFilePathOk

`func (o *SingleDocSplitParam) GetFilePathOk() (*string, bool)`

GetFilePathOk returns a tuple with the FilePath field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFilePath

`func (o *SingleDocSplitParam) SetFilePath(v string)`

SetFilePath sets FilePath field to given value.

### HasFilePath

`func (o *SingleDocSplitParam) HasFilePath() bool`

HasFilePath returns a boolean if a field has been set.

### GetParams

`func (o *SingleDocSplitParam) GetParams() map[string]interface{}`

GetParams returns the Params field if non-nil, zero value otherwise.

### GetParamsOk

`func (o *SingleDocSplitParam) GetParamsOk() (*map[string]interface{}, bool)`

GetParamsOk returns a tuple with the Params field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetParams

`func (o *SingleDocSplitParam) SetParams(v map[string]interface{})`

SetParams sets Params field to given value.

### HasParams

`func (o *SingleDocSplitParam) HasParams() bool`

HasParams returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


