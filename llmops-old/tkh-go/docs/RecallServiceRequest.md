# RecallServiceRequest

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**UserQueries** | Pointer to **[]string** | User Queries | [optional] [default to []]
**Sources** | Pointer to [**[]KnowledgeSource**](KnowledgeSource.md) | Sources | [optional] [default to []]
**HippoDataSources** | Pointer to **[]string** | Hippo Data Sources | [optional] [default to []]
**ScopeDataSources** | Pointer to **[]string** | Scope Data Sources | [optional] [default to []]
**ReservedConditionCompany** | Pointer to **[]string** | Reserved Condition Company | [optional] [default to []]
**ReservedConditionTime** | Pointer to **[]string** | Reserved Condition Time | [optional] [default to []]
**PdfName** | Pointer to **string** | Pdf Name | [optional] [default to ""]
**DocId** | Pointer to **string** | Doc Id | [optional] [default to ""]
**TopK** | Pointer to **int32** | Top K | [optional] [default to 5]
**FragmentTextFiler** | Pointer to **bool** | Fragment Text Filer | [optional] [default to true]
**Deduplicate** | Pointer to **bool** | Deduplicate | [optional] [default to true]
**TableReformat** | Pointer to **bool** | Table Reformat | [optional] [default to true]
**Rerank** | Pointer to **bool** | Rerank | [optional] [default to true]
**CompactText** | Pointer to **bool** | Compact Text | [optional] [default to true]

## Methods

### NewRecallServiceRequest

`func NewRecallServiceRequest() *RecallServiceRequest`

NewRecallServiceRequest instantiates a new RecallServiceRequest object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewRecallServiceRequestWithDefaults

`func NewRecallServiceRequestWithDefaults() *RecallServiceRequest`

NewRecallServiceRequestWithDefaults instantiates a new RecallServiceRequest object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetUserQueries

`func (o *RecallServiceRequest) GetUserQueries() []string`

GetUserQueries returns the UserQueries field if non-nil, zero value otherwise.

### GetUserQueriesOk

`func (o *RecallServiceRequest) GetUserQueriesOk() (*[]string, bool)`

GetUserQueriesOk returns a tuple with the UserQueries field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUserQueries

`func (o *RecallServiceRequest) SetUserQueries(v []string)`

SetUserQueries sets UserQueries field to given value.

### HasUserQueries

`func (o *RecallServiceRequest) HasUserQueries() bool`

HasUserQueries returns a boolean if a field has been set.

### GetSources

`func (o *RecallServiceRequest) GetSources() []KnowledgeSource`

GetSources returns the Sources field if non-nil, zero value otherwise.

### GetSourcesOk

`func (o *RecallServiceRequest) GetSourcesOk() (*[]KnowledgeSource, bool)`

GetSourcesOk returns a tuple with the Sources field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetSources

`func (o *RecallServiceRequest) SetSources(v []KnowledgeSource)`

SetSources sets Sources field to given value.

### HasSources

`func (o *RecallServiceRequest) HasSources() bool`

HasSources returns a boolean if a field has been set.

### GetHippoDataSources

`func (o *RecallServiceRequest) GetHippoDataSources() []string`

GetHippoDataSources returns the HippoDataSources field if non-nil, zero value otherwise.

### GetHippoDataSourcesOk

`func (o *RecallServiceRequest) GetHippoDataSourcesOk() (*[]string, bool)`

GetHippoDataSourcesOk returns a tuple with the HippoDataSources field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetHippoDataSources

`func (o *RecallServiceRequest) SetHippoDataSources(v []string)`

SetHippoDataSources sets HippoDataSources field to given value.

### HasHippoDataSources

`func (o *RecallServiceRequest) HasHippoDataSources() bool`

HasHippoDataSources returns a boolean if a field has been set.

### GetScopeDataSources

`func (o *RecallServiceRequest) GetScopeDataSources() []string`

GetScopeDataSources returns the ScopeDataSources field if non-nil, zero value otherwise.

### GetScopeDataSourcesOk

`func (o *RecallServiceRequest) GetScopeDataSourcesOk() (*[]string, bool)`

GetScopeDataSourcesOk returns a tuple with the ScopeDataSources field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetScopeDataSources

`func (o *RecallServiceRequest) SetScopeDataSources(v []string)`

SetScopeDataSources sets ScopeDataSources field to given value.

### HasScopeDataSources

`func (o *RecallServiceRequest) HasScopeDataSources() bool`

HasScopeDataSources returns a boolean if a field has been set.

### GetReservedConditionCompany

`func (o *RecallServiceRequest) GetReservedConditionCompany() []string`

GetReservedConditionCompany returns the ReservedConditionCompany field if non-nil, zero value otherwise.

### GetReservedConditionCompanyOk

`func (o *RecallServiceRequest) GetReservedConditionCompanyOk() (*[]string, bool)`

GetReservedConditionCompanyOk returns a tuple with the ReservedConditionCompany field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetReservedConditionCompany

`func (o *RecallServiceRequest) SetReservedConditionCompany(v []string)`

SetReservedConditionCompany sets ReservedConditionCompany field to given value.

### HasReservedConditionCompany

`func (o *RecallServiceRequest) HasReservedConditionCompany() bool`

HasReservedConditionCompany returns a boolean if a field has been set.

### GetReservedConditionTime

`func (o *RecallServiceRequest) GetReservedConditionTime() []string`

GetReservedConditionTime returns the ReservedConditionTime field if non-nil, zero value otherwise.

### GetReservedConditionTimeOk

`func (o *RecallServiceRequest) GetReservedConditionTimeOk() (*[]string, bool)`

GetReservedConditionTimeOk returns a tuple with the ReservedConditionTime field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetReservedConditionTime

`func (o *RecallServiceRequest) SetReservedConditionTime(v []string)`

SetReservedConditionTime sets ReservedConditionTime field to given value.

### HasReservedConditionTime

`func (o *RecallServiceRequest) HasReservedConditionTime() bool`

HasReservedConditionTime returns a boolean if a field has been set.

### GetPdfName

`func (o *RecallServiceRequest) GetPdfName() string`

GetPdfName returns the PdfName field if non-nil, zero value otherwise.

### GetPdfNameOk

`func (o *RecallServiceRequest) GetPdfNameOk() (*string, bool)`

GetPdfNameOk returns a tuple with the PdfName field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetPdfName

`func (o *RecallServiceRequest) SetPdfName(v string)`

SetPdfName sets PdfName field to given value.

### HasPdfName

`func (o *RecallServiceRequest) HasPdfName() bool`

HasPdfName returns a boolean if a field has been set.

### GetDocId

`func (o *RecallServiceRequest) GetDocId() string`

GetDocId returns the DocId field if non-nil, zero value otherwise.

### GetDocIdOk

`func (o *RecallServiceRequest) GetDocIdOk() (*string, bool)`

GetDocIdOk returns a tuple with the DocId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDocId

`func (o *RecallServiceRequest) SetDocId(v string)`

SetDocId sets DocId field to given value.

### HasDocId

`func (o *RecallServiceRequest) HasDocId() bool`

HasDocId returns a boolean if a field has been set.

### GetTopK

`func (o *RecallServiceRequest) GetTopK() int32`

GetTopK returns the TopK field if non-nil, zero value otherwise.

### GetTopKOk

`func (o *RecallServiceRequest) GetTopKOk() (*int32, bool)`

GetTopKOk returns a tuple with the TopK field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetTopK

`func (o *RecallServiceRequest) SetTopK(v int32)`

SetTopK sets TopK field to given value.

### HasTopK

`func (o *RecallServiceRequest) HasTopK() bool`

HasTopK returns a boolean if a field has been set.

### GetFragmentTextFiler

`func (o *RecallServiceRequest) GetFragmentTextFiler() bool`

GetFragmentTextFiler returns the FragmentTextFiler field if non-nil, zero value otherwise.

### GetFragmentTextFilerOk

`func (o *RecallServiceRequest) GetFragmentTextFilerOk() (*bool, bool)`

GetFragmentTextFilerOk returns a tuple with the FragmentTextFiler field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFragmentTextFiler

`func (o *RecallServiceRequest) SetFragmentTextFiler(v bool)`

SetFragmentTextFiler sets FragmentTextFiler field to given value.

### HasFragmentTextFiler

`func (o *RecallServiceRequest) HasFragmentTextFiler() bool`

HasFragmentTextFiler returns a boolean if a field has been set.

### GetDeduplicate

`func (o *RecallServiceRequest) GetDeduplicate() bool`

GetDeduplicate returns the Deduplicate field if non-nil, zero value otherwise.

### GetDeduplicateOk

`func (o *RecallServiceRequest) GetDeduplicateOk() (*bool, bool)`

GetDeduplicateOk returns a tuple with the Deduplicate field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDeduplicate

`func (o *RecallServiceRequest) SetDeduplicate(v bool)`

SetDeduplicate sets Deduplicate field to given value.

### HasDeduplicate

`func (o *RecallServiceRequest) HasDeduplicate() bool`

HasDeduplicate returns a boolean if a field has been set.

### GetTableReformat

`func (o *RecallServiceRequest) GetTableReformat() bool`

GetTableReformat returns the TableReformat field if non-nil, zero value otherwise.

### GetTableReformatOk

`func (o *RecallServiceRequest) GetTableReformatOk() (*bool, bool)`

GetTableReformatOk returns a tuple with the TableReformat field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetTableReformat

`func (o *RecallServiceRequest) SetTableReformat(v bool)`

SetTableReformat sets TableReformat field to given value.

### HasTableReformat

`func (o *RecallServiceRequest) HasTableReformat() bool`

HasTableReformat returns a boolean if a field has been set.

### GetRerank

`func (o *RecallServiceRequest) GetRerank() bool`

GetRerank returns the Rerank field if non-nil, zero value otherwise.

### GetRerankOk

`func (o *RecallServiceRequest) GetRerankOk() (*bool, bool)`

GetRerankOk returns a tuple with the Rerank field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetRerank

`func (o *RecallServiceRequest) SetRerank(v bool)`

SetRerank sets Rerank field to given value.

### HasRerank

`func (o *RecallServiceRequest) HasRerank() bool`

HasRerank returns a boolean if a field has been set.

### GetCompactText

`func (o *RecallServiceRequest) GetCompactText() bool`

GetCompactText returns the CompactText field if non-nil, zero value otherwise.

### GetCompactTextOk

`func (o *RecallServiceRequest) GetCompactTextOk() (*bool, bool)`

GetCompactTextOk returns a tuple with the CompactText field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCompactText

`func (o *RecallServiceRequest) SetCompactText(v bool)`

SetCompactText sets CompactText field to given value.

### HasCompactText

`func (o *RecallServiceRequest) HasCompactText() bool`

HasCompactText returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


