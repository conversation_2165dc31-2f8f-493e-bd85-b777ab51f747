# Repository

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**CreateTime** | Pointer to **string** | 创建时间 | [optional] 
**Creator** | Pointer to **string** | 创建者 | [optional] 
**DatasourceId** | Pointer to **int64** | 关联数据源id,仅repository_type&#x3D;2才有效 | [optional] 
**Description** | Pointer to **string** | 描述 | [optional] 
**DocumentTreeId** | Pointer to **int64** | 文档树ID | [optional] 
**Id** | Pointer to **int64** |  | [optional] 
**Name** | Pointer to **string** | 名称 | [optional] 
**OwnerType** | Pointer to **int32** | 仓库所属人类型  1 &#x3D;公共 2&#x3D; 私有 | [optional] 
**RepositoryStorageType** | Pointer to **int32** | 知识库存储类型 枚举 1 &#x3D; 网络存储系统  2 &#x3D; 本地文件系统 | [optional] 
**RepositoryType** | Pointer to **int32** | 知识库类型 枚举 1 &#x3D; 文本  2 &#x3D; sdb | [optional] 
**UpdateTime** | Pointer to **string** | 更新时间 | [optional] 
**UploadEnable** | Pointer to **bool** | 用户是否有权限执行上传 | [optional] 
**Uuid** | Pointer to **string** | uuid | [optional] 

## Methods

### NewRepository

`func NewRepository() *Repository`

NewRepository instantiates a new Repository object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewRepositoryWithDefaults

`func NewRepositoryWithDefaults() *Repository`

NewRepositoryWithDefaults instantiates a new Repository object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetCreateTime

`func (o *Repository) GetCreateTime() string`

GetCreateTime returns the CreateTime field if non-nil, zero value otherwise.

### GetCreateTimeOk

`func (o *Repository) GetCreateTimeOk() (*string, bool)`

GetCreateTimeOk returns a tuple with the CreateTime field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCreateTime

`func (o *Repository) SetCreateTime(v string)`

SetCreateTime sets CreateTime field to given value.

### HasCreateTime

`func (o *Repository) HasCreateTime() bool`

HasCreateTime returns a boolean if a field has been set.

### GetCreator

`func (o *Repository) GetCreator() string`

GetCreator returns the Creator field if non-nil, zero value otherwise.

### GetCreatorOk

`func (o *Repository) GetCreatorOk() (*string, bool)`

GetCreatorOk returns a tuple with the Creator field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCreator

`func (o *Repository) SetCreator(v string)`

SetCreator sets Creator field to given value.

### HasCreator

`func (o *Repository) HasCreator() bool`

HasCreator returns a boolean if a field has been set.

### GetDatasourceId

`func (o *Repository) GetDatasourceId() int64`

GetDatasourceId returns the DatasourceId field if non-nil, zero value otherwise.

### GetDatasourceIdOk

`func (o *Repository) GetDatasourceIdOk() (*int64, bool)`

GetDatasourceIdOk returns a tuple with the DatasourceId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDatasourceId

`func (o *Repository) SetDatasourceId(v int64)`

SetDatasourceId sets DatasourceId field to given value.

### HasDatasourceId

`func (o *Repository) HasDatasourceId() bool`

HasDatasourceId returns a boolean if a field has been set.

### GetDescription

`func (o *Repository) GetDescription() string`

GetDescription returns the Description field if non-nil, zero value otherwise.

### GetDescriptionOk

`func (o *Repository) GetDescriptionOk() (*string, bool)`

GetDescriptionOk returns a tuple with the Description field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDescription

`func (o *Repository) SetDescription(v string)`

SetDescription sets Description field to given value.

### HasDescription

`func (o *Repository) HasDescription() bool`

HasDescription returns a boolean if a field has been set.

### GetDocumentTreeId

`func (o *Repository) GetDocumentTreeId() int64`

GetDocumentTreeId returns the DocumentTreeId field if non-nil, zero value otherwise.

### GetDocumentTreeIdOk

`func (o *Repository) GetDocumentTreeIdOk() (*int64, bool)`

GetDocumentTreeIdOk returns a tuple with the DocumentTreeId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDocumentTreeId

`func (o *Repository) SetDocumentTreeId(v int64)`

SetDocumentTreeId sets DocumentTreeId field to given value.

### HasDocumentTreeId

`func (o *Repository) HasDocumentTreeId() bool`

HasDocumentTreeId returns a boolean if a field has been set.

### GetId

`func (o *Repository) GetId() int64`

GetId returns the Id field if non-nil, zero value otherwise.

### GetIdOk

`func (o *Repository) GetIdOk() (*int64, bool)`

GetIdOk returns a tuple with the Id field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetId

`func (o *Repository) SetId(v int64)`

SetId sets Id field to given value.

### HasId

`func (o *Repository) HasId() bool`

HasId returns a boolean if a field has been set.

### GetName

`func (o *Repository) GetName() string`

GetName returns the Name field if non-nil, zero value otherwise.

### GetNameOk

`func (o *Repository) GetNameOk() (*string, bool)`

GetNameOk returns a tuple with the Name field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetName

`func (o *Repository) SetName(v string)`

SetName sets Name field to given value.

### HasName

`func (o *Repository) HasName() bool`

HasName returns a boolean if a field has been set.

### GetOwnerType

`func (o *Repository) GetOwnerType() int32`

GetOwnerType returns the OwnerType field if non-nil, zero value otherwise.

### GetOwnerTypeOk

`func (o *Repository) GetOwnerTypeOk() (*int32, bool)`

GetOwnerTypeOk returns a tuple with the OwnerType field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetOwnerType

`func (o *Repository) SetOwnerType(v int32)`

SetOwnerType sets OwnerType field to given value.

### HasOwnerType

`func (o *Repository) HasOwnerType() bool`

HasOwnerType returns a boolean if a field has been set.

### GetRepositoryStorageType

`func (o *Repository) GetRepositoryStorageType() int32`

GetRepositoryStorageType returns the RepositoryStorageType field if non-nil, zero value otherwise.

### GetRepositoryStorageTypeOk

`func (o *Repository) GetRepositoryStorageTypeOk() (*int32, bool)`

GetRepositoryStorageTypeOk returns a tuple with the RepositoryStorageType field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetRepositoryStorageType

`func (o *Repository) SetRepositoryStorageType(v int32)`

SetRepositoryStorageType sets RepositoryStorageType field to given value.

### HasRepositoryStorageType

`func (o *Repository) HasRepositoryStorageType() bool`

HasRepositoryStorageType returns a boolean if a field has been set.

### GetRepositoryType

`func (o *Repository) GetRepositoryType() int32`

GetRepositoryType returns the RepositoryType field if non-nil, zero value otherwise.

### GetRepositoryTypeOk

`func (o *Repository) GetRepositoryTypeOk() (*int32, bool)`

GetRepositoryTypeOk returns a tuple with the RepositoryType field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetRepositoryType

`func (o *Repository) SetRepositoryType(v int32)`

SetRepositoryType sets RepositoryType field to given value.

### HasRepositoryType

`func (o *Repository) HasRepositoryType() bool`

HasRepositoryType returns a boolean if a field has been set.

### GetUpdateTime

`func (o *Repository) GetUpdateTime() string`

GetUpdateTime returns the UpdateTime field if non-nil, zero value otherwise.

### GetUpdateTimeOk

`func (o *Repository) GetUpdateTimeOk() (*string, bool)`

GetUpdateTimeOk returns a tuple with the UpdateTime field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUpdateTime

`func (o *Repository) SetUpdateTime(v string)`

SetUpdateTime sets UpdateTime field to given value.

### HasUpdateTime

`func (o *Repository) HasUpdateTime() bool`

HasUpdateTime returns a boolean if a field has been set.

### GetUploadEnable

`func (o *Repository) GetUploadEnable() bool`

GetUploadEnable returns the UploadEnable field if non-nil, zero value otherwise.

### GetUploadEnableOk

`func (o *Repository) GetUploadEnableOk() (*bool, bool)`

GetUploadEnableOk returns a tuple with the UploadEnable field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUploadEnable

`func (o *Repository) SetUploadEnable(v bool)`

SetUploadEnable sets UploadEnable field to given value.

### HasUploadEnable

`func (o *Repository) HasUploadEnable() bool`

HasUploadEnable returns a boolean if a field has been set.

### GetUuid

`func (o *Repository) GetUuid() string`

GetUuid returns the Uuid field if non-nil, zero value otherwise.

### GetUuidOk

`func (o *Repository) GetUuidOk() (*string, bool)`

GetUuidOk returns a tuple with the Uuid field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUuid

`func (o *Repository) SetUuid(v string)`

SetUuid sets Uuid field to given value.

### HasUuid

`func (o *Repository) HasUuid() bool`

HasUuid returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


