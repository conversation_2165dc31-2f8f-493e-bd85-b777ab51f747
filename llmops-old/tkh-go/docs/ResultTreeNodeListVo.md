# ResultTreeNodeListVo

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Code** | Pointer to **int32** |  | [optional] 
**Data** | Pointer to [**TreeNodeListVo**](TreeNodeListVo.md) |  | [optional] 
**Message** | Pointer to **string** |  | [optional] 

## Methods

### NewResultTreeNodeListVo

`func NewResultTreeNodeListVo() *ResultTreeNodeListVo`

NewResultTreeNodeListVo instantiates a new ResultTreeNodeListVo object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewResultTreeNodeListVoWithDefaults

`func NewResultTreeNodeListVoWithDefaults() *ResultTreeNodeListVo`

NewResultTreeNodeListVoWithDefaults instantiates a new ResultTreeNodeListVo object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetCode

`func (o *ResultTreeNodeListVo) GetCode() int32`

GetCode returns the Code field if non-nil, zero value otherwise.

### GetCodeOk

`func (o *ResultTreeNodeListVo) GetCodeOk() (*int32, bool)`

GetCodeOk returns a tuple with the Code field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCode

`func (o *ResultTreeNodeListVo) SetCode(v int32)`

SetCode sets Code field to given value.

### HasCode

`func (o *ResultTreeNodeListVo) HasCode() bool`

HasCode returns a boolean if a field has been set.

### GetData

`func (o *ResultTreeNodeListVo) GetData() TreeNodeListVo`

GetData returns the Data field if non-nil, zero value otherwise.

### GetDataOk

`func (o *ResultTreeNodeListVo) GetDataOk() (*TreeNodeListVo, bool)`

GetDataOk returns a tuple with the Data field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetData

`func (o *ResultTreeNodeListVo) SetData(v TreeNodeListVo)`

SetData sets Data field to given value.

### HasData

`func (o *ResultTreeNodeListVo) HasData() bool`

HasData returns a boolean if a field has been set.

### GetMessage

`func (o *ResultTreeNodeListVo) GetMessage() string`

GetMessage returns the Message field if non-nil, zero value otherwise.

### GetMessageOk

`func (o *ResultTreeNodeListVo) GetMessageOk() (*string, bool)`

GetMessageOk returns a tuple with the Message field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetMessage

`func (o *ResultTreeNodeListVo) SetMessage(v string)`

SetMessage sets Message field to given value.

### HasMessage

`func (o *ResultTreeNodeListVo) HasMessage() bool`

HasMessage returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


