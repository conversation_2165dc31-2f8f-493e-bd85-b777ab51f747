# ImageQueryRequest

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**KbIdParams** | Pointer to [**[]KnowledgeBaseDirParams**](KnowledgeBaseDirParams.md) |  | [optional] 
**Mode** | Pointer to **string** |  | [optional] 
**ReturnImage** | Pointer to **bool** |  | [optional] 
**Text** | Pointer to **string** |  | [optional] 
**TopK** | Pointer to **int32** |  | [optional] 

## Methods

### NewImageQueryRequest

`func NewImageQueryRequest() *ImageQueryRequest`

NewImageQueryRequest instantiates a new ImageQueryRequest object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewImageQueryRequestWithDefaults

`func NewImageQueryRequestWithDefaults() *ImageQueryRequest`

NewImageQueryRequestWithDefaults instantiates a new ImageQueryRequest object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetKbIdParams

`func (o *ImageQueryRequest) GetKbIdParams() []KnowledgeBaseDirParams`

GetKbIdParams returns the KbIdParams field if non-nil, zero value otherwise.

### GetKbIdParamsOk

`func (o *ImageQueryRequest) GetKbIdParamsOk() (*[]KnowledgeBaseDirParams, bool)`

GetKbIdParamsOk returns a tuple with the KbIdParams field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetKbIdParams

`func (o *ImageQueryRequest) SetKbIdParams(v []KnowledgeBaseDirParams)`

SetKbIdParams sets KbIdParams field to given value.

### HasKbIdParams

`func (o *ImageQueryRequest) HasKbIdParams() bool`

HasKbIdParams returns a boolean if a field has been set.

### GetMode

`func (o *ImageQueryRequest) GetMode() string`

GetMode returns the Mode field if non-nil, zero value otherwise.

### GetModeOk

`func (o *ImageQueryRequest) GetModeOk() (*string, bool)`

GetModeOk returns a tuple with the Mode field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetMode

`func (o *ImageQueryRequest) SetMode(v string)`

SetMode sets Mode field to given value.

### HasMode

`func (o *ImageQueryRequest) HasMode() bool`

HasMode returns a boolean if a field has been set.

### GetReturnImage

`func (o *ImageQueryRequest) GetReturnImage() bool`

GetReturnImage returns the ReturnImage field if non-nil, zero value otherwise.

### GetReturnImageOk

`func (o *ImageQueryRequest) GetReturnImageOk() (*bool, bool)`

GetReturnImageOk returns a tuple with the ReturnImage field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetReturnImage

`func (o *ImageQueryRequest) SetReturnImage(v bool)`

SetReturnImage sets ReturnImage field to given value.

### HasReturnImage

`func (o *ImageQueryRequest) HasReturnImage() bool`

HasReturnImage returns a boolean if a field has been set.

### GetText

`func (o *ImageQueryRequest) GetText() string`

GetText returns the Text field if non-nil, zero value otherwise.

### GetTextOk

`func (o *ImageQueryRequest) GetTextOk() (*string, bool)`

GetTextOk returns a tuple with the Text field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetText

`func (o *ImageQueryRequest) SetText(v string)`

SetText sets Text field to given value.

### HasText

`func (o *ImageQueryRequest) HasText() bool`

HasText returns a boolean if a field has been set.

### GetTopK

`func (o *ImageQueryRequest) GetTopK() int32`

GetTopK returns the TopK field if non-nil, zero value otherwise.

### GetTopKOk

`func (o *ImageQueryRequest) GetTopKOk() (*int32, bool)`

GetTopKOk returns a tuple with the TopK field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetTopK

`func (o *ImageQueryRequest) SetTopK(v int32)`

SetTopK sets TopK field to given value.

### HasTopK

`func (o *ImageQueryRequest) HasTopK() bool`

HasTopK returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


