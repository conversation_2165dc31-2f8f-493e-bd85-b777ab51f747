# DtoResultCaptcha

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Base64Img** | Pointer to **string** |  | [optional] 
**Key** | Pointer to **string** |  | [optional] 

## Methods

### NewDtoResultCaptcha

`func NewDtoResultCaptcha() *DtoResultCaptcha`

NewDtoResultCaptcha instantiates a new DtoResultCaptcha object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewDtoResultCaptchaWithDefaults

`func NewDtoResultCaptchaWithDefaults() *DtoResultCaptcha`

NewDtoResultCaptchaWithDefaults instantiates a new DtoResultCaptcha object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetBase64Img

`func (o *DtoResultCaptcha) GetBase64Img() string`

GetBase64Img returns the Base64Img field if non-nil, zero value otherwise.

### GetBase64ImgOk

`func (o *DtoResultCaptcha) GetBase64ImgOk() (*string, bool)`

GetBase64ImgOk returns a tuple with the Base64Img field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetBase64Img

`func (o *DtoResultCaptcha) SetBase64Img(v string)`

SetBase64Img sets Base64Img field to given value.

### HasBase64Img

`func (o *DtoResultCaptcha) HasBase64Img() bool`

HasBase64Img returns a boolean if a field has been set.

### GetKey

`func (o *DtoResultCaptcha) GetKey() string`

GetKey returns the Key field if non-nil, zero value otherwise.

### GetKeyOk

`func (o *DtoResultCaptcha) GetKeyOk() (*string, bool)`

GetKeyOk returns a tuple with the Key field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetKey

`func (o *DtoResultCaptcha) SetKey(v string)`

SetKey sets Key field to given value.

### HasKey

`func (o *DtoResultCaptcha) HasKey() bool`

HasKey returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


