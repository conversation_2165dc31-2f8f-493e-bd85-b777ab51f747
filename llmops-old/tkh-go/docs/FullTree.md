# FullTree

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Children** | Pointer to [**[]FullTree**](FullTree.md) |  | [optional] 
**Node** | Pointer to [**DocumentTree**](DocumentTree.md) |  | [optional] 

## Methods

### NewFullTree

`func NewFullTree() *FullTree`

NewFullTree instantiates a new FullTree object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewFullTreeWithDefaults

`func NewFullTreeWithDefaults() *FullTree`

NewFullTreeWithDefaults instantiates a new FullTree object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetChildren

`func (o *FullTree) GetChildren() []FullTree`

GetChildren returns the Children field if non-nil, zero value otherwise.

### GetChildrenOk

`func (o *FullTree) GetChildrenOk() (*[]FullTree, bool)`

GetChildrenOk returns a tuple with the Children field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetChildren

`func (o *FullTree) SetChildren(v []FullTree)`

SetChildren sets Children field to given value.

### HasChildren

`func (o *FullTree) HasChildren() bool`

HasChildren returns a boolean if a field has been set.

### GetNode

`func (o *FullTree) GetNode() DocumentTree`

GetNode returns the Node field if non-nil, zero value otherwise.

### GetNodeOk

`func (o *FullTree) GetNodeOk() (*DocumentTree, bool)`

GetNodeOk returns a tuple with the Node field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetNode

`func (o *FullTree) SetNode(v DocumentTree)`

SetNode sets Node field to given value.

### HasNode

`func (o *FullTree) HasNode() bool`

HasNode returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


