# KnowledgeSource

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**KbId** | Pointer to **string** | Kb Id，知识库id | [optional] 
**DocId** | Pointer to **string** | Doc Id，文档id | [optional] 
**DirId** | Pointer to **string** | Dir Id | [optional] 

## Methods

### NewKnowledgeSource

`func NewKnowledgeSource() *KnowledgeSource`

NewKnowledgeSource instantiates a new KnowledgeSource object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewKnowledgeSourceWithDefaults

`func NewKnowledgeSourceWithDefaults() *KnowledgeSource`

NewKnowledgeSourceWithDefaults instantiates a new KnowledgeSource object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetKbId

`func (o *KnowledgeSource) GetKbId() string`

GetKbId returns the KbId field if non-nil, zero value otherwise.

### GetKbIdOk

`func (o *KnowledgeSource) GetKbIdOk() (*string, bool)`

GetKbIdOk returns a tuple with the KbId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetKbId

`func (o *KnowledgeSource) SetKbId(v string)`

SetKbId sets KbId field to given value.

### HasKbId

`func (o *KnowledgeSource) HasKbId() bool`

HasKbId returns a boolean if a field has been set.

### GetDocId

`func (o *KnowledgeSource) GetDocId() string`

GetDocId returns the DocId field if non-nil, zero value otherwise.

### GetDocIdOk

`func (o *KnowledgeSource) GetDocIdOk() (*string, bool)`

GetDocIdOk returns a tuple with the DocId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDocId

`func (o *KnowledgeSource) SetDocId(v string)`

SetDocId sets DocId field to given value.

### HasDocId

`func (o *KnowledgeSource) HasDocId() bool`

HasDocId returns a boolean if a field has been set.

### GetDirId

`func (o *KnowledgeSource) GetDirId() string`

GetDirId returns the DirId field if non-nil, zero value otherwise.

### GetDirIdOk

`func (o *KnowledgeSource) GetDirIdOk() (*string, bool)`

GetDirIdOk returns a tuple with the DirId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDirId

`func (o *KnowledgeSource) SetDirId(v string)`

SetDirId sets DirId field to given value.

### HasDirId

`func (o *KnowledgeSource) HasDirId() bool`

HasDirId returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


