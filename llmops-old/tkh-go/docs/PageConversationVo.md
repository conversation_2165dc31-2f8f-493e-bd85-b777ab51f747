# PageConversationVo

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**CountId** | Pointer to **string** |  | [optional] 
**Current** | Pointer to **int64** |  | [optional] 
**MaxLimit** | Pointer to **int64** |  | [optional] 
**OptimizeCountSql** | Pointer to **bool** |  | [optional] 
**Orders** | Pointer to [**[]OrderItem**](OrderItem.md) |  | [optional] 
**Pages** | Pointer to **int64** |  | [optional] 
**Records** | Pointer to [**[]ConversationVo**](ConversationVo.md) |  | [optional] 
**SearchCount** | Pointer to **bool** |  | [optional] 
**Size** | Pointer to **int64** |  | [optional] 
**Total** | Pointer to **int64** |  | [optional] 

## Methods

### NewPageConversationVo

`func NewPageConversationVo() *PageConversationVo`

NewPageConversationVo instantiates a new PageConversationVo object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewPageConversationVoWithDefaults

`func NewPageConversationVoWithDefaults() *PageConversationVo`

NewPageConversationVoWithDefaults instantiates a new PageConversationVo object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetCountId

`func (o *PageConversationVo) GetCountId() string`

GetCountId returns the CountId field if non-nil, zero value otherwise.

### GetCountIdOk

`func (o *PageConversationVo) GetCountIdOk() (*string, bool)`

GetCountIdOk returns a tuple with the CountId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCountId

`func (o *PageConversationVo) SetCountId(v string)`

SetCountId sets CountId field to given value.

### HasCountId

`func (o *PageConversationVo) HasCountId() bool`

HasCountId returns a boolean if a field has been set.

### GetCurrent

`func (o *PageConversationVo) GetCurrent() int64`

GetCurrent returns the Current field if non-nil, zero value otherwise.

### GetCurrentOk

`func (o *PageConversationVo) GetCurrentOk() (*int64, bool)`

GetCurrentOk returns a tuple with the Current field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCurrent

`func (o *PageConversationVo) SetCurrent(v int64)`

SetCurrent sets Current field to given value.

### HasCurrent

`func (o *PageConversationVo) HasCurrent() bool`

HasCurrent returns a boolean if a field has been set.

### GetMaxLimit

`func (o *PageConversationVo) GetMaxLimit() int64`

GetMaxLimit returns the MaxLimit field if non-nil, zero value otherwise.

### GetMaxLimitOk

`func (o *PageConversationVo) GetMaxLimitOk() (*int64, bool)`

GetMaxLimitOk returns a tuple with the MaxLimit field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetMaxLimit

`func (o *PageConversationVo) SetMaxLimit(v int64)`

SetMaxLimit sets MaxLimit field to given value.

### HasMaxLimit

`func (o *PageConversationVo) HasMaxLimit() bool`

HasMaxLimit returns a boolean if a field has been set.

### GetOptimizeCountSql

`func (o *PageConversationVo) GetOptimizeCountSql() bool`

GetOptimizeCountSql returns the OptimizeCountSql field if non-nil, zero value otherwise.

### GetOptimizeCountSqlOk

`func (o *PageConversationVo) GetOptimizeCountSqlOk() (*bool, bool)`

GetOptimizeCountSqlOk returns a tuple with the OptimizeCountSql field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetOptimizeCountSql

`func (o *PageConversationVo) SetOptimizeCountSql(v bool)`

SetOptimizeCountSql sets OptimizeCountSql field to given value.

### HasOptimizeCountSql

`func (o *PageConversationVo) HasOptimizeCountSql() bool`

HasOptimizeCountSql returns a boolean if a field has been set.

### GetOrders

`func (o *PageConversationVo) GetOrders() []OrderItem`

GetOrders returns the Orders field if non-nil, zero value otherwise.

### GetOrdersOk

`func (o *PageConversationVo) GetOrdersOk() (*[]OrderItem, bool)`

GetOrdersOk returns a tuple with the Orders field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetOrders

`func (o *PageConversationVo) SetOrders(v []OrderItem)`

SetOrders sets Orders field to given value.

### HasOrders

`func (o *PageConversationVo) HasOrders() bool`

HasOrders returns a boolean if a field has been set.

### GetPages

`func (o *PageConversationVo) GetPages() int64`

GetPages returns the Pages field if non-nil, zero value otherwise.

### GetPagesOk

`func (o *PageConversationVo) GetPagesOk() (*int64, bool)`

GetPagesOk returns a tuple with the Pages field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetPages

`func (o *PageConversationVo) SetPages(v int64)`

SetPages sets Pages field to given value.

### HasPages

`func (o *PageConversationVo) HasPages() bool`

HasPages returns a boolean if a field has been set.

### GetRecords

`func (o *PageConversationVo) GetRecords() []ConversationVo`

GetRecords returns the Records field if non-nil, zero value otherwise.

### GetRecordsOk

`func (o *PageConversationVo) GetRecordsOk() (*[]ConversationVo, bool)`

GetRecordsOk returns a tuple with the Records field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetRecords

`func (o *PageConversationVo) SetRecords(v []ConversationVo)`

SetRecords sets Records field to given value.

### HasRecords

`func (o *PageConversationVo) HasRecords() bool`

HasRecords returns a boolean if a field has been set.

### GetSearchCount

`func (o *PageConversationVo) GetSearchCount() bool`

GetSearchCount returns the SearchCount field if non-nil, zero value otherwise.

### GetSearchCountOk

`func (o *PageConversationVo) GetSearchCountOk() (*bool, bool)`

GetSearchCountOk returns a tuple with the SearchCount field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetSearchCount

`func (o *PageConversationVo) SetSearchCount(v bool)`

SetSearchCount sets SearchCount field to given value.

### HasSearchCount

`func (o *PageConversationVo) HasSearchCount() bool`

HasSearchCount returns a boolean if a field has been set.

### GetSize

`func (o *PageConversationVo) GetSize() int64`

GetSize returns the Size field if non-nil, zero value otherwise.

### GetSizeOk

`func (o *PageConversationVo) GetSizeOk() (*int64, bool)`

GetSizeOk returns a tuple with the Size field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetSize

`func (o *PageConversationVo) SetSize(v int64)`

SetSize sets Size field to given value.

### HasSize

`func (o *PageConversationVo) HasSize() bool`

HasSize returns a boolean if a field has been set.

### GetTotal

`func (o *PageConversationVo) GetTotal() int64`

GetTotal returns the Total field if non-nil, zero value otherwise.

### GetTotalOk

`func (o *PageConversationVo) GetTotalOk() (*int64, bool)`

GetTotalOk returns a tuple with the Total field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetTotal

`func (o *PageConversationVo) SetTotal(v int64)`

SetTotal sets Total field to given value.

### HasTotal

`func (o *PageConversationVo) HasTotal() bool`

HasTotal returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


