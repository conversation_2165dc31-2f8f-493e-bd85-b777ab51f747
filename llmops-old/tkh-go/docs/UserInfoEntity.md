# UserInfoEntity

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**CreateTime** | Pointer to [**time.Time**](time.Time.md) | 创建时间 | [optional] 
**CreateUser** | Pointer to **string** | 创建人 | [optional] 
**DisplayWorkspace** | Pointer to **string** | 显示工作区 | [optional] 
**Email** | Pointer to **string** |  | [optional] 
**Id** | Pointer to **string** |  | [optional] 
**IsAdmin** | Pointer to **bool** | 是否系统管理员 | [optional] 
**IsWorkspaceAdmin** | Pointer to **bool** | 是否工作区管理员 | [optional] 
**Locale** | Pointer to **string** | 语言环境 | [optional] 
**OrganId** | Pointer to **string** | 用户机构 | [optional] 
**OrganName** | Pointer to **string** | 用户所属机构名称，前端展示需要 | [optional] 
**OwnGroupNames** | Pointer to **[]string** | 所属工作组 | [optional] 
**OwnWorkspaceIds** | Pointer to **[]string** | 管理的工作区列表 | [optional] 
**Password** | Pointer to **string** |  | [optional] 
**RealName** | Pointer to **string** |  | [optional] 
**RoleIds** | Pointer to **[]string** | 用户角色列表，或组内角色 | [optional] 
**RoleInfoMap** | Pointer to **map[string]string** | 用户所属角色ID和角色中文名对应关系Map，前端展示需要 | [optional] 
**Status** | Pointer to **string** | 是否启用 | [optional] 
**Telephone** | Pointer to **string** |  | [optional] 
**UpdateTime** | Pointer to [**time.Time**](time.Time.md) | 更新时间 | [optional] 
**UpdateUser** | Pointer to **string** | 修改人 | [optional] 
**Username** | Pointer to **string** |  | [optional] 
**WorkspaceName** | Pointer to **string** | 工作区名称 | [optional] 
**WorkspaceRoleIds** | Pointer to [**map[string][]string**](array.md) | 工作区角色 | [optional] 

## Methods

### NewUserInfoEntity

`func NewUserInfoEntity() *UserInfoEntity`

NewUserInfoEntity instantiates a new UserInfoEntity object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewUserInfoEntityWithDefaults

`func NewUserInfoEntityWithDefaults() *UserInfoEntity`

NewUserInfoEntityWithDefaults instantiates a new UserInfoEntity object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetCreateTime

`func (o *UserInfoEntity) GetCreateTime() time.Time`

GetCreateTime returns the CreateTime field if non-nil, zero value otherwise.

### GetCreateTimeOk

`func (o *UserInfoEntity) GetCreateTimeOk() (*time.Time, bool)`

GetCreateTimeOk returns a tuple with the CreateTime field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCreateTime

`func (o *UserInfoEntity) SetCreateTime(v time.Time)`

SetCreateTime sets CreateTime field to given value.

### HasCreateTime

`func (o *UserInfoEntity) HasCreateTime() bool`

HasCreateTime returns a boolean if a field has been set.

### GetCreateUser

`func (o *UserInfoEntity) GetCreateUser() string`

GetCreateUser returns the CreateUser field if non-nil, zero value otherwise.

### GetCreateUserOk

`func (o *UserInfoEntity) GetCreateUserOk() (*string, bool)`

GetCreateUserOk returns a tuple with the CreateUser field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCreateUser

`func (o *UserInfoEntity) SetCreateUser(v string)`

SetCreateUser sets CreateUser field to given value.

### HasCreateUser

`func (o *UserInfoEntity) HasCreateUser() bool`

HasCreateUser returns a boolean if a field has been set.

### GetDisplayWorkspace

`func (o *UserInfoEntity) GetDisplayWorkspace() string`

GetDisplayWorkspace returns the DisplayWorkspace field if non-nil, zero value otherwise.

### GetDisplayWorkspaceOk

`func (o *UserInfoEntity) GetDisplayWorkspaceOk() (*string, bool)`

GetDisplayWorkspaceOk returns a tuple with the DisplayWorkspace field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDisplayWorkspace

`func (o *UserInfoEntity) SetDisplayWorkspace(v string)`

SetDisplayWorkspace sets DisplayWorkspace field to given value.

### HasDisplayWorkspace

`func (o *UserInfoEntity) HasDisplayWorkspace() bool`

HasDisplayWorkspace returns a boolean if a field has been set.

### GetEmail

`func (o *UserInfoEntity) GetEmail() string`

GetEmail returns the Email field if non-nil, zero value otherwise.

### GetEmailOk

`func (o *UserInfoEntity) GetEmailOk() (*string, bool)`

GetEmailOk returns a tuple with the Email field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetEmail

`func (o *UserInfoEntity) SetEmail(v string)`

SetEmail sets Email field to given value.

### HasEmail

`func (o *UserInfoEntity) HasEmail() bool`

HasEmail returns a boolean if a field has been set.

### GetId

`func (o *UserInfoEntity) GetId() string`

GetId returns the Id field if non-nil, zero value otherwise.

### GetIdOk

`func (o *UserInfoEntity) GetIdOk() (*string, bool)`

GetIdOk returns a tuple with the Id field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetId

`func (o *UserInfoEntity) SetId(v string)`

SetId sets Id field to given value.

### HasId

`func (o *UserInfoEntity) HasId() bool`

HasId returns a boolean if a field has been set.

### GetIsAdmin

`func (o *UserInfoEntity) GetIsAdmin() bool`

GetIsAdmin returns the IsAdmin field if non-nil, zero value otherwise.

### GetIsAdminOk

`func (o *UserInfoEntity) GetIsAdminOk() (*bool, bool)`

GetIsAdminOk returns a tuple with the IsAdmin field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetIsAdmin

`func (o *UserInfoEntity) SetIsAdmin(v bool)`

SetIsAdmin sets IsAdmin field to given value.

### HasIsAdmin

`func (o *UserInfoEntity) HasIsAdmin() bool`

HasIsAdmin returns a boolean if a field has been set.

### GetIsWorkspaceAdmin

`func (o *UserInfoEntity) GetIsWorkspaceAdmin() bool`

GetIsWorkspaceAdmin returns the IsWorkspaceAdmin field if non-nil, zero value otherwise.

### GetIsWorkspaceAdminOk

`func (o *UserInfoEntity) GetIsWorkspaceAdminOk() (*bool, bool)`

GetIsWorkspaceAdminOk returns a tuple with the IsWorkspaceAdmin field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetIsWorkspaceAdmin

`func (o *UserInfoEntity) SetIsWorkspaceAdmin(v bool)`

SetIsWorkspaceAdmin sets IsWorkspaceAdmin field to given value.

### HasIsWorkspaceAdmin

`func (o *UserInfoEntity) HasIsWorkspaceAdmin() bool`

HasIsWorkspaceAdmin returns a boolean if a field has been set.

### GetLocale

`func (o *UserInfoEntity) GetLocale() string`

GetLocale returns the Locale field if non-nil, zero value otherwise.

### GetLocaleOk

`func (o *UserInfoEntity) GetLocaleOk() (*string, bool)`

GetLocaleOk returns a tuple with the Locale field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetLocale

`func (o *UserInfoEntity) SetLocale(v string)`

SetLocale sets Locale field to given value.

### HasLocale

`func (o *UserInfoEntity) HasLocale() bool`

HasLocale returns a boolean if a field has been set.

### GetOrganId

`func (o *UserInfoEntity) GetOrganId() string`

GetOrganId returns the OrganId field if non-nil, zero value otherwise.

### GetOrganIdOk

`func (o *UserInfoEntity) GetOrganIdOk() (*string, bool)`

GetOrganIdOk returns a tuple with the OrganId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetOrganId

`func (o *UserInfoEntity) SetOrganId(v string)`

SetOrganId sets OrganId field to given value.

### HasOrganId

`func (o *UserInfoEntity) HasOrganId() bool`

HasOrganId returns a boolean if a field has been set.

### GetOrganName

`func (o *UserInfoEntity) GetOrganName() string`

GetOrganName returns the OrganName field if non-nil, zero value otherwise.

### GetOrganNameOk

`func (o *UserInfoEntity) GetOrganNameOk() (*string, bool)`

GetOrganNameOk returns a tuple with the OrganName field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetOrganName

`func (o *UserInfoEntity) SetOrganName(v string)`

SetOrganName sets OrganName field to given value.

### HasOrganName

`func (o *UserInfoEntity) HasOrganName() bool`

HasOrganName returns a boolean if a field has been set.

### GetOwnGroupNames

`func (o *UserInfoEntity) GetOwnGroupNames() []string`

GetOwnGroupNames returns the OwnGroupNames field if non-nil, zero value otherwise.

### GetOwnGroupNamesOk

`func (o *UserInfoEntity) GetOwnGroupNamesOk() (*[]string, bool)`

GetOwnGroupNamesOk returns a tuple with the OwnGroupNames field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetOwnGroupNames

`func (o *UserInfoEntity) SetOwnGroupNames(v []string)`

SetOwnGroupNames sets OwnGroupNames field to given value.

### HasOwnGroupNames

`func (o *UserInfoEntity) HasOwnGroupNames() bool`

HasOwnGroupNames returns a boolean if a field has been set.

### GetOwnWorkspaceIds

`func (o *UserInfoEntity) GetOwnWorkspaceIds() []string`

GetOwnWorkspaceIds returns the OwnWorkspaceIds field if non-nil, zero value otherwise.

### GetOwnWorkspaceIdsOk

`func (o *UserInfoEntity) GetOwnWorkspaceIdsOk() (*[]string, bool)`

GetOwnWorkspaceIdsOk returns a tuple with the OwnWorkspaceIds field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetOwnWorkspaceIds

`func (o *UserInfoEntity) SetOwnWorkspaceIds(v []string)`

SetOwnWorkspaceIds sets OwnWorkspaceIds field to given value.

### HasOwnWorkspaceIds

`func (o *UserInfoEntity) HasOwnWorkspaceIds() bool`

HasOwnWorkspaceIds returns a boolean if a field has been set.

### GetPassword

`func (o *UserInfoEntity) GetPassword() string`

GetPassword returns the Password field if non-nil, zero value otherwise.

### GetPasswordOk

`func (o *UserInfoEntity) GetPasswordOk() (*string, bool)`

GetPasswordOk returns a tuple with the Password field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetPassword

`func (o *UserInfoEntity) SetPassword(v string)`

SetPassword sets Password field to given value.

### HasPassword

`func (o *UserInfoEntity) HasPassword() bool`

HasPassword returns a boolean if a field has been set.

### GetRealName

`func (o *UserInfoEntity) GetRealName() string`

GetRealName returns the RealName field if non-nil, zero value otherwise.

### GetRealNameOk

`func (o *UserInfoEntity) GetRealNameOk() (*string, bool)`

GetRealNameOk returns a tuple with the RealName field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetRealName

`func (o *UserInfoEntity) SetRealName(v string)`

SetRealName sets RealName field to given value.

### HasRealName

`func (o *UserInfoEntity) HasRealName() bool`

HasRealName returns a boolean if a field has been set.

### GetRoleIds

`func (o *UserInfoEntity) GetRoleIds() []string`

GetRoleIds returns the RoleIds field if non-nil, zero value otherwise.

### GetRoleIdsOk

`func (o *UserInfoEntity) GetRoleIdsOk() (*[]string, bool)`

GetRoleIdsOk returns a tuple with the RoleIds field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetRoleIds

`func (o *UserInfoEntity) SetRoleIds(v []string)`

SetRoleIds sets RoleIds field to given value.

### HasRoleIds

`func (o *UserInfoEntity) HasRoleIds() bool`

HasRoleIds returns a boolean if a field has been set.

### GetRoleInfoMap

`func (o *UserInfoEntity) GetRoleInfoMap() map[string]string`

GetRoleInfoMap returns the RoleInfoMap field if non-nil, zero value otherwise.

### GetRoleInfoMapOk

`func (o *UserInfoEntity) GetRoleInfoMapOk() (*map[string]string, bool)`

GetRoleInfoMapOk returns a tuple with the RoleInfoMap field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetRoleInfoMap

`func (o *UserInfoEntity) SetRoleInfoMap(v map[string]string)`

SetRoleInfoMap sets RoleInfoMap field to given value.

### HasRoleInfoMap

`func (o *UserInfoEntity) HasRoleInfoMap() bool`

HasRoleInfoMap returns a boolean if a field has been set.

### GetStatus

`func (o *UserInfoEntity) GetStatus() string`

GetStatus returns the Status field if non-nil, zero value otherwise.

### GetStatusOk

`func (o *UserInfoEntity) GetStatusOk() (*string, bool)`

GetStatusOk returns a tuple with the Status field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetStatus

`func (o *UserInfoEntity) SetStatus(v string)`

SetStatus sets Status field to given value.

### HasStatus

`func (o *UserInfoEntity) HasStatus() bool`

HasStatus returns a boolean if a field has been set.

### GetTelephone

`func (o *UserInfoEntity) GetTelephone() string`

GetTelephone returns the Telephone field if non-nil, zero value otherwise.

### GetTelephoneOk

`func (o *UserInfoEntity) GetTelephoneOk() (*string, bool)`

GetTelephoneOk returns a tuple with the Telephone field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetTelephone

`func (o *UserInfoEntity) SetTelephone(v string)`

SetTelephone sets Telephone field to given value.

### HasTelephone

`func (o *UserInfoEntity) HasTelephone() bool`

HasTelephone returns a boolean if a field has been set.

### GetUpdateTime

`func (o *UserInfoEntity) GetUpdateTime() time.Time`

GetUpdateTime returns the UpdateTime field if non-nil, zero value otherwise.

### GetUpdateTimeOk

`func (o *UserInfoEntity) GetUpdateTimeOk() (*time.Time, bool)`

GetUpdateTimeOk returns a tuple with the UpdateTime field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUpdateTime

`func (o *UserInfoEntity) SetUpdateTime(v time.Time)`

SetUpdateTime sets UpdateTime field to given value.

### HasUpdateTime

`func (o *UserInfoEntity) HasUpdateTime() bool`

HasUpdateTime returns a boolean if a field has been set.

### GetUpdateUser

`func (o *UserInfoEntity) GetUpdateUser() string`

GetUpdateUser returns the UpdateUser field if non-nil, zero value otherwise.

### GetUpdateUserOk

`func (o *UserInfoEntity) GetUpdateUserOk() (*string, bool)`

GetUpdateUserOk returns a tuple with the UpdateUser field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUpdateUser

`func (o *UserInfoEntity) SetUpdateUser(v string)`

SetUpdateUser sets UpdateUser field to given value.

### HasUpdateUser

`func (o *UserInfoEntity) HasUpdateUser() bool`

HasUpdateUser returns a boolean if a field has been set.

### GetUsername

`func (o *UserInfoEntity) GetUsername() string`

GetUsername returns the Username field if non-nil, zero value otherwise.

### GetUsernameOk

`func (o *UserInfoEntity) GetUsernameOk() (*string, bool)`

GetUsernameOk returns a tuple with the Username field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUsername

`func (o *UserInfoEntity) SetUsername(v string)`

SetUsername sets Username field to given value.

### HasUsername

`func (o *UserInfoEntity) HasUsername() bool`

HasUsername returns a boolean if a field has been set.

### GetWorkspaceName

`func (o *UserInfoEntity) GetWorkspaceName() string`

GetWorkspaceName returns the WorkspaceName field if non-nil, zero value otherwise.

### GetWorkspaceNameOk

`func (o *UserInfoEntity) GetWorkspaceNameOk() (*string, bool)`

GetWorkspaceNameOk returns a tuple with the WorkspaceName field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetWorkspaceName

`func (o *UserInfoEntity) SetWorkspaceName(v string)`

SetWorkspaceName sets WorkspaceName field to given value.

### HasWorkspaceName

`func (o *UserInfoEntity) HasWorkspaceName() bool`

HasWorkspaceName returns a boolean if a field has been set.

### GetWorkspaceRoleIds

`func (o *UserInfoEntity) GetWorkspaceRoleIds() map[string][]string`

GetWorkspaceRoleIds returns the WorkspaceRoleIds field if non-nil, zero value otherwise.

### GetWorkspaceRoleIdsOk

`func (o *UserInfoEntity) GetWorkspaceRoleIdsOk() (*map[string][]string, bool)`

GetWorkspaceRoleIdsOk returns a tuple with the WorkspaceRoleIds field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetWorkspaceRoleIds

`func (o *UserInfoEntity) SetWorkspaceRoleIds(v map[string][]string)`

SetWorkspaceRoleIds sets WorkspaceRoleIds field to given value.

### HasWorkspaceRoleIds

`func (o *UserInfoEntity) HasWorkspaceRoleIds() bool`

HasWorkspaceRoleIds returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


