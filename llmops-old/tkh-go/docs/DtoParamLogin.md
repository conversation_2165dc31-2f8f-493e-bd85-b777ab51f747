# DtoParamLogin

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Code** | Pointer to **string** |  | [optional] 
**Key** | Pointer to **string** |  | [optional] 
**Password** | Pointer to **string** |  | [optional] 
**Username** | Pointer to **string** |  | [optional] 

## Methods

### NewDtoParamLogin

`func NewDtoParamLogin() *DtoParamLogin`

NewDtoParamLogin instantiates a new DtoParamLogin object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewDtoParamLoginWithDefaults

`func NewDtoParamLoginWithDefaults() *DtoParamLogin`

NewDtoParamLoginWithDefaults instantiates a new DtoParamLogin object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetCode

`func (o *DtoParamLogin) GetCode() string`

GetCode returns the Code field if non-nil, zero value otherwise.

### GetCodeOk

`func (o *DtoParamLogin) GetCodeOk() (*string, bool)`

GetCodeOk returns a tuple with the Code field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCode

`func (o *DtoParamLogin) SetCode(v string)`

SetCode sets Code field to given value.

### HasCode

`func (o *DtoParamLogin) HasCode() bool`

HasCode returns a boolean if a field has been set.

### GetKey

`func (o *DtoParamLogin) GetKey() string`

GetKey returns the Key field if non-nil, zero value otherwise.

### GetKeyOk

`func (o *DtoParamLogin) GetKeyOk() (*string, bool)`

GetKeyOk returns a tuple with the Key field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetKey

`func (o *DtoParamLogin) SetKey(v string)`

SetKey sets Key field to given value.

### HasKey

`func (o *DtoParamLogin) HasKey() bool`

HasKey returns a boolean if a field has been set.

### GetPassword

`func (o *DtoParamLogin) GetPassword() string`

GetPassword returns the Password field if non-nil, zero value otherwise.

### GetPasswordOk

`func (o *DtoParamLogin) GetPasswordOk() (*string, bool)`

GetPasswordOk returns a tuple with the Password field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetPassword

`func (o *DtoParamLogin) SetPassword(v string)`

SetPassword sets Password field to given value.

### HasPassword

`func (o *DtoParamLogin) HasPassword() bool`

HasPassword returns a boolean if a field has been set.

### GetUsername

`func (o *DtoParamLogin) GetUsername() string`

GetUsername returns the Username field if non-nil, zero value otherwise.

### GetUsernameOk

`func (o *DtoParamLogin) GetUsernameOk() (*string, bool)`

GetUsernameOk returns a tuple with the Username field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUsername

`func (o *DtoParamLogin) SetUsername(v string)`

SetUsername sets Username field to given value.

### HasUsername

`func (o *DtoParamLogin) HasUsername() bool`

HasUsername returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


