# StockTrendRequest

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**DateCode** | Pointer to **int32** |  | [optional] 
**StockCode** | Pointer to **string** |  | [optional] 

## Methods

### NewStockTrendRequest

`func NewStockTrendRequest() *StockTrendRequest`

NewStockTrendRequest instantiates a new StockTrendRequest object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewStockTrendRequestWithDefaults

`func NewStockTrendRequestWithDefaults() *StockTrendRequest`

NewStockTrendRequestWithDefaults instantiates a new StockTrendRequest object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetDateCode

`func (o *StockTrendRequest) GetDateCode() int32`

GetDateCode returns the DateCode field if non-nil, zero value otherwise.

### GetDateCodeOk

`func (o *StockTrendRequest) GetDateCodeOk() (*int32, bool)`

GetDateCodeOk returns a tuple with the DateCode field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDateCode

`func (o *StockTrendRequest) SetDateCode(v int32)`

SetDateCode sets DateCode field to given value.

### HasDateCode

`func (o *StockTrendRequest) HasDateCode() bool`

HasDateCode returns a boolean if a field has been set.

### GetStockCode

`func (o *StockTrendRequest) GetStockCode() string`

GetStockCode returns the StockCode field if non-nil, zero value otherwise.

### GetStockCodeOk

`func (o *StockTrendRequest) GetStockCodeOk() (*string, bool)`

GetStockCodeOk returns a tuple with the StockCode field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetStockCode

`func (o *StockTrendRequest) SetStockCode(v string)`

SetStockCode sets StockCode field to given value.

### HasStockCode

`func (o *StockTrendRequest) HasStockCode() bool`

HasStockCode returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


