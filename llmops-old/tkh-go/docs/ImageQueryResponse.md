# ImageQueryResponse

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**ImageList** | Pointer to [**[]ImageDetail**](ImageDetail.md) |  | [optional] 
**Model** | Pointer to **string** |  | [optional] 
**SearchTime** | Pointer to **string** |  | [optional] 
**Size** | Pointer to **int32** |  | [optional] 

## Methods

### NewImageQueryResponse

`func NewImageQueryResponse() *ImageQueryResponse`

NewImageQueryResponse instantiates a new ImageQueryResponse object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewImageQueryResponseWithDefaults

`func NewImageQueryResponseWithDefaults() *ImageQueryResponse`

NewImageQueryResponseWithDefaults instantiates a new ImageQueryResponse object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetImageList

`func (o *ImageQueryResponse) GetImageList() []ImageDetail`

GetImageList returns the ImageList field if non-nil, zero value otherwise.

### GetImageListOk

`func (o *ImageQueryResponse) GetImageListOk() (*[]ImageDetail, bool)`

GetImageListOk returns a tuple with the ImageList field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetImageList

`func (o *ImageQueryResponse) SetImageList(v []ImageDetail)`

SetImageList sets ImageList field to given value.

### HasImageList

`func (o *ImageQueryResponse) HasImageList() bool`

HasImageList returns a boolean if a field has been set.

### GetModel

`func (o *ImageQueryResponse) GetModel() string`

GetModel returns the Model field if non-nil, zero value otherwise.

### GetModelOk

`func (o *ImageQueryResponse) GetModelOk() (*string, bool)`

GetModelOk returns a tuple with the Model field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetModel

`func (o *ImageQueryResponse) SetModel(v string)`

SetModel sets Model field to given value.

### HasModel

`func (o *ImageQueryResponse) HasModel() bool`

HasModel returns a boolean if a field has been set.

### GetSearchTime

`func (o *ImageQueryResponse) GetSearchTime() string`

GetSearchTime returns the SearchTime field if non-nil, zero value otherwise.

### GetSearchTimeOk

`func (o *ImageQueryResponse) GetSearchTimeOk() (*string, bool)`

GetSearchTimeOk returns a tuple with the SearchTime field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetSearchTime

`func (o *ImageQueryResponse) SetSearchTime(v string)`

SetSearchTime sets SearchTime field to given value.

### HasSearchTime

`func (o *ImageQueryResponse) HasSearchTime() bool`

HasSearchTime returns a boolean if a field has been set.

### GetSize

`func (o *ImageQueryResponse) GetSize() int32`

GetSize returns the Size field if non-nil, zero value otherwise.

### GetSizeOk

`func (o *ImageQueryResponse) GetSizeOk() (*int32, bool)`

GetSizeOk returns a tuple with the Size field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetSize

`func (o *ImageQueryResponse) SetSize(v int32)`

SetSize sets Size field to given value.

### HasSize

`func (o *ImageQueryResponse) HasSize() bool`

HasSize returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


