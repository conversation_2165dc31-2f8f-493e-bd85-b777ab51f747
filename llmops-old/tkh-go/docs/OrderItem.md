# OrderItem

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Asc** | Pointer to **bool** |  | [optional] 
**Column** | Pointer to **string** |  | [optional] 

## Methods

### NewOrderItem

`func NewOrderItem() *OrderItem`

NewOrderItem instantiates a new OrderItem object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewOrderItemWithDefaults

`func NewOrderItemWithDefaults() *OrderItem`

NewOrderItemWithDefaults instantiates a new OrderItem object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetAsc

`func (o *OrderItem) GetAsc() bool`

GetAsc returns the Asc field if non-nil, zero value otherwise.

### GetAscOk

`func (o *OrderItem) GetAscOk() (*bool, bool)`

GetAscOk returns a tuple with the Asc field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetAsc

`func (o *OrderItem) SetAsc(v bool)`

SetAsc sets Asc field to given value.

### HasAsc

`func (o *OrderItem) HasAsc() bool`

HasAsc returns a boolean if a field has been set.

### GetColumn

`func (o *OrderItem) GetColumn() string`

GetColumn returns the Column field if non-nil, zero value otherwise.

### GetColumnOk

`func (o *OrderItem) GetColumnOk() (*string, bool)`

GetColumnOk returns a tuple with the Column field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetColumn

`func (o *OrderItem) SetColumn(v string)`

SetColumn sets Column field to given value.

### HasColumn

`func (o *OrderItem) HasColumn() bool`

HasColumn returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


