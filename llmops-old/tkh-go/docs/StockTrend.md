# StockTrend

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Close** | Pointer to **float64** |  | [optional] 
**High** | Pointer to **float64** |  | [optional] 
**Low** | Pointer to **float64** |  | [optional] 
**Open** | Pointer to **float64** |  | [optional] 
**TradeDate** | Pointer to **string** |  | [optional] 
**UpAndDown** | Pointer to **int32** |  | [optional] 
**Volume** | Pointer to **float64** |  | [optional] 

## Methods

### NewStockTrend

`func NewStockTrend() *StockTrend`

NewStockTrend instantiates a new StockTrend object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewStockTrendWithDefaults

`func NewStockTrendWithDefaults() *StockTrend`

NewStockTrendWithDefaults instantiates a new StockTrend object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetClose

`func (o *StockTrend) GetClose() float64`

GetClose returns the Close field if non-nil, zero value otherwise.

### GetCloseOk

`func (o *StockTrend) GetCloseOk() (*float64, bool)`

GetCloseOk returns a tuple with the Close field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetClose

`func (o *StockTrend) SetClose(v float64)`

SetClose sets Close field to given value.

### HasClose

`func (o *StockTrend) HasClose() bool`

HasClose returns a boolean if a field has been set.

### GetHigh

`func (o *StockTrend) GetHigh() float64`

GetHigh returns the High field if non-nil, zero value otherwise.

### GetHighOk

`func (o *StockTrend) GetHighOk() (*float64, bool)`

GetHighOk returns a tuple with the High field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetHigh

`func (o *StockTrend) SetHigh(v float64)`

SetHigh sets High field to given value.

### HasHigh

`func (o *StockTrend) HasHigh() bool`

HasHigh returns a boolean if a field has been set.

### GetLow

`func (o *StockTrend) GetLow() float64`

GetLow returns the Low field if non-nil, zero value otherwise.

### GetLowOk

`func (o *StockTrend) GetLowOk() (*float64, bool)`

GetLowOk returns a tuple with the Low field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetLow

`func (o *StockTrend) SetLow(v float64)`

SetLow sets Low field to given value.

### HasLow

`func (o *StockTrend) HasLow() bool`

HasLow returns a boolean if a field has been set.

### GetOpen

`func (o *StockTrend) GetOpen() float64`

GetOpen returns the Open field if non-nil, zero value otherwise.

### GetOpenOk

`func (o *StockTrend) GetOpenOk() (*float64, bool)`

GetOpenOk returns a tuple with the Open field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetOpen

`func (o *StockTrend) SetOpen(v float64)`

SetOpen sets Open field to given value.

### HasOpen

`func (o *StockTrend) HasOpen() bool`

HasOpen returns a boolean if a field has been set.

### GetTradeDate

`func (o *StockTrend) GetTradeDate() string`

GetTradeDate returns the TradeDate field if non-nil, zero value otherwise.

### GetTradeDateOk

`func (o *StockTrend) GetTradeDateOk() (*string, bool)`

GetTradeDateOk returns a tuple with the TradeDate field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetTradeDate

`func (o *StockTrend) SetTradeDate(v string)`

SetTradeDate sets TradeDate field to given value.

### HasTradeDate

`func (o *StockTrend) HasTradeDate() bool`

HasTradeDate returns a boolean if a field has been set.

### GetUpAndDown

`func (o *StockTrend) GetUpAndDown() int32`

GetUpAndDown returns the UpAndDown field if non-nil, zero value otherwise.

### GetUpAndDownOk

`func (o *StockTrend) GetUpAndDownOk() (*int32, bool)`

GetUpAndDownOk returns a tuple with the UpAndDown field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUpAndDown

`func (o *StockTrend) SetUpAndDown(v int32)`

SetUpAndDown sets UpAndDown field to given value.

### HasUpAndDown

`func (o *StockTrend) HasUpAndDown() bool`

HasUpAndDown returns a boolean if a field has been set.

### GetVolume

`func (o *StockTrend) GetVolume() float64`

GetVolume returns the Volume field if non-nil, zero value otherwise.

### GetVolumeOk

`func (o *StockTrend) GetVolumeOk() (*float64, bool)`

GetVolumeOk returns a tuple with the Volume field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetVolume

`func (o *StockTrend) SetVolume(v float64)`

SetVolume sets Volume field to given value.

### HasVolume

`func (o *StockTrend) HasVolume() bool`

HasVolume returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


