# AddRepoRequest

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Description** | Pointer to **string** | 知识库描述 | [optional] 
**Name** | Pointer to **string** | 知识库名称 | [optional] 
**Password** | Pointer to **string** | 密码 | [optional] 
**RepositoryType** | Pointer to **int32** | 知识库类型 枚举 1 &#x3D; 文本  2 &#x3D; sdb | [optional] 
**SdbConfigureType** | Pointer to **int32** | 类型 1&#x3D; tks内部服务 2&#x3D;本地直连 图谱相关字段仅类型为sdb有效 | [optional] 
**TableName** | Pointer to **string** | 表名 | [optional] 
**Url** | Pointer to **string** | URL | [optional] 
**Username** | Pointer to **string** | 用户名 | [optional] 

## Methods

### NewAddRepoRequest

`func NewAddRepoRequest() *AddRepoRequest`

NewAddRepoRequest instantiates a new AddRepoRequest object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewAddRepoRequestWithDefaults

`func NewAddRepoRequestWithDefaults() *AddRepoRequest`

NewAddRepoRequestWithDefaults instantiates a new AddRepoRequest object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetDescription

`func (o *AddRepoRequest) GetDescription() string`

GetDescription returns the Description field if non-nil, zero value otherwise.

### GetDescriptionOk

`func (o *AddRepoRequest) GetDescriptionOk() (*string, bool)`

GetDescriptionOk returns a tuple with the Description field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDescription

`func (o *AddRepoRequest) SetDescription(v string)`

SetDescription sets Description field to given value.

### HasDescription

`func (o *AddRepoRequest) HasDescription() bool`

HasDescription returns a boolean if a field has been set.

### GetName

`func (o *AddRepoRequest) GetName() string`

GetName returns the Name field if non-nil, zero value otherwise.

### GetNameOk

`func (o *AddRepoRequest) GetNameOk() (*string, bool)`

GetNameOk returns a tuple with the Name field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetName

`func (o *AddRepoRequest) SetName(v string)`

SetName sets Name field to given value.

### HasName

`func (o *AddRepoRequest) HasName() bool`

HasName returns a boolean if a field has been set.

### GetPassword

`func (o *AddRepoRequest) GetPassword() string`

GetPassword returns the Password field if non-nil, zero value otherwise.

### GetPasswordOk

`func (o *AddRepoRequest) GetPasswordOk() (*string, bool)`

GetPasswordOk returns a tuple with the Password field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetPassword

`func (o *AddRepoRequest) SetPassword(v string)`

SetPassword sets Password field to given value.

### HasPassword

`func (o *AddRepoRequest) HasPassword() bool`

HasPassword returns a boolean if a field has been set.

### GetRepositoryType

`func (o *AddRepoRequest) GetRepositoryType() int32`

GetRepositoryType returns the RepositoryType field if non-nil, zero value otherwise.

### GetRepositoryTypeOk

`func (o *AddRepoRequest) GetRepositoryTypeOk() (*int32, bool)`

GetRepositoryTypeOk returns a tuple with the RepositoryType field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetRepositoryType

`func (o *AddRepoRequest) SetRepositoryType(v int32)`

SetRepositoryType sets RepositoryType field to given value.

### HasRepositoryType

`func (o *AddRepoRequest) HasRepositoryType() bool`

HasRepositoryType returns a boolean if a field has been set.

### GetSdbConfigureType

`func (o *AddRepoRequest) GetSdbConfigureType() int32`

GetSdbConfigureType returns the SdbConfigureType field if non-nil, zero value otherwise.

### GetSdbConfigureTypeOk

`func (o *AddRepoRequest) GetSdbConfigureTypeOk() (*int32, bool)`

GetSdbConfigureTypeOk returns a tuple with the SdbConfigureType field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetSdbConfigureType

`func (o *AddRepoRequest) SetSdbConfigureType(v int32)`

SetSdbConfigureType sets SdbConfigureType field to given value.

### HasSdbConfigureType

`func (o *AddRepoRequest) HasSdbConfigureType() bool`

HasSdbConfigureType returns a boolean if a field has been set.

### GetTableName

`func (o *AddRepoRequest) GetTableName() string`

GetTableName returns the TableName field if non-nil, zero value otherwise.

### GetTableNameOk

`func (o *AddRepoRequest) GetTableNameOk() (*string, bool)`

GetTableNameOk returns a tuple with the TableName field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetTableName

`func (o *AddRepoRequest) SetTableName(v string)`

SetTableName sets TableName field to given value.

### HasTableName

`func (o *AddRepoRequest) HasTableName() bool`

HasTableName returns a boolean if a field has been set.

### GetUrl

`func (o *AddRepoRequest) GetUrl() string`

GetUrl returns the Url field if non-nil, zero value otherwise.

### GetUrlOk

`func (o *AddRepoRequest) GetUrlOk() (*string, bool)`

GetUrlOk returns a tuple with the Url field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUrl

`func (o *AddRepoRequest) SetUrl(v string)`

SetUrl sets Url field to given value.

### HasUrl

`func (o *AddRepoRequest) HasUrl() bool`

HasUrl returns a boolean if a field has been set.

### GetUsername

`func (o *AddRepoRequest) GetUsername() string`

GetUsername returns the Username field if non-nil, zero value otherwise.

### GetUsernameOk

`func (o *AddRepoRequest) GetUsernameOk() (*string, bool)`

GetUsernameOk returns a tuple with the Username field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUsername

`func (o *AddRepoRequest) SetUsername(v string)`

SetUsername sets Username field to given value.

### HasUsername

`func (o *AddRepoRequest) HasUsername() bool`

HasUsername returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


