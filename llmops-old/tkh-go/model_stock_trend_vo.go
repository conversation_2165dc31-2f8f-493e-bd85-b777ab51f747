/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// StockTrendVo StockTrendVO
type StockTrendVo struct {
	Fluctuations *float64 `json:"fluctuations,omitempty"`
	Price *float64 `json:"price,omitempty"`
	StockTrendList *[]StockTrend `json:"stockTrendList,omitempty"`
}

// NewStockTrendVo instantiates a new StockTrendVo object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewStockTrendVo() *StockTrendVo {
	this := StockTrendVo{}
	return &this
}

// NewStockTrendVoWithDefaults instantiates a new StockTrendVo object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewStockTrendVoWithDefaults() *StockTrendVo {
	this := StockTrendVo{}
	return &this
}

// GetFluctuations returns the Fluctuations field value if set, zero value otherwise.
func (o *StockTrendVo) GetFluctuations() float64 {
	if o == nil || o.Fluctuations == nil {
		var ret float64
		return ret
	}
	return *o.Fluctuations
}

// GetFluctuationsOk returns a tuple with the Fluctuations field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *StockTrendVo) GetFluctuationsOk() (*float64, bool) {
	if o == nil || o.Fluctuations == nil {
		return nil, false
	}
	return o.Fluctuations, true
}

// HasFluctuations returns a boolean if a field has been set.
func (o *StockTrendVo) HasFluctuations() bool {
	if o != nil && o.Fluctuations != nil {
		return true
	}

	return false
}

// SetFluctuations gets a reference to the given float64 and assigns it to the Fluctuations field.
func (o *StockTrendVo) SetFluctuations(v float64) {
	o.Fluctuations = &v
}

// GetPrice returns the Price field value if set, zero value otherwise.
func (o *StockTrendVo) GetPrice() float64 {
	if o == nil || o.Price == nil {
		var ret float64
		return ret
	}
	return *o.Price
}

// GetPriceOk returns a tuple with the Price field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *StockTrendVo) GetPriceOk() (*float64, bool) {
	if o == nil || o.Price == nil {
		return nil, false
	}
	return o.Price, true
}

// HasPrice returns a boolean if a field has been set.
func (o *StockTrendVo) HasPrice() bool {
	if o != nil && o.Price != nil {
		return true
	}

	return false
}

// SetPrice gets a reference to the given float64 and assigns it to the Price field.
func (o *StockTrendVo) SetPrice(v float64) {
	o.Price = &v
}

// GetStockTrendList returns the StockTrendList field value if set, zero value otherwise.
func (o *StockTrendVo) GetStockTrendList() []StockTrend {
	if o == nil || o.StockTrendList == nil {
		var ret []StockTrend
		return ret
	}
	return *o.StockTrendList
}

// GetStockTrendListOk returns a tuple with the StockTrendList field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *StockTrendVo) GetStockTrendListOk() (*[]StockTrend, bool) {
	if o == nil || o.StockTrendList == nil {
		return nil, false
	}
	return o.StockTrendList, true
}

// HasStockTrendList returns a boolean if a field has been set.
func (o *StockTrendVo) HasStockTrendList() bool {
	if o != nil && o.StockTrendList != nil {
		return true
	}

	return false
}

// SetStockTrendList gets a reference to the given []StockTrend and assigns it to the StockTrendList field.
func (o *StockTrendVo) SetStockTrendList(v []StockTrend) {
	o.StockTrendList = &v
}

func (o StockTrendVo) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Fluctuations != nil {
		toSerialize["fluctuations"] = o.Fluctuations
	}
	if o.Price != nil {
		toSerialize["price"] = o.Price
	}
	if o.StockTrendList != nil {
		toSerialize["stockTrendList"] = o.StockTrendList
	}
	return json.Marshal(toSerialize)
}

type NullableStockTrendVo struct {
	value *StockTrendVo
	isSet bool
}

func (v NullableStockTrendVo) Get() *StockTrendVo {
	return v.value
}

func (v *NullableStockTrendVo) Set(val *StockTrendVo) {
	v.value = val
	v.isSet = true
}

func (v NullableStockTrendVo) IsSet() bool {
	return v.isSet
}

func (v *NullableStockTrendVo) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableStockTrendVo(val *StockTrendVo) *NullableStockTrendVo {
	return &NullableStockTrendVo{value: val, isSet: true}
}

func (v NullableStockTrendVo) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableStockTrendVo) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
