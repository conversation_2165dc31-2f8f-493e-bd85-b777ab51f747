/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// TokenRequestInfo TokenRequestInfo
type TokenRequestInfo struct {
	// app
	ClientId *string `json:"clientId,omitempty"`
	// secret
	ClientSecret *string `json:"clientSecret,omitempty"`
	Password *string `json:"password,omitempty"`
	UserName *string `json:"userName,omitempty"`
}

// NewTokenRequestInfo instantiates a new TokenRequestInfo object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewTokenRequestInfo() *TokenRequestInfo {
	this := TokenRequestInfo{}
	return &this
}

// NewTokenRequestInfoWithDefaults instantiates a new TokenRequestInfo object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewTokenRequestInfoWithDefaults() *TokenRequestInfo {
	this := TokenRequestInfo{}
	return &this
}

// GetClientId returns the ClientId field value if set, zero value otherwise.
func (o *TokenRequestInfo) GetClientId() string {
	if o == nil || o.ClientId == nil {
		var ret string
		return ret
	}
	return *o.ClientId
}

// GetClientIdOk returns a tuple with the ClientId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *TokenRequestInfo) GetClientIdOk() (*string, bool) {
	if o == nil || o.ClientId == nil {
		return nil, false
	}
	return o.ClientId, true
}

// HasClientId returns a boolean if a field has been set.
func (o *TokenRequestInfo) HasClientId() bool {
	if o != nil && o.ClientId != nil {
		return true
	}

	return false
}

// SetClientId gets a reference to the given string and assigns it to the ClientId field.
func (o *TokenRequestInfo) SetClientId(v string) {
	o.ClientId = &v
}

// GetClientSecret returns the ClientSecret field value if set, zero value otherwise.
func (o *TokenRequestInfo) GetClientSecret() string {
	if o == nil || o.ClientSecret == nil {
		var ret string
		return ret
	}
	return *o.ClientSecret
}

// GetClientSecretOk returns a tuple with the ClientSecret field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *TokenRequestInfo) GetClientSecretOk() (*string, bool) {
	if o == nil || o.ClientSecret == nil {
		return nil, false
	}
	return o.ClientSecret, true
}

// HasClientSecret returns a boolean if a field has been set.
func (o *TokenRequestInfo) HasClientSecret() bool {
	if o != nil && o.ClientSecret != nil {
		return true
	}

	return false
}

// SetClientSecret gets a reference to the given string and assigns it to the ClientSecret field.
func (o *TokenRequestInfo) SetClientSecret(v string) {
	o.ClientSecret = &v
}

// GetPassword returns the Password field value if set, zero value otherwise.
func (o *TokenRequestInfo) GetPassword() string {
	if o == nil || o.Password == nil {
		var ret string
		return ret
	}
	return *o.Password
}

// GetPasswordOk returns a tuple with the Password field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *TokenRequestInfo) GetPasswordOk() (*string, bool) {
	if o == nil || o.Password == nil {
		return nil, false
	}
	return o.Password, true
}

// HasPassword returns a boolean if a field has been set.
func (o *TokenRequestInfo) HasPassword() bool {
	if o != nil && o.Password != nil {
		return true
	}

	return false
}

// SetPassword gets a reference to the given string and assigns it to the Password field.
func (o *TokenRequestInfo) SetPassword(v string) {
	o.Password = &v
}

// GetUserName returns the UserName field value if set, zero value otherwise.
func (o *TokenRequestInfo) GetUserName() string {
	if o == nil || o.UserName == nil {
		var ret string
		return ret
	}
	return *o.UserName
}

// GetUserNameOk returns a tuple with the UserName field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *TokenRequestInfo) GetUserNameOk() (*string, bool) {
	if o == nil || o.UserName == nil {
		return nil, false
	}
	return o.UserName, true
}

// HasUserName returns a boolean if a field has been set.
func (o *TokenRequestInfo) HasUserName() bool {
	if o != nil && o.UserName != nil {
		return true
	}

	return false
}

// SetUserName gets a reference to the given string and assigns it to the UserName field.
func (o *TokenRequestInfo) SetUserName(v string) {
	o.UserName = &v
}

func (o TokenRequestInfo) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.ClientId != nil {
		toSerialize["clientId"] = o.ClientId
	}
	if o.ClientSecret != nil {
		toSerialize["clientSecret"] = o.ClientSecret
	}
	if o.Password != nil {
		toSerialize["password"] = o.Password
	}
	if o.UserName != nil {
		toSerialize["userName"] = o.UserName
	}
	return json.Marshal(toSerialize)
}

type NullableTokenRequestInfo struct {
	value *TokenRequestInfo
	isSet bool
}

func (v NullableTokenRequestInfo) Get() *TokenRequestInfo {
	return v.value
}

func (v *NullableTokenRequestInfo) Set(val *TokenRequestInfo) {
	v.value = val
	v.isSet = true
}

func (v NullableTokenRequestInfo) IsSet() bool {
	return v.isSet
}

func (v *NullableTokenRequestInfo) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableTokenRequestInfo(val *TokenRequestInfo) *NullableTokenRequestInfo {
	return &NullableTokenRequestInfo{value: val, isSet: true}
}

func (v NullableTokenRequestInfo) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableTokenRequestInfo) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
