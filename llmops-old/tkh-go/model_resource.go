/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// Resource Resource
type Resource struct {
	Description *string `json:"description,omitempty"`
	File *File `json:"file,omitempty"`
	Filename *string `json:"filename,omitempty"`
	// InputStream
	InputStream *map[string]interface{} `json:"inputStream,omitempty"`
	Open *bool `json:"open,omitempty"`
	Readable *bool `json:"readable,omitempty"`
	Uri *Uri `json:"uri,omitempty"`
	Url *Url `json:"url,omitempty"`
}

// NewResource instantiates a new Resource object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewResource() *Resource {
	this := Resource{}
	return &this
}

// NewResourceWithDefaults instantiates a new Resource object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewResourceWithDefaults() *Resource {
	this := Resource{}
	return &this
}

// GetDescription returns the Description field value if set, zero value otherwise.
func (o *Resource) GetDescription() string {
	if o == nil || o.Description == nil {
		var ret string
		return ret
	}
	return *o.Description
}

// GetDescriptionOk returns a tuple with the Description field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Resource) GetDescriptionOk() (*string, bool) {
	if o == nil || o.Description == nil {
		return nil, false
	}
	return o.Description, true
}

// HasDescription returns a boolean if a field has been set.
func (o *Resource) HasDescription() bool {
	if o != nil && o.Description != nil {
		return true
	}

	return false
}

// SetDescription gets a reference to the given string and assigns it to the Description field.
func (o *Resource) SetDescription(v string) {
	o.Description = &v
}

// GetFile returns the File field value if set, zero value otherwise.
func (o *Resource) GetFile() File {
	if o == nil || o.File == nil {
		var ret File
		return ret
	}
	return *o.File
}

// GetFileOk returns a tuple with the File field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Resource) GetFileOk() (*File, bool) {
	if o == nil || o.File == nil {
		return nil, false
	}
	return o.File, true
}

// HasFile returns a boolean if a field has been set.
func (o *Resource) HasFile() bool {
	if o != nil && o.File != nil {
		return true
	}

	return false
}

// SetFile gets a reference to the given File and assigns it to the File field.
func (o *Resource) SetFile(v File) {
	o.File = &v
}

// GetFilename returns the Filename field value if set, zero value otherwise.
func (o *Resource) GetFilename() string {
	if o == nil || o.Filename == nil {
		var ret string
		return ret
	}
	return *o.Filename
}

// GetFilenameOk returns a tuple with the Filename field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Resource) GetFilenameOk() (*string, bool) {
	if o == nil || o.Filename == nil {
		return nil, false
	}
	return o.Filename, true
}

// HasFilename returns a boolean if a field has been set.
func (o *Resource) HasFilename() bool {
	if o != nil && o.Filename != nil {
		return true
	}

	return false
}

// SetFilename gets a reference to the given string and assigns it to the Filename field.
func (o *Resource) SetFilename(v string) {
	o.Filename = &v
}

// GetInputStream returns the InputStream field value if set, zero value otherwise.
func (o *Resource) GetInputStream() map[string]interface{} {
	if o == nil || o.InputStream == nil {
		var ret map[string]interface{}
		return ret
	}
	return *o.InputStream
}

// GetInputStreamOk returns a tuple with the InputStream field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Resource) GetInputStreamOk() (*map[string]interface{}, bool) {
	if o == nil || o.InputStream == nil {
		return nil, false
	}
	return o.InputStream, true
}

// HasInputStream returns a boolean if a field has been set.
func (o *Resource) HasInputStream() bool {
	if o != nil && o.InputStream != nil {
		return true
	}

	return false
}

// SetInputStream gets a reference to the given map[string]interface{} and assigns it to the InputStream field.
func (o *Resource) SetInputStream(v map[string]interface{}) {
	o.InputStream = &v
}

// GetOpen returns the Open field value if set, zero value otherwise.
func (o *Resource) GetOpen() bool {
	if o == nil || o.Open == nil {
		var ret bool
		return ret
	}
	return *o.Open
}

// GetOpenOk returns a tuple with the Open field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Resource) GetOpenOk() (*bool, bool) {
	if o == nil || o.Open == nil {
		return nil, false
	}
	return o.Open, true
}

// HasOpen returns a boolean if a field has been set.
func (o *Resource) HasOpen() bool {
	if o != nil && o.Open != nil {
		return true
	}

	return false
}

// SetOpen gets a reference to the given bool and assigns it to the Open field.
func (o *Resource) SetOpen(v bool) {
	o.Open = &v
}

// GetReadable returns the Readable field value if set, zero value otherwise.
func (o *Resource) GetReadable() bool {
	if o == nil || o.Readable == nil {
		var ret bool
		return ret
	}
	return *o.Readable
}

// GetReadableOk returns a tuple with the Readable field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Resource) GetReadableOk() (*bool, bool) {
	if o == nil || o.Readable == nil {
		return nil, false
	}
	return o.Readable, true
}

// HasReadable returns a boolean if a field has been set.
func (o *Resource) HasReadable() bool {
	if o != nil && o.Readable != nil {
		return true
	}

	return false
}

// SetReadable gets a reference to the given bool and assigns it to the Readable field.
func (o *Resource) SetReadable(v bool) {
	o.Readable = &v
}

// GetUri returns the Uri field value if set, zero value otherwise.
func (o *Resource) GetUri() Uri {
	if o == nil || o.Uri == nil {
		var ret Uri
		return ret
	}
	return *o.Uri
}

// GetUriOk returns a tuple with the Uri field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Resource) GetUriOk() (*Uri, bool) {
	if o == nil || o.Uri == nil {
		return nil, false
	}
	return o.Uri, true
}

// HasUri returns a boolean if a field has been set.
func (o *Resource) HasUri() bool {
	if o != nil && o.Uri != nil {
		return true
	}

	return false
}

// SetUri gets a reference to the given Uri and assigns it to the Uri field.
func (o *Resource) SetUri(v Uri) {
	o.Uri = &v
}

// GetUrl returns the Url field value if set, zero value otherwise.
func (o *Resource) GetUrl() Url {
	if o == nil || o.Url == nil {
		var ret Url
		return ret
	}
	return *o.Url
}

// GetUrlOk returns a tuple with the Url field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Resource) GetUrlOk() (*Url, bool) {
	if o == nil || o.Url == nil {
		return nil, false
	}
	return o.Url, true
}

// HasUrl returns a boolean if a field has been set.
func (o *Resource) HasUrl() bool {
	if o != nil && o.Url != nil {
		return true
	}

	return false
}

// SetUrl gets a reference to the given Url and assigns it to the Url field.
func (o *Resource) SetUrl(v Url) {
	o.Url = &v
}

func (o Resource) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Description != nil {
		toSerialize["description"] = o.Description
	}
	if o.File != nil {
		toSerialize["file"] = o.File
	}
	if o.Filename != nil {
		toSerialize["filename"] = o.Filename
	}
	if o.InputStream != nil {
		toSerialize["inputStream"] = o.InputStream
	}
	if o.Open != nil {
		toSerialize["open"] = o.Open
	}
	if o.Readable != nil {
		toSerialize["readable"] = o.Readable
	}
	if o.Uri != nil {
		toSerialize["uri"] = o.Uri
	}
	if o.Url != nil {
		toSerialize["url"] = o.Url
	}
	return json.Marshal(toSerialize)
}

type NullableResource struct {
	value *Resource
	isSet bool
}

func (v NullableResource) Get() *Resource {
	return v.value
}

func (v *NullableResource) Set(val *Resource) {
	v.value = val
	v.isSet = true
}

func (v NullableResource) IsSet() bool {
	return v.isSet
}

func (v *NullableResource) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableResource(val *Resource) *NullableResource {
	return &NullableResource{value: val, isSet: true}
}

func (v NullableResource) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableResource) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
