/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// ConversationUpdateRequest ConversationUpdateRequest
type ConversationUpdateRequest struct {
	// 会话id
	ConversationId string `json:"conversationId"`
	Title *string `json:"title,omitempty"`
}

// NewConversationUpdateRequest instantiates a new ConversationUpdateRequest object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewConversationUpdateRequest(conversationId string, ) *ConversationUpdateRequest {
	this := ConversationUpdateRequest{}
	this.ConversationId = conversationId
	return &this
}

// NewConversationUpdateRequestWithDefaults instantiates a new ConversationUpdateRequest object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewConversationUpdateRequestWithDefaults() *ConversationUpdateRequest {
	this := ConversationUpdateRequest{}
	return &this
}

// GetConversationId returns the ConversationId field value
func (o *ConversationUpdateRequest) GetConversationId() string {
	if o == nil  {
		var ret string
		return ret
	}

	return o.ConversationId
}

// GetConversationIdOk returns a tuple with the ConversationId field value
// and a boolean to check if the value has been set.
func (o *ConversationUpdateRequest) GetConversationIdOk() (*string, bool) {
	if o == nil  {
		return nil, false
	}
	return &o.ConversationId, true
}

// SetConversationId sets field value
func (o *ConversationUpdateRequest) SetConversationId(v string) {
	o.ConversationId = v
}

// GetTitle returns the Title field value if set, zero value otherwise.
func (o *ConversationUpdateRequest) GetTitle() string {
	if o == nil || o.Title == nil {
		var ret string
		return ret
	}
	return *o.Title
}

// GetTitleOk returns a tuple with the Title field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ConversationUpdateRequest) GetTitleOk() (*string, bool) {
	if o == nil || o.Title == nil {
		return nil, false
	}
	return o.Title, true
}

// HasTitle returns a boolean if a field has been set.
func (o *ConversationUpdateRequest) HasTitle() bool {
	if o != nil && o.Title != nil {
		return true
	}

	return false
}

// SetTitle gets a reference to the given string and assigns it to the Title field.
func (o *ConversationUpdateRequest) SetTitle(v string) {
	o.Title = &v
}

func (o ConversationUpdateRequest) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if true {
		toSerialize["conversationId"] = o.ConversationId
	}
	if o.Title != nil {
		toSerialize["title"] = o.Title
	}
	return json.Marshal(toSerialize)
}

type NullableConversationUpdateRequest struct {
	value *ConversationUpdateRequest
	isSet bool
}

func (v NullableConversationUpdateRequest) Get() *ConversationUpdateRequest {
	return v.value
}

func (v *NullableConversationUpdateRequest) Set(val *ConversationUpdateRequest) {
	v.value = val
	v.isSet = true
}

func (v NullableConversationUpdateRequest) IsSet() bool {
	return v.isSet
}

func (v *NullableConversationUpdateRequest) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableConversationUpdateRequest(val *ConversationUpdateRequest) *NullableConversationUpdateRequest {
	return &NullableConversationUpdateRequest{value: val, isSet: true}
}

func (v NullableConversationUpdateRequest) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableConversationUpdateRequest) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
