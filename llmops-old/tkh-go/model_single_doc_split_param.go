/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// SingleDocSplitParam SingleDocSplitParam
type SingleDocSplitParam struct {
	// 文档uuid
	DocId *string `json:"doc_id,omitempty"`
	// 解析器字符串
	DocumentParser *string `json:"document_parser,omitempty"`
	FilePath *string `json:"file_path,omitempty"`
	// map结构，包含 chunksize 
	Params *map[string]interface{} `json:"params,omitempty"`
}

// NewSingleDocSplitParam instantiates a new SingleDocSplitParam object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewSingleDocSplitParam() *SingleDocSplitParam {
	this := SingleDocSplitParam{}
	return &this
}

// NewSingleDocSplitParamWithDefaults instantiates a new SingleDocSplitParam object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewSingleDocSplitParamWithDefaults() *SingleDocSplitParam {
	this := SingleDocSplitParam{}
	return &this
}

// GetDocId returns the DocId field value if set, zero value otherwise.
func (o *SingleDocSplitParam) GetDocId() string {
	if o == nil || o.DocId == nil {
		var ret string
		return ret
	}
	return *o.DocId
}

// GetDocIdOk returns a tuple with the DocId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *SingleDocSplitParam) GetDocIdOk() (*string, bool) {
	if o == nil || o.DocId == nil {
		return nil, false
	}
	return o.DocId, true
}

// HasDocId returns a boolean if a field has been set.
func (o *SingleDocSplitParam) HasDocId() bool {
	if o != nil && o.DocId != nil {
		return true
	}

	return false
}

// SetDocId gets a reference to the given string and assigns it to the DocId field.
func (o *SingleDocSplitParam) SetDocId(v string) {
	o.DocId = &v
}

// GetDocumentParser returns the DocumentParser field value if set, zero value otherwise.
func (o *SingleDocSplitParam) GetDocumentParser() string {
	if o == nil || o.DocumentParser == nil {
		var ret string
		return ret
	}
	return *o.DocumentParser
}

// GetDocumentParserOk returns a tuple with the DocumentParser field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *SingleDocSplitParam) GetDocumentParserOk() (*string, bool) {
	if o == nil || o.DocumentParser == nil {
		return nil, false
	}
	return o.DocumentParser, true
}

// HasDocumentParser returns a boolean if a field has been set.
func (o *SingleDocSplitParam) HasDocumentParser() bool {
	if o != nil && o.DocumentParser != nil {
		return true
	}

	return false
}

// SetDocumentParser gets a reference to the given string and assigns it to the DocumentParser field.
func (o *SingleDocSplitParam) SetDocumentParser(v string) {
	o.DocumentParser = &v
}

// GetFilePath returns the FilePath field value if set, zero value otherwise.
func (o *SingleDocSplitParam) GetFilePath() string {
	if o == nil || o.FilePath == nil {
		var ret string
		return ret
	}
	return *o.FilePath
}

// GetFilePathOk returns a tuple with the FilePath field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *SingleDocSplitParam) GetFilePathOk() (*string, bool) {
	if o == nil || o.FilePath == nil {
		return nil, false
	}
	return o.FilePath, true
}

// HasFilePath returns a boolean if a field has been set.
func (o *SingleDocSplitParam) HasFilePath() bool {
	if o != nil && o.FilePath != nil {
		return true
	}

	return false
}

// SetFilePath gets a reference to the given string and assigns it to the FilePath field.
func (o *SingleDocSplitParam) SetFilePath(v string) {
	o.FilePath = &v
}

// GetParams returns the Params field value if set, zero value otherwise.
func (o *SingleDocSplitParam) GetParams() map[string]interface{} {
	if o == nil || o.Params == nil {
		var ret map[string]interface{}
		return ret
	}
	return *o.Params
}

// GetParamsOk returns a tuple with the Params field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *SingleDocSplitParam) GetParamsOk() (*map[string]interface{}, bool) {
	if o == nil || o.Params == nil {
		return nil, false
	}
	return o.Params, true
}

// HasParams returns a boolean if a field has been set.
func (o *SingleDocSplitParam) HasParams() bool {
	if o != nil && o.Params != nil {
		return true
	}

	return false
}

// SetParams gets a reference to the given map[string]interface{} and assigns it to the Params field.
func (o *SingleDocSplitParam) SetParams(v map[string]interface{}) {
	o.Params = &v
}

func (o SingleDocSplitParam) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.DocId != nil {
		toSerialize["doc_id"] = o.DocId
	}
	if o.DocumentParser != nil {
		toSerialize["document_parser"] = o.DocumentParser
	}
	if o.FilePath != nil {
		toSerialize["file_path"] = o.FilePath
	}
	if o.Params != nil {
		toSerialize["params"] = o.Params
	}
	return json.Marshal(toSerialize)
}

type NullableSingleDocSplitParam struct {
	value *SingleDocSplitParam
	isSet bool
}

func (v NullableSingleDocSplitParam) Get() *SingleDocSplitParam {
	return v.value
}

func (v *NullableSingleDocSplitParam) Set(val *SingleDocSplitParam) {
	v.value = val
	v.isSet = true
}

func (v NullableSingleDocSplitParam) IsSet() bool {
	return v.isSet
}

func (v *NullableSingleDocSplitParam) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableSingleDocSplitParam(val *SingleDocSplitParam) *NullableSingleDocSplitParam {
	return &NullableSingleDocSplitParam{value: val, isSet: true}
}

func (v NullableSingleDocSplitParam) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableSingleDocSplitParam) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
