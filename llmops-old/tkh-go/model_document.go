/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// Document Document，文档表
type Document struct {
	// 创建时间
	CreateTime *string `json:"createTime,omitempty"`
	// 创建者
	Creator *string `json:"creator,omitempty"`
	// 文档树ID
	DocumentTreeId *int64 `json:"documentTreeId,omitempty"`
	// 上传文档业务归类 PDF=1, IMG=2, DOCX=3, PPTX=4, TABLE=5, TXT=6, AUDIO=7, VIDEO=8, NONE=9 
	FileBusinessType *int32 `json:"fileBusinessType,omitempty"`
	// 上传文档contentType枚举 TYPE_PDF=1, TYPE_IMG_JPEG=21, TYPE_IMG_PNG=22, TYPE_IMG_GIF=23, TYPE_IMG_GMP=24, TYPE_DOCX_VND=31, TYPE_DOCX_MS=32, TYPE_PPT_OPENXML=41, TYPE_PPT_MS=42, TYPE_TABLE_VND=51, TYPE_TABLE_CSV=52, TYPE_TEXT_PLAIN=61, TYPE_AUDIO_MEPG=71, TYPE_AUDIO_WAV=72, TYPE_AUDIO_WAVE=73, TYPE_AUDIO_XWAV=74, TYPE_AUDIO_OGG=75, TYPE_AUDIO_AAC=76, TYPE_AUDIO_FLAC=77, TYPE_AUDIO_MP4=78, TYPE_VIDEO_AVI=81, TYPE_VIDEO_MP4=82, TYPE_VIDEO_FLV=83, TYPE_VIDEO_WMV=84, TYPE_VIDEO_MKV=85, TYPE_NONE=91
	FileContentType *int32 `json:"fileContentType,omitempty"`
	// 文件MD5值
	FileMd5 *string `json:"fileMd5,omitempty"`
	FilePath *string `json:"filePath,omitempty"`
	// 上传文档大小-bytes
	FileSize *int64 `json:"fileSize,omitempty"`
	// 枚举 1= waiting 2=uploading 3=upload_Failed  4= uploaded 5=parsing、6=parse_failed 、7= parse_success 、8= edited  9=saved_failed 10 saved
	FileStatus *int32 `json:"fileStatus,omitempty"`
	// 上传文档后缀枚举值 PDF=1, PNG=21, JPEG=22, JPG=23, GIF=24, BMP=25, DOC=31, DOCX=32, PPT=41, PPTX=42, XLSX=51, CSV=52, TXT=61, MP3=71, WAV=72, OGG=73, AAC=74, FLAC=75, M4A=76, AVI=81, MP4=82, FLV=83, WMV=84, MKV=85, NONE=91
	FileSuffix *int32 `json:"fileSuffix,omitempty"`
	// 关联上传任务id
	FileUploadTaskId *int64 `json:"fileUploadTaskId,omitempty"`
	// 文件uuid
	FileUuid *string `json:"fileUuid,omitempty"`
	// ID
	Id *int64 `json:"id,omitempty"`
	// 名称
	Name *string `json:"name,omitempty"`
	// 更新时间
	UpdateTime *string `json:"updateTime,omitempty"`
}

// NewDocument instantiates a new Document object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDocument() *Document {
	this := Document{}
	return &this
}

// NewDocumentWithDefaults instantiates a new Document object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDocumentWithDefaults() *Document {
	this := Document{}
	return &this
}

// GetCreateTime returns the CreateTime field value if set, zero value otherwise.
func (o *Document) GetCreateTime() string {
	if o == nil || o.CreateTime == nil {
		var ret string
		return ret
	}
	return *o.CreateTime
}

// GetCreateTimeOk returns a tuple with the CreateTime field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Document) GetCreateTimeOk() (*string, bool) {
	if o == nil || o.CreateTime == nil {
		return nil, false
	}
	return o.CreateTime, true
}

// HasCreateTime returns a boolean if a field has been set.
func (o *Document) HasCreateTime() bool {
	if o != nil && o.CreateTime != nil {
		return true
	}

	return false
}

// SetCreateTime gets a reference to the given string and assigns it to the CreateTime field.
func (o *Document) SetCreateTime(v string) {
	o.CreateTime = &v
}

// GetCreator returns the Creator field value if set, zero value otherwise.
func (o *Document) GetCreator() string {
	if o == nil || o.Creator == nil {
		var ret string
		return ret
	}
	return *o.Creator
}

// GetCreatorOk returns a tuple with the Creator field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Document) GetCreatorOk() (*string, bool) {
	if o == nil || o.Creator == nil {
		return nil, false
	}
	return o.Creator, true
}

// HasCreator returns a boolean if a field has been set.
func (o *Document) HasCreator() bool {
	if o != nil && o.Creator != nil {
		return true
	}

	return false
}

// SetCreator gets a reference to the given string and assigns it to the Creator field.
func (o *Document) SetCreator(v string) {
	o.Creator = &v
}

// GetDocumentTreeId returns the DocumentTreeId field value if set, zero value otherwise.
func (o *Document) GetDocumentTreeId() int64 {
	if o == nil || o.DocumentTreeId == nil {
		var ret int64
		return ret
	}
	return *o.DocumentTreeId
}

// GetDocumentTreeIdOk returns a tuple with the DocumentTreeId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Document) GetDocumentTreeIdOk() (*int64, bool) {
	if o == nil || o.DocumentTreeId == nil {
		return nil, false
	}
	return o.DocumentTreeId, true
}

// HasDocumentTreeId returns a boolean if a field has been set.
func (o *Document) HasDocumentTreeId() bool {
	if o != nil && o.DocumentTreeId != nil {
		return true
	}

	return false
}

// SetDocumentTreeId gets a reference to the given int64 and assigns it to the DocumentTreeId field.
func (o *Document) SetDocumentTreeId(v int64) {
	o.DocumentTreeId = &v
}

// GetFileBusinessType returns the FileBusinessType field value if set, zero value otherwise.
func (o *Document) GetFileBusinessType() int32 {
	if o == nil || o.FileBusinessType == nil {
		var ret int32
		return ret
	}
	return *o.FileBusinessType
}

// GetFileBusinessTypeOk returns a tuple with the FileBusinessType field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Document) GetFileBusinessTypeOk() (*int32, bool) {
	if o == nil || o.FileBusinessType == nil {
		return nil, false
	}
	return o.FileBusinessType, true
}

// HasFileBusinessType returns a boolean if a field has been set.
func (o *Document) HasFileBusinessType() bool {
	if o != nil && o.FileBusinessType != nil {
		return true
	}

	return false
}

// SetFileBusinessType gets a reference to the given int32 and assigns it to the FileBusinessType field.
func (o *Document) SetFileBusinessType(v int32) {
	o.FileBusinessType = &v
}

// GetFileContentType returns the FileContentType field value if set, zero value otherwise.
func (o *Document) GetFileContentType() int32 {
	if o == nil || o.FileContentType == nil {
		var ret int32
		return ret
	}
	return *o.FileContentType
}

// GetFileContentTypeOk returns a tuple with the FileContentType field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Document) GetFileContentTypeOk() (*int32, bool) {
	if o == nil || o.FileContentType == nil {
		return nil, false
	}
	return o.FileContentType, true
}

// HasFileContentType returns a boolean if a field has been set.
func (o *Document) HasFileContentType() bool {
	if o != nil && o.FileContentType != nil {
		return true
	}

	return false
}

// SetFileContentType gets a reference to the given int32 and assigns it to the FileContentType field.
func (o *Document) SetFileContentType(v int32) {
	o.FileContentType = &v
}

// GetFileMd5 returns the FileMd5 field value if set, zero value otherwise.
func (o *Document) GetFileMd5() string {
	if o == nil || o.FileMd5 == nil {
		var ret string
		return ret
	}
	return *o.FileMd5
}

// GetFileMd5Ok returns a tuple with the FileMd5 field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Document) GetFileMd5Ok() (*string, bool) {
	if o == nil || o.FileMd5 == nil {
		return nil, false
	}
	return o.FileMd5, true
}

// HasFileMd5 returns a boolean if a field has been set.
func (o *Document) HasFileMd5() bool {
	if o != nil && o.FileMd5 != nil {
		return true
	}

	return false
}

// SetFileMd5 gets a reference to the given string and assigns it to the FileMd5 field.
func (o *Document) SetFileMd5(v string) {
	o.FileMd5 = &v
}

// GetFilePath returns the FilePath field value if set, zero value otherwise.
func (o *Document) GetFilePath() string {
	if o == nil || o.FilePath == nil {
		var ret string
		return ret
	}
	return *o.FilePath
}

// GetFilePathOk returns a tuple with the FilePath field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Document) GetFilePathOk() (*string, bool) {
	if o == nil || o.FilePath == nil {
		return nil, false
	}
	return o.FilePath, true
}

// HasFilePath returns a boolean if a field has been set.
func (o *Document) HasFilePath() bool {
	if o != nil && o.FilePath != nil {
		return true
	}

	return false
}

// SetFilePath gets a reference to the given string and assigns it to the FilePath field.
func (o *Document) SetFilePath(v string) {
	o.FilePath = &v
}

// GetFileSize returns the FileSize field value if set, zero value otherwise.
func (o *Document) GetFileSize() int64 {
	if o == nil || o.FileSize == nil {
		var ret int64
		return ret
	}
	return *o.FileSize
}

// GetFileSizeOk returns a tuple with the FileSize field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Document) GetFileSizeOk() (*int64, bool) {
	if o == nil || o.FileSize == nil {
		return nil, false
	}
	return o.FileSize, true
}

// HasFileSize returns a boolean if a field has been set.
func (o *Document) HasFileSize() bool {
	if o != nil && o.FileSize != nil {
		return true
	}

	return false
}

// SetFileSize gets a reference to the given int64 and assigns it to the FileSize field.
func (o *Document) SetFileSize(v int64) {
	o.FileSize = &v
}

// GetFileStatus returns the FileStatus field value if set, zero value otherwise.
func (o *Document) GetFileStatus() int32 {
	if o == nil || o.FileStatus == nil {
		var ret int32
		return ret
	}
	return *o.FileStatus
}

// GetFileStatusOk returns a tuple with the FileStatus field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Document) GetFileStatusOk() (*int32, bool) {
	if o == nil || o.FileStatus == nil {
		return nil, false
	}
	return o.FileStatus, true
}

// HasFileStatus returns a boolean if a field has been set.
func (o *Document) HasFileStatus() bool {
	if o != nil && o.FileStatus != nil {
		return true
	}

	return false
}

// SetFileStatus gets a reference to the given int32 and assigns it to the FileStatus field.
func (o *Document) SetFileStatus(v int32) {
	o.FileStatus = &v
}

// GetFileSuffix returns the FileSuffix field value if set, zero value otherwise.
func (o *Document) GetFileSuffix() int32 {
	if o == nil || o.FileSuffix == nil {
		var ret int32
		return ret
	}
	return *o.FileSuffix
}

// GetFileSuffixOk returns a tuple with the FileSuffix field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Document) GetFileSuffixOk() (*int32, bool) {
	if o == nil || o.FileSuffix == nil {
		return nil, false
	}
	return o.FileSuffix, true
}

// HasFileSuffix returns a boolean if a field has been set.
func (o *Document) HasFileSuffix() bool {
	if o != nil && o.FileSuffix != nil {
		return true
	}

	return false
}

// SetFileSuffix gets a reference to the given int32 and assigns it to the FileSuffix field.
func (o *Document) SetFileSuffix(v int32) {
	o.FileSuffix = &v
}

// GetFileUploadTaskId returns the FileUploadTaskId field value if set, zero value otherwise.
func (o *Document) GetFileUploadTaskId() int64 {
	if o == nil || o.FileUploadTaskId == nil {
		var ret int64
		return ret
	}
	return *o.FileUploadTaskId
}

// GetFileUploadTaskIdOk returns a tuple with the FileUploadTaskId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Document) GetFileUploadTaskIdOk() (*int64, bool) {
	if o == nil || o.FileUploadTaskId == nil {
		return nil, false
	}
	return o.FileUploadTaskId, true
}

// HasFileUploadTaskId returns a boolean if a field has been set.
func (o *Document) HasFileUploadTaskId() bool {
	if o != nil && o.FileUploadTaskId != nil {
		return true
	}

	return false
}

// SetFileUploadTaskId gets a reference to the given int64 and assigns it to the FileUploadTaskId field.
func (o *Document) SetFileUploadTaskId(v int64) {
	o.FileUploadTaskId = &v
}

// GetFileUuid returns the FileUuid field value if set, zero value otherwise.
func (o *Document) GetFileUuid() string {
	if o == nil || o.FileUuid == nil {
		var ret string
		return ret
	}
	return *o.FileUuid
}

// GetFileUuidOk returns a tuple with the FileUuid field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Document) GetFileUuidOk() (*string, bool) {
	if o == nil || o.FileUuid == nil {
		return nil, false
	}
	return o.FileUuid, true
}

// HasFileUuid returns a boolean if a field has been set.
func (o *Document) HasFileUuid() bool {
	if o != nil && o.FileUuid != nil {
		return true
	}

	return false
}

// SetFileUuid gets a reference to the given string and assigns it to the FileUuid field.
func (o *Document) SetFileUuid(v string) {
	o.FileUuid = &v
}

// GetId returns the Id field value if set, zero value otherwise.
func (o *Document) GetId() int64 {
	if o == nil || o.Id == nil {
		var ret int64
		return ret
	}
	return *o.Id
}

// GetIdOk returns a tuple with the Id field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Document) GetIdOk() (*int64, bool) {
	if o == nil || o.Id == nil {
		return nil, false
	}
	return o.Id, true
}

// HasId returns a boolean if a field has been set.
func (o *Document) HasId() bool {
	if o != nil && o.Id != nil {
		return true
	}

	return false
}

// SetId gets a reference to the given int64 and assigns it to the Id field.
func (o *Document) SetId(v int64) {
	o.Id = &v
}

// GetName returns the Name field value if set, zero value otherwise.
func (o *Document) GetName() string {
	if o == nil || o.Name == nil {
		var ret string
		return ret
	}
	return *o.Name
}

// GetNameOk returns a tuple with the Name field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Document) GetNameOk() (*string, bool) {
	if o == nil || o.Name == nil {
		return nil, false
	}
	return o.Name, true
}

// HasName returns a boolean if a field has been set.
func (o *Document) HasName() bool {
	if o != nil && o.Name != nil {
		return true
	}

	return false
}

// SetName gets a reference to the given string and assigns it to the Name field.
func (o *Document) SetName(v string) {
	o.Name = &v
}

// GetUpdateTime returns the UpdateTime field value if set, zero value otherwise.
func (o *Document) GetUpdateTime() string {
	if o == nil || o.UpdateTime == nil {
		var ret string
		return ret
	}
	return *o.UpdateTime
}

// GetUpdateTimeOk returns a tuple with the UpdateTime field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Document) GetUpdateTimeOk() (*string, bool) {
	if o == nil || o.UpdateTime == nil {
		return nil, false
	}
	return o.UpdateTime, true
}

// HasUpdateTime returns a boolean if a field has been set.
func (o *Document) HasUpdateTime() bool {
	if o != nil && o.UpdateTime != nil {
		return true
	}

	return false
}

// SetUpdateTime gets a reference to the given string and assigns it to the UpdateTime field.
func (o *Document) SetUpdateTime(v string) {
	o.UpdateTime = &v
}

func (o Document) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.CreateTime != nil {
		toSerialize["createTime"] = o.CreateTime
	}
	if o.Creator != nil {
		toSerialize["creator"] = o.Creator
	}
	if o.DocumentTreeId != nil {
		toSerialize["documentTreeId"] = o.DocumentTreeId
	}
	if o.FileBusinessType != nil {
		toSerialize["fileBusinessType"] = o.FileBusinessType
	}
	if o.FileContentType != nil {
		toSerialize["fileContentType"] = o.FileContentType
	}
	if o.FileMd5 != nil {
		toSerialize["fileMd5"] = o.FileMd5
	}
	if o.FilePath != nil {
		toSerialize["filePath"] = o.FilePath
	}
	if o.FileSize != nil {
		toSerialize["fileSize"] = o.FileSize
	}
	if o.FileStatus != nil {
		toSerialize["fileStatus"] = o.FileStatus
	}
	if o.FileSuffix != nil {
		toSerialize["fileSuffix"] = o.FileSuffix
	}
	if o.FileUploadTaskId != nil {
		toSerialize["fileUploadTaskId"] = o.FileUploadTaskId
	}
	if o.FileUuid != nil {
		toSerialize["fileUuid"] = o.FileUuid
	}
	if o.Id != nil {
		toSerialize["id"] = o.Id
	}
	if o.Name != nil {
		toSerialize["name"] = o.Name
	}
	if o.UpdateTime != nil {
		toSerialize["updateTime"] = o.UpdateTime
	}
	return json.Marshal(toSerialize)
}

type NullableDocument struct {
	value *Document
	isSet bool
}

func (v NullableDocument) Get() *Document {
	return v.value
}

func (v *NullableDocument) Set(val *Document) {
	v.value = val
	v.isSet = true
}

func (v NullableDocument) IsSet() bool {
	return v.isSet
}

func (v *NullableDocument) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDocument(val *Document) *NullableDocument {
	return &NullableDocument{value: val, isSet: true}
}

func (v NullableDocument) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDocument) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
