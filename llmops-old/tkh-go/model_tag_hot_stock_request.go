/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// TagHotStockRequest TagHotStockRequest
type TagHotStockRequest struct {
	StockName *[]string `json:"stockName,omitempty"`
}

// NewTagHotStockRequest instantiates a new TagHotStockRequest object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewTagHotStockRequest() *TagHotStockRequest {
	this := TagHotStockRequest{}
	return &this
}

// NewTagHotStockRequestWithDefaults instantiates a new TagHotStockRequest object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewTagHotStockRequestWithDefaults() *TagHotStockRequest {
	this := TagHotStockRequest{}
	return &this
}

// GetStockName returns the StockName field value if set, zero value otherwise.
func (o *TagHotStockRequest) GetStockName() []string {
	if o == nil || o.StockName == nil {
		var ret []string
		return ret
	}
	return *o.StockName
}

// GetStockNameOk returns a tuple with the StockName field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *TagHotStockRequest) GetStockNameOk() (*[]string, bool) {
	if o == nil || o.StockName == nil {
		return nil, false
	}
	return o.StockName, true
}

// HasStockName returns a boolean if a field has been set.
func (o *TagHotStockRequest) HasStockName() bool {
	if o != nil && o.StockName != nil {
		return true
	}

	return false
}

// SetStockName gets a reference to the given []string and assigns it to the StockName field.
func (o *TagHotStockRequest) SetStockName(v []string) {
	o.StockName = &v
}

func (o TagHotStockRequest) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.StockName != nil {
		toSerialize["stockName"] = o.StockName
	}
	return json.Marshal(toSerialize)
}

type NullableTagHotStockRequest struct {
	value *TagHotStockRequest
	isSet bool
}

func (v NullableTagHotStockRequest) Get() *TagHotStockRequest {
	return v.value
}

func (v *NullableTagHotStockRequest) Set(val *TagHotStockRequest) {
	v.value = val
	v.isSet = true
}

func (v NullableTagHotStockRequest) IsSet() bool {
	return v.isSet
}

func (v *NullableTagHotStockRequest) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableTagHotStockRequest(val *TagHotStockRequest) *NullableTagHotStockRequest {
	return &NullableTagHotStockRequest{value: val, isSet: true}
}

func (v NullableTagHotStockRequest) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableTagHotStockRequest) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
