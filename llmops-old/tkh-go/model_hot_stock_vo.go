/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// HotStockVo HotStockVO
type HotStockVo struct {
	StockCode *string `json:"stockCode,omitempty"`
	StockName *string `json:"stockName,omitempty"`
}

// NewHotStockVo instantiates a new HotStockVo object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewHotStockVo() *HotStockVo {
	this := HotStockVo{}
	return &this
}

// NewHotStockVoWithDefaults instantiates a new HotStockVo object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewHotStockVoWithDefaults() *HotStockVo {
	this := HotStockVo{}
	return &this
}

// GetStockCode returns the StockCode field value if set, zero value otherwise.
func (o *HotStockVo) GetStockCode() string {
	if o == nil || o.StockCode == nil {
		var ret string
		return ret
	}
	return *o.StockCode
}

// GetStockCodeOk returns a tuple with the StockCode field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *HotStockVo) GetStockCodeOk() (*string, bool) {
	if o == nil || o.StockCode == nil {
		return nil, false
	}
	return o.StockCode, true
}

// HasStockCode returns a boolean if a field has been set.
func (o *HotStockVo) HasStockCode() bool {
	if o != nil && o.StockCode != nil {
		return true
	}

	return false
}

// SetStockCode gets a reference to the given string and assigns it to the StockCode field.
func (o *HotStockVo) SetStockCode(v string) {
	o.StockCode = &v
}

// GetStockName returns the StockName field value if set, zero value otherwise.
func (o *HotStockVo) GetStockName() string {
	if o == nil || o.StockName == nil {
		var ret string
		return ret
	}
	return *o.StockName
}

// GetStockNameOk returns a tuple with the StockName field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *HotStockVo) GetStockNameOk() (*string, bool) {
	if o == nil || o.StockName == nil {
		return nil, false
	}
	return o.StockName, true
}

// HasStockName returns a boolean if a field has been set.
func (o *HotStockVo) HasStockName() bool {
	if o != nil && o.StockName != nil {
		return true
	}

	return false
}

// SetStockName gets a reference to the given string and assigns it to the StockName field.
func (o *HotStockVo) SetStockName(v string) {
	o.StockName = &v
}

func (o HotStockVo) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.StockCode != nil {
		toSerialize["stockCode"] = o.StockCode
	}
	if o.StockName != nil {
		toSerialize["stockName"] = o.StockName
	}
	return json.Marshal(toSerialize)
}

type NullableHotStockVo struct {
	value *HotStockVo
	isSet bool
}

func (v NullableHotStockVo) Get() *HotStockVo {
	return v.value
}

func (v *NullableHotStockVo) Set(val *HotStockVo) {
	v.value = val
	v.isSet = true
}

func (v NullableHotStockVo) IsSet() bool {
	return v.isSet
}

func (v *NullableHotStockVo) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableHotStockVo(val *HotStockVo) *NullableHotStockVo {
	return &NullableHotStockVo{value: val, isSet: true}
}

func (v NullableHotStockVo) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableHotStockVo) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
