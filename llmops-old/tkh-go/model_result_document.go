/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// ResultDocument Result«Document»
type ResultDocument struct {
	Code *int32 `json:"code,omitempty"`
	Data *Document `json:"data,omitempty"`
	Message *string `json:"message,omitempty"`
}

// NewResultDocument instantiates a new ResultDocument object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewResultDocument() *ResultDocument {
	this := ResultDocument{}
	return &this
}

// NewResultDocumentWithDefaults instantiates a new ResultDocument object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewResultDocumentWithDefaults() *ResultDocument {
	this := ResultDocument{}
	return &this
}

// GetCode returns the Code field value if set, zero value otherwise.
func (o *ResultDocument) GetCode() int32 {
	if o == nil || o.Code == nil {
		var ret int32
		return ret
	}
	return *o.Code
}

// GetCodeOk returns a tuple with the Code field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ResultDocument) GetCodeOk() (*int32, bool) {
	if o == nil || o.Code == nil {
		return nil, false
	}
	return o.Code, true
}

// HasCode returns a boolean if a field has been set.
func (o *ResultDocument) HasCode() bool {
	if o != nil && o.Code != nil {
		return true
	}

	return false
}

// SetCode gets a reference to the given int32 and assigns it to the Code field.
func (o *ResultDocument) SetCode(v int32) {
	o.Code = &v
}

// GetData returns the Data field value if set, zero value otherwise.
func (o *ResultDocument) GetData() Document {
	if o == nil || o.Data == nil {
		var ret Document
		return ret
	}
	return *o.Data
}

// GetDataOk returns a tuple with the Data field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ResultDocument) GetDataOk() (*Document, bool) {
	if o == nil || o.Data == nil {
		return nil, false
	}
	return o.Data, true
}

// HasData returns a boolean if a field has been set.
func (o *ResultDocument) HasData() bool {
	if o != nil && o.Data != nil {
		return true
	}

	return false
}

// SetData gets a reference to the given Document and assigns it to the Data field.
func (o *ResultDocument) SetData(v Document) {
	o.Data = &v
}

// GetMessage returns the Message field value if set, zero value otherwise.
func (o *ResultDocument) GetMessage() string {
	if o == nil || o.Message == nil {
		var ret string
		return ret
	}
	return *o.Message
}

// GetMessageOk returns a tuple with the Message field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ResultDocument) GetMessageOk() (*string, bool) {
	if o == nil || o.Message == nil {
		return nil, false
	}
	return o.Message, true
}

// HasMessage returns a boolean if a field has been set.
func (o *ResultDocument) HasMessage() bool {
	if o != nil && o.Message != nil {
		return true
	}

	return false
}

// SetMessage gets a reference to the given string and assigns it to the Message field.
func (o *ResultDocument) SetMessage(v string) {
	o.Message = &v
}

func (o ResultDocument) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Code != nil {
		toSerialize["code"] = o.Code
	}
	if o.Data != nil {
		toSerialize["data"] = o.Data
	}
	if o.Message != nil {
		toSerialize["message"] = o.Message
	}
	return json.Marshal(toSerialize)
}

type NullableResultDocument struct {
	value *ResultDocument
	isSet bool
}

func (v NullableResultDocument) Get() *ResultDocument {
	return v.value
}

func (v *NullableResultDocument) Set(val *ResultDocument) {
	v.value = val
	v.isSet = true
}

func (v NullableResultDocument) IsSet() bool {
	return v.isSet
}

func (v *NullableResultDocument) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableResultDocument(val *ResultDocument) *NullableResultDocument {
	return &NullableResultDocument{value: val, isSet: true}
}

func (v NullableResultDocument) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableResultDocument) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
