/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// DocCacheRequest DocCacheRequest
type DocCacheRequest struct {
	ChunkList *[]DocChunk `json:"chunkList,omitempty"`
	FileUuid *string `json:"fileUuid,omitempty"`
}

// NewDocCacheRequest instantiates a new DocCacheRequest object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDocCacheRequest() *DocCacheRequest {
	this := DocCacheRequest{}
	return &this
}

// NewDocCacheRequestWithDefaults instantiates a new DocCacheRequest object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDocCacheRequestWithDefaults() *DocCacheRequest {
	this := DocCacheRequest{}
	return &this
}

// GetChunkList returns the ChunkList field value if set, zero value otherwise.
func (o *DocCacheRequest) GetChunkList() []DocChunk {
	if o == nil || o.ChunkList == nil {
		var ret []DocChunk
		return ret
	}
	return *o.ChunkList
}

// GetChunkListOk returns a tuple with the ChunkList field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocCacheRequest) GetChunkListOk() (*[]DocChunk, bool) {
	if o == nil || o.ChunkList == nil {
		return nil, false
	}
	return o.ChunkList, true
}

// HasChunkList returns a boolean if a field has been set.
func (o *DocCacheRequest) HasChunkList() bool {
	if o != nil && o.ChunkList != nil {
		return true
	}

	return false
}

// SetChunkList gets a reference to the given []DocChunk and assigns it to the ChunkList field.
func (o *DocCacheRequest) SetChunkList(v []DocChunk) {
	o.ChunkList = &v
}

// GetFileUuid returns the FileUuid field value if set, zero value otherwise.
func (o *DocCacheRequest) GetFileUuid() string {
	if o == nil || o.FileUuid == nil {
		var ret string
		return ret
	}
	return *o.FileUuid
}

// GetFileUuidOk returns a tuple with the FileUuid field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocCacheRequest) GetFileUuidOk() (*string, bool) {
	if o == nil || o.FileUuid == nil {
		return nil, false
	}
	return o.FileUuid, true
}

// HasFileUuid returns a boolean if a field has been set.
func (o *DocCacheRequest) HasFileUuid() bool {
	if o != nil && o.FileUuid != nil {
		return true
	}

	return false
}

// SetFileUuid gets a reference to the given string and assigns it to the FileUuid field.
func (o *DocCacheRequest) SetFileUuid(v string) {
	o.FileUuid = &v
}

func (o DocCacheRequest) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.ChunkList != nil {
		toSerialize["chunkList"] = o.ChunkList
	}
	if o.FileUuid != nil {
		toSerialize["fileUuid"] = o.FileUuid
	}
	return json.Marshal(toSerialize)
}

type NullableDocCacheRequest struct {
	value *DocCacheRequest
	isSet bool
}

func (v NullableDocCacheRequest) Get() *DocCacheRequest {
	return v.value
}

func (v *NullableDocCacheRequest) Set(val *DocCacheRequest) {
	v.value = val
	v.isSet = true
}

func (v NullableDocCacheRequest) IsSet() bool {
	return v.isSet
}

func (v *NullableDocCacheRequest) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDocCacheRequest(val *DocCacheRequest) *NullableDocCacheRequest {
	return &NullableDocCacheRequest{value: val, isSet: true}
}

func (v NullableDocCacheRequest) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDocCacheRequest) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
