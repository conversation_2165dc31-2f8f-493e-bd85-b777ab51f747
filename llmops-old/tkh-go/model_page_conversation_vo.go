/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// PageConversationVo Page«ConversationVO»
type PageConversationVo struct {
	CountId *string `json:"countId,omitempty"`
	Current *int64 `json:"current,omitempty"`
	MaxLimit *int64 `json:"maxLimit,omitempty"`
	OptimizeCountSql *bool `json:"optimizeCountSql,omitempty"`
	Orders *[]OrderItem `json:"orders,omitempty"`
	Pages *int64 `json:"pages,omitempty"`
	Records *[]ConversationVo `json:"records,omitempty"`
	SearchCount *bool `json:"searchCount,omitempty"`
	Size *int64 `json:"size,omitempty"`
	Total *int64 `json:"total,omitempty"`
}

// NewPageConversationVo instantiates a new PageConversationVo object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewPageConversationVo() *PageConversationVo {
	this := PageConversationVo{}
	return &this
}

// NewPageConversationVoWithDefaults instantiates a new PageConversationVo object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewPageConversationVoWithDefaults() *PageConversationVo {
	this := PageConversationVo{}
	return &this
}

// GetCountId returns the CountId field value if set, zero value otherwise.
func (o *PageConversationVo) GetCountId() string {
	if o == nil || o.CountId == nil {
		var ret string
		return ret
	}
	return *o.CountId
}

// GetCountIdOk returns a tuple with the CountId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *PageConversationVo) GetCountIdOk() (*string, bool) {
	if o == nil || o.CountId == nil {
		return nil, false
	}
	return o.CountId, true
}

// HasCountId returns a boolean if a field has been set.
func (o *PageConversationVo) HasCountId() bool {
	if o != nil && o.CountId != nil {
		return true
	}

	return false
}

// SetCountId gets a reference to the given string and assigns it to the CountId field.
func (o *PageConversationVo) SetCountId(v string) {
	o.CountId = &v
}

// GetCurrent returns the Current field value if set, zero value otherwise.
func (o *PageConversationVo) GetCurrent() int64 {
	if o == nil || o.Current == nil {
		var ret int64
		return ret
	}
	return *o.Current
}

// GetCurrentOk returns a tuple with the Current field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *PageConversationVo) GetCurrentOk() (*int64, bool) {
	if o == nil || o.Current == nil {
		return nil, false
	}
	return o.Current, true
}

// HasCurrent returns a boolean if a field has been set.
func (o *PageConversationVo) HasCurrent() bool {
	if o != nil && o.Current != nil {
		return true
	}

	return false
}

// SetCurrent gets a reference to the given int64 and assigns it to the Current field.
func (o *PageConversationVo) SetCurrent(v int64) {
	o.Current = &v
}

// GetMaxLimit returns the MaxLimit field value if set, zero value otherwise.
func (o *PageConversationVo) GetMaxLimit() int64 {
	if o == nil || o.MaxLimit == nil {
		var ret int64
		return ret
	}
	return *o.MaxLimit
}

// GetMaxLimitOk returns a tuple with the MaxLimit field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *PageConversationVo) GetMaxLimitOk() (*int64, bool) {
	if o == nil || o.MaxLimit == nil {
		return nil, false
	}
	return o.MaxLimit, true
}

// HasMaxLimit returns a boolean if a field has been set.
func (o *PageConversationVo) HasMaxLimit() bool {
	if o != nil && o.MaxLimit != nil {
		return true
	}

	return false
}

// SetMaxLimit gets a reference to the given int64 and assigns it to the MaxLimit field.
func (o *PageConversationVo) SetMaxLimit(v int64) {
	o.MaxLimit = &v
}

// GetOptimizeCountSql returns the OptimizeCountSql field value if set, zero value otherwise.
func (o *PageConversationVo) GetOptimizeCountSql() bool {
	if o == nil || o.OptimizeCountSql == nil {
		var ret bool
		return ret
	}
	return *o.OptimizeCountSql
}

// GetOptimizeCountSqlOk returns a tuple with the OptimizeCountSql field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *PageConversationVo) GetOptimizeCountSqlOk() (*bool, bool) {
	if o == nil || o.OptimizeCountSql == nil {
		return nil, false
	}
	return o.OptimizeCountSql, true
}

// HasOptimizeCountSql returns a boolean if a field has been set.
func (o *PageConversationVo) HasOptimizeCountSql() bool {
	if o != nil && o.OptimizeCountSql != nil {
		return true
	}

	return false
}

// SetOptimizeCountSql gets a reference to the given bool and assigns it to the OptimizeCountSql field.
func (o *PageConversationVo) SetOptimizeCountSql(v bool) {
	o.OptimizeCountSql = &v
}

// GetOrders returns the Orders field value if set, zero value otherwise.
func (o *PageConversationVo) GetOrders() []OrderItem {
	if o == nil || o.Orders == nil {
		var ret []OrderItem
		return ret
	}
	return *o.Orders
}

// GetOrdersOk returns a tuple with the Orders field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *PageConversationVo) GetOrdersOk() (*[]OrderItem, bool) {
	if o == nil || o.Orders == nil {
		return nil, false
	}
	return o.Orders, true
}

// HasOrders returns a boolean if a field has been set.
func (o *PageConversationVo) HasOrders() bool {
	if o != nil && o.Orders != nil {
		return true
	}

	return false
}

// SetOrders gets a reference to the given []OrderItem and assigns it to the Orders field.
func (o *PageConversationVo) SetOrders(v []OrderItem) {
	o.Orders = &v
}

// GetPages returns the Pages field value if set, zero value otherwise.
func (o *PageConversationVo) GetPages() int64 {
	if o == nil || o.Pages == nil {
		var ret int64
		return ret
	}
	return *o.Pages
}

// GetPagesOk returns a tuple with the Pages field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *PageConversationVo) GetPagesOk() (*int64, bool) {
	if o == nil || o.Pages == nil {
		return nil, false
	}
	return o.Pages, true
}

// HasPages returns a boolean if a field has been set.
func (o *PageConversationVo) HasPages() bool {
	if o != nil && o.Pages != nil {
		return true
	}

	return false
}

// SetPages gets a reference to the given int64 and assigns it to the Pages field.
func (o *PageConversationVo) SetPages(v int64) {
	o.Pages = &v
}

// GetRecords returns the Records field value if set, zero value otherwise.
func (o *PageConversationVo) GetRecords() []ConversationVo {
	if o == nil || o.Records == nil {
		var ret []ConversationVo
		return ret
	}
	return *o.Records
}

// GetRecordsOk returns a tuple with the Records field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *PageConversationVo) GetRecordsOk() (*[]ConversationVo, bool) {
	if o == nil || o.Records == nil {
		return nil, false
	}
	return o.Records, true
}

// HasRecords returns a boolean if a field has been set.
func (o *PageConversationVo) HasRecords() bool {
	if o != nil && o.Records != nil {
		return true
	}

	return false
}

// SetRecords gets a reference to the given []ConversationVo and assigns it to the Records field.
func (o *PageConversationVo) SetRecords(v []ConversationVo) {
	o.Records = &v
}

// GetSearchCount returns the SearchCount field value if set, zero value otherwise.
func (o *PageConversationVo) GetSearchCount() bool {
	if o == nil || o.SearchCount == nil {
		var ret bool
		return ret
	}
	return *o.SearchCount
}

// GetSearchCountOk returns a tuple with the SearchCount field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *PageConversationVo) GetSearchCountOk() (*bool, bool) {
	if o == nil || o.SearchCount == nil {
		return nil, false
	}
	return o.SearchCount, true
}

// HasSearchCount returns a boolean if a field has been set.
func (o *PageConversationVo) HasSearchCount() bool {
	if o != nil && o.SearchCount != nil {
		return true
	}

	return false
}

// SetSearchCount gets a reference to the given bool and assigns it to the SearchCount field.
func (o *PageConversationVo) SetSearchCount(v bool) {
	o.SearchCount = &v
}

// GetSize returns the Size field value if set, zero value otherwise.
func (o *PageConversationVo) GetSize() int64 {
	if o == nil || o.Size == nil {
		var ret int64
		return ret
	}
	return *o.Size
}

// GetSizeOk returns a tuple with the Size field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *PageConversationVo) GetSizeOk() (*int64, bool) {
	if o == nil || o.Size == nil {
		return nil, false
	}
	return o.Size, true
}

// HasSize returns a boolean if a field has been set.
func (o *PageConversationVo) HasSize() bool {
	if o != nil && o.Size != nil {
		return true
	}

	return false
}

// SetSize gets a reference to the given int64 and assigns it to the Size field.
func (o *PageConversationVo) SetSize(v int64) {
	o.Size = &v
}

// GetTotal returns the Total field value if set, zero value otherwise.
func (o *PageConversationVo) GetTotal() int64 {
	if o == nil || o.Total == nil {
		var ret int64
		return ret
	}
	return *o.Total
}

// GetTotalOk returns a tuple with the Total field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *PageConversationVo) GetTotalOk() (*int64, bool) {
	if o == nil || o.Total == nil {
		return nil, false
	}
	return o.Total, true
}

// HasTotal returns a boolean if a field has been set.
func (o *PageConversationVo) HasTotal() bool {
	if o != nil && o.Total != nil {
		return true
	}

	return false
}

// SetTotal gets a reference to the given int64 and assigns it to the Total field.
func (o *PageConversationVo) SetTotal(v int64) {
	o.Total = &v
}

func (o PageConversationVo) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.CountId != nil {
		toSerialize["countId"] = o.CountId
	}
	if o.Current != nil {
		toSerialize["current"] = o.Current
	}
	if o.MaxLimit != nil {
		toSerialize["maxLimit"] = o.MaxLimit
	}
	if o.OptimizeCountSql != nil {
		toSerialize["optimizeCountSql"] = o.OptimizeCountSql
	}
	if o.Orders != nil {
		toSerialize["orders"] = o.Orders
	}
	if o.Pages != nil {
		toSerialize["pages"] = o.Pages
	}
	if o.Records != nil {
		toSerialize["records"] = o.Records
	}
	if o.SearchCount != nil {
		toSerialize["searchCount"] = o.SearchCount
	}
	if o.Size != nil {
		toSerialize["size"] = o.Size
	}
	if o.Total != nil {
		toSerialize["total"] = o.Total
	}
	return json.Marshal(toSerialize)
}

type NullablePageConversationVo struct {
	value *PageConversationVo
	isSet bool
}

func (v NullablePageConversationVo) Get() *PageConversationVo {
	return v.value
}

func (v *NullablePageConversationVo) Set(val *PageConversationVo) {
	v.value = val
	v.isSet = true
}

func (v NullablePageConversationVo) IsSet() bool {
	return v.isSet
}

func (v *NullablePageConversationVo) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullablePageConversationVo(val *PageConversationVo) *NullablePageConversationVo {
	return &NullablePageConversationVo{value: val, isSet: true}
}

func (v NullablePageConversationVo) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullablePageConversationVo) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
