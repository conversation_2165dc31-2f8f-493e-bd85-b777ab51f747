/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// DtoResultCaptcha DtoResultCaptcha
type DtoResultCaptcha struct {
	Base64Img *string `json:"base64Img,omitempty"`
	Key *string `json:"key,omitempty"`
}

// NewDtoResultCaptcha instantiates a new DtoResultCaptcha object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDtoResultCaptcha() *DtoResultCaptcha {
	this := DtoResultCaptcha{}
	return &this
}

// NewDtoResultCaptchaWithDefaults instantiates a new DtoResultCaptcha object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDtoResultCaptchaWithDefaults() *DtoResultCaptcha {
	this := DtoResultCaptcha{}
	return &this
}

// GetBase64Img returns the Base64Img field value if set, zero value otherwise.
func (o *DtoResultCaptcha) GetBase64Img() string {
	if o == nil || o.Base64Img == nil {
		var ret string
		return ret
	}
	return *o.Base64Img
}

// GetBase64ImgOk returns a tuple with the Base64Img field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DtoResultCaptcha) GetBase64ImgOk() (*string, bool) {
	if o == nil || o.Base64Img == nil {
		return nil, false
	}
	return o.Base64Img, true
}

// HasBase64Img returns a boolean if a field has been set.
func (o *DtoResultCaptcha) HasBase64Img() bool {
	if o != nil && o.Base64Img != nil {
		return true
	}

	return false
}

// SetBase64Img gets a reference to the given string and assigns it to the Base64Img field.
func (o *DtoResultCaptcha) SetBase64Img(v string) {
	o.Base64Img = &v
}

// GetKey returns the Key field value if set, zero value otherwise.
func (o *DtoResultCaptcha) GetKey() string {
	if o == nil || o.Key == nil {
		var ret string
		return ret
	}
	return *o.Key
}

// GetKeyOk returns a tuple with the Key field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DtoResultCaptcha) GetKeyOk() (*string, bool) {
	if o == nil || o.Key == nil {
		return nil, false
	}
	return o.Key, true
}

// HasKey returns a boolean if a field has been set.
func (o *DtoResultCaptcha) HasKey() bool {
	if o != nil && o.Key != nil {
		return true
	}

	return false
}

// SetKey gets a reference to the given string and assigns it to the Key field.
func (o *DtoResultCaptcha) SetKey(v string) {
	o.Key = &v
}

func (o DtoResultCaptcha) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Base64Img != nil {
		toSerialize["base64Img"] = o.Base64Img
	}
	if o.Key != nil {
		toSerialize["key"] = o.Key
	}
	return json.Marshal(toSerialize)
}

type NullableDtoResultCaptcha struct {
	value *DtoResultCaptcha
	isSet bool
}

func (v NullableDtoResultCaptcha) Get() *DtoResultCaptcha {
	return v.value
}

func (v *NullableDtoResultCaptcha) Set(val *DtoResultCaptcha) {
	v.value = val
	v.isSet = true
}

func (v NullableDtoResultCaptcha) IsSet() bool {
	return v.isSet
}

func (v *NullableDtoResultCaptcha) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDtoResultCaptcha(val *DtoResultCaptcha) *NullableDtoResultCaptcha {
	return &NullableDtoResultCaptcha{value: val, isSet: true}
}

func (v NullableDtoResultCaptcha) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDtoResultCaptcha) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
