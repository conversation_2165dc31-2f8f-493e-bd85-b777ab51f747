/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// ConversationVo ConversationVO
type ConversationVo struct {
	ConversationId *string `json:"conversationId,omitempty"`
	CreateTime *string `json:"createTime,omitempty"`
	SourceIds *string `json:"sourceIds,omitempty"`
	Title *string `json:"title,omitempty"`
}

// NewConversationVo instantiates a new ConversationVo object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewConversationVo() *ConversationVo {
	this := ConversationVo{}
	return &this
}

// NewConversationVoWithDefaults instantiates a new ConversationVo object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewConversationVoWithDefaults() *ConversationVo {
	this := ConversationVo{}
	return &this
}

// GetConversationId returns the ConversationId field value if set, zero value otherwise.
func (o *ConversationVo) GetConversationId() string {
	if o == nil || o.ConversationId == nil {
		var ret string
		return ret
	}
	return *o.ConversationId
}

// GetConversationIdOk returns a tuple with the ConversationId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ConversationVo) GetConversationIdOk() (*string, bool) {
	if o == nil || o.ConversationId == nil {
		return nil, false
	}
	return o.ConversationId, true
}

// HasConversationId returns a boolean if a field has been set.
func (o *ConversationVo) HasConversationId() bool {
	if o != nil && o.ConversationId != nil {
		return true
	}

	return false
}

// SetConversationId gets a reference to the given string and assigns it to the ConversationId field.
func (o *ConversationVo) SetConversationId(v string) {
	o.ConversationId = &v
}

// GetCreateTime returns the CreateTime field value if set, zero value otherwise.
func (o *ConversationVo) GetCreateTime() string {
	if o == nil || o.CreateTime == nil {
		var ret string
		return ret
	}
	return *o.CreateTime
}

// GetCreateTimeOk returns a tuple with the CreateTime field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ConversationVo) GetCreateTimeOk() (*string, bool) {
	if o == nil || o.CreateTime == nil {
		return nil, false
	}
	return o.CreateTime, true
}

// HasCreateTime returns a boolean if a field has been set.
func (o *ConversationVo) HasCreateTime() bool {
	if o != nil && o.CreateTime != nil {
		return true
	}

	return false
}

// SetCreateTime gets a reference to the given string and assigns it to the CreateTime field.
func (o *ConversationVo) SetCreateTime(v string) {
	o.CreateTime = &v
}

// GetSourceIds returns the SourceIds field value if set, zero value otherwise.
func (o *ConversationVo) GetSourceIds() string {
	if o == nil || o.SourceIds == nil {
		var ret string
		return ret
	}
	return *o.SourceIds
}

// GetSourceIdsOk returns a tuple with the SourceIds field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ConversationVo) GetSourceIdsOk() (*string, bool) {
	if o == nil || o.SourceIds == nil {
		return nil, false
	}
	return o.SourceIds, true
}

// HasSourceIds returns a boolean if a field has been set.
func (o *ConversationVo) HasSourceIds() bool {
	if o != nil && o.SourceIds != nil {
		return true
	}

	return false
}

// SetSourceIds gets a reference to the given string and assigns it to the SourceIds field.
func (o *ConversationVo) SetSourceIds(v string) {
	o.SourceIds = &v
}

// GetTitle returns the Title field value if set, zero value otherwise.
func (o *ConversationVo) GetTitle() string {
	if o == nil || o.Title == nil {
		var ret string
		return ret
	}
	return *o.Title
}

// GetTitleOk returns a tuple with the Title field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ConversationVo) GetTitleOk() (*string, bool) {
	if o == nil || o.Title == nil {
		return nil, false
	}
	return o.Title, true
}

// HasTitle returns a boolean if a field has been set.
func (o *ConversationVo) HasTitle() bool {
	if o != nil && o.Title != nil {
		return true
	}

	return false
}

// SetTitle gets a reference to the given string and assigns it to the Title field.
func (o *ConversationVo) SetTitle(v string) {
	o.Title = &v
}

func (o ConversationVo) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.ConversationId != nil {
		toSerialize["conversationId"] = o.ConversationId
	}
	if o.CreateTime != nil {
		toSerialize["createTime"] = o.CreateTime
	}
	if o.SourceIds != nil {
		toSerialize["sourceIds"] = o.SourceIds
	}
	if o.Title != nil {
		toSerialize["title"] = o.Title
	}
	return json.Marshal(toSerialize)
}

type NullableConversationVo struct {
	value *ConversationVo
	isSet bool
}

func (v NullableConversationVo) Get() *ConversationVo {
	return v.value
}

func (v *NullableConversationVo) Set(val *ConversationVo) {
	v.value = val
	v.isSet = true
}

func (v NullableConversationVo) IsSet() bool {
	return v.isSet
}

func (v *NullableConversationVo) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableConversationVo(val *ConversationVo) *NullableConversationVo {
	return &NullableConversationVo{value: val, isSet: true}
}

func (v NullableConversationVo) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableConversationVo) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
