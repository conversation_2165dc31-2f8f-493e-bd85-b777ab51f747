/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// DocChunk DocChunk
type DocChunk struct {
	Content *string `json:"content,omitempty"`
	Metadata *ChunkMetadata `json:"metadata,omitempty"`
}

// NewDocChunk instantiates a new DocChunk object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDocChunk() *DocChunk {
	this := DocChunk{}
	return &this
}

// NewDocChunkWithDefaults instantiates a new DocChunk object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDocChunkWithDefaults() *DocChunk {
	this := DocChunk{}
	return &this
}

// GetContent returns the Content field value if set, zero value otherwise.
func (o *DocChunk) GetContent() string {
	if o == nil || o.Content == nil {
		var ret string
		return ret
	}
	return *o.Content
}

// GetContentOk returns a tuple with the Content field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocChunk) GetContentOk() (*string, bool) {
	if o == nil || o.Content == nil {
		return nil, false
	}
	return o.Content, true
}

// HasContent returns a boolean if a field has been set.
func (o *DocChunk) HasContent() bool {
	if o != nil && o.Content != nil {
		return true
	}

	return false
}

// SetContent gets a reference to the given string and assigns it to the Content field.
func (o *DocChunk) SetContent(v string) {
	o.Content = &v
}

// GetMetadata returns the Metadata field value if set, zero value otherwise.
func (o *DocChunk) GetMetadata() ChunkMetadata {
	if o == nil || o.Metadata == nil {
		var ret ChunkMetadata
		return ret
	}
	return *o.Metadata
}

// GetMetadataOk returns a tuple with the Metadata field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocChunk) GetMetadataOk() (*ChunkMetadata, bool) {
	if o == nil || o.Metadata == nil {
		return nil, false
	}
	return o.Metadata, true
}

// HasMetadata returns a boolean if a field has been set.
func (o *DocChunk) HasMetadata() bool {
	if o != nil && o.Metadata != nil {
		return true
	}

	return false
}

// SetMetadata gets a reference to the given ChunkMetadata and assigns it to the Metadata field.
func (o *DocChunk) SetMetadata(v ChunkMetadata) {
	o.Metadata = &v
}

func (o DocChunk) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Content != nil {
		toSerialize["content"] = o.Content
	}
	if o.Metadata != nil {
		toSerialize["metadata"] = o.Metadata
	}
	return json.Marshal(toSerialize)
}

type NullableDocChunk struct {
	value *DocChunk
	isSet bool
}

func (v NullableDocChunk) Get() *DocChunk {
	return v.value
}

func (v *NullableDocChunk) Set(val *DocChunk) {
	v.value = val
	v.isSet = true
}

func (v NullableDocChunk) IsSet() bool {
	return v.isSet
}

func (v *NullableDocChunk) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDocChunk(val *DocChunk) *NullableDocChunk {
	return &NullableDocChunk{value: val, isSet: true}
}

func (v NullableDocChunk) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDocChunk) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
