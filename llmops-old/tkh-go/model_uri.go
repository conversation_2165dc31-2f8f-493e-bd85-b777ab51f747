/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// Uri URI
type Uri struct {
	Absolute *bool `json:"absolute,omitempty"`
	Authority *string `json:"authority,omitempty"`
	Fragment *string `json:"fragment,omitempty"`
	Host *string `json:"host,omitempty"`
	Opaque *bool `json:"opaque,omitempty"`
	Path *string `json:"path,omitempty"`
	Port *int32 `json:"port,omitempty"`
	Query *string `json:"query,omitempty"`
	RawAuthority *string `json:"rawAuthority,omitempty"`
	RawFragment *string `json:"rawFragment,omitempty"`
	RawPath *string `json:"rawPath,omitempty"`
	RawQuery *string `json:"rawQuery,omitempty"`
	RawSchemeSpecificPart *string `json:"rawSchemeSpecificPart,omitempty"`
	RawUserInfo *string `json:"rawUserInfo,omitempty"`
	Scheme *string `json:"scheme,omitempty"`
	SchemeSpecificPart *string `json:"schemeSpecificPart,omitempty"`
	UserInfo *string `json:"userInfo,omitempty"`
}

// NewUri instantiates a new Uri object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewUri() *Uri {
	this := Uri{}
	return &this
}

// NewUriWithDefaults instantiates a new Uri object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewUriWithDefaults() *Uri {
	this := Uri{}
	return &this
}

// GetAbsolute returns the Absolute field value if set, zero value otherwise.
func (o *Uri) GetAbsolute() bool {
	if o == nil || o.Absolute == nil {
		var ret bool
		return ret
	}
	return *o.Absolute
}

// GetAbsoluteOk returns a tuple with the Absolute field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Uri) GetAbsoluteOk() (*bool, bool) {
	if o == nil || o.Absolute == nil {
		return nil, false
	}
	return o.Absolute, true
}

// HasAbsolute returns a boolean if a field has been set.
func (o *Uri) HasAbsolute() bool {
	if o != nil && o.Absolute != nil {
		return true
	}

	return false
}

// SetAbsolute gets a reference to the given bool and assigns it to the Absolute field.
func (o *Uri) SetAbsolute(v bool) {
	o.Absolute = &v
}

// GetAuthority returns the Authority field value if set, zero value otherwise.
func (o *Uri) GetAuthority() string {
	if o == nil || o.Authority == nil {
		var ret string
		return ret
	}
	return *o.Authority
}

// GetAuthorityOk returns a tuple with the Authority field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Uri) GetAuthorityOk() (*string, bool) {
	if o == nil || o.Authority == nil {
		return nil, false
	}
	return o.Authority, true
}

// HasAuthority returns a boolean if a field has been set.
func (o *Uri) HasAuthority() bool {
	if o != nil && o.Authority != nil {
		return true
	}

	return false
}

// SetAuthority gets a reference to the given string and assigns it to the Authority field.
func (o *Uri) SetAuthority(v string) {
	o.Authority = &v
}

// GetFragment returns the Fragment field value if set, zero value otherwise.
func (o *Uri) GetFragment() string {
	if o == nil || o.Fragment == nil {
		var ret string
		return ret
	}
	return *o.Fragment
}

// GetFragmentOk returns a tuple with the Fragment field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Uri) GetFragmentOk() (*string, bool) {
	if o == nil || o.Fragment == nil {
		return nil, false
	}
	return o.Fragment, true
}

// HasFragment returns a boolean if a field has been set.
func (o *Uri) HasFragment() bool {
	if o != nil && o.Fragment != nil {
		return true
	}

	return false
}

// SetFragment gets a reference to the given string and assigns it to the Fragment field.
func (o *Uri) SetFragment(v string) {
	o.Fragment = &v
}

// GetHost returns the Host field value if set, zero value otherwise.
func (o *Uri) GetHost() string {
	if o == nil || o.Host == nil {
		var ret string
		return ret
	}
	return *o.Host
}

// GetHostOk returns a tuple with the Host field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Uri) GetHostOk() (*string, bool) {
	if o == nil || o.Host == nil {
		return nil, false
	}
	return o.Host, true
}

// HasHost returns a boolean if a field has been set.
func (o *Uri) HasHost() bool {
	if o != nil && o.Host != nil {
		return true
	}

	return false
}

// SetHost gets a reference to the given string and assigns it to the Host field.
func (o *Uri) SetHost(v string) {
	o.Host = &v
}

// GetOpaque returns the Opaque field value if set, zero value otherwise.
func (o *Uri) GetOpaque() bool {
	if o == nil || o.Opaque == nil {
		var ret bool
		return ret
	}
	return *o.Opaque
}

// GetOpaqueOk returns a tuple with the Opaque field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Uri) GetOpaqueOk() (*bool, bool) {
	if o == nil || o.Opaque == nil {
		return nil, false
	}
	return o.Opaque, true
}

// HasOpaque returns a boolean if a field has been set.
func (o *Uri) HasOpaque() bool {
	if o != nil && o.Opaque != nil {
		return true
	}

	return false
}

// SetOpaque gets a reference to the given bool and assigns it to the Opaque field.
func (o *Uri) SetOpaque(v bool) {
	o.Opaque = &v
}

// GetPath returns the Path field value if set, zero value otherwise.
func (o *Uri) GetPath() string {
	if o == nil || o.Path == nil {
		var ret string
		return ret
	}
	return *o.Path
}

// GetPathOk returns a tuple with the Path field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Uri) GetPathOk() (*string, bool) {
	if o == nil || o.Path == nil {
		return nil, false
	}
	return o.Path, true
}

// HasPath returns a boolean if a field has been set.
func (o *Uri) HasPath() bool {
	if o != nil && o.Path != nil {
		return true
	}

	return false
}

// SetPath gets a reference to the given string and assigns it to the Path field.
func (o *Uri) SetPath(v string) {
	o.Path = &v
}

// GetPort returns the Port field value if set, zero value otherwise.
func (o *Uri) GetPort() int32 {
	if o == nil || o.Port == nil {
		var ret int32
		return ret
	}
	return *o.Port
}

// GetPortOk returns a tuple with the Port field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Uri) GetPortOk() (*int32, bool) {
	if o == nil || o.Port == nil {
		return nil, false
	}
	return o.Port, true
}

// HasPort returns a boolean if a field has been set.
func (o *Uri) HasPort() bool {
	if o != nil && o.Port != nil {
		return true
	}

	return false
}

// SetPort gets a reference to the given int32 and assigns it to the Port field.
func (o *Uri) SetPort(v int32) {
	o.Port = &v
}

// GetQuery returns the Query field value if set, zero value otherwise.
func (o *Uri) GetQuery() string {
	if o == nil || o.Query == nil {
		var ret string
		return ret
	}
	return *o.Query
}

// GetQueryOk returns a tuple with the Query field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Uri) GetQueryOk() (*string, bool) {
	if o == nil || o.Query == nil {
		return nil, false
	}
	return o.Query, true
}

// HasQuery returns a boolean if a field has been set.
func (o *Uri) HasQuery() bool {
	if o != nil && o.Query != nil {
		return true
	}

	return false
}

// SetQuery gets a reference to the given string and assigns it to the Query field.
func (o *Uri) SetQuery(v string) {
	o.Query = &v
}

// GetRawAuthority returns the RawAuthority field value if set, zero value otherwise.
func (o *Uri) GetRawAuthority() string {
	if o == nil || o.RawAuthority == nil {
		var ret string
		return ret
	}
	return *o.RawAuthority
}

// GetRawAuthorityOk returns a tuple with the RawAuthority field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Uri) GetRawAuthorityOk() (*string, bool) {
	if o == nil || o.RawAuthority == nil {
		return nil, false
	}
	return o.RawAuthority, true
}

// HasRawAuthority returns a boolean if a field has been set.
func (o *Uri) HasRawAuthority() bool {
	if o != nil && o.RawAuthority != nil {
		return true
	}

	return false
}

// SetRawAuthority gets a reference to the given string and assigns it to the RawAuthority field.
func (o *Uri) SetRawAuthority(v string) {
	o.RawAuthority = &v
}

// GetRawFragment returns the RawFragment field value if set, zero value otherwise.
func (o *Uri) GetRawFragment() string {
	if o == nil || o.RawFragment == nil {
		var ret string
		return ret
	}
	return *o.RawFragment
}

// GetRawFragmentOk returns a tuple with the RawFragment field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Uri) GetRawFragmentOk() (*string, bool) {
	if o == nil || o.RawFragment == nil {
		return nil, false
	}
	return o.RawFragment, true
}

// HasRawFragment returns a boolean if a field has been set.
func (o *Uri) HasRawFragment() bool {
	if o != nil && o.RawFragment != nil {
		return true
	}

	return false
}

// SetRawFragment gets a reference to the given string and assigns it to the RawFragment field.
func (o *Uri) SetRawFragment(v string) {
	o.RawFragment = &v
}

// GetRawPath returns the RawPath field value if set, zero value otherwise.
func (o *Uri) GetRawPath() string {
	if o == nil || o.RawPath == nil {
		var ret string
		return ret
	}
	return *o.RawPath
}

// GetRawPathOk returns a tuple with the RawPath field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Uri) GetRawPathOk() (*string, bool) {
	if o == nil || o.RawPath == nil {
		return nil, false
	}
	return o.RawPath, true
}

// HasRawPath returns a boolean if a field has been set.
func (o *Uri) HasRawPath() bool {
	if o != nil && o.RawPath != nil {
		return true
	}

	return false
}

// SetRawPath gets a reference to the given string and assigns it to the RawPath field.
func (o *Uri) SetRawPath(v string) {
	o.RawPath = &v
}

// GetRawQuery returns the RawQuery field value if set, zero value otherwise.
func (o *Uri) GetRawQuery() string {
	if o == nil || o.RawQuery == nil {
		var ret string
		return ret
	}
	return *o.RawQuery
}

// GetRawQueryOk returns a tuple with the RawQuery field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Uri) GetRawQueryOk() (*string, bool) {
	if o == nil || o.RawQuery == nil {
		return nil, false
	}
	return o.RawQuery, true
}

// HasRawQuery returns a boolean if a field has been set.
func (o *Uri) HasRawQuery() bool {
	if o != nil && o.RawQuery != nil {
		return true
	}

	return false
}

// SetRawQuery gets a reference to the given string and assigns it to the RawQuery field.
func (o *Uri) SetRawQuery(v string) {
	o.RawQuery = &v
}

// GetRawSchemeSpecificPart returns the RawSchemeSpecificPart field value if set, zero value otherwise.
func (o *Uri) GetRawSchemeSpecificPart() string {
	if o == nil || o.RawSchemeSpecificPart == nil {
		var ret string
		return ret
	}
	return *o.RawSchemeSpecificPart
}

// GetRawSchemeSpecificPartOk returns a tuple with the RawSchemeSpecificPart field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Uri) GetRawSchemeSpecificPartOk() (*string, bool) {
	if o == nil || o.RawSchemeSpecificPart == nil {
		return nil, false
	}
	return o.RawSchemeSpecificPart, true
}

// HasRawSchemeSpecificPart returns a boolean if a field has been set.
func (o *Uri) HasRawSchemeSpecificPart() bool {
	if o != nil && o.RawSchemeSpecificPart != nil {
		return true
	}

	return false
}

// SetRawSchemeSpecificPart gets a reference to the given string and assigns it to the RawSchemeSpecificPart field.
func (o *Uri) SetRawSchemeSpecificPart(v string) {
	o.RawSchemeSpecificPart = &v
}

// GetRawUserInfo returns the RawUserInfo field value if set, zero value otherwise.
func (o *Uri) GetRawUserInfo() string {
	if o == nil || o.RawUserInfo == nil {
		var ret string
		return ret
	}
	return *o.RawUserInfo
}

// GetRawUserInfoOk returns a tuple with the RawUserInfo field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Uri) GetRawUserInfoOk() (*string, bool) {
	if o == nil || o.RawUserInfo == nil {
		return nil, false
	}
	return o.RawUserInfo, true
}

// HasRawUserInfo returns a boolean if a field has been set.
func (o *Uri) HasRawUserInfo() bool {
	if o != nil && o.RawUserInfo != nil {
		return true
	}

	return false
}

// SetRawUserInfo gets a reference to the given string and assigns it to the RawUserInfo field.
func (o *Uri) SetRawUserInfo(v string) {
	o.RawUserInfo = &v
}

// GetScheme returns the Scheme field value if set, zero value otherwise.
func (o *Uri) GetScheme() string {
	if o == nil || o.Scheme == nil {
		var ret string
		return ret
	}
	return *o.Scheme
}

// GetSchemeOk returns a tuple with the Scheme field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Uri) GetSchemeOk() (*string, bool) {
	if o == nil || o.Scheme == nil {
		return nil, false
	}
	return o.Scheme, true
}

// HasScheme returns a boolean if a field has been set.
func (o *Uri) HasScheme() bool {
	if o != nil && o.Scheme != nil {
		return true
	}

	return false
}

// SetScheme gets a reference to the given string and assigns it to the Scheme field.
func (o *Uri) SetScheme(v string) {
	o.Scheme = &v
}

// GetSchemeSpecificPart returns the SchemeSpecificPart field value if set, zero value otherwise.
func (o *Uri) GetSchemeSpecificPart() string {
	if o == nil || o.SchemeSpecificPart == nil {
		var ret string
		return ret
	}
	return *o.SchemeSpecificPart
}

// GetSchemeSpecificPartOk returns a tuple with the SchemeSpecificPart field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Uri) GetSchemeSpecificPartOk() (*string, bool) {
	if o == nil || o.SchemeSpecificPart == nil {
		return nil, false
	}
	return o.SchemeSpecificPart, true
}

// HasSchemeSpecificPart returns a boolean if a field has been set.
func (o *Uri) HasSchemeSpecificPart() bool {
	if o != nil && o.SchemeSpecificPart != nil {
		return true
	}

	return false
}

// SetSchemeSpecificPart gets a reference to the given string and assigns it to the SchemeSpecificPart field.
func (o *Uri) SetSchemeSpecificPart(v string) {
	o.SchemeSpecificPart = &v
}

// GetUserInfo returns the UserInfo field value if set, zero value otherwise.
func (o *Uri) GetUserInfo() string {
	if o == nil || o.UserInfo == nil {
		var ret string
		return ret
	}
	return *o.UserInfo
}

// GetUserInfoOk returns a tuple with the UserInfo field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Uri) GetUserInfoOk() (*string, bool) {
	if o == nil || o.UserInfo == nil {
		return nil, false
	}
	return o.UserInfo, true
}

// HasUserInfo returns a boolean if a field has been set.
func (o *Uri) HasUserInfo() bool {
	if o != nil && o.UserInfo != nil {
		return true
	}

	return false
}

// SetUserInfo gets a reference to the given string and assigns it to the UserInfo field.
func (o *Uri) SetUserInfo(v string) {
	o.UserInfo = &v
}

func (o Uri) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Absolute != nil {
		toSerialize["absolute"] = o.Absolute
	}
	if o.Authority != nil {
		toSerialize["authority"] = o.Authority
	}
	if o.Fragment != nil {
		toSerialize["fragment"] = o.Fragment
	}
	if o.Host != nil {
		toSerialize["host"] = o.Host
	}
	if o.Opaque != nil {
		toSerialize["opaque"] = o.Opaque
	}
	if o.Path != nil {
		toSerialize["path"] = o.Path
	}
	if o.Port != nil {
		toSerialize["port"] = o.Port
	}
	if o.Query != nil {
		toSerialize["query"] = o.Query
	}
	if o.RawAuthority != nil {
		toSerialize["rawAuthority"] = o.RawAuthority
	}
	if o.RawFragment != nil {
		toSerialize["rawFragment"] = o.RawFragment
	}
	if o.RawPath != nil {
		toSerialize["rawPath"] = o.RawPath
	}
	if o.RawQuery != nil {
		toSerialize["rawQuery"] = o.RawQuery
	}
	if o.RawSchemeSpecificPart != nil {
		toSerialize["rawSchemeSpecificPart"] = o.RawSchemeSpecificPart
	}
	if o.RawUserInfo != nil {
		toSerialize["rawUserInfo"] = o.RawUserInfo
	}
	if o.Scheme != nil {
		toSerialize["scheme"] = o.Scheme
	}
	if o.SchemeSpecificPart != nil {
		toSerialize["schemeSpecificPart"] = o.SchemeSpecificPart
	}
	if o.UserInfo != nil {
		toSerialize["userInfo"] = o.UserInfo
	}
	return json.Marshal(toSerialize)
}

type NullableUri struct {
	value *Uri
	isSet bool
}

func (v NullableUri) Get() *Uri {
	return v.value
}

func (v *NullableUri) Set(val *Uri) {
	v.value = val
	v.isSet = true
}

func (v NullableUri) IsSet() bool {
	return v.isSet
}

func (v *NullableUri) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableUri(val *Uri) *NullableUri {
	return &NullableUri{value: val, isSet: true}
}

func (v NullableUri) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableUri) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
