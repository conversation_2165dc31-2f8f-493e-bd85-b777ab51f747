/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// ConversationRequest ConversationRequest
type ConversationRequest struct {
	FirstAnswer *string `json:"firstAnswer,omitempty"`
	FirstQuestion *string `json:"firstQuestion,omitempty"`
	// 保留字段，文档来源，逗号分割，仅文档问答生效
	SourceIds *string `json:"sourceIds,omitempty"`
	// 文档类型:0 智能问答 1文档问答 2智能写作
	Type int32 `json:"type"`
}

// NewConversationRequest instantiates a new ConversationRequest object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewConversationRequest(type_ int32, ) *ConversationRequest {
	this := ConversationRequest{}
	this.Type = type_
	return &this
}

// NewConversationRequestWithDefaults instantiates a new ConversationRequest object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewConversationRequestWithDefaults() *ConversationRequest {
	this := ConversationRequest{}
	return &this
}

// GetFirstAnswer returns the FirstAnswer field value if set, zero value otherwise.
func (o *ConversationRequest) GetFirstAnswer() string {
	if o == nil || o.FirstAnswer == nil {
		var ret string
		return ret
	}
	return *o.FirstAnswer
}

// GetFirstAnswerOk returns a tuple with the FirstAnswer field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ConversationRequest) GetFirstAnswerOk() (*string, bool) {
	if o == nil || o.FirstAnswer == nil {
		return nil, false
	}
	return o.FirstAnswer, true
}

// HasFirstAnswer returns a boolean if a field has been set.
func (o *ConversationRequest) HasFirstAnswer() bool {
	if o != nil && o.FirstAnswer != nil {
		return true
	}

	return false
}

// SetFirstAnswer gets a reference to the given string and assigns it to the FirstAnswer field.
func (o *ConversationRequest) SetFirstAnswer(v string) {
	o.FirstAnswer = &v
}

// GetFirstQuestion returns the FirstQuestion field value if set, zero value otherwise.
func (o *ConversationRequest) GetFirstQuestion() string {
	if o == nil || o.FirstQuestion == nil {
		var ret string
		return ret
	}
	return *o.FirstQuestion
}

// GetFirstQuestionOk returns a tuple with the FirstQuestion field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ConversationRequest) GetFirstQuestionOk() (*string, bool) {
	if o == nil || o.FirstQuestion == nil {
		return nil, false
	}
	return o.FirstQuestion, true
}

// HasFirstQuestion returns a boolean if a field has been set.
func (o *ConversationRequest) HasFirstQuestion() bool {
	if o != nil && o.FirstQuestion != nil {
		return true
	}

	return false
}

// SetFirstQuestion gets a reference to the given string and assigns it to the FirstQuestion field.
func (o *ConversationRequest) SetFirstQuestion(v string) {
	o.FirstQuestion = &v
}

// GetSourceIds returns the SourceIds field value if set, zero value otherwise.
func (o *ConversationRequest) GetSourceIds() string {
	if o == nil || o.SourceIds == nil {
		var ret string
		return ret
	}
	return *o.SourceIds
}

// GetSourceIdsOk returns a tuple with the SourceIds field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ConversationRequest) GetSourceIdsOk() (*string, bool) {
	if o == nil || o.SourceIds == nil {
		return nil, false
	}
	return o.SourceIds, true
}

// HasSourceIds returns a boolean if a field has been set.
func (o *ConversationRequest) HasSourceIds() bool {
	if o != nil && o.SourceIds != nil {
		return true
	}

	return false
}

// SetSourceIds gets a reference to the given string and assigns it to the SourceIds field.
func (o *ConversationRequest) SetSourceIds(v string) {
	o.SourceIds = &v
}

// GetType returns the Type field value
func (o *ConversationRequest) GetType() int32 {
	if o == nil  {
		var ret int32
		return ret
	}

	return o.Type
}

// GetTypeOk returns a tuple with the Type field value
// and a boolean to check if the value has been set.
func (o *ConversationRequest) GetTypeOk() (*int32, bool) {
	if o == nil  {
		return nil, false
	}
	return &o.Type, true
}

// SetType sets field value
func (o *ConversationRequest) SetType(v int32) {
	o.Type = v
}

func (o ConversationRequest) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.FirstAnswer != nil {
		toSerialize["firstAnswer"] = o.FirstAnswer
	}
	if o.FirstQuestion != nil {
		toSerialize["firstQuestion"] = o.FirstQuestion
	}
	if o.SourceIds != nil {
		toSerialize["sourceIds"] = o.SourceIds
	}
	if true {
		toSerialize["type"] = o.Type
	}
	return json.Marshal(toSerialize)
}

type NullableConversationRequest struct {
	value *ConversationRequest
	isSet bool
}

func (v NullableConversationRequest) Get() *ConversationRequest {
	return v.value
}

func (v *NullableConversationRequest) Set(val *ConversationRequest) {
	v.value = val
	v.isSet = true
}

func (v NullableConversationRequest) IsSet() bool {
	return v.isSet
}

func (v *NullableConversationRequest) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableConversationRequest(val *ConversationRequest) *NullableConversationRequest {
	return &NullableConversationRequest{value: val, isSet: true}
}

func (v NullableConversationRequest) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableConversationRequest) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
