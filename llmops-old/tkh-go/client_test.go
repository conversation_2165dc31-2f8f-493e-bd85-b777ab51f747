package tkh

import (
	"context"
	"encoding/json"
	"io"
	"testing"
)

var api *BaseApiService
var chatApi *ChatRecallApiService

func init() {
	// Setup
	userAgent := "test-application"
	cfg := &Configuration{
		Host:          "*************:8090",
		Scheme:        "http",
		UserAgent:     userAgent,
		HTTPClient:    nil,
		DefaultHeader: map[string]string{"authorization": "Bearer c99732dd-5b1d-41d1-a1a2-03f9a54fa9df"},
		Servers: ServerConfigurations{
			{
				URL:         "",
				Description: "No description provided",
			},
		},
		// OperationServers: map[string]ServerConfigurations{},
	}

	// Execute
	client := NewAPIClient(cfg)
	api = client.BaseApi
	chatApi = client.ChatRecallApi
}

func TestListAllDocTreesUsingGET(t *testing.T) {

	ctx := context.Background()
	req := api.ListAllDocTreesUsingGET(ctx)
	ret, res, err := req.Execute()
	t.Log(ret, res, err)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(ret)
}

func TestListAllDocTreesUsingPost(t *testing.T) {
	ctx := context.Background()
	req := api.GetFullTreeByIdUsingPOST(ctx)
	req = req.Id(1)
	ret, res, err := req.Execute()
	t.Log(ret, res, err)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(ret)
}

func TestChatRecall(t *testing.T) {
	// kbid := "738"
	// kbid:="411"
	docId1 := "e31f0af1-9a14-4fe3-8f10-52e82860f78c"
	docId2 := "414c0ac5-3317-4da2-b0cb-9c02fc98a2e9"

	var topk int32 = 1
	ctx := context.Background()
	req := chatApi.RecallInfinityChatRecallPost(ctx)
	req = req.RecallServiceRequest(RecallServiceRequest{
		UserQueries: &[]string{"scope"},
		Sources: &[]KnowledgeSource{
			{DocId: &docId1},
			{DocId: &docId2},
		},
		TopK: &topk,
	},
	)

	ret, res, err := req.Execute()
	t.Log(ret, res, err)
	if err != nil {
		bs, _ := io.ReadAll(res.Body)
		t.Fatal(err, string(bs))
	}
	t.Log(ret)

	data := new(TkhRecallResponse)
	if err := json.Unmarshal([]byte(ret), data); err != nil {
		t.Fatal(err)
	}
	t.Log(data)
}
