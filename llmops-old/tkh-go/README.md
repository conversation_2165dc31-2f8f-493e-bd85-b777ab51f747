# Go API client for tkh

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

## Overview
This API client was generated by the [OpenAPI Generator](https://openapi-generator.tech) project.  By using the [OpenAPI-spec](https://www.openapis.org/) from a remote server, you can easily generate an API client.

- API version: 1.0.0
- Package version: 1.0.0
- Build package: org.openapitools.codegen.languages.GoClientExperimentalCodegen

## Installation

Install the following dependencies:

```shell
go get github.com/stretchr/testify/assert
go get golang.org/x/oauth2
go get golang.org/x/net/context
```

Put the package under your project folder and add the following in import:

```golang
import sw "./tkh"
```

## Configuration of Server URL

Default configuration comes with `Servers` field that contains server objects as defined in the OpenAPI specification.

### Select Server Configuration

For using other server than the one defined on index 0 set context value `sw.ContextServerIndex` of type `int`.

```golang
ctx := context.WithValue(context.Background(), sw.ContextServerIndex, 1)
```

### Templated Server URL

Templated server URL is formatted using default variables from configuration or from context value `sw.ContextServerVariables` of type `map[string]string`.

```golang
ctx := context.WithValue(context.Background(), sw.ContextServerVariables, map[string]string{
	"basePath": "v2",
})
```

Note, enum values are always validated and all unused variables are silently ignored.

### URLs Configuration per Operation

Each operation can use different server URL defined using `OperationServers` map in the `Configuration`.
An operation is uniquely identifield by `"{classname}Service.{nickname}"` string.
Similar rules for overriding default operation server index and variables applies by using `sw.ContextOperationServerIndices` and `sw.ContextOperationServerVariables` context maps.

```
ctx := context.WithValue(context.Background(), sw.ContextOperationServerIndices, map[string]int{
	"{classname}Service.{nickname}": 2,
})
ctx = context.WithValue(context.Background(), sw.ContextOperationServerVariables, map[string]map[string]string{
	"{classname}Service.{nickname}": {
		"port": "8443",
	},
})
```

## Documentation for API Endpoints

All URIs are relative to *http://localhost*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
*BaseApi* | [**AddConversationUsingPOST**](docs/BaseApi.md#addconversationusingpost) | **Post** /infinity/base/qa/add-conversation | 会话新增
*BaseApi* | [**AddDirectoryUsingPOST**](docs/BaseApi.md#adddirectoryusingpost) | **Post** /infinity/base/documents/add-directory | 知识库下创建一个文件夹
*BaseApi* | [**AddQAUsingPOST**](docs/BaseApi.md#addqausingpost) | **Post** /infinity/base/qa/add-QA | 问答记录新增
*BaseApi* | [**AddRepositoryUsingPOST**](docs/BaseApi.md#addrepositoryusingpost) | **Post** /infinity/base/documents/add-repository | 创建一个基础知识库
*BaseApi* | [**AddUsersUsingPOST**](docs/BaseApi.md#addusersusingpost) | **Post** /infinity/base/user/admin/add | admin-创建用户
*BaseApi* | [**ApplyForApikeyUsingPOST**](docs/BaseApi.md#applyforapikeyusingpost) | **Post** /infinity/base/approve/apikey/apply | 申请apikey
*BaseApi* | [**ApprovalForApikeyUsingGET**](docs/BaseApi.md#approvalforapikeyusingget) | **Get** /infinity/base/approve/admin/apikey/approval | 审批apikey
*BaseApi* | [**DelUsersUsingGET**](docs/BaseApi.md#delusersusingget) | **Get** /infinity/base/user/admin/del | admin-删除用户
*BaseApi* | [**DeleteByIdUsingDELETE**](docs/BaseApi.md#deletebyidusingdelete) | **Delete** /infinity/base/approve/apikey/approval | 删除申请记录 根据id
*BaseApi* | [**DeleteConversationUsingDELETE**](docs/BaseApi.md#deleteconversationusingdelete) | **Delete** /infinity/base/qa/delete-conversation | 会话删除
*BaseApi* | [**DocumentsSplitUsingPOST**](docs/BaseApi.md#documentssplitusingpost) | **Post** /infinity/base/documents/split/documents | 批量文档解析
*BaseApi* | [**FullProcessingFilesUsingPOST**](docs/BaseApi.md#fullprocessingfilesusingpost) | **Post** /infinity/base/documents/full-processing-files | 批量上传解析存储文件--全同步
*BaseApi* | [**FullProcessingSingleFileUsingPOST**](docs/BaseApi.md#fullprocessingsinglefileusingpost) | **Post** /infinity/base/documents/full-processing-single-file | 上传解析存储单个文件--同步
*BaseApi* | [**GetCaptchaUsingGET**](docs/BaseApi.md#getcaptchausingget) | **Get** /infinity/base/auth/any/captcha | 获取验证码
*BaseApi* | [**GetConversationDetailUsingGET**](docs/BaseApi.md#getconversationdetailusingget) | **Get** /infinity/base/qa/get-conversation-detail | 会话详情查询
*BaseApi* | [**GetConversationListUsingGET**](docs/BaseApi.md#getconversationlistusingget) | **Get** /infinity/base/qa/get-conversation | 会话列表查询
*BaseApi* | [**GetDatasourceByIdUsingPOST**](docs/BaseApi.md#getdatasourcebyidusingpost) | **Post** /infinity/base/documents/get-datasource | 获取datasource连接信息
*BaseApi* | [**GetDocTreeByIdUsingGET**](docs/BaseApi.md#getdoctreebyidusingget) | **Get** /infinity/base/documents/{id}/list | 根据id获取文档目录
*BaseApi* | [**GetFullTreeByIdUsingPOST**](docs/BaseApi.md#getfulltreebyidusingpost) | **Post** /infinity/base/documents/get-full-tree | 根据id获取完整树
*BaseApi* | [**GetIndTrendListUsingPOST**](docs/BaseApi.md#getindtrendlistusingpost) | **Post** /infinity/base/qa/get-ind-trend | 行业走势
*BaseApi* | [**GetStockListUsingGET**](docs/BaseApi.md#getstocklistusingget) | **Get** /infinity/base/qa/get-stock | 根据行业或概念名称获取个股列表
*BaseApi* | [**GetStockTrendListUsingPOST**](docs/BaseApi.md#getstocktrendlistusingpost) | **Post** /infinity/base/qa/get-stock-trend | 上市公司/股票代码股价走势
*BaseApi* | [**GetTagStockListUsingPOST**](docs/BaseApi.md#gettagstocklistusingpost) | **Post** /infinity/base/qa/get-ind | 根据股票名称list获取行业或概念
*BaseApi* | [**GetTdsUserInfoUsingGET**](docs/BaseApi.md#gettdsuserinfousingget) | **Get** /infinity/base/auth/tds-user-info | 获取tds用户详情
*BaseApi* | [**GetUploadTaskDetailUsingPOST**](docs/BaseApi.md#getuploadtaskdetailusingpost) | **Post** /infinity/base/documents/get-upload-task-detail | 获取上传任务详情，包括任务状况，各文件状态
*BaseApi* | [**GetUserListUsingGET**](docs/BaseApi.md#getuserlistusingget) | **Get** /infinity/base/user/admin/user-list | admin-根据用户名模糊查找用户list
*BaseApi* | [**GetUserUsingGET**](docs/BaseApi.md#getuserusingget) | **Get** /infinity/base/auth/info | 获取当前登录用户个人信息
*BaseApi* | [**HighlightUsingGET**](docs/BaseApi.md#highlightusingget) | **Get** /infinity/base/documents/highlight | 根据文本片段ID对文档高亮
*BaseApi* | [**HighlightUsingPOST**](docs/BaseApi.md#highlightusingpost) | **Post** /infinity/base/documents/highlight/online | 在线根据坐标对文档高亮
*BaseApi* | [**HighlightUsingPOST1**](docs/BaseApi.md#highlightusingpost1) | **Post** /infinity/base/documents/highlight | 根据坐标或文本对文档高亮
*BaseApi* | [**ListAllDocTreesUsingGET**](docs/BaseApi.md#listalldoctreesusingget) | **Get** /infinity/base/documents/list | 获取所有文本类型知识库列表
*BaseApi* | [**ListApproveInfoUsingGET**](docs/BaseApi.md#listapproveinfousingget) | **Get** /infinity/base/approve/apikey/approval | 获取apikey申请列表
*BaseApi* | [**ListFilesByBusinessTypeUsingPOST**](docs/BaseApi.md#listfilesbybusinesstypeusingpost) | **Post** /infinity/base/documents/list-files-business-type | id获取文件夹下按照业务类型分组的所有文档(树展开状态)
*BaseApi* | [**ListUserDocTreesUsingPOST**](docs/BaseApi.md#listuserdoctreesusingpost) | **Post** /infinity/base/documents/list-user-trees | 获取用户有权限的知识库列表,包含私库+sdb
*BaseApi* | [**LoginTDSUsingPOST**](docs/BaseApi.md#logintdsusingpost) | **Post** /infinity/base/auth/any/login-tds | 登录TDS
*BaseApi* | [**LoginUsingPOST**](docs/BaseApi.md#loginusingpost) | **Post** /infinity/base/auth/any/login | 登录
*BaseApi* | [**MatchImagesUsingGET**](docs/BaseApi.md#matchimagesusingget) | **Get** /infinity/base/image/match | 根据图片检索图片
*BaseApi* | [**ModifyPermissionUsingGET**](docs/BaseApi.md#modifypermissionusingget) | **Get** /infinity/base/user/admin/permission | admin-修改用户权限
*BaseApi* | [**PreviewDocUsingPOST**](docs/BaseApi.md#previewdocusingpost) | **Post** /infinity/base/documents/preview | 预览指定文档
*BaseApi* | [**PrintServicesUsingGET**](docs/BaseApi.md#printservicesusingget) | **Get** /infinity/base/documents/print-services | printServices
*BaseApi* | [**QueryImagesUsingPOST**](docs/BaseApi.md#queryimagesusingpost) | **Post** /infinity/base/image/query | 根据文本检索图片
*BaseApi* | [**ResetUserUsingGET**](docs/BaseApi.md#resetuserusingget) | **Get** /infinity/base/user/admin/reset | admin-重置用户
*BaseApi* | [**SingleDocumentSplitUsingPOST**](docs/BaseApi.md#singledocumentsplitusingpost) | **Post** /infinity/base/documents/split/batch/save | 批量文档保存
*BaseApi* | [**SingleDocumentSplitUsingPOST1**](docs/BaseApi.md#singledocumentsplitusingpost1) | **Post** /infinity/base/documents/split/cache/save | 单文档编辑确定
*BaseApi* | [**SplitDocReadUsingPOST**](docs/BaseApi.md#splitdocreadusingpost) | **Post** /infinity/base/documents/split/doc/read | 编辑文档查看
*BaseApi* | [**SplitDocumentUsingPOST**](docs/BaseApi.md#splitdocumentusingpost) | **Post** /infinity/base/documents/split/document | 单个文件的解析服务
*BaseApi* | [**UpdateConversationUsingPOST**](docs/BaseApi.md#updateconversationusingpost) | **Post** /infinity/base/qa/update-conversation | 会话标题修改
*BaseApi* | [**UpdatePasswordUsingPOST**](docs/BaseApi.md#updatepasswordusingpost) | **Post** /infinity/base/user/password | 修改密码
*BaseApi* | [**UploadLocalUsingPOST**](docs/BaseApi.md#uploadlocalusingpost) | **Post** /infinity/base/documents/upload/local | 批量上传文件
*BaseApi* | [**UploadRepositoryUsingPOST**](docs/BaseApi.md#uploadrepositoryusingpost) | **Post** /infinity/base/documents/upload/repository | 获取知识库文件详情
*ChatRecallApi* | [**RecallInfinityChatRecallPost**](docs/ChatRecallApi.md#recallinfinitychatrecallpost) | **Post** /infinity/chat/recall | Recall


## Documentation For Models

 - [AddDirectoryRequest](docs/AddDirectoryRequest.md)
 - [AddRepoRequest](docs/AddRepoRequest.md)
 - [ApproveInfoDuiXiang](docs/ApproveInfoDuiXiang.md)
 - [ChunkMetadata](docs/ChunkMetadata.md)
 - [ConversationDetail](docs/ConversationDetail.md)
 - [ConversationDetailVo](docs/ConversationDetailVo.md)
 - [ConversationRequest](docs/ConversationRequest.md)
 - [ConversationUpdateRequest](docs/ConversationUpdateRequest.md)
 - [ConversationVo](docs/ConversationVo.md)
 - [DocBusinessTypeVo](docs/DocBusinessTypeVo.md)
 - [DocCacheRequest](docs/DocCacheRequest.md)
 - [DocChunk](docs/DocChunk.md)
 - [DocHighlightRequest](docs/DocHighlightRequest.md)
 - [DocPreviewRequest](docs/DocPreviewRequest.md)
 - [DocTreeDetail](docs/DocTreeDetail.md)
 - [Document](docs/Document.md)
 - [DocumentTree](docs/DocumentTree.md)
 - [DocumentUploadTask](docs/DocumentUploadTask.md)
 - [DtoParamApply](docs/DtoParamApply.md)
 - [DtoParamLogin](docs/DtoParamLogin.md)
 - [DtoParamUpdatePassword](docs/DtoParamUpdatePassword.md)
 - [DtoResultCaptcha](docs/DtoResultCaptcha.md)
 - [File](docs/File.md)
 - [FullTree](docs/FullTree.md)
 - [HotStockVo](docs/HotStockVo.md)
 - [ImageDetail](docs/ImageDetail.md)
 - [ImageMatchRequest](docs/ImageMatchRequest.md)
 - [ImageQueryRequest](docs/ImageQueryRequest.md)
 - [ImageQueryResponse](docs/ImageQueryResponse.md)
 - [IndOrConceptVo](docs/IndOrConceptVo.md)
 - [IndTrendRequest](docs/IndTrendRequest.md)
 - [IndTrendVo](docs/IndTrendVo.md)
 - [InlineObject](docs/InlineObject.md)
 - [KnowledgeBaseDirParams](docs/KnowledgeBaseDirParams.md)
 - [KnowledgeSource](docs/KnowledgeSource.md)
 - [OrderItem](docs/OrderItem.md)
 - [PageConversationVo](docs/PageConversationVo.md)
 - [QaRequest](docs/QaRequest.md)
 - [RecallServiceRequest](docs/RecallServiceRequest.md)
 - [Repository](docs/Repository.md)
 - [Resource](docs/Resource.md)
 - [ResultApproveInfoDuiXiang](docs/ResultApproveInfoDuiXiang.md)
 - [ResultBoolean](docs/ResultBoolean.md)
 - [ResultConversationDetailVo](docs/ResultConversationDetailVo.md)
 - [ResultDocBusinessTypeVo](docs/ResultDocBusinessTypeVo.md)
 - [ResultDocument](docs/ResultDocument.md)
 - [ResultDocumentTree](docs/ResultDocumentTree.md)
 - [ResultDocumentUploadTask](docs/ResultDocumentUploadTask.md)
 - [ResultDtoResultCaptcha](docs/ResultDtoResultCaptcha.md)
 - [ResultFullTree](docs/ResultFullTree.md)
 - [ResultImageQueryResponse](docs/ResultImageQueryResponse.md)
 - [ResultListApproveInfoDuiXiang](docs/ResultListApproveInfoDuiXiang.md)
 - [ResultListDocChunk](docs/ResultListDocChunk.md)
 - [ResultListDocument](docs/ResultListDocument.md)
 - [ResultListHotStockVo](docs/ResultListHotStockVo.md)
 - [ResultListIndOrConceptVo](docs/ResultListIndOrConceptVo.md)
 - [ResultListIndTrendVo](docs/ResultListIndTrendVo.md)
 - [ResultListRepository](docs/ResultListRepository.md)
 - [ResultListUserInfoDuiXiang](docs/ResultListUserInfoDuiXiang.md)
 - [ResultLong](docs/ResultLong.md)
 - [ResultPageConversationVo](docs/ResultPageConversationVo.md)
 - [ResultRepository](docs/ResultRepository.md)
 - [ResultStockTrendVo](docs/ResultStockTrendVo.md)
 - [ResultString](docs/ResultString.md)
 - [ResultTreeNodeListVo](docs/ResultTreeNodeListVo.md)
 - [ResultUserDocTreeResponse](docs/ResultUserDocTreeResponse.md)
 - [ResultUserInfoDuiXiang](docs/ResultUserInfoDuiXiang.md)
 - [ResultUserInfoEntity](docs/ResultUserInfoEntity.md)
 - [SingleDocSplitParam](docs/SingleDocSplitParam.md)
 - [StockTrend](docs/StockTrend.md)
 - [StockTrendRequest](docs/StockTrendRequest.md)
 - [StockTrendVo](docs/StockTrendVo.md)
 - [TagHotStockRequest](docs/TagHotStockRequest.md)
 - [TokenRequestInfo](docs/TokenRequestInfo.md)
 - [TreeNodeListVo](docs/TreeNodeListVo.md)
 - [Uri](docs/Uri.md)
 - [Url](docs/Url.md)
 - [UserDocTreeResponse](docs/UserDocTreeResponse.md)
 - [UserInfoDuiXiang](docs/UserInfoDuiXiang.md)
 - [UserInfoEntity](docs/UserInfoEntity.md)


## Documentation For Authorization

 Endpoints do not require authorization.


## Documentation for Utility Methods

Due to the fact that model structure members are all pointers, this package contains
a number of utility functions to easily obtain pointers to values of basic types.
Each of these functions takes a value of the given basic type and returns a pointer to it:

* `PtrBool`
* `PtrInt`
* `PtrInt32`
* `PtrInt64`
* `PtrFloat`
* `PtrFloat32`
* `PtrFloat64`
* `PtrString`
* `PtrTime`

## Author



