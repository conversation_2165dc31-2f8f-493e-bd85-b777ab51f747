/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// ImageQueryResponse ImageQueryResponse
type ImageQueryResponse struct {
	ImageList *[]ImageDetail `json:"image_list,omitempty"`
	Model *string `json:"model,omitempty"`
	SearchTime *string `json:"search_time,omitempty"`
	Size *int32 `json:"size,omitempty"`
}

// NewImageQueryResponse instantiates a new ImageQueryResponse object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewImageQueryResponse() *ImageQueryResponse {
	this := ImageQueryResponse{}
	return &this
}

// NewImageQueryResponseWithDefaults instantiates a new ImageQueryResponse object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewImageQueryResponseWithDefaults() *ImageQueryResponse {
	this := ImageQueryResponse{}
	return &this
}

// GetImageList returns the ImageList field value if set, zero value otherwise.
func (o *ImageQueryResponse) GetImageList() []ImageDetail {
	if o == nil || o.ImageList == nil {
		var ret []ImageDetail
		return ret
	}
	return *o.ImageList
}

// GetImageListOk returns a tuple with the ImageList field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ImageQueryResponse) GetImageListOk() (*[]ImageDetail, bool) {
	if o == nil || o.ImageList == nil {
		return nil, false
	}
	return o.ImageList, true
}

// HasImageList returns a boolean if a field has been set.
func (o *ImageQueryResponse) HasImageList() bool {
	if o != nil && o.ImageList != nil {
		return true
	}

	return false
}

// SetImageList gets a reference to the given []ImageDetail and assigns it to the ImageList field.
func (o *ImageQueryResponse) SetImageList(v []ImageDetail) {
	o.ImageList = &v
}

// GetModel returns the Model field value if set, zero value otherwise.
func (o *ImageQueryResponse) GetModel() string {
	if o == nil || o.Model == nil {
		var ret string
		return ret
	}
	return *o.Model
}

// GetModelOk returns a tuple with the Model field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ImageQueryResponse) GetModelOk() (*string, bool) {
	if o == nil || o.Model == nil {
		return nil, false
	}
	return o.Model, true
}

// HasModel returns a boolean if a field has been set.
func (o *ImageQueryResponse) HasModel() bool {
	if o != nil && o.Model != nil {
		return true
	}

	return false
}

// SetModel gets a reference to the given string and assigns it to the Model field.
func (o *ImageQueryResponse) SetModel(v string) {
	o.Model = &v
}

// GetSearchTime returns the SearchTime field value if set, zero value otherwise.
func (o *ImageQueryResponse) GetSearchTime() string {
	if o == nil || o.SearchTime == nil {
		var ret string
		return ret
	}
	return *o.SearchTime
}

// GetSearchTimeOk returns a tuple with the SearchTime field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ImageQueryResponse) GetSearchTimeOk() (*string, bool) {
	if o == nil || o.SearchTime == nil {
		return nil, false
	}
	return o.SearchTime, true
}

// HasSearchTime returns a boolean if a field has been set.
func (o *ImageQueryResponse) HasSearchTime() bool {
	if o != nil && o.SearchTime != nil {
		return true
	}

	return false
}

// SetSearchTime gets a reference to the given string and assigns it to the SearchTime field.
func (o *ImageQueryResponse) SetSearchTime(v string) {
	o.SearchTime = &v
}

// GetSize returns the Size field value if set, zero value otherwise.
func (o *ImageQueryResponse) GetSize() int32 {
	if o == nil || o.Size == nil {
		var ret int32
		return ret
	}
	return *o.Size
}

// GetSizeOk returns a tuple with the Size field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ImageQueryResponse) GetSizeOk() (*int32, bool) {
	if o == nil || o.Size == nil {
		return nil, false
	}
	return o.Size, true
}

// HasSize returns a boolean if a field has been set.
func (o *ImageQueryResponse) HasSize() bool {
	if o != nil && o.Size != nil {
		return true
	}

	return false
}

// SetSize gets a reference to the given int32 and assigns it to the Size field.
func (o *ImageQueryResponse) SetSize(v int32) {
	o.Size = &v
}

func (o ImageQueryResponse) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.ImageList != nil {
		toSerialize["image_list"] = o.ImageList
	}
	if o.Model != nil {
		toSerialize["model"] = o.Model
	}
	if o.SearchTime != nil {
		toSerialize["search_time"] = o.SearchTime
	}
	if o.Size != nil {
		toSerialize["size"] = o.Size
	}
	return json.Marshal(toSerialize)
}

type NullableImageQueryResponse struct {
	value *ImageQueryResponse
	isSet bool
}

func (v NullableImageQueryResponse) Get() *ImageQueryResponse {
	return v.value
}

func (v *NullableImageQueryResponse) Set(val *ImageQueryResponse) {
	v.value = val
	v.isSet = true
}

func (v NullableImageQueryResponse) IsSet() bool {
	return v.isSet
}

func (v *NullableImageQueryResponse) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableImageQueryResponse(val *ImageQueryResponse) *NullableImageQueryResponse {
	return &NullableImageQueryResponse{value: val, isSet: true}
}

func (v NullableImageQueryResponse) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableImageQueryResponse) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
