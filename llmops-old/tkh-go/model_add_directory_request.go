/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// AddDirectoryRequest AddDirectoryRequest
type AddDirectoryRequest struct {
	// 文件及名称
	Name *string `json:"name,omitempty"`
	// 父级ID
	ParentId *int64 `json:"parentId,omitempty"`
}

// NewAddDirectoryRequest instantiates a new AddDirectoryRequest object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewAddDirectoryRequest() *AddDirectoryRequest {
	this := AddDirectoryRequest{}
	return &this
}

// NewAddDirectoryRequestWithDefaults instantiates a new AddDirectoryRequest object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewAddDirectoryRequestWithDefaults() *AddDirectoryRequest {
	this := AddDirectoryRequest{}
	return &this
}

// GetName returns the Name field value if set, zero value otherwise.
func (o *AddDirectoryRequest) GetName() string {
	if o == nil || o.Name == nil {
		var ret string
		return ret
	}
	return *o.Name
}

// GetNameOk returns a tuple with the Name field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AddDirectoryRequest) GetNameOk() (*string, bool) {
	if o == nil || o.Name == nil {
		return nil, false
	}
	return o.Name, true
}

// HasName returns a boolean if a field has been set.
func (o *AddDirectoryRequest) HasName() bool {
	if o != nil && o.Name != nil {
		return true
	}

	return false
}

// SetName gets a reference to the given string and assigns it to the Name field.
func (o *AddDirectoryRequest) SetName(v string) {
	o.Name = &v
}

// GetParentId returns the ParentId field value if set, zero value otherwise.
func (o *AddDirectoryRequest) GetParentId() int64 {
	if o == nil || o.ParentId == nil {
		var ret int64
		return ret
	}
	return *o.ParentId
}

// GetParentIdOk returns a tuple with the ParentId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AddDirectoryRequest) GetParentIdOk() (*int64, bool) {
	if o == nil || o.ParentId == nil {
		return nil, false
	}
	return o.ParentId, true
}

// HasParentId returns a boolean if a field has been set.
func (o *AddDirectoryRequest) HasParentId() bool {
	if o != nil && o.ParentId != nil {
		return true
	}

	return false
}

// SetParentId gets a reference to the given int64 and assigns it to the ParentId field.
func (o *AddDirectoryRequest) SetParentId(v int64) {
	o.ParentId = &v
}

func (o AddDirectoryRequest) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Name != nil {
		toSerialize["name"] = o.Name
	}
	if o.ParentId != nil {
		toSerialize["parentId"] = o.ParentId
	}
	return json.Marshal(toSerialize)
}

type NullableAddDirectoryRequest struct {
	value *AddDirectoryRequest
	isSet bool
}

func (v NullableAddDirectoryRequest) Get() *AddDirectoryRequest {
	return v.value
}

func (v *NullableAddDirectoryRequest) Set(val *AddDirectoryRequest) {
	v.value = val
	v.isSet = true
}

func (v NullableAddDirectoryRequest) IsSet() bool {
	return v.isSet
}

func (v *NullableAddDirectoryRequest) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableAddDirectoryRequest(val *AddDirectoryRequest) *NullableAddDirectoryRequest {
	return &NullableAddDirectoryRequest{value: val, isSet: true}
}

func (v NullableAddDirectoryRequest) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableAddDirectoryRequest) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
