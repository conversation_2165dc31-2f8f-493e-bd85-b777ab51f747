/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// Url URL
type Url struct {
	Authority *string `json:"authority,omitempty"`
	Content *map[string]interface{} `json:"content,omitempty"`
	DefaultPort *int32 `json:"defaultPort,omitempty"`
	File *string `json:"file,omitempty"`
	Host *string `json:"host,omitempty"`
	Path *string `json:"path,omitempty"`
	Port *int32 `json:"port,omitempty"`
	Protocol *string `json:"protocol,omitempty"`
	Query *string `json:"query,omitempty"`
	Ref *string `json:"ref,omitempty"`
	UserInfo *string `json:"userInfo,omitempty"`
}

// NewUrl instantiates a new Url object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewUrl() *Url {
	this := Url{}
	return &this
}

// NewUrlWithDefaults instantiates a new Url object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewUrlWithDefaults() *Url {
	this := Url{}
	return &this
}

// GetAuthority returns the Authority field value if set, zero value otherwise.
func (o *Url) GetAuthority() string {
	if o == nil || o.Authority == nil {
		var ret string
		return ret
	}
	return *o.Authority
}

// GetAuthorityOk returns a tuple with the Authority field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Url) GetAuthorityOk() (*string, bool) {
	if o == nil || o.Authority == nil {
		return nil, false
	}
	return o.Authority, true
}

// HasAuthority returns a boolean if a field has been set.
func (o *Url) HasAuthority() bool {
	if o != nil && o.Authority != nil {
		return true
	}

	return false
}

// SetAuthority gets a reference to the given string and assigns it to the Authority field.
func (o *Url) SetAuthority(v string) {
	o.Authority = &v
}

// GetContent returns the Content field value if set, zero value otherwise.
func (o *Url) GetContent() map[string]interface{} {
	if o == nil || o.Content == nil {
		var ret map[string]interface{}
		return ret
	}
	return *o.Content
}

// GetContentOk returns a tuple with the Content field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Url) GetContentOk() (*map[string]interface{}, bool) {
	if o == nil || o.Content == nil {
		return nil, false
	}
	return o.Content, true
}

// HasContent returns a boolean if a field has been set.
func (o *Url) HasContent() bool {
	if o != nil && o.Content != nil {
		return true
	}

	return false
}

// SetContent gets a reference to the given map[string]interface{} and assigns it to the Content field.
func (o *Url) SetContent(v map[string]interface{}) {
	o.Content = &v
}

// GetDefaultPort returns the DefaultPort field value if set, zero value otherwise.
func (o *Url) GetDefaultPort() int32 {
	if o == nil || o.DefaultPort == nil {
		var ret int32
		return ret
	}
	return *o.DefaultPort
}

// GetDefaultPortOk returns a tuple with the DefaultPort field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Url) GetDefaultPortOk() (*int32, bool) {
	if o == nil || o.DefaultPort == nil {
		return nil, false
	}
	return o.DefaultPort, true
}

// HasDefaultPort returns a boolean if a field has been set.
func (o *Url) HasDefaultPort() bool {
	if o != nil && o.DefaultPort != nil {
		return true
	}

	return false
}

// SetDefaultPort gets a reference to the given int32 and assigns it to the DefaultPort field.
func (o *Url) SetDefaultPort(v int32) {
	o.DefaultPort = &v
}

// GetFile returns the File field value if set, zero value otherwise.
func (o *Url) GetFile() string {
	if o == nil || o.File == nil {
		var ret string
		return ret
	}
	return *o.File
}

// GetFileOk returns a tuple with the File field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Url) GetFileOk() (*string, bool) {
	if o == nil || o.File == nil {
		return nil, false
	}
	return o.File, true
}

// HasFile returns a boolean if a field has been set.
func (o *Url) HasFile() bool {
	if o != nil && o.File != nil {
		return true
	}

	return false
}

// SetFile gets a reference to the given string and assigns it to the File field.
func (o *Url) SetFile(v string) {
	o.File = &v
}

// GetHost returns the Host field value if set, zero value otherwise.
func (o *Url) GetHost() string {
	if o == nil || o.Host == nil {
		var ret string
		return ret
	}
	return *o.Host
}

// GetHostOk returns a tuple with the Host field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Url) GetHostOk() (*string, bool) {
	if o == nil || o.Host == nil {
		return nil, false
	}
	return o.Host, true
}

// HasHost returns a boolean if a field has been set.
func (o *Url) HasHost() bool {
	if o != nil && o.Host != nil {
		return true
	}

	return false
}

// SetHost gets a reference to the given string and assigns it to the Host field.
func (o *Url) SetHost(v string) {
	o.Host = &v
}

// GetPath returns the Path field value if set, zero value otherwise.
func (o *Url) GetPath() string {
	if o == nil || o.Path == nil {
		var ret string
		return ret
	}
	return *o.Path
}

// GetPathOk returns a tuple with the Path field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Url) GetPathOk() (*string, bool) {
	if o == nil || o.Path == nil {
		return nil, false
	}
	return o.Path, true
}

// HasPath returns a boolean if a field has been set.
func (o *Url) HasPath() bool {
	if o != nil && o.Path != nil {
		return true
	}

	return false
}

// SetPath gets a reference to the given string and assigns it to the Path field.
func (o *Url) SetPath(v string) {
	o.Path = &v
}

// GetPort returns the Port field value if set, zero value otherwise.
func (o *Url) GetPort() int32 {
	if o == nil || o.Port == nil {
		var ret int32
		return ret
	}
	return *o.Port
}

// GetPortOk returns a tuple with the Port field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Url) GetPortOk() (*int32, bool) {
	if o == nil || o.Port == nil {
		return nil, false
	}
	return o.Port, true
}

// HasPort returns a boolean if a field has been set.
func (o *Url) HasPort() bool {
	if o != nil && o.Port != nil {
		return true
	}

	return false
}

// SetPort gets a reference to the given int32 and assigns it to the Port field.
func (o *Url) SetPort(v int32) {
	o.Port = &v
}

// GetProtocol returns the Protocol field value if set, zero value otherwise.
func (o *Url) GetProtocol() string {
	if o == nil || o.Protocol == nil {
		var ret string
		return ret
	}
	return *o.Protocol
}

// GetProtocolOk returns a tuple with the Protocol field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Url) GetProtocolOk() (*string, bool) {
	if o == nil || o.Protocol == nil {
		return nil, false
	}
	return o.Protocol, true
}

// HasProtocol returns a boolean if a field has been set.
func (o *Url) HasProtocol() bool {
	if o != nil && o.Protocol != nil {
		return true
	}

	return false
}

// SetProtocol gets a reference to the given string and assigns it to the Protocol field.
func (o *Url) SetProtocol(v string) {
	o.Protocol = &v
}

// GetQuery returns the Query field value if set, zero value otherwise.
func (o *Url) GetQuery() string {
	if o == nil || o.Query == nil {
		var ret string
		return ret
	}
	return *o.Query
}

// GetQueryOk returns a tuple with the Query field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Url) GetQueryOk() (*string, bool) {
	if o == nil || o.Query == nil {
		return nil, false
	}
	return o.Query, true
}

// HasQuery returns a boolean if a field has been set.
func (o *Url) HasQuery() bool {
	if o != nil && o.Query != nil {
		return true
	}

	return false
}

// SetQuery gets a reference to the given string and assigns it to the Query field.
func (o *Url) SetQuery(v string) {
	o.Query = &v
}

// GetRef returns the Ref field value if set, zero value otherwise.
func (o *Url) GetRef() string {
	if o == nil || o.Ref == nil {
		var ret string
		return ret
	}
	return *o.Ref
}

// GetRefOk returns a tuple with the Ref field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Url) GetRefOk() (*string, bool) {
	if o == nil || o.Ref == nil {
		return nil, false
	}
	return o.Ref, true
}

// HasRef returns a boolean if a field has been set.
func (o *Url) HasRef() bool {
	if o != nil && o.Ref != nil {
		return true
	}

	return false
}

// SetRef gets a reference to the given string and assigns it to the Ref field.
func (o *Url) SetRef(v string) {
	o.Ref = &v
}

// GetUserInfo returns the UserInfo field value if set, zero value otherwise.
func (o *Url) GetUserInfo() string {
	if o == nil || o.UserInfo == nil {
		var ret string
		return ret
	}
	return *o.UserInfo
}

// GetUserInfoOk returns a tuple with the UserInfo field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Url) GetUserInfoOk() (*string, bool) {
	if o == nil || o.UserInfo == nil {
		return nil, false
	}
	return o.UserInfo, true
}

// HasUserInfo returns a boolean if a field has been set.
func (o *Url) HasUserInfo() bool {
	if o != nil && o.UserInfo != nil {
		return true
	}

	return false
}

// SetUserInfo gets a reference to the given string and assigns it to the UserInfo field.
func (o *Url) SetUserInfo(v string) {
	o.UserInfo = &v
}

func (o Url) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Authority != nil {
		toSerialize["authority"] = o.Authority
	}
	if o.Content != nil {
		toSerialize["content"] = o.Content
	}
	if o.DefaultPort != nil {
		toSerialize["defaultPort"] = o.DefaultPort
	}
	if o.File != nil {
		toSerialize["file"] = o.File
	}
	if o.Host != nil {
		toSerialize["host"] = o.Host
	}
	if o.Path != nil {
		toSerialize["path"] = o.Path
	}
	if o.Port != nil {
		toSerialize["port"] = o.Port
	}
	if o.Protocol != nil {
		toSerialize["protocol"] = o.Protocol
	}
	if o.Query != nil {
		toSerialize["query"] = o.Query
	}
	if o.Ref != nil {
		toSerialize["ref"] = o.Ref
	}
	if o.UserInfo != nil {
		toSerialize["userInfo"] = o.UserInfo
	}
	return json.Marshal(toSerialize)
}

type NullableUrl struct {
	value *Url
	isSet bool
}

func (v NullableUrl) Get() *Url {
	return v.value
}

func (v *NullableUrl) Set(val *Url) {
	v.value = val
	v.isSet = true
}

func (v NullableUrl) IsSet() bool {
	return v.isSet
}

func (v *NullableUrl) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableUrl(val *Url) *NullableUrl {
	return &NullableUrl{value: val, isSet: true}
}

func (v NullableUrl) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableUrl) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
