/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// DocBusinessTypeVo DocBusinessTypeVO
type DocBusinessTypeVo struct {
	DocsList *[]DocumentTree `json:"docsList,omitempty"`
	ImgList *[]DocumentTree `json:"imgList,omitempty"`
	NoneList *[]DocumentTree `json:"noneList,omitempty"`
	PdfList *[]DocumentTree `json:"pdfList,omitempty"`
	PptxList *[]DocumentTree `json:"pptxList,omitempty"`
	TableList *[]DocumentTree `json:"tableList,omitempty"`
	TxtList *[]DocumentTree `json:"txtList,omitempty"`
}

// NewDocBusinessTypeVo instantiates a new DocBusinessTypeVo object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDocBusinessTypeVo() *DocBusinessTypeVo {
	this := DocBusinessTypeVo{}
	return &this
}

// NewDocBusinessTypeVoWithDefaults instantiates a new DocBusinessTypeVo object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDocBusinessTypeVoWithDefaults() *DocBusinessTypeVo {
	this := DocBusinessTypeVo{}
	return &this
}

// GetDocsList returns the DocsList field value if set, zero value otherwise.
func (o *DocBusinessTypeVo) GetDocsList() []DocumentTree {
	if o == nil || o.DocsList == nil {
		var ret []DocumentTree
		return ret
	}
	return *o.DocsList
}

// GetDocsListOk returns a tuple with the DocsList field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocBusinessTypeVo) GetDocsListOk() (*[]DocumentTree, bool) {
	if o == nil || o.DocsList == nil {
		return nil, false
	}
	return o.DocsList, true
}

// HasDocsList returns a boolean if a field has been set.
func (o *DocBusinessTypeVo) HasDocsList() bool {
	if o != nil && o.DocsList != nil {
		return true
	}

	return false
}

// SetDocsList gets a reference to the given []DocumentTree and assigns it to the DocsList field.
func (o *DocBusinessTypeVo) SetDocsList(v []DocumentTree) {
	o.DocsList = &v
}

// GetImgList returns the ImgList field value if set, zero value otherwise.
func (o *DocBusinessTypeVo) GetImgList() []DocumentTree {
	if o == nil || o.ImgList == nil {
		var ret []DocumentTree
		return ret
	}
	return *o.ImgList
}

// GetImgListOk returns a tuple with the ImgList field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocBusinessTypeVo) GetImgListOk() (*[]DocumentTree, bool) {
	if o == nil || o.ImgList == nil {
		return nil, false
	}
	return o.ImgList, true
}

// HasImgList returns a boolean if a field has been set.
func (o *DocBusinessTypeVo) HasImgList() bool {
	if o != nil && o.ImgList != nil {
		return true
	}

	return false
}

// SetImgList gets a reference to the given []DocumentTree and assigns it to the ImgList field.
func (o *DocBusinessTypeVo) SetImgList(v []DocumentTree) {
	o.ImgList = &v
}

// GetNoneList returns the NoneList field value if set, zero value otherwise.
func (o *DocBusinessTypeVo) GetNoneList() []DocumentTree {
	if o == nil || o.NoneList == nil {
		var ret []DocumentTree
		return ret
	}
	return *o.NoneList
}

// GetNoneListOk returns a tuple with the NoneList field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocBusinessTypeVo) GetNoneListOk() (*[]DocumentTree, bool) {
	if o == nil || o.NoneList == nil {
		return nil, false
	}
	return o.NoneList, true
}

// HasNoneList returns a boolean if a field has been set.
func (o *DocBusinessTypeVo) HasNoneList() bool {
	if o != nil && o.NoneList != nil {
		return true
	}

	return false
}

// SetNoneList gets a reference to the given []DocumentTree and assigns it to the NoneList field.
func (o *DocBusinessTypeVo) SetNoneList(v []DocumentTree) {
	o.NoneList = &v
}

// GetPdfList returns the PdfList field value if set, zero value otherwise.
func (o *DocBusinessTypeVo) GetPdfList() []DocumentTree {
	if o == nil || o.PdfList == nil {
		var ret []DocumentTree
		return ret
	}
	return *o.PdfList
}

// GetPdfListOk returns a tuple with the PdfList field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocBusinessTypeVo) GetPdfListOk() (*[]DocumentTree, bool) {
	if o == nil || o.PdfList == nil {
		return nil, false
	}
	return o.PdfList, true
}

// HasPdfList returns a boolean if a field has been set.
func (o *DocBusinessTypeVo) HasPdfList() bool {
	if o != nil && o.PdfList != nil {
		return true
	}

	return false
}

// SetPdfList gets a reference to the given []DocumentTree and assigns it to the PdfList field.
func (o *DocBusinessTypeVo) SetPdfList(v []DocumentTree) {
	o.PdfList = &v
}

// GetPptxList returns the PptxList field value if set, zero value otherwise.
func (o *DocBusinessTypeVo) GetPptxList() []DocumentTree {
	if o == nil || o.PptxList == nil {
		var ret []DocumentTree
		return ret
	}
	return *o.PptxList
}

// GetPptxListOk returns a tuple with the PptxList field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocBusinessTypeVo) GetPptxListOk() (*[]DocumentTree, bool) {
	if o == nil || o.PptxList == nil {
		return nil, false
	}
	return o.PptxList, true
}

// HasPptxList returns a boolean if a field has been set.
func (o *DocBusinessTypeVo) HasPptxList() bool {
	if o != nil && o.PptxList != nil {
		return true
	}

	return false
}

// SetPptxList gets a reference to the given []DocumentTree and assigns it to the PptxList field.
func (o *DocBusinessTypeVo) SetPptxList(v []DocumentTree) {
	o.PptxList = &v
}

// GetTableList returns the TableList field value if set, zero value otherwise.
func (o *DocBusinessTypeVo) GetTableList() []DocumentTree {
	if o == nil || o.TableList == nil {
		var ret []DocumentTree
		return ret
	}
	return *o.TableList
}

// GetTableListOk returns a tuple with the TableList field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocBusinessTypeVo) GetTableListOk() (*[]DocumentTree, bool) {
	if o == nil || o.TableList == nil {
		return nil, false
	}
	return o.TableList, true
}

// HasTableList returns a boolean if a field has been set.
func (o *DocBusinessTypeVo) HasTableList() bool {
	if o != nil && o.TableList != nil {
		return true
	}

	return false
}

// SetTableList gets a reference to the given []DocumentTree and assigns it to the TableList field.
func (o *DocBusinessTypeVo) SetTableList(v []DocumentTree) {
	o.TableList = &v
}

// GetTxtList returns the TxtList field value if set, zero value otherwise.
func (o *DocBusinessTypeVo) GetTxtList() []DocumentTree {
	if o == nil || o.TxtList == nil {
		var ret []DocumentTree
		return ret
	}
	return *o.TxtList
}

// GetTxtListOk returns a tuple with the TxtList field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocBusinessTypeVo) GetTxtListOk() (*[]DocumentTree, bool) {
	if o == nil || o.TxtList == nil {
		return nil, false
	}
	return o.TxtList, true
}

// HasTxtList returns a boolean if a field has been set.
func (o *DocBusinessTypeVo) HasTxtList() bool {
	if o != nil && o.TxtList != nil {
		return true
	}

	return false
}

// SetTxtList gets a reference to the given []DocumentTree and assigns it to the TxtList field.
func (o *DocBusinessTypeVo) SetTxtList(v []DocumentTree) {
	o.TxtList = &v
}

func (o DocBusinessTypeVo) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.DocsList != nil {
		toSerialize["docsList"] = o.DocsList
	}
	if o.ImgList != nil {
		toSerialize["imgList"] = o.ImgList
	}
	if o.NoneList != nil {
		toSerialize["noneList"] = o.NoneList
	}
	if o.PdfList != nil {
		toSerialize["pdfList"] = o.PdfList
	}
	if o.PptxList != nil {
		toSerialize["pptxList"] = o.PptxList
	}
	if o.TableList != nil {
		toSerialize["tableList"] = o.TableList
	}
	if o.TxtList != nil {
		toSerialize["txtList"] = o.TxtList
	}
	return json.Marshal(toSerialize)
}

type NullableDocBusinessTypeVo struct {
	value *DocBusinessTypeVo
	isSet bool
}

func (v NullableDocBusinessTypeVo) Get() *DocBusinessTypeVo {
	return v.value
}

func (v *NullableDocBusinessTypeVo) Set(val *DocBusinessTypeVo) {
	v.value = val
	v.isSet = true
}

func (v NullableDocBusinessTypeVo) IsSet() bool {
	return v.isSet
}

func (v *NullableDocBusinessTypeVo) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDocBusinessTypeVo(val *DocBusinessTypeVo) *NullableDocBusinessTypeVo {
	return &NullableDocBusinessTypeVo{value: val, isSet: true}
}

func (v NullableDocBusinessTypeVo) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDocBusinessTypeVo) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
