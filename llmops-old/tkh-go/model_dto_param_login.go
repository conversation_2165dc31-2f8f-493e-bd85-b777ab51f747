/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// DtoParamLogin DtoParamLogin
type DtoParamLogin struct {
	Code *string `json:"code,omitempty"`
	Key *string `json:"key,omitempty"`
	Password *string `json:"password,omitempty"`
	Username *string `json:"username,omitempty"`
}

// NewDtoParamLogin instantiates a new DtoParamLogin object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDtoParamLogin() *DtoParamLogin {
	this := DtoParamLogin{}
	return &this
}

// NewDtoParamLoginWithDefaults instantiates a new DtoParamLogin object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDtoParamLoginWithDefaults() *DtoParamLogin {
	this := DtoParamLogin{}
	return &this
}

// GetCode returns the Code field value if set, zero value otherwise.
func (o *DtoParamLogin) GetCode() string {
	if o == nil || o.Code == nil {
		var ret string
		return ret
	}
	return *o.Code
}

// GetCodeOk returns a tuple with the Code field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DtoParamLogin) GetCodeOk() (*string, bool) {
	if o == nil || o.Code == nil {
		return nil, false
	}
	return o.Code, true
}

// HasCode returns a boolean if a field has been set.
func (o *DtoParamLogin) HasCode() bool {
	if o != nil && o.Code != nil {
		return true
	}

	return false
}

// SetCode gets a reference to the given string and assigns it to the Code field.
func (o *DtoParamLogin) SetCode(v string) {
	o.Code = &v
}

// GetKey returns the Key field value if set, zero value otherwise.
func (o *DtoParamLogin) GetKey() string {
	if o == nil || o.Key == nil {
		var ret string
		return ret
	}
	return *o.Key
}

// GetKeyOk returns a tuple with the Key field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DtoParamLogin) GetKeyOk() (*string, bool) {
	if o == nil || o.Key == nil {
		return nil, false
	}
	return o.Key, true
}

// HasKey returns a boolean if a field has been set.
func (o *DtoParamLogin) HasKey() bool {
	if o != nil && o.Key != nil {
		return true
	}

	return false
}

// SetKey gets a reference to the given string and assigns it to the Key field.
func (o *DtoParamLogin) SetKey(v string) {
	o.Key = &v
}

// GetPassword returns the Password field value if set, zero value otherwise.
func (o *DtoParamLogin) GetPassword() string {
	if o == nil || o.Password == nil {
		var ret string
		return ret
	}
	return *o.Password
}

// GetPasswordOk returns a tuple with the Password field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DtoParamLogin) GetPasswordOk() (*string, bool) {
	if o == nil || o.Password == nil {
		return nil, false
	}
	return o.Password, true
}

// HasPassword returns a boolean if a field has been set.
func (o *DtoParamLogin) HasPassword() bool {
	if o != nil && o.Password != nil {
		return true
	}

	return false
}

// SetPassword gets a reference to the given string and assigns it to the Password field.
func (o *DtoParamLogin) SetPassword(v string) {
	o.Password = &v
}

// GetUsername returns the Username field value if set, zero value otherwise.
func (o *DtoParamLogin) GetUsername() string {
	if o == nil || o.Username == nil {
		var ret string
		return ret
	}
	return *o.Username
}

// GetUsernameOk returns a tuple with the Username field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DtoParamLogin) GetUsernameOk() (*string, bool) {
	if o == nil || o.Username == nil {
		return nil, false
	}
	return o.Username, true
}

// HasUsername returns a boolean if a field has been set.
func (o *DtoParamLogin) HasUsername() bool {
	if o != nil && o.Username != nil {
		return true
	}

	return false
}

// SetUsername gets a reference to the given string and assigns it to the Username field.
func (o *DtoParamLogin) SetUsername(v string) {
	o.Username = &v
}

func (o DtoParamLogin) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Code != nil {
		toSerialize["code"] = o.Code
	}
	if o.Key != nil {
		toSerialize["key"] = o.Key
	}
	if o.Password != nil {
		toSerialize["password"] = o.Password
	}
	if o.Username != nil {
		toSerialize["username"] = o.Username
	}
	return json.Marshal(toSerialize)
}

type NullableDtoParamLogin struct {
	value *DtoParamLogin
	isSet bool
}

func (v NullableDtoParamLogin) Get() *DtoParamLogin {
	return v.value
}

func (v *NullableDtoParamLogin) Set(val *DtoParamLogin) {
	v.value = val
	v.isSet = true
}

func (v NullableDtoParamLogin) IsSet() bool {
	return v.isSet
}

func (v *NullableDtoParamLogin) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDtoParamLogin(val *DtoParamLogin) *NullableDtoParamLogin {
	return &NullableDtoParamLogin{value: val, isSet: true}
}

func (v NullableDtoParamLogin) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDtoParamLogin) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
