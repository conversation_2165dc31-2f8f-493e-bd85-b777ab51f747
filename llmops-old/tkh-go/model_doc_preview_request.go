/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// DocPreviewRequest DocPreviewRequest
type DocPreviewRequest struct {
	DocUuid *string `json:"docUuid,omitempty"`
	EndPage *int32 `json:"endPage,omitempty"`
	StartPage *int32 `json:"startPage,omitempty"`
}

// NewDocPreviewRequest instantiates a new DocPreviewRequest object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDocPreviewRequest() *DocPreviewRequest {
	this := DocPreviewRequest{}
	return &this
}

// NewDocPreviewRequestWithDefaults instantiates a new DocPreviewRequest object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDocPreviewRequestWithDefaults() *DocPreviewRequest {
	this := DocPreviewRequest{}
	return &this
}

// GetDocUuid returns the DocUuid field value if set, zero value otherwise.
func (o *DocPreviewRequest) GetDocUuid() string {
	if o == nil || o.DocUuid == nil {
		var ret string
		return ret
	}
	return *o.DocUuid
}

// GetDocUuidOk returns a tuple with the DocUuid field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocPreviewRequest) GetDocUuidOk() (*string, bool) {
	if o == nil || o.DocUuid == nil {
		return nil, false
	}
	return o.DocUuid, true
}

// HasDocUuid returns a boolean if a field has been set.
func (o *DocPreviewRequest) HasDocUuid() bool {
	if o != nil && o.DocUuid != nil {
		return true
	}

	return false
}

// SetDocUuid gets a reference to the given string and assigns it to the DocUuid field.
func (o *DocPreviewRequest) SetDocUuid(v string) {
	o.DocUuid = &v
}

// GetEndPage returns the EndPage field value if set, zero value otherwise.
func (o *DocPreviewRequest) GetEndPage() int32 {
	if o == nil || o.EndPage == nil {
		var ret int32
		return ret
	}
	return *o.EndPage
}

// GetEndPageOk returns a tuple with the EndPage field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocPreviewRequest) GetEndPageOk() (*int32, bool) {
	if o == nil || o.EndPage == nil {
		return nil, false
	}
	return o.EndPage, true
}

// HasEndPage returns a boolean if a field has been set.
func (o *DocPreviewRequest) HasEndPage() bool {
	if o != nil && o.EndPage != nil {
		return true
	}

	return false
}

// SetEndPage gets a reference to the given int32 and assigns it to the EndPage field.
func (o *DocPreviewRequest) SetEndPage(v int32) {
	o.EndPage = &v
}

// GetStartPage returns the StartPage field value if set, zero value otherwise.
func (o *DocPreviewRequest) GetStartPage() int32 {
	if o == nil || o.StartPage == nil {
		var ret int32
		return ret
	}
	return *o.StartPage
}

// GetStartPageOk returns a tuple with the StartPage field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocPreviewRequest) GetStartPageOk() (*int32, bool) {
	if o == nil || o.StartPage == nil {
		return nil, false
	}
	return o.StartPage, true
}

// HasStartPage returns a boolean if a field has been set.
func (o *DocPreviewRequest) HasStartPage() bool {
	if o != nil && o.StartPage != nil {
		return true
	}

	return false
}

// SetStartPage gets a reference to the given int32 and assigns it to the StartPage field.
func (o *DocPreviewRequest) SetStartPage(v int32) {
	o.StartPage = &v
}

func (o DocPreviewRequest) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.DocUuid != nil {
		toSerialize["docUuid"] = o.DocUuid
	}
	if o.EndPage != nil {
		toSerialize["endPage"] = o.EndPage
	}
	if o.StartPage != nil {
		toSerialize["startPage"] = o.StartPage
	}
	return json.Marshal(toSerialize)
}

type NullableDocPreviewRequest struct {
	value *DocPreviewRequest
	isSet bool
}

func (v NullableDocPreviewRequest) Get() *DocPreviewRequest {
	return v.value
}

func (v *NullableDocPreviewRequest) Set(val *DocPreviewRequest) {
	v.value = val
	v.isSet = true
}

func (v NullableDocPreviewRequest) IsSet() bool {
	return v.isSet
}

func (v *NullableDocPreviewRequest) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDocPreviewRequest(val *DocPreviewRequest) *NullableDocPreviewRequest {
	return &NullableDocPreviewRequest{value: val, isSet: true}
}

func (v NullableDocPreviewRequest) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDocPreviewRequest) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
