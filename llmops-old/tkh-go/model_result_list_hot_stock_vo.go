/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// ResultListHotStockVo Result«List«HotStockVO»»
type ResultListHotStockVo struct {
	Code *int32 `json:"code,omitempty"`
	Data *[]HotStockVo `json:"data,omitempty"`
	Message *string `json:"message,omitempty"`
}

// NewResultListHotStockVo instantiates a new ResultListHotStockVo object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewResultListHotStockVo() *ResultListHotStockVo {
	this := ResultListHotStockVo{}
	return &this
}

// NewResultListHotStockVoWithDefaults instantiates a new ResultListHotStockVo object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewResultListHotStockVoWithDefaults() *ResultListHotStockVo {
	this := ResultListHotStockVo{}
	return &this
}

// GetCode returns the Code field value if set, zero value otherwise.
func (o *ResultListHotStockVo) GetCode() int32 {
	if o == nil || o.Code == nil {
		var ret int32
		return ret
	}
	return *o.Code
}

// GetCodeOk returns a tuple with the Code field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ResultListHotStockVo) GetCodeOk() (*int32, bool) {
	if o == nil || o.Code == nil {
		return nil, false
	}
	return o.Code, true
}

// HasCode returns a boolean if a field has been set.
func (o *ResultListHotStockVo) HasCode() bool {
	if o != nil && o.Code != nil {
		return true
	}

	return false
}

// SetCode gets a reference to the given int32 and assigns it to the Code field.
func (o *ResultListHotStockVo) SetCode(v int32) {
	o.Code = &v
}

// GetData returns the Data field value if set, zero value otherwise.
func (o *ResultListHotStockVo) GetData() []HotStockVo {
	if o == nil || o.Data == nil {
		var ret []HotStockVo
		return ret
	}
	return *o.Data
}

// GetDataOk returns a tuple with the Data field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ResultListHotStockVo) GetDataOk() (*[]HotStockVo, bool) {
	if o == nil || o.Data == nil {
		return nil, false
	}
	return o.Data, true
}

// HasData returns a boolean if a field has been set.
func (o *ResultListHotStockVo) HasData() bool {
	if o != nil && o.Data != nil {
		return true
	}

	return false
}

// SetData gets a reference to the given []HotStockVo and assigns it to the Data field.
func (o *ResultListHotStockVo) SetData(v []HotStockVo) {
	o.Data = &v
}

// GetMessage returns the Message field value if set, zero value otherwise.
func (o *ResultListHotStockVo) GetMessage() string {
	if o == nil || o.Message == nil {
		var ret string
		return ret
	}
	return *o.Message
}

// GetMessageOk returns a tuple with the Message field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ResultListHotStockVo) GetMessageOk() (*string, bool) {
	if o == nil || o.Message == nil {
		return nil, false
	}
	return o.Message, true
}

// HasMessage returns a boolean if a field has been set.
func (o *ResultListHotStockVo) HasMessage() bool {
	if o != nil && o.Message != nil {
		return true
	}

	return false
}

// SetMessage gets a reference to the given string and assigns it to the Message field.
func (o *ResultListHotStockVo) SetMessage(v string) {
	o.Message = &v
}

func (o ResultListHotStockVo) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Code != nil {
		toSerialize["code"] = o.Code
	}
	if o.Data != nil {
		toSerialize["data"] = o.Data
	}
	if o.Message != nil {
		toSerialize["message"] = o.Message
	}
	return json.Marshal(toSerialize)
}

type NullableResultListHotStockVo struct {
	value *ResultListHotStockVo
	isSet bool
}

func (v NullableResultListHotStockVo) Get() *ResultListHotStockVo {
	return v.value
}

func (v *NullableResultListHotStockVo) Set(val *ResultListHotStockVo) {
	v.value = val
	v.isSet = true
}

func (v NullableResultListHotStockVo) IsSet() bool {
	return v.isSet
}

func (v *NullableResultListHotStockVo) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableResultListHotStockVo(val *ResultListHotStockVo) *NullableResultListHotStockVo {
	return &NullableResultListHotStockVo{value: val, isSet: true}
}

func (v NullableResultListHotStockVo) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableResultListHotStockVo) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
