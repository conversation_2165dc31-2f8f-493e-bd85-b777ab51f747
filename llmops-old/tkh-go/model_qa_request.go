/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// QaRequest QARequest
type QaRequest struct {
	Answer *string `json:"answer,omitempty"`
	// 会话id
	ConversationId string `json:"conversationId"`
	Question *string `json:"question,omitempty"`
}

// NewQaRequest instantiates a new QaRequest object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewQaRequest(conversationId string, ) *QaRequest {
	this := QaRequest{}
	this.ConversationId = conversationId
	return &this
}

// NewQaRequestWithDefaults instantiates a new QaRequest object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewQaRequestWithDefaults() *QaRequest {
	this := QaRequest{}
	return &this
}

// GetAnswer returns the Answer field value if set, zero value otherwise.
func (o *QaRequest) GetAnswer() string {
	if o == nil || o.Answer == nil {
		var ret string
		return ret
	}
	return *o.Answer
}

// GetAnswerOk returns a tuple with the Answer field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *QaRequest) GetAnswerOk() (*string, bool) {
	if o == nil || o.Answer == nil {
		return nil, false
	}
	return o.Answer, true
}

// HasAnswer returns a boolean if a field has been set.
func (o *QaRequest) HasAnswer() bool {
	if o != nil && o.Answer != nil {
		return true
	}

	return false
}

// SetAnswer gets a reference to the given string and assigns it to the Answer field.
func (o *QaRequest) SetAnswer(v string) {
	o.Answer = &v
}

// GetConversationId returns the ConversationId field value
func (o *QaRequest) GetConversationId() string {
	if o == nil  {
		var ret string
		return ret
	}

	return o.ConversationId
}

// GetConversationIdOk returns a tuple with the ConversationId field value
// and a boolean to check if the value has been set.
func (o *QaRequest) GetConversationIdOk() (*string, bool) {
	if o == nil  {
		return nil, false
	}
	return &o.ConversationId, true
}

// SetConversationId sets field value
func (o *QaRequest) SetConversationId(v string) {
	o.ConversationId = v
}

// GetQuestion returns the Question field value if set, zero value otherwise.
func (o *QaRequest) GetQuestion() string {
	if o == nil || o.Question == nil {
		var ret string
		return ret
	}
	return *o.Question
}

// GetQuestionOk returns a tuple with the Question field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *QaRequest) GetQuestionOk() (*string, bool) {
	if o == nil || o.Question == nil {
		return nil, false
	}
	return o.Question, true
}

// HasQuestion returns a boolean if a field has been set.
func (o *QaRequest) HasQuestion() bool {
	if o != nil && o.Question != nil {
		return true
	}

	return false
}

// SetQuestion gets a reference to the given string and assigns it to the Question field.
func (o *QaRequest) SetQuestion(v string) {
	o.Question = &v
}

func (o QaRequest) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Answer != nil {
		toSerialize["answer"] = o.Answer
	}
	if true {
		toSerialize["conversationId"] = o.ConversationId
	}
	if o.Question != nil {
		toSerialize["question"] = o.Question
	}
	return json.Marshal(toSerialize)
}

type NullableQaRequest struct {
	value *QaRequest
	isSet bool
}

func (v NullableQaRequest) Get() *QaRequest {
	return v.value
}

func (v *NullableQaRequest) Set(val *QaRequest) {
	v.value = val
	v.isSet = true
}

func (v NullableQaRequest) IsSet() bool {
	return v.isSet
}

func (v *NullableQaRequest) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableQaRequest(val *QaRequest) *NullableQaRequest {
	return &NullableQaRequest{value: val, isSet: true}
}

func (v NullableQaRequest) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableQaRequest) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
