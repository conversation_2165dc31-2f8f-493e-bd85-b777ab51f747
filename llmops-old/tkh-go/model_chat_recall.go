package tkh

type TkhRecallResponse []TkhRecallResponseElement

type TkhRecallResponseElement struct {
	Metadata    RecallRspMetadata `json:"metadata"`
	PageContent string            `json:"page_content"`
}

type RecallRspMetadata struct {
	Content     *string         `json:"content,omitempty"`
	Creator     string          `json:"creator"`
	DirIDS      []string        `json:"dir_ids,omitempty"`
	DocID       string          `json:"doc_id"`
	DocName     string          `json:"doc_name"`
	DocType     string          `json:"doc_type"`
	Index       string          `json:"index"`
	InsertTime  string          `json:"insert_time"`
	ItemID      string          `json:"item_id"`
	KBID        string          `json:"kb_id"`
	Link2Source *string         `json:"link2source,omitempty"`
	LinkTitle   *string         `json:"link_title,omitempty"`
	Pages       []string        `json:"pages"`
	PublishTime string          `json:"publish_time"`
	Rectangles  interface{}     `json:"rectangles"`
	Score       float64         `json:"score"`
	Source      RecallRspSource `json:"source"`
	StartPage   int64           `json:"start_page"`
	Text        string          `json:"text"`
	Type        string          `json:"type"`
}

type RecallRspSource struct {
	DirID interface{} `json:"dir_id"`
	DocID string      `json:"doc_id"`
	KBID  string      `json:"kb_id"`
}
