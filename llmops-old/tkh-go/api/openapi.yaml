openapi: 3.0.1
info:
  title: tkh3
  version: 1.0.0
servers:
- url: /
tags:
- name: chat
- name: chat/recall
- name: base
- name: base/审批管理
- name: base/认证管理
- name: base/知识库文档操作
- name: base/文档预览
- name: base/图片检索
- name: base/文档及智能问答
- name: base/用户管理
paths:
  /infinity/chat/recall:
    post:
      deprecated: false
      operationId: recall_infinity_chat_recall_post
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RecallServiceRequest'
      responses:
        "200":
          content:
            application/json:
              schema:
                type: string
          description: Successful Response
        "422":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-ignore-properties: []
                x-apifox-orders: []
          description: Validation Error
      security: []
      summary: Recall
      tags:
      - chat/recall
      x-apifox-folder: chat/recall
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160180437-run
  /infinity/base/approve/admin/apikey/approval:
    get:
      deprecated: false
      operationId: approvalForApikeyUsingGET
      parameters:
      - description: id
        explode: true
        in: query
        name: id
        required: true
        schema:
          type: integer
        style: form
      - description: status
        explode: true
        in: query
        name: status
        required: true
        schema:
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultBoolean'
          description: OK
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 审批apikey
      tags:
      - base/审批管理
      x-apifox-folder: base/审批管理
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182401-run
  /infinity/base/approve/apikey/apply:
    post:
      deprecated: false
      operationId: applyForApikeyUsingPOST
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DtoParamApply'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultApproveInfoDuiXiang'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 申请apikey
      tags:
      - base/审批管理
      x-apifox-folder: base/审批管理
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182402-run
  /infinity/base/approve/apikey/approval:
    delete:
      deprecated: false
      operationId: deleteByIdUsingDELETE
      parameters:
      - description: id
        explode: true
        in: query
        name: id
        required: true
        schema:
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultBoolean'
          description: OK
        "204":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: No Content
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
      security: []
      summary: 删除申请记录 根据id
      tags:
      - base/审批管理
      x-apifox-folder: base/审批管理
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182404-run
    get:
      deprecated: false
      operationId: listApproveInfoUsingGET
      parameters: []
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultListApproveInfoDuiXiang'
          description: OK
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 获取apikey申请列表
      tags:
      - base/审批管理
      x-apifox-folder: base/审批管理
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182403-run
  /infinity/base/auth/any/captcha:
    get:
      deprecated: false
      operationId: getCaptchaUsingGET
      parameters: []
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultDtoResultCaptcha'
          description: OK
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 获取验证码
      tags:
      - base/认证管理
      x-apifox-folder: base/认证管理
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182405-run
  /infinity/base/auth/any/login:
    post:
      deprecated: false
      operationId: loginUsingPOST
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DtoParamLogin'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultUserInfoDuiXiang'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 登录
      tags:
      - base/认证管理
      x-apifox-folder: base/认证管理
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182406-run
  /infinity/base/auth/any/login-tds:
    post:
      deprecated: false
      operationId: loginTDSUsingPOST
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TokenRequestInfo'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultUserInfoEntity'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 登录TDS
      tags:
      - base/认证管理
      x-apifox-folder: base/认证管理
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182407-run
  /infinity/base/auth/info:
    get:
      deprecated: false
      operationId: getUserUsingGET
      parameters: []
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultUserInfoDuiXiang'
          description: OK
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 获取当前登录用户个人信息
      tags:
      - base/认证管理
      x-apifox-folder: base/认证管理
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182408-run
  /infinity/base/auth/tds-user-info:
    get:
      deprecated: false
      operationId: getTdsUserInfoUsingGET
      parameters:
      - description: Authorization
        explode: false
        in: header
        name: Authorization
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultUserInfoEntity'
          description: OK
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 获取tds用户详情
      tags:
      - base/认证管理
      x-apifox-folder: base/认证管理
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182409-run
  /infinity/base/documents/add-directory:
    post:
      deprecated: false
      operationId: addDirectoryUsingPOST
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddDirectoryRequest'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultDocumentTree'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 知识库下创建一个文件夹
      tags:
      - base/知识库文档操作
      x-apifox-folder: base/知识库文档操作
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182410-run
  /infinity/base/documents/add-repository:
    post:
      deprecated: false
      operationId: addRepositoryUsingPOST
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddRepoRequest'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultRepository'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 创建一个基础知识库
      tags:
      - base/知识库文档操作
      x-apifox-folder: base/知识库文档操作
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182411-run
  /infinity/base/documents/full-processing-files:
    post:
      deprecated: false
      description: 适用于标准全流程类型，包括图片 文档
      operationId: fullProcessingFilesUsingPOST
      parameters: []
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              properties: {}
              type: object
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultLong'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 批量上传解析存储文件--全同步
      tags:
      - base/知识库文档操作
      x-apifox-folder: base/知识库文档操作
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182412-run
  /infinity/base/documents/full-processing-single-file:
    post:
      deprecated: false
      description: id可不传，默认在个人知识库根目录下
      operationId: fullProcessingSingleFileUsingPOST
      parameters: []
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties: {}
              type: object
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultDocument'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 上传解析存储单个文件--同步
      tags:
      - base/知识库文档操作
      x-apifox-folder: base/知识库文档操作
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182413-run
  /infinity/base/documents/get-datasource:
    post:
      deprecated: false
      operationId: getDatasourceByIdUsingPOST
      parameters:
      - description: datasourceId
        explode: true
        in: query
        name: datasourceId
        required: true
        schema:
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultListDocument'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 获取datasource连接信息
      tags:
      - base/知识库文档操作
      x-apifox-folder: base/知识库文档操作
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182414-run
  /infinity/base/documents/get-full-tree:
    post:
      deprecated: false
      operationId: getFullTreeByIdUsingPOST
      parameters:
      - description: id
        explode: true
        in: query
        name: id
        required: true
        schema:
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultFullTree'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 根据id获取完整树
      tags:
      - base/知识库文档操作
      x-apifox-folder: base/知识库文档操作
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182415-run
  /infinity/base/documents/get-upload-task-detail:
    post:
      deprecated: false
      operationId: getUploadTaskDetailUsingPOST
      parameters:
      - description: id
        explode: true
        in: query
        name: id
        required: true
        schema:
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultDocumentUploadTask'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 获取上传任务详情，包括任务状况，各文件状态
      tags:
      - base/知识库文档操作
      x-apifox-folder: base/知识库文档操作
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182416-run
  /infinity/base/documents/list:
    get:
      deprecated: false
      operationId: listAllDocTreesUsingGET
      parameters: []
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultListRepository'
          description: OK
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 获取所有文本类型知识库列表
      tags:
      - base/知识库文档操作
      x-apifox-folder: base/知识库文档操作
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182417-run
  /infinity/base/documents/list-files-business-type:
    post:
      deprecated: false
      operationId: listFilesByBusinessTypeUsingPOST
      parameters:
      - description: id
        explode: true
        in: query
        name: id
        required: true
        schema:
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultDocBusinessTypeVo'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: id获取文件夹下按照业务类型分组的所有文档(树展开状态)
      tags:
      - base/知识库文档操作
      x-apifox-folder: base/知识库文档操作
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182418-run
  /infinity/base/documents/list-user-trees:
    post:
      deprecated: false
      operationId: listUserDocTreesUsingPOST
      parameters: []
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultUserDocTreeResponse'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 获取用户有权限的知识库列表,包含私库+sdb
      tags:
      - base/知识库文档操作
      x-apifox-folder: base/知识库文档操作
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182419-run
  /infinity/base/documents/print-services:
    get:
      deprecated: false
      operationId: printServicesUsingGET
      parameters: []
      responses:
        "200":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: OK
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: printServices
      tags:
      - base/知识库文档操作
      x-apifox-folder: base/知识库文档操作
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182420-run
  /infinity/base/documents/split/batch/save:
    post:
      deprecated: false
      operationId: singleDocumentSplitUsingPOST
      parameters:
      - description: uploadTaskId
        explode: true
        in: query
        name: uploadTaskId
        required: true
        schema:
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultString'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 批量文档保存
      tags:
      - base/知识库文档操作
      x-apifox-folder: base/知识库文档操作
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182421-run
  /infinity/base/documents/split/cache/save:
    post:
      deprecated: false
      operationId: singleDocumentSplitUsingPOST_1
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocCacheRequest'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultString'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 单文档编辑确定
      tags:
      - base/知识库文档操作
      x-apifox-folder: base/知识库文档操作
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182422-run
  /infinity/base/documents/split/doc/read:
    post:
      deprecated: false
      operationId: splitDocReadUsingPOST
      parameters:
      - description: fileUuid
        explode: true
        in: query
        name: fileUuid
        required: true
        schema:
          type: string
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultListDocChunk'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 编辑文档查看
      tags:
      - base/知识库文档操作
      x-apifox-folder: base/知识库文档操作
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182423-run
  /infinity/base/documents/split/document:
    post:
      deprecated: false
      operationId: splitDocumentUsingPOST
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SingleDocSplitParam'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultDocumentUploadTask'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 单个文件的解析服务
      tags:
      - base/知识库文档操作
      x-apifox-folder: base/知识库文档操作
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182424-run
  /infinity/base/documents/split/documents:
    post:
      deprecated: false
      operationId: documentsSplitUsingPOST
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              items:
                $ref: '#/components/schemas/SingleDocSplitParam'
              type: array
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultDocumentUploadTask'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 批量文档解析
      tags:
      - base/知识库文档操作
      x-apifox-folder: base/知识库文档操作
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182425-run
  /infinity/base/documents/upload/local:
    post:
      deprecated: false
      operationId: uploadLocalUsingPOST
      parameters: []
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              properties: {}
              type: object
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultLong'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 批量上传文件
      tags:
      - base/知识库文档操作
      x-apifox-folder: base/知识库文档操作
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182426-run
  /infinity/base/documents/upload/repository:
    post:
      deprecated: false
      operationId: uploadRepositoryUsingPOST
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              items:
                format: int64
                type: integer
              type: array
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultListDocument'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 获取知识库文件详情
      tags:
      - base/知识库文档操作
      x-apifox-folder: base/知识库文档操作
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182427-run
  /infinity/base/documents/{id}/list:
    get:
      deprecated: false
      operationId: getDocTreeByIdUsingGET
      parameters:
      - description: id
        explode: false
        in: path
        name: id
        required: true
        schema:
          type: integer
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultTreeNodeListVo'
          description: OK
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 根据id获取文档目录
      tags:
      - base/知识库文档操作
      x-apifox-folder: base/知识库文档操作
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182428-run
  /infinity/base/documents/highlight:
    get:
      deprecated: false
      operationId: highlightUsingGET
      parameters:
      - description: image
        explode: true
        in: query
        name: image
        required: false
        schema:
          type: boolean
        style: form
      - description: item_id
        explode: true
        in: query
        name: item_id
        required: true
        schema:
          type: string
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: OK
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 根据文本片段ID对文档高亮
      tags:
      - base/文档预览
      x-apifox-folder: base/文档预览
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182429-run
    post:
      deprecated: false
      operationId: highlightUsingPOST_1
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocHighlightRequest'
      responses:
        "200":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 根据坐标或文本对文档高亮
      tags:
      - base/文档预览
      x-apifox-folder: base/文档预览
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182430-run
  /infinity/base/documents/highlight/online:
    post:
      deprecated: false
      operationId: highlightUsingPOST
      parameters:
      - description: endPage
        explode: true
        in: query
        name: endPage
        required: true
        schema:
          type: integer
        style: form
      - description: rectangles
        explode: true
        in: query
        name: rectangles
        required: true
        schema:
          items:
            type: string
          type: array
        style: form
      - description: startPage
        explode: true
        in: query
        name: startPage
        required: true
        schema:
          type: integer
        style: form
      requestBody:
        $ref: '#/components/requestBodies/inline_object'
        content:
          multipart/form-data:
            schema:
              properties:
                file:
                  description: file
                  format: binary
                  type: string
              required:
              - file
              type: object
      responses:
        "200":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 在线根据坐标对文档高亮
      tags:
      - base/文档预览
      x-apifox-folder: base/文档预览
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182431-run
  /infinity/base/documents/preview:
    post:
      deprecated: false
      operationId: previewDocUsingPOST
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocPreviewRequest'
      responses:
        "200":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 预览指定文档
      tags:
      - base/文档预览
      x-apifox-folder: base/文档预览
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182432-run
  /infinity/base/image/match:
    get:
      deprecated: false
      operationId: matchImagesUsingGET
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ImageMatchRequest'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultImageQueryResponse'
          description: OK
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 根据图片检索图片
      tags:
      - base/图片检索
      x-apifox-folder: base/图片检索
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182433-run
  /infinity/base/image/query:
    post:
      deprecated: false
      operationId: queryImagesUsingPOST
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ImageQueryRequest'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultImageQueryResponse'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 根据文本检索图片
      tags:
      - base/图片检索
      x-apifox-folder: base/图片检索
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182434-run
  /infinity/base/qa/add-QA:
    post:
      deprecated: false
      operationId: addQAUsingPOST
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QaRequest'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultString'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 问答记录新增
      tags:
      - base/文档及智能问答
      x-apifox-folder: base/文档及智能问答
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182435-run
  /infinity/base/qa/add-conversation:
    post:
      deprecated: false
      operationId: addConversationUsingPOST
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConversationRequest'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultString'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 会话新增
      tags:
      - base/文档及智能问答
      x-apifox-folder: base/文档及智能问答
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182436-run
  /infinity/base/qa/delete-conversation:
    delete:
      deprecated: false
      operationId: deleteConversationUsingDELETE
      parameters:
      - description: conversationId
        explode: true
        in: query
        name: conversationId
        required: true
        schema:
          type: string
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultString'
          description: OK
        "204":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: No Content
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
      security: []
      summary: 会话删除
      tags:
      - base/文档及智能问答
      x-apifox-folder: base/文档及智能问答
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182437-run
  /infinity/base/qa/get-conversation:
    get:
      deprecated: false
      operationId: getConversationListUsingGET
      parameters:
      - description: pageNum
        explode: true
        in: query
        name: pageNum
        required: true
        schema:
          type: integer
        style: form
      - description: pageSize
        explode: true
        in: query
        name: pageSize
        required: true
        schema:
          type: integer
        style: form
      - description: type
        explode: true
        in: query
        name: type
        required: false
        schema:
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultPageConversationVo'
          description: OK
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 会话列表查询
      tags:
      - base/文档及智能问答
      x-apifox-folder: base/文档及智能问答
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182438-run
  /infinity/base/qa/get-conversation-detail:
    get:
      deprecated: false
      operationId: getConversationDetailUsingGET
      parameters:
      - description: conversationId
        explode: true
        in: query
        name: conversationId
        required: true
        schema:
          type: string
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultConversationDetailVo'
          description: OK
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 会话详情查询
      tags:
      - base/文档及智能问答
      x-apifox-folder: base/文档及智能问答
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182439-run
  /infinity/base/qa/get-ind:
    post:
      deprecated: false
      operationId: getTagStockListUsingPOST
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TagHotStockRequest'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultListIndOrConceptVo'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 根据股票名称list获取行业或概念
      tags:
      - base/文档及智能问答
      x-apifox-folder: base/文档及智能问答
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182440-run
  /infinity/base/qa/get-ind-trend:
    post:
      deprecated: false
      operationId: getIndTrendListUsingPOST
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IndTrendRequest'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultListIndTrendVo'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 行业走势
      tags:
      - base/文档及智能问答
      x-apifox-folder: base/文档及智能问答
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182441-run
  /infinity/base/qa/get-stock:
    get:
      deprecated: false
      operationId: getStockListUsingGET
      parameters:
      - description: indName
        explode: true
        in: query
        name: indName
        required: true
        schema:
          type: string
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultListHotStockVo'
          description: OK
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 根据行业或概念名称获取个股列表
      tags:
      - base/文档及智能问答
      x-apifox-folder: base/文档及智能问答
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182442-run
  /infinity/base/qa/get-stock-trend:
    post:
      deprecated: false
      operationId: getStockTrendListUsingPOST
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StockTrendRequest'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultStockTrendVo'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 上市公司/股票代码股价走势
      tags:
      - base/文档及智能问答
      x-apifox-folder: base/文档及智能问答
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182443-run
  /infinity/base/qa/update-conversation:
    post:
      deprecated: false
      operationId: updateConversationUsingPOST
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConversationUpdateRequest'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultString'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 会话标题修改
      tags:
      - base/文档及智能问答
      x-apifox-folder: base/文档及智能问答
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182444-run
  /infinity/base/user/admin/add:
    post:
      deprecated: false
      operationId: addUsersUsingPOST
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              items:
                $ref: '#/components/schemas/UserInfoDuiXiang'
              type: array
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultBoolean'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: admin-创建用户
      tags:
      - base/用户管理
      x-apifox-folder: base/用户管理
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182445-run
  /infinity/base/user/admin/del:
    get:
      deprecated: false
      operationId: delUsersUsingGET
      parameters:
      - description: id
        explode: true
        in: query
        name: id
        required: true
        schema:
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultBoolean'
          description: OK
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: admin-删除用户
      tags:
      - base/用户管理
      x-apifox-folder: base/用户管理
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182446-run
  /infinity/base/user/admin/permission:
    get:
      deprecated: false
      operationId: modifyPermissionUsingGET
      parameters:
      - description: isAdmin
        explode: true
        in: query
        name: isAdmin
        required: true
        schema:
          type: boolean
        style: form
      - description: uid
        explode: true
        in: query
        name: uid
        required: true
        schema:
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultBoolean'
          description: OK
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: admin-修改用户权限
      tags:
      - base/用户管理
      x-apifox-folder: base/用户管理
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182447-run
  /infinity/base/user/admin/reset:
    get:
      deprecated: false
      operationId: resetUserUsingGET
      parameters:
      - description: uid
        explode: true
        in: query
        name: uid
        required: true
        schema:
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultBoolean'
          description: OK
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: admin-重置用户
      tags:
      - base/用户管理
      x-apifox-folder: base/用户管理
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182448-run
  /infinity/base/user/admin/user-list:
    get:
      deprecated: false
      operationId: getUserListUsingGET
      parameters:
      - description: likeName
        explode: true
        in: query
        name: likeName
        required: false
        schema:
          type: string
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultListUserInfoDuiXiang'
          description: OK
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: admin-根据用户名模糊查找用户list
      tags:
      - base/用户管理
      x-apifox-folder: base/用户管理
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182449-run
  /infinity/base/user/password:
    post:
      deprecated: false
      operationId: updatePasswordUsingPOST
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DtoParamUpdatePassword'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResultBoolean'
          description: OK
        "201":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Created
        "401":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Unauthorized
        "403":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Forbidden
        "404":
          content:
            application/json:
              schema:
                properties: {}
                type: object
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          description: Not Found
      security: []
      summary: 修改密码
      tags:
      - base/用户管理
      x-apifox-folder: base/用户管理
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/4243661/apis/api-160182450-run
components:
  requestBodies:
    inline_object:
      content:
        multipart/form-data:
          schema:
            $ref: '#/components/schemas/inline_object'
  schemas:
    UserInfoDuiXiang:
      description: UserInfo对象
      example:
        password: password
        createTime: createTime
        admin: true
        updateTime: updateTime
        id: 6
        status: 1
        username: username
      properties:
        admin:
          type: boolean
        createTime:
          type: string
        id:
          format: int32
          type: integer
        password:
          type: string
        status:
          format: int32
          type: integer
        updateTime:
          type: string
        username:
          type: string
      title: UserInfo对象
      type: object
      x-apifox-orders:
      - admin
      - createTime
      - id
      - password
      - status
      - updateTime
      - username
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    UserInfoEntity:
      description: UserInfoEntity
      example:
        ownWorkspaceIds:
        - ownWorkspaceIds
        - ownWorkspaceIds
        isWorkspaceAdmin: true
        updateUser: updateUser
        roleInfoMap:
          key: roleInfoMap
        telephone: telephone
        updateTime: 2000-01-23T04:56:07.000+00:00
        isAdmin: true
        locale: locale
        organName: organName
        realName: realName
        password: password
        workspaceRoleIds:
          key:
          - workspaceRoleIds
          - workspaceRoleIds
        roleIds:
        - roleIds
        - roleIds
        displayWorkspace: displayWorkspace
        createTime: 2000-01-23T04:56:07.000+00:00
        organId: organId
        createUser: createUser
        workspaceName: workspaceName
        id: id
        ownGroupNames:
        - ownGroupNames
        - ownGroupNames
        email: email
        status: Normal
        username: username
      properties:
        createTime:
          description: 创建时间
          format: date-time
          type: string
        createUser:
          description: 创建人
          type: string
        displayWorkspace:
          description: 显示工作区
          type: string
        email:
          type: string
        id:
          type: string
        isAdmin:
          description: 是否系统管理员
          type: boolean
        isWorkspaceAdmin:
          description: 是否工作区管理员
          type: boolean
        locale:
          description: 语言环境
          type: string
        organId:
          description: 用户机构
          type: string
        organName:
          description: 用户所属机构名称，前端展示需要
          type: string
        ownGroupNames:
          description: 所属工作组
          items:
            type: string
          type: array
        ownWorkspaceIds:
          description: 管理的工作区列表
          items:
            type: string
          type: array
        password:
          type: string
        realName:
          type: string
        roleIds:
          description: 用户角色列表，或组内角色
          items:
            type: string
          type: array
        roleInfoMap:
          additionalProperties:
            type: string
          description: 用户所属角色ID和角色中文名对应关系Map，前端展示需要
          properties: {}
          type: object
          x-apifox-orders: []
          x-apifox-ignore-properties: []
        status:
          description: 是否启用
          enum:
          - Normal
          - Disable
          - PWD_Lock
          type: string
        telephone:
          type: string
        updateTime:
          description: 更新时间
          format: date-time
          type: string
        updateUser:
          description: 修改人
          type: string
        username:
          type: string
        workspaceName:
          description: 工作区名称
          type: string
        workspaceRoleIds:
          additionalProperties:
            items:
              type: string
            type: array
          description: 工作区角色
          properties: {}
          type: object
          x-apifox-orders: []
          x-apifox-ignore-properties: []
      title: UserInfoEntity
      type: object
      x-apifox-orders:
      - createTime
      - createUser
      - displayWorkspace
      - email
      - id
      - isAdmin
      - isWorkspaceAdmin
      - locale
      - organId
      - organName
      - ownGroupNames
      - ownWorkspaceIds
      - password
      - realName
      - roleIds
      - roleInfoMap
      - status
      - telephone
      - updateTime
      - updateUser
      - username
      - workspaceName
      - workspaceRoleIds
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    UserDocTreeResponse:
      description: UserDocTreeResponse
      example:
        publicTree:
        - ownerType: 5
          creator: creator
          documentTreeId: 1
          uploadEnable: true
          description: description
          repositoryStorageType: 2
          updateTime: updateTime
          uuid: uuid
          createTime: createTime
          datasourceId: 6
          name: name
          repositoryType: 7
          id: 5
        - ownerType: 5
          creator: creator
          documentTreeId: 1
          uploadEnable: true
          description: description
          repositoryStorageType: 2
          updateTime: updateTime
          uuid: uuid
          createTime: createTime
          datasourceId: 6
          name: name
          repositoryType: 7
          id: 5
        financeTree:
        - ownerType: 5
          creator: creator
          documentTreeId: 1
          uploadEnable: true
          description: description
          repositoryStorageType: 2
          updateTime: updateTime
          uuid: uuid
          createTime: createTime
          datasourceId: 6
          name: name
          repositoryType: 7
          id: 5
        - ownerType: 5
          creator: creator
          documentTreeId: 1
          uploadEnable: true
          description: description
          repositoryStorageType: 2
          updateTime: updateTime
          uuid: uuid
          createTime: createTime
          datasourceId: 6
          name: name
          repositoryType: 7
          id: 5
        privateTree:
        - ownerType: 5
          creator: creator
          documentTreeId: 1
          uploadEnable: true
          description: description
          repositoryStorageType: 2
          updateTime: updateTime
          uuid: uuid
          createTime: createTime
          datasourceId: 6
          name: name
          repositoryType: 7
          id: 5
        - ownerType: 5
          creator: creator
          documentTreeId: 1
          uploadEnable: true
          description: description
          repositoryStorageType: 2
          updateTime: updateTime
          uuid: uuid
          createTime: createTime
          datasourceId: 6
          name: name
          repositoryType: 7
          id: 5
        lawTree:
        - ownerType: 5
          creator: creator
          documentTreeId: 1
          uploadEnable: true
          description: description
          repositoryStorageType: 2
          updateTime: updateTime
          uuid: uuid
          createTime: createTime
          datasourceId: 6
          name: name
          repositoryType: 7
          id: 5
        - ownerType: 5
          creator: creator
          documentTreeId: 1
          uploadEnable: true
          description: description
          repositoryStorageType: 2
          updateTime: updateTime
          uuid: uuid
          createTime: createTime
          datasourceId: 6
          name: name
          repositoryType: 7
          id: 5
        internetTree:
        - ownerType: 5
          creator: creator
          documentTreeId: 1
          uploadEnable: true
          description: description
          repositoryStorageType: 2
          updateTime: updateTime
          uuid: uuid
          createTime: createTime
          datasourceId: 6
          name: name
          repositoryType: 7
          id: 5
        - ownerType: 5
          creator: creator
          documentTreeId: 1
          uploadEnable: true
          description: description
          repositoryStorageType: 2
          updateTime: updateTime
          uuid: uuid
          createTime: createTime
          datasourceId: 6
          name: name
          repositoryType: 7
          id: 5
      properties:
        financeTree:
          items:
            $ref: '#/components/schemas/Repository'
          type: array
        internetTree:
          items:
            $ref: '#/components/schemas/Repository'
          type: array
        lawTree:
          items:
            $ref: '#/components/schemas/Repository'
          type: array
        privateTree:
          items:
            $ref: '#/components/schemas/Repository'
          type: array
        publicTree:
          items:
            $ref: '#/components/schemas/Repository'
          type: array
      title: UserDocTreeResponse
      type: object
      x-apifox-orders:
      - financeTree
      - internetTree
      - lawTree
      - privateTree
      - publicTree
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    Url:
      description: URL
      properties:
        authority:
          type: string
        content:
          properties: {}
          type: object
          x-apifox-orders: []
          x-apifox-ignore-properties: []
        defaultPort:
          format: int32
          type: integer
        file:
          type: string
        host:
          type: string
        path:
          type: string
        port:
          format: int32
          type: integer
        protocol:
          type: string
        query:
          type: string
        ref:
          type: string
        userInfo:
          type: string
      title: URL
      type: object
      x-apifox-orders:
      - authority
      - content
      - defaultPort
      - file
      - host
      - path
      - port
      - protocol
      - query
      - ref
      - userInfo
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    Uri:
      description: URI
      properties:
        absolute:
          type: boolean
        authority:
          type: string
        fragment:
          type: string
        host:
          type: string
        opaque:
          type: boolean
        path:
          type: string
        port:
          format: int32
          type: integer
        query:
          type: string
        rawAuthority:
          type: string
        rawFragment:
          type: string
        rawPath:
          type: string
        rawQuery:
          type: string
        rawSchemeSpecificPart:
          type: string
        rawUserInfo:
          type: string
        scheme:
          type: string
        schemeSpecificPart:
          type: string
        userInfo:
          type: string
      title: URI
      type: object
      x-apifox-orders:
      - absolute
      - authority
      - fragment
      - host
      - opaque
      - path
      - port
      - query
      - rawAuthority
      - rawFragment
      - rawPath
      - rawQuery
      - rawSchemeSpecificPart
      - rawUserInfo
      - scheme
      - schemeSpecificPart
      - userInfo
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    TreeNodeListVo:
      description: TreeNodeListVO
      example:
        children:
        - creator: creator
          fileSuffix: 7
          fileBusinessType: 1
          updateTime: updateTime
          fileMd5: fileMd5
          fileUploadTaskId: 9
          fileContentType: 5
          parentId: 2
          createTime: createTime
          fileSize: 5
          fileStatus: 2
          name: name
          id: 3
          category: 6
          fileUuid: fileUuid
        - creator: creator
          fileSuffix: 7
          fileBusinessType: 1
          updateTime: updateTime
          fileMd5: fileMd5
          fileUploadTaskId: 9
          fileContentType: 5
          parentId: 2
          createTime: createTime
          fileSize: 5
          fileStatus: 2
          name: name
          id: 3
          category: 6
          fileUuid: fileUuid
        repositoryId: 7
        parentId: 4
      properties:
        children:
          items:
            $ref: '#/components/schemas/DocTreeDetail'
          type: array
        parentId:
          format: int64
          type: integer
        repositoryId:
          format: int64
          type: integer
      title: TreeNodeListVO
      type: object
      x-apifox-orders:
      - children
      - parentId
      - repositoryId
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    TokenRequestInfo:
      description: TokenRequestInfo
      example:
        password: password
        clientId: clientId
        clientSecret: clientSecret
        userName: userName
      properties:
        clientId:
          description: app
          type: string
        clientSecret:
          description: secret
          type: string
        password:
          type: string
        userName:
          type: string
      title: TokenRequestInfo
      type: object
      x-apifox-orders:
      - clientId
      - clientSecret
      - password
      - userName
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    TagHotStockRequest:
      description: TagHotStockRequest
      example:
        stockName:
        - stockName
        - stockName
      properties:
        stockName:
          items:
            type: string
          type: array
      title: TagHotStockRequest
      type: object
      x-apifox-orders:
      - stockName
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    StockTrendVo:
      description: StockTrendVO
      example:
        stockTrendList:
        - volume: 3.616076749251911
          high: 5.637376656633329
          low: 2.3021358869347655
          upAndDown: 9
          tradeDate: tradeDate
          close: 5.962133916683182
          open: 7.061401241503109
        - volume: 3.616076749251911
          high: 5.637376656633329
          low: 2.3021358869347655
          upAndDown: 9
          tradeDate: tradeDate
          close: 5.962133916683182
          open: 7.061401241503109
        fluctuations: 6.***********0403
        price: 1.4658129805029452
      properties:
        fluctuations:
          format: double
          type: number
        price:
          format: double
          type: number
        stockTrendList:
          items:
            $ref: '#/components/schemas/StockTrend'
          type: array
      title: StockTrendVO
      type: object
      x-apifox-orders:
      - fluctuations
      - price
      - stockTrendList
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    StockTrendRequest:
      description: StockTrendRequest
      example:
        dateCode: 0
        stockCode: stockCode
      properties:
        dateCode:
          format: int32
          type: integer
        stockCode:
          type: string
      title: StockTrendRequest
      type: object
      x-apifox-orders:
      - dateCode
      - stockCode
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    StockTrend:
      description: StockTrend
      example:
        volume: 3.616076749251911
        high: 5.637376656633329
        low: 2.3021358869347655
        upAndDown: 9
        tradeDate: tradeDate
        close: 5.962133916683182
        open: 7.061401241503109
      properties:
        close:
          format: double
          type: number
        high:
          format: double
          type: number
        low:
          format: double
          type: number
        open:
          format: double
          type: number
        tradeDate:
          type: string
        upAndDown:
          format: int32
          type: integer
        volume:
          format: double
          type: number
      title: StockTrend
      type: object
      x-apifox-orders:
      - close
      - high
      - low
      - open
      - tradeDate
      - upAndDown
      - volume
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    SingleDocSplitParam:
      description: SingleDocSplitParam
      example:
        file_path: file_path
        document_parser: document_parser
        params: '{}'
        doc_id: doc_id
      properties:
        doc_id:
          description: 文档uuid
          type: string
        document_parser:
          description: 解析器字符串
          type: string
        file_path:
          type: string
        params:
          description: 'map结构，包含 chunksize '
          properties: {}
          type: object
          x-apifox-orders: []
          x-apifox-ignore-properties: []
      title: SingleDocSplitParam
      type: object
      x-apifox-orders:
      - doc_id
      - document_parser
      - file_path
      - params
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultString:
      description: Result«string»
      example:
        code: 0
        data: data
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          type: string
        message:
          type: string
      title: Result«string»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultLong:
      description: Result«long»
      example:
        code: 0
        data: 6
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          format: int64
          type: integer
        message:
          type: string
      title: Result«long»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultBoolean:
      description: Result«boolean»
      example:
        code: 0
        data: true
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          type: boolean
        message:
          type: string
      title: Result«boolean»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultUserInfoDuiXiang:
      description: Result«UserInfo对象»
      example:
        code: 0
        data:
          password: password
          createTime: createTime
          admin: true
          updateTime: updateTime
          id: 6
          status: 1
          username: username
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          $ref: '#/components/schemas/UserInfoDuiXiang'
        message:
          type: string
      title: Result«UserInfo对象»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultUserInfoEntity:
      description: Result«UserInfoEntity»
      example:
        code: 0
        data:
          ownWorkspaceIds:
          - ownWorkspaceIds
          - ownWorkspaceIds
          isWorkspaceAdmin: true
          updateUser: updateUser
          roleInfoMap:
            key: roleInfoMap
          telephone: telephone
          updateTime: 2000-01-23T04:56:07.000+00:00
          isAdmin: true
          locale: locale
          organName: organName
          realName: realName
          password: password
          workspaceRoleIds:
            key:
            - workspaceRoleIds
            - workspaceRoleIds
          roleIds:
          - roleIds
          - roleIds
          displayWorkspace: displayWorkspace
          createTime: 2000-01-23T04:56:07.000+00:00
          organId: organId
          createUser: createUser
          workspaceName: workspaceName
          id: id
          ownGroupNames:
          - ownGroupNames
          - ownGroupNames
          email: email
          status: Normal
          username: username
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          $ref: '#/components/schemas/UserInfoEntity'
        message:
          type: string
      title: Result«UserInfoEntity»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultUserDocTreeResponse:
      description: Result«UserDocTreeResponse»
      example:
        code: 0
        data:
          publicTree:
          - ownerType: 5
            creator: creator
            documentTreeId: 1
            uploadEnable: true
            description: description
            repositoryStorageType: 2
            updateTime: updateTime
            uuid: uuid
            createTime: createTime
            datasourceId: 6
            name: name
            repositoryType: 7
            id: 5
          - ownerType: 5
            creator: creator
            documentTreeId: 1
            uploadEnable: true
            description: description
            repositoryStorageType: 2
            updateTime: updateTime
            uuid: uuid
            createTime: createTime
            datasourceId: 6
            name: name
            repositoryType: 7
            id: 5
          financeTree:
          - ownerType: 5
            creator: creator
            documentTreeId: 1
            uploadEnable: true
            description: description
            repositoryStorageType: 2
            updateTime: updateTime
            uuid: uuid
            createTime: createTime
            datasourceId: 6
            name: name
            repositoryType: 7
            id: 5
          - ownerType: 5
            creator: creator
            documentTreeId: 1
            uploadEnable: true
            description: description
            repositoryStorageType: 2
            updateTime: updateTime
            uuid: uuid
            createTime: createTime
            datasourceId: 6
            name: name
            repositoryType: 7
            id: 5
          privateTree:
          - ownerType: 5
            creator: creator
            documentTreeId: 1
            uploadEnable: true
            description: description
            repositoryStorageType: 2
            updateTime: updateTime
            uuid: uuid
            createTime: createTime
            datasourceId: 6
            name: name
            repositoryType: 7
            id: 5
          - ownerType: 5
            creator: creator
            documentTreeId: 1
            uploadEnable: true
            description: description
            repositoryStorageType: 2
            updateTime: updateTime
            uuid: uuid
            createTime: createTime
            datasourceId: 6
            name: name
            repositoryType: 7
            id: 5
          lawTree:
          - ownerType: 5
            creator: creator
            documentTreeId: 1
            uploadEnable: true
            description: description
            repositoryStorageType: 2
            updateTime: updateTime
            uuid: uuid
            createTime: createTime
            datasourceId: 6
            name: name
            repositoryType: 7
            id: 5
          - ownerType: 5
            creator: creator
            documentTreeId: 1
            uploadEnable: true
            description: description
            repositoryStorageType: 2
            updateTime: updateTime
            uuid: uuid
            createTime: createTime
            datasourceId: 6
            name: name
            repositoryType: 7
            id: 5
          internetTree:
          - ownerType: 5
            creator: creator
            documentTreeId: 1
            uploadEnable: true
            description: description
            repositoryStorageType: 2
            updateTime: updateTime
            uuid: uuid
            createTime: createTime
            datasourceId: 6
            name: name
            repositoryType: 7
            id: 5
          - ownerType: 5
            creator: creator
            documentTreeId: 1
            uploadEnable: true
            description: description
            repositoryStorageType: 2
            updateTime: updateTime
            uuid: uuid
            createTime: createTime
            datasourceId: 6
            name: name
            repositoryType: 7
            id: 5
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          $ref: '#/components/schemas/UserDocTreeResponse'
        message:
          type: string
      title: Result«UserDocTreeResponse»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultTreeNodeListVo:
      description: Result«TreeNodeListVO»
      example:
        code: 0
        data:
          children:
          - creator: creator
            fileSuffix: 7
            fileBusinessType: 1
            updateTime: updateTime
            fileMd5: fileMd5
            fileUploadTaskId: 9
            fileContentType: 5
            parentId: 2
            createTime: createTime
            fileSize: 5
            fileStatus: 2
            name: name
            id: 3
            category: 6
            fileUuid: fileUuid
          - creator: creator
            fileSuffix: 7
            fileBusinessType: 1
            updateTime: updateTime
            fileMd5: fileMd5
            fileUploadTaskId: 9
            fileContentType: 5
            parentId: 2
            createTime: createTime
            fileSize: 5
            fileStatus: 2
            name: name
            id: 3
            category: 6
            fileUuid: fileUuid
          repositoryId: 7
          parentId: 4
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          $ref: '#/components/schemas/TreeNodeListVo'
        message:
          type: string
      title: Result«TreeNodeListVO»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultStockTrendVo:
      description: Result«StockTrendVO»
      example:
        code: 0
        data:
          stockTrendList:
          - volume: 3.616076749251911
            high: 5.637376656633329
            low: 2.3021358869347655
            upAndDown: 9
            tradeDate: tradeDate
            close: 5.962133916683182
            open: 7.061401241503109
          - volume: 3.616076749251911
            high: 5.637376656633329
            low: 2.3021358869347655
            upAndDown: 9
            tradeDate: tradeDate
            close: 5.962133916683182
            open: 7.061401241503109
          fluctuations: 6.***********0403
          price: 1.4658129805029452
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          $ref: '#/components/schemas/StockTrendVo'
        message:
          type: string
      title: Result«StockTrendVO»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultRepository:
      description: Result«Repository»
      example:
        code: 0
        data:
          ownerType: 5
          creator: creator
          documentTreeId: 1
          uploadEnable: true
          description: description
          repositoryStorageType: 2
          updateTime: updateTime
          uuid: uuid
          createTime: createTime
          datasourceId: 6
          name: name
          repositoryType: 7
          id: 5
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          $ref: '#/components/schemas/Repository'
        message:
          type: string
      title: Result«Repository»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultPageConversationVo:
      description: Result«Page«ConversationVO»»
      example:
        code: 0
        data:
          current: 6
          total: 2
          pages: 5
          optimizeCountSql: true
          size: 5
          maxLimit: 1
          records:
          - createTime: createTime
            conversationId: conversationId
            title: title
            sourceIds: sourceIds
          - createTime: createTime
            conversationId: conversationId
            title: title
            sourceIds: sourceIds
          searchCount: true
          orders:
          - asc: true
            column: column
          - asc: true
            column: column
          countId: countId
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          $ref: '#/components/schemas/PageConversationVo'
        message:
          type: string
      title: Result«Page«ConversationVO»»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultListUserInfoDuiXiang:
      description: Result«List«UserInfo对象»»
      example:
        code: 0
        data:
        - password: password
          createTime: createTime
          admin: true
          updateTime: updateTime
          id: 6
          status: 1
          username: username
        - password: password
          createTime: createTime
          admin: true
          updateTime: updateTime
          id: 6
          status: 1
          username: username
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          items:
            $ref: '#/components/schemas/UserInfoDuiXiang'
          type: array
        message:
          type: string
      title: Result«List«UserInfo对象»»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultListRepository:
      description: Result«List«Repository»»
      example:
        code: 0
        data:
        - ownerType: 5
          creator: creator
          documentTreeId: 1
          uploadEnable: true
          description: description
          repositoryStorageType: 2
          updateTime: updateTime
          uuid: uuid
          createTime: createTime
          datasourceId: 6
          name: name
          repositoryType: 7
          id: 5
        - ownerType: 5
          creator: creator
          documentTreeId: 1
          uploadEnable: true
          description: description
          repositoryStorageType: 2
          updateTime: updateTime
          uuid: uuid
          createTime: createTime
          datasourceId: 6
          name: name
          repositoryType: 7
          id: 5
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          items:
            $ref: '#/components/schemas/Repository'
          type: array
        message:
          type: string
      title: Result«List«Repository»»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultListIndTrendVo:
      description: Result«List«IndTrendVO»»
      example:
        code: 0
        data:
        - index: 6.***********0403
          tradeDate: tradeDate
        - index: 6.***********0403
          tradeDate: tradeDate
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          items:
            $ref: '#/components/schemas/IndTrendVo'
          type: array
        message:
          type: string
      title: Result«List«IndTrendVO»»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultListIndOrConceptVo:
      description: Result«List«IndOrConceptVO»»
      example:
        code: 0
        data:
        - name: name
          type: type
        - name: name
          type: type
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          items:
            $ref: '#/components/schemas/IndOrConceptVo'
          type: array
        message:
          type: string
      title: Result«List«IndOrConceptVO»»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultListHotStockVo:
      description: Result«List«HotStockVO»»
      example:
        code: 0
        data:
        - stockName: stockName
          stockCode: stockCode
        - stockName: stockName
          stockCode: stockCode
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          items:
            $ref: '#/components/schemas/HotStockVo'
          type: array
        message:
          type: string
      title: Result«List«HotStockVO»»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultListDocument:
      description: Result«List«Document»»
      example:
        code: 0
        data:
        - creator: creator
          documentTreeId: 6
          fileSuffix: 7
          fileBusinessType: 1
          filePath: filePath
          updateTime: updateTime
          fileMd5: fileMd5
          fileUploadTaskId: 9
          fileContentType: 5
          createTime: createTime
          fileSize: 5
          fileStatus: 2
          name: name
          id: 3
          fileUuid: fileUuid
        - creator: creator
          documentTreeId: 6
          fileSuffix: 7
          fileBusinessType: 1
          filePath: filePath
          updateTime: updateTime
          fileMd5: fileMd5
          fileUploadTaskId: 9
          fileContentType: 5
          createTime: createTime
          fileSize: 5
          fileStatus: 2
          name: name
          id: 3
          fileUuid: fileUuid
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          items:
            $ref: '#/components/schemas/Document'
          type: array
        message:
          type: string
      title: Result«List«Document»»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultListDocChunk:
      description: Result«List«DocChunk»»
      example:
        code: 0
        data:
        - metadata:
            totalPages: 0
            page: page
          content: content
        - metadata:
            totalPages: 0
            page: page
          content: content
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          items:
            $ref: '#/components/schemas/DocChunk'
          type: array
        message:
          type: string
      title: Result«List«DocChunk»»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultListApproveInfoDuiXiang:
      description: Result«List«ApproveInfo对象»»
      example:
        code: 0
        data:
        - apikey: apikey
          purpose: purpose
          startTime: startTime
          endTime: endTime
          id: 6
          status: 1
          username: username
        - apikey: apikey
          purpose: purpose
          startTime: startTime
          endTime: endTime
          id: 6
          status: 1
          username: username
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          items:
            $ref: '#/components/schemas/ApproveInfoDuiXiang'
          type: array
        message:
          type: string
      title: Result«List«ApproveInfo对象»»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultImageQueryResponse:
      description: Result«ImageQueryResponse»
      example:
        code: 0
        data:
          size: 6
          model: model
          image_list:
          - kb_id: kb_id
            distance: distance
            file_name: file_name
            image_data: image_data
            dir_ids:
            - dir_ids
            - dir_ids
            doc_id: doc_id
          - kb_id: kb_id
            distance: distance
            file_name: file_name
            image_data: image_data
            dir_ids:
            - dir_ids
            - dir_ids
            doc_id: doc_id
          search_time: search_time
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          $ref: '#/components/schemas/ImageQueryResponse'
        message:
          type: string
      title: Result«ImageQueryResponse»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultFullTree:
      description: Result«FullTree»
      example:
        code: 0
        data:
          node:
            fileSuffix: 7
            fileBusinessType: 1
            updateTime: updateTime
            fileMd5: fileMd5
            fileUploadTaskId: 9
            fileContentType: 5
            parentId: 2
            path: path
            createTime: createTime
            fileSize: 5
            fileStatus: 2
            name: name
            repositoryId: 4
            id: 3
            category: 6
            fileUuid: fileUuid
          children:
          - null
          - null
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          $ref: '#/components/schemas/FullTree'
        message:
          type: string
      title: Result«FullTree»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultDtoResultCaptcha:
      description: Result«DtoResultCaptcha»
      example:
        code: 0
        data:
          base64Img: base64Img
          key: key
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          $ref: '#/components/schemas/DtoResultCaptcha'
        message:
          type: string
      title: Result«DtoResultCaptcha»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultDocument:
      description: Result«Document»
      example:
        code: 0
        data:
          creator: creator
          documentTreeId: 6
          fileSuffix: 7
          fileBusinessType: 1
          filePath: filePath
          updateTime: updateTime
          fileMd5: fileMd5
          fileUploadTaskId: 9
          fileContentType: 5
          createTime: createTime
          fileSize: 5
          fileStatus: 2
          name: name
          id: 3
          fileUuid: fileUuid
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          $ref: '#/components/schemas/Document'
        message:
          type: string
      title: Result«Document»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultDocumentUploadTask:
      description: Result«DocumentUploadTask»
      example:
        code: 0
        data:
          uploadTaskStatus: 5
          documents:
          - creator: creator
            documentTreeId: 6
            fileSuffix: 7
            fileBusinessType: 1
            filePath: filePath
            updateTime: updateTime
            fileMd5: fileMd5
            fileUploadTaskId: 9
            fileContentType: 5
            createTime: createTime
            fileSize: 5
            fileStatus: 2
            name: name
            id: 3
            fileUuid: fileUuid
          - creator: creator
            documentTreeId: 6
            fileSuffix: 7
            fileBusinessType: 1
            filePath: filePath
            updateTime: updateTime
            fileMd5: fileMd5
            fileUploadTaskId: 9
            fileContentType: 5
            createTime: createTime
            fileSize: 5
            fileStatus: 2
            name: name
            id: 3
            fileUuid: fileUuid
          uploadType: 2
          startTime: startTime
          endTime: endTime
          id: 6
          percent: percent
          successDocs: 1
          totalDocs: 5
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          $ref: '#/components/schemas/DocumentUploadTask'
        message:
          type: string
      title: Result«DocumentUploadTask»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultDocumentTree:
      description: Result«DocumentTree»
      example:
        code: 0
        data:
          fileSuffix: 7
          fileBusinessType: 1
          updateTime: updateTime
          fileMd5: fileMd5
          fileUploadTaskId: 9
          fileContentType: 5
          parentId: 2
          path: path
          createTime: createTime
          fileSize: 5
          fileStatus: 2
          name: name
          repositoryId: 4
          id: 3
          category: 6
          fileUuid: fileUuid
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          $ref: '#/components/schemas/DocumentTree'
        message:
          type: string
      title: Result«DocumentTree»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultDocBusinessTypeVo:
      description: Result«DocBusinessTypeVO»
      example:
        code: 0
        data:
          pdfList:
          - fileSuffix: 7
            fileBusinessType: 1
            updateTime: updateTime
            fileMd5: fileMd5
            fileUploadTaskId: 9
            fileContentType: 5
            parentId: 2
            path: path
            createTime: createTime
            fileSize: 5
            fileStatus: 2
            name: name
            repositoryId: 4
            id: 3
            category: 6
            fileUuid: fileUuid
          - fileSuffix: 7
            fileBusinessType: 1
            updateTime: updateTime
            fileMd5: fileMd5
            fileUploadTaskId: 9
            fileContentType: 5
            parentId: 2
            path: path
            createTime: createTime
            fileSize: 5
            fileStatus: 2
            name: name
            repositoryId: 4
            id: 3
            category: 6
            fileUuid: fileUuid
          txtList:
          - fileSuffix: 7
            fileBusinessType: 1
            updateTime: updateTime
            fileMd5: fileMd5
            fileUploadTaskId: 9
            fileContentType: 5
            parentId: 2
            path: path
            createTime: createTime
            fileSize: 5
            fileStatus: 2
            name: name
            repositoryId: 4
            id: 3
            category: 6
            fileUuid: fileUuid
          - fileSuffix: 7
            fileBusinessType: 1
            updateTime: updateTime
            fileMd5: fileMd5
            fileUploadTaskId: 9
            fileContentType: 5
            parentId: 2
            path: path
            createTime: createTime
            fileSize: 5
            fileStatus: 2
            name: name
            repositoryId: 4
            id: 3
            category: 6
            fileUuid: fileUuid
          docsList:
          - fileSuffix: 7
            fileBusinessType: 1
            updateTime: updateTime
            fileMd5: fileMd5
            fileUploadTaskId: 9
            fileContentType: 5
            parentId: 2
            path: path
            createTime: createTime
            fileSize: 5
            fileStatus: 2
            name: name
            repositoryId: 4
            id: 3
            category: 6
            fileUuid: fileUuid
          - fileSuffix: 7
            fileBusinessType: 1
            updateTime: updateTime
            fileMd5: fileMd5
            fileUploadTaskId: 9
            fileContentType: 5
            parentId: 2
            path: path
            createTime: createTime
            fileSize: 5
            fileStatus: 2
            name: name
            repositoryId: 4
            id: 3
            category: 6
            fileUuid: fileUuid
          tableList:
          - fileSuffix: 7
            fileBusinessType: 1
            updateTime: updateTime
            fileMd5: fileMd5
            fileUploadTaskId: 9
            fileContentType: 5
            parentId: 2
            path: path
            createTime: createTime
            fileSize: 5
            fileStatus: 2
            name: name
            repositoryId: 4
            id: 3
            category: 6
            fileUuid: fileUuid
          - fileSuffix: 7
            fileBusinessType: 1
            updateTime: updateTime
            fileMd5: fileMd5
            fileUploadTaskId: 9
            fileContentType: 5
            parentId: 2
            path: path
            createTime: createTime
            fileSize: 5
            fileStatus: 2
            name: name
            repositoryId: 4
            id: 3
            category: 6
            fileUuid: fileUuid
          noneList:
          - fileSuffix: 7
            fileBusinessType: 1
            updateTime: updateTime
            fileMd5: fileMd5
            fileUploadTaskId: 9
            fileContentType: 5
            parentId: 2
            path: path
            createTime: createTime
            fileSize: 5
            fileStatus: 2
            name: name
            repositoryId: 4
            id: 3
            category: 6
            fileUuid: fileUuid
          - fileSuffix: 7
            fileBusinessType: 1
            updateTime: updateTime
            fileMd5: fileMd5
            fileUploadTaskId: 9
            fileContentType: 5
            parentId: 2
            path: path
            createTime: createTime
            fileSize: 5
            fileStatus: 2
            name: name
            repositoryId: 4
            id: 3
            category: 6
            fileUuid: fileUuid
          pptxList:
          - fileSuffix: 7
            fileBusinessType: 1
            updateTime: updateTime
            fileMd5: fileMd5
            fileUploadTaskId: 9
            fileContentType: 5
            parentId: 2
            path: path
            createTime: createTime
            fileSize: 5
            fileStatus: 2
            name: name
            repositoryId: 4
            id: 3
            category: 6
            fileUuid: fileUuid
          - fileSuffix: 7
            fileBusinessType: 1
            updateTime: updateTime
            fileMd5: fileMd5
            fileUploadTaskId: 9
            fileContentType: 5
            parentId: 2
            path: path
            createTime: createTime
            fileSize: 5
            fileStatus: 2
            name: name
            repositoryId: 4
            id: 3
            category: 6
            fileUuid: fileUuid
          imgList:
          - fileSuffix: 7
            fileBusinessType: 1
            updateTime: updateTime
            fileMd5: fileMd5
            fileUploadTaskId: 9
            fileContentType: 5
            parentId: 2
            path: path
            createTime: createTime
            fileSize: 5
            fileStatus: 2
            name: name
            repositoryId: 4
            id: 3
            category: 6
            fileUuid: fileUuid
          - fileSuffix: 7
            fileBusinessType: 1
            updateTime: updateTime
            fileMd5: fileMd5
            fileUploadTaskId: 9
            fileContentType: 5
            parentId: 2
            path: path
            createTime: createTime
            fileSize: 5
            fileStatus: 2
            name: name
            repositoryId: 4
            id: 3
            category: 6
            fileUuid: fileUuid
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          $ref: '#/components/schemas/DocBusinessTypeVo'
        message:
          type: string
      title: Result«DocBusinessTypeVO»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultConversationDetailVo:
      description: Result«ConversationDetailVO»
      example:
        code: 0
        data:
          qalist:
          - answer: answer
            question: question
          - answer: answer
            question: question
          conversationId: conversationId
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          $ref: '#/components/schemas/ConversationDetailVo'
        message:
          type: string
      title: Result«ConversationDetailVO»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ResultApproveInfoDuiXiang:
      description: Result«ApproveInfo对象»
      example:
        code: 0
        data:
          apikey: apikey
          purpose: purpose
          startTime: startTime
          endTime: endTime
          id: 6
          status: 1
          username: username
        message: message
      properties:
        code:
          format: int32
          type: integer
        data:
          $ref: '#/components/schemas/ApproveInfoDuiXiang'
        message:
          type: string
      title: Result«ApproveInfo对象»
      type: object
      x-apifox-orders:
      - code
      - data
      - message
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    Resource:
      description: Resource
      properties:
        description:
          type: string
        file:
          $ref: '#/components/schemas/File'
        filename:
          type: string
        inputStream:
          description: InputStream
          properties: {}
          title: InputStream
          type: object
          x-apifox-orders: []
          x-apifox-ignore-properties: []
          x-apifox-folder: base/Schemas
        open:
          type: boolean
        readable:
          type: boolean
        uri:
          $ref: '#/components/schemas/Uri'
        url:
          $ref: '#/components/schemas/Url'
      title: Resource
      type: object
      x-apifox-orders:
      - description
      - file
      - filename
      - inputStream
      - open
      - readable
      - uri
      - url
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    Repository:
      description: Repository，Repository实体类
      example:
        ownerType: 5
        creator: creator
        documentTreeId: 1
        uploadEnable: true
        description: description
        repositoryStorageType: 2
        updateTime: updateTime
        uuid: uuid
        createTime: createTime
        datasourceId: 6
        name: name
        repositoryType: 7
        id: 5
      properties:
        createTime:
          description: 创建时间
          type: string
        creator:
          description: 创建者
          type: string
        datasourceId:
          description: 关联数据源id,仅repository_type=2才有效
          format: int64
          type: integer
        description:
          description: 描述
          type: string
        documentTreeId:
          description: 文档树ID
          format: int64
          type: integer
        id:
          format: int64
          type: integer
        name:
          description: 名称
          type: string
        ownerType:
          description: 仓库所属人类型  1 =公共 2= 私有
          format: int32
          type: integer
        repositoryStorageType:
          description: 知识库存储类型 枚举 1 = 网络存储系统  2 = 本地文件系统
          format: int32
          type: integer
        repositoryType:
          description: 知识库类型 枚举 1 = 文本  2 = sdb
          format: int32
          type: integer
        updateTime:
          description: 更新时间
          type: string
        uploadEnable:
          description: 用户是否有权限执行上传
          type: boolean
        uuid:
          description: uuid
          type: string
      title: Repository
      type: object
      x-apifox-orders:
      - createTime
      - creator
      - datasourceId
      - description
      - documentTreeId
      - id
      - name
      - ownerType
      - repositoryStorageType
      - repositoryType
      - updateTime
      - uploadEnable
      - uuid
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    QaRequest:
      description: QARequest
      example:
        answer: answer
        question: question
        conversationId: conversationId
      properties:
        answer:
          type: string
        conversationId:
          description: 会话id
          type: string
        question:
          type: string
      required:
      - conversationId
      title: QARequest
      type: object
      x-apifox-orders:
      - answer
      - conversationId
      - question
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    PageConversationVo:
      description: Page«ConversationVO»
      example:
        current: 6
        total: 2
        pages: 5
        optimizeCountSql: true
        size: 5
        maxLimit: 1
        records:
        - createTime: createTime
          conversationId: conversationId
          title: title
          sourceIds: sourceIds
        - createTime: createTime
          conversationId: conversationId
          title: title
          sourceIds: sourceIds
        searchCount: true
        orders:
        - asc: true
          column: column
        - asc: true
          column: column
        countId: countId
      properties:
        countId:
          type: string
        current:
          format: int64
          type: integer
        maxLimit:
          format: int64
          type: integer
        optimizeCountSql:
          type: boolean
        orders:
          items:
            $ref: '#/components/schemas/OrderItem'
          type: array
        pages:
          format: int64
          type: integer
        records:
          items:
            $ref: '#/components/schemas/ConversationVo'
          type: array
        searchCount:
          type: boolean
        size:
          format: int64
          type: integer
        total:
          format: int64
          type: integer
      title: Page«ConversationVO»
      type: object
      x-apifox-orders:
      - countId
      - current
      - maxLimit
      - optimizeCountSql
      - orders
      - pages
      - records
      - searchCount
      - size
      - total
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    OrderItem:
      description: OrderItem
      example:
        asc: true
        column: column
      properties:
        asc:
          type: boolean
        column:
          type: string
      title: OrderItem
      type: object
      x-apifox-orders:
      - asc
      - column
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    KnowledgeBaseDirParams:
      description: KnowledgeBaseDirParams
      example:
        kb_id: kb_id
        dir_ids:
        - dir_ids
        - dir_ids
      properties:
        dir_ids:
          items:
            type: string
          type: array
        kb_id:
          type: string
      title: KnowledgeBaseDirParams
      type: object
      x-apifox-orders:
      - dir_ids
      - kb_id
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    InputStream:
      description: InputStream
      properties: {}
      title: InputStream
      type: object
      x-apifox-orders: []
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    IndTrendVo:
      description: IndTrendVO
      example:
        index: 6.***********0403
        tradeDate: tradeDate
      properties:
        index:
          format: double
          type: number
        tradeDate:
          type: string
      title: IndTrendVO
      type: object
      x-apifox-orders:
      - index
      - tradeDate
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    IndTrendRequest:
      description: IndTrendRequest
      example:
        indName: indName
        endDate: endDate
        indType: indType
        startDate: startDate
      properties:
        endDate:
          type: string
        indName:
          type: string
        indType:
          type: string
        startDate:
          type: string
      title: IndTrendRequest
      type: object
      x-apifox-orders:
      - endDate
      - indName
      - indType
      - startDate
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    IndOrConceptVo:
      description: IndOrConceptVO
      example:
        name: name
        type: type
      properties:
        name:
          type: string
        type:
          type: string
      title: IndOrConceptVO
      type: object
      x-apifox-orders:
      - name
      - type
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ImageQueryResponse:
      description: ImageQueryResponse
      example:
        size: 6
        model: model
        image_list:
        - kb_id: kb_id
          distance: distance
          file_name: file_name
          image_data: image_data
          dir_ids:
          - dir_ids
          - dir_ids
          doc_id: doc_id
        - kb_id: kb_id
          distance: distance
          file_name: file_name
          image_data: image_data
          dir_ids:
          - dir_ids
          - dir_ids
          doc_id: doc_id
        search_time: search_time
      properties:
        image_list:
          items:
            $ref: '#/components/schemas/ImageDetail'
          type: array
        model:
          type: string
        search_time:
          type: string
        size:
          format: int32
          type: integer
      title: ImageQueryResponse
      type: object
      x-apifox-orders:
      - image_list
      - model
      - search_time
      - size
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ImageQueryRequest:
      description: ImageQueryRequest
      example:
        mode: mode
        return_image: true
        top_k: 0
        text: text
        kb_id_params:
        - kb_id: kb_id
          dir_ids:
          - dir_ids
          - dir_ids
        - kb_id: kb_id
          dir_ids:
          - dir_ids
          - dir_ids
      properties:
        kb_id_params:
          items:
            $ref: '#/components/schemas/KnowledgeBaseDirParams'
          type: array
        mode:
          type: string
        return_image:
          type: boolean
        text:
          type: string
        top_k:
          format: int32
          type: integer
      title: ImageQueryRequest
      type: object
      x-apifox-orders:
      - kb_id_params
      - mode
      - return_image
      - text
      - top_k
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ImageMatchRequest:
      description: ImageMatchRequest
      example:
        return_image: true
        image_data: image_data
        top_k: 0
        kb_id_params:
        - kb_id: kb_id
          dir_ids:
          - dir_ids
          - dir_ids
        - kb_id: kb_id
          dir_ids:
          - dir_ids
          - dir_ids
      properties:
        image_data:
          type: string
        kb_id_params:
          items:
            $ref: '#/components/schemas/KnowledgeBaseDirParams'
          type: array
        return_image:
          type: boolean
        top_k:
          format: int32
          type: integer
      title: ImageMatchRequest
      type: object
      x-apifox-orders:
      - image_data
      - kb_id_params
      - return_image
      - top_k
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ImageDetail:
      description: ImageDetail
      example:
        kb_id: kb_id
        distance: distance
        file_name: file_name
        image_data: image_data
        dir_ids:
        - dir_ids
        - dir_ids
        doc_id: doc_id
      properties:
        dir_ids:
          items:
            type: string
          type: array
        distance:
          type: string
        doc_id:
          type: string
        file_name:
          type: string
        image_data:
          type: string
        kb_id:
          type: string
      title: ImageDetail
      type: object
      x-apifox-orders:
      - dir_ids
      - distance
      - doc_id
      - file_name
      - image_data
      - kb_id
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    HotStockVo:
      description: HotStockVO
      example:
        stockName: stockName
        stockCode: stockCode
      properties:
        stockCode:
          type: string
        stockName:
          type: string
      title: HotStockVO
      type: object
      x-apifox-orders:
      - stockCode
      - stockName
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    FullTree:
      description: FullTree
      example:
        node:
          fileSuffix: 7
          fileBusinessType: 1
          updateTime: updateTime
          fileMd5: fileMd5
          fileUploadTaskId: 9
          fileContentType: 5
          parentId: 2
          path: path
          createTime: createTime
          fileSize: 5
          fileStatus: 2
          name: name
          repositoryId: 4
          id: 3
          category: 6
          fileUuid: fileUuid
        children:
        - null
        - null
      properties:
        children:
          items:
            $ref: '#/components/schemas/FullTree'
          type: array
        node:
          $ref: '#/components/schemas/DocumentTree'
      title: FullTree
      type: object
      x-apifox-orders:
      - children
      - node
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    File:
      description: File
      properties:
        absolute:
          type: boolean
        absoluteFile:
          $ref: '#/components/schemas/File'
        absolutePath:
          type: string
        canonicalFile:
          $ref: '#/components/schemas/File'
        canonicalPath:
          type: string
        directory:
          type: boolean
        executable:
          type: boolean
        file:
          type: boolean
        freeSpace:
          format: int64
          type: integer
        hidden:
          type: boolean
        lastModified:
          format: int64
          type: integer
        name:
          type: string
        parent:
          type: string
        parentFile:
          $ref: '#/components/schemas/File'
        path:
          type: string
        readable:
          type: boolean
        totalSpace:
          format: int64
          type: integer
        usableSpace:
          format: int64
          type: integer
        writable:
          type: boolean
      title: File
      type: object
      x-apifox-orders:
      - absolute
      - absoluteFile
      - absolutePath
      - canonicalFile
      - canonicalPath
      - directory
      - executable
      - file
      - freeSpace
      - hidden
      - lastModified
      - name
      - parent
      - parentFile
      - path
      - readable
      - totalSpace
      - usableSpace
      - writable
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    DtoResultCaptcha:
      description: DtoResultCaptcha
      example:
        base64Img: base64Img
        key: key
      properties:
        base64Img:
          type: string
        key:
          type: string
      title: DtoResultCaptcha
      type: object
      x-apifox-orders:
      - base64Img
      - key
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    DtoParamUpdatePassword:
      description: DtoParamUpdatePassword
      example:
        oldPassword: oldPassword
        newPassword: newPassword
      properties:
        newPassword:
          type: string
        oldPassword:
          type: string
      title: DtoParamUpdatePassword
      type: object
      x-apifox-orders:
      - newPassword
      - oldPassword
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    DtoParamLogin:
      description: DtoParamLogin
      example:
        password: password
        code: code
        key: key
        username: username
      properties:
        code:
          type: string
        key:
          type: string
        password:
          type: string
        username:
          type: string
      title: DtoParamLogin
      type: object
      x-apifox-orders:
      - code
      - key
      - password
      - username
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    DtoParamApply:
      description: DtoParamApply
      example:
        purpose: purpose
        endTime: endTime
      properties:
        endTime:
          type: string
        purpose:
          type: string
      title: DtoParamApply
      type: object
      x-apifox-orders:
      - endTime
      - purpose
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    DocumentUploadTask:
      description: DocumentUploadTask，文件上传任务实体
      example:
        uploadTaskStatus: 5
        documents:
        - creator: creator
          documentTreeId: 6
          fileSuffix: 7
          fileBusinessType: 1
          filePath: filePath
          updateTime: updateTime
          fileMd5: fileMd5
          fileUploadTaskId: 9
          fileContentType: 5
          createTime: createTime
          fileSize: 5
          fileStatus: 2
          name: name
          id: 3
          fileUuid: fileUuid
        - creator: creator
          documentTreeId: 6
          fileSuffix: 7
          fileBusinessType: 1
          filePath: filePath
          updateTime: updateTime
          fileMd5: fileMd5
          fileUploadTaskId: 9
          fileContentType: 5
          createTime: createTime
          fileSize: 5
          fileStatus: 2
          name: name
          id: 3
          fileUuid: fileUuid
        uploadType: 2
        startTime: startTime
        endTime: endTime
        id: 6
        percent: percent
        successDocs: 1
        totalDocs: 5
      properties:
        documents:
          items:
            $ref: '#/components/schemas/Document'
          type: array
        endTime:
          description: 创建时间
          type: string
        id:
          description: ID
          format: int64
          type: integer
        percent:
          type: string
        startTime:
          description: 创建时间
          type: string
        successDocs:
          format: int32
          type: integer
        totalDocs:
          format: int32
          type: integer
        uploadTaskStatus:
          description: 任务状态 1= 任务创建 2=上传中 3=上传成功  4=上传失败
          format: int32
          type: integer
        uploadType:
          description: 上传类型 1 =本地上传 2 知识库上传 default = 1
          format: int32
          type: integer
      title: DocumentUploadTask
      type: object
      x-apifox-orders:
      - documents
      - endTime
      - id
      - percent
      - startTime
      - successDocs
      - totalDocs
      - uploadTaskStatus
      - uploadType
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    DocumentTree:
      description: DocumentTree，文档树实体
      example:
        fileSuffix: 7
        fileBusinessType: 1
        updateTime: updateTime
        fileMd5: fileMd5
        fileUploadTaskId: 9
        fileContentType: 5
        parentId: 2
        path: path
        createTime: createTime
        fileSize: 5
        fileStatus: 2
        name: name
        repositoryId: 4
        id: 3
        category: 6
        fileUuid: fileUuid
      properties:
        category:
          description: 类别 枚举:1=目录 2=文件
          format: int32
          type: integer
        createTime:
          description: 创建时间
          type: string
        fileBusinessType:
          description: '上传文档业务归类 1=PDF 2=IMG 3=DOCX 4=PPTX '
          format: int32
          type: integer
        fileContentType:
          description: 上传文档contentType枚举
          format: int32
          type: integer
        fileMd5:
          description: 文件MD5值
          type: string
        fileSize:
          description: 上传文档大小-byte
          format: int64
          type: integer
        fileStatus:
          description: 枚举 1= waiting 2=uploading 3=upload_Failed  4= uploaded 5=parsing、6=parse_failed
            、7= parse_success 、8= edited  9=saved_failed 10 saved
          format: int32
          type: integer
        fileSuffix:
          description: 上传文档后缀枚举值 1=PDF 2=PNG 3=JPEG 4=GIF 5=BMP 6=PPT 7=PPTX 8=DOC
            9=DOCX 10=XLSX 11=CSV 12=NONE
          format: int32
          type: integer
        fileUploadTaskId:
          description: 关联上传任务id
          format: int64
          type: integer
        fileUuid:
          description: 文件uuid
          type: string
        id:
          description: ID
          format: int64
          type: integer
        name:
          description: 文件夹或者节点name
          type: string
        parentId:
          description: 父级ID
          format: int64
          type: integer
        path:
          description: 路径
          type: string
        repositoryId:
          description: 仓库ID
          format: int64
          type: integer
        updateTime:
          description: 更新时间
          type: string
      title: DocumentTree
      type: object
      x-apifox-orders:
      - category
      - createTime
      - fileBusinessType
      - fileContentType
      - fileMd5
      - fileSize
      - fileStatus
      - fileSuffix
      - fileUploadTaskId
      - fileUuid
      - id
      - name
      - parentId
      - path
      - repositoryId
      - updateTime
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    Document:
      description: Document，文档表
      example:
        creator: creator
        documentTreeId: 6
        fileSuffix: 7
        fileBusinessType: 1
        filePath: filePath
        updateTime: updateTime
        fileMd5: fileMd5
        fileUploadTaskId: 9
        fileContentType: 5
        createTime: createTime
        fileSize: 5
        fileStatus: 2
        name: name
        id: 3
        fileUuid: fileUuid
      properties:
        createTime:
          description: 创建时间
          type: string
        creator:
          description: 创建者
          type: string
        documentTreeId:
          description: 文档树ID
          format: int64
          type: integer
        fileBusinessType:
          description: '上传文档业务归类 PDF=1, IMG=2, DOCX=3, PPTX=4, TABLE=5, TXT=6, AUDIO=7,
            VIDEO=8, NONE=9 '
          format: int32
          type: integer
        fileContentType:
          description: 上传文档contentType枚举 TYPE_PDF=1, TYPE_IMG_JPEG=21, TYPE_IMG_PNG=22,
            TYPE_IMG_GIF=23, TYPE_IMG_GMP=24, TYPE_DOCX_VND=31, TYPE_DOCX_MS=32, TYPE_PPT_OPENXML=41,
            TYPE_PPT_MS=42, TYPE_TABLE_VND=51, TYPE_TABLE_CSV=52, TYPE_TEXT_PLAIN=61,
            TYPE_AUDIO_MEPG=71, TYPE_AUDIO_WAV=72, TYPE_AUDIO_WAVE=73, TYPE_AUDIO_XWAV=74,
            TYPE_AUDIO_OGG=75, TYPE_AUDIO_AAC=76, TYPE_AUDIO_FLAC=77, TYPE_AUDIO_MP4=78,
            TYPE_VIDEO_AVI=81, TYPE_VIDEO_MP4=82, TYPE_VIDEO_FLV=83, TYPE_VIDEO_WMV=84,
            TYPE_VIDEO_MKV=85, TYPE_NONE=91
          format: int32
          type: integer
        fileMd5:
          description: 文件MD5值
          type: string
        filePath:
          type: string
        fileSize:
          description: 上传文档大小-bytes
          format: int64
          type: integer
        fileStatus:
          description: 枚举 1= waiting 2=uploading 3=upload_Failed  4= uploaded 5=parsing、6=parse_failed
            、7= parse_success 、8= edited  9=saved_failed 10 saved
          format: int32
          type: integer
        fileSuffix:
          description: 上传文档后缀枚举值 PDF=1, PNG=21, JPEG=22, JPG=23, GIF=24, BMP=25, DOC=31,
            DOCX=32, PPT=41, PPTX=42, XLSX=51, CSV=52, TXT=61, MP3=71, WAV=72, OGG=73,
            AAC=74, FLAC=75, M4A=76, AVI=81, MP4=82, FLV=83, WMV=84, MKV=85, NONE=91
          format: int32
          type: integer
        fileUploadTaskId:
          description: 关联上传任务id
          format: int64
          type: integer
        fileUuid:
          description: 文件uuid
          type: string
        id:
          description: ID
          format: int64
          type: integer
        name:
          description: 名称
          type: string
        updateTime:
          description: 更新时间
          type: string
      title: Document
      type: object
      x-apifox-orders:
      - createTime
      - creator
      - documentTreeId
      - fileBusinessType
      - fileContentType
      - fileMd5
      - filePath
      - fileSize
      - fileStatus
      - fileSuffix
      - fileUploadTaskId
      - fileUuid
      - id
      - name
      - updateTime
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    DocTreeDetail:
      description: DocTreeDetail
      example:
        creator: creator
        fileSuffix: 7
        fileBusinessType: 1
        updateTime: updateTime
        fileMd5: fileMd5
        fileUploadTaskId: 9
        fileContentType: 5
        parentId: 2
        createTime: createTime
        fileSize: 5
        fileStatus: 2
        name: name
        id: 3
        category: 6
        fileUuid: fileUuid
      properties:
        category:
          description: 类别 枚举:1=目录 2=文件
          format: int32
          type: integer
        createTime:
          description: 创建时间
          type: string
        creator:
          description: 创建者
          type: string
        fileBusinessType:
          description: '上传文档业务归类 1=PDF 2=IMG 3=DOCX 4=PPTX '
          format: int32
          type: integer
        fileContentType:
          description: 上传文档contentType枚举
          format: int32
          type: integer
        fileMd5:
          description: 文件MD5值
          type: string
        fileSize:
          description: 上传文档大小-byte
          format: int64
          type: integer
        fileStatus:
          format: int32
          type: integer
        fileSuffix:
          description: 上传文档后缀枚举值 1=PDF 2=PNG 3=JPEG 4=GIF 5=BMP 6=PPT 7=PPTX 8=DOC
            9=DOCX 10=XLSX 11=CSV 12=NONE
          format: int32
          type: integer
        fileUploadTaskId:
          description: 关联上传任务id
          format: int64
          type: integer
        fileUuid:
          description: 文件唯一标识
          type: string
        id:
          format: int64
          type: integer
        name:
          description: 文件夹或者节点name
          type: string
        parentId:
          description: 父级ID
          format: int64
          type: integer
        updateTime:
          description: 更新时间
          type: string
      title: DocTreeDetail
      type: object
      x-apifox-orders:
      - category
      - createTime
      - creator
      - fileBusinessType
      - fileContentType
      - fileMd5
      - fileSize
      - fileStatus
      - fileSuffix
      - fileUploadTaskId
      - fileUuid
      - id
      - name
      - parentId
      - updateTime
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    DocPreviewRequest:
      description: DocPreviewRequest
      example:
        startPage: 6
        docUuid: docUuid
        endPage: 0
      properties:
        docUuid:
          type: string
        endPage:
          format: int32
          type: integer
        startPage:
          format: int32
          type: integer
      title: DocPreviewRequest
      type: object
      x-apifox-orders:
      - docUuid
      - endPage
      - startPage
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    DocHighlightRequest:
      description: DocHighlightRequest
      example:
        image: true
        startPage: 1
        rectangles:
        - 6.***********0403
        - 6.***********0403
        docUuid: docUuid
        endPage: 0
        text: text
      properties:
        docUuid:
          type: string
        endPage:
          format: int32
          type: integer
        image:
          type: boolean
        rectangles:
          items:
            format: double
            type: number
          type: array
        startPage:
          format: int32
          type: integer
        text:
          type: string
      title: DocHighlightRequest
      type: object
      x-apifox-orders:
      - docUuid
      - endPage
      - image
      - rectangles
      - startPage
      - text
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    DocChunk:
      description: DocChunk
      example:
        metadata:
          totalPages: 0
          page: page
        content: content
      properties:
        content:
          type: string
        metadata:
          $ref: '#/components/schemas/ChunkMetadata'
      title: DocChunk
      type: object
      x-apifox-orders:
      - content
      - metadata
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    DocCacheRequest:
      description: DocCacheRequest
      example:
        chunkList:
        - metadata:
            totalPages: 0
            page: page
          content: content
        - metadata:
            totalPages: 0
            page: page
          content: content
        fileUuid: fileUuid
      properties:
        chunkList:
          items:
            $ref: '#/components/schemas/DocChunk'
          type: array
        fileUuid:
          type: string
      title: DocCacheRequest
      type: object
      x-apifox-orders:
      - chunkList
      - fileUuid
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    DocBusinessTypeVo:
      description: DocBusinessTypeVO
      example:
        pdfList:
        - fileSuffix: 7
          fileBusinessType: 1
          updateTime: updateTime
          fileMd5: fileMd5
          fileUploadTaskId: 9
          fileContentType: 5
          parentId: 2
          path: path
          createTime: createTime
          fileSize: 5
          fileStatus: 2
          name: name
          repositoryId: 4
          id: 3
          category: 6
          fileUuid: fileUuid
        - fileSuffix: 7
          fileBusinessType: 1
          updateTime: updateTime
          fileMd5: fileMd5
          fileUploadTaskId: 9
          fileContentType: 5
          parentId: 2
          path: path
          createTime: createTime
          fileSize: 5
          fileStatus: 2
          name: name
          repositoryId: 4
          id: 3
          category: 6
          fileUuid: fileUuid
        txtList:
        - fileSuffix: 7
          fileBusinessType: 1
          updateTime: updateTime
          fileMd5: fileMd5
          fileUploadTaskId: 9
          fileContentType: 5
          parentId: 2
          path: path
          createTime: createTime
          fileSize: 5
          fileStatus: 2
          name: name
          repositoryId: 4
          id: 3
          category: 6
          fileUuid: fileUuid
        - fileSuffix: 7
          fileBusinessType: 1
          updateTime: updateTime
          fileMd5: fileMd5
          fileUploadTaskId: 9
          fileContentType: 5
          parentId: 2
          path: path
          createTime: createTime
          fileSize: 5
          fileStatus: 2
          name: name
          repositoryId: 4
          id: 3
          category: 6
          fileUuid: fileUuid
        docsList:
        - fileSuffix: 7
          fileBusinessType: 1
          updateTime: updateTime
          fileMd5: fileMd5
          fileUploadTaskId: 9
          fileContentType: 5
          parentId: 2
          path: path
          createTime: createTime
          fileSize: 5
          fileStatus: 2
          name: name
          repositoryId: 4
          id: 3
          category: 6
          fileUuid: fileUuid
        - fileSuffix: 7
          fileBusinessType: 1
          updateTime: updateTime
          fileMd5: fileMd5
          fileUploadTaskId: 9
          fileContentType: 5
          parentId: 2
          path: path
          createTime: createTime
          fileSize: 5
          fileStatus: 2
          name: name
          repositoryId: 4
          id: 3
          category: 6
          fileUuid: fileUuid
        tableList:
        - fileSuffix: 7
          fileBusinessType: 1
          updateTime: updateTime
          fileMd5: fileMd5
          fileUploadTaskId: 9
          fileContentType: 5
          parentId: 2
          path: path
          createTime: createTime
          fileSize: 5
          fileStatus: 2
          name: name
          repositoryId: 4
          id: 3
          category: 6
          fileUuid: fileUuid
        - fileSuffix: 7
          fileBusinessType: 1
          updateTime: updateTime
          fileMd5: fileMd5
          fileUploadTaskId: 9
          fileContentType: 5
          parentId: 2
          path: path
          createTime: createTime
          fileSize: 5
          fileStatus: 2
          name: name
          repositoryId: 4
          id: 3
          category: 6
          fileUuid: fileUuid
        noneList:
        - fileSuffix: 7
          fileBusinessType: 1
          updateTime: updateTime
          fileMd5: fileMd5
          fileUploadTaskId: 9
          fileContentType: 5
          parentId: 2
          path: path
          createTime: createTime
          fileSize: 5
          fileStatus: 2
          name: name
          repositoryId: 4
          id: 3
          category: 6
          fileUuid: fileUuid
        - fileSuffix: 7
          fileBusinessType: 1
          updateTime: updateTime
          fileMd5: fileMd5
          fileUploadTaskId: 9
          fileContentType: 5
          parentId: 2
          path: path
          createTime: createTime
          fileSize: 5
          fileStatus: 2
          name: name
          repositoryId: 4
          id: 3
          category: 6
          fileUuid: fileUuid
        pptxList:
        - fileSuffix: 7
          fileBusinessType: 1
          updateTime: updateTime
          fileMd5: fileMd5
          fileUploadTaskId: 9
          fileContentType: 5
          parentId: 2
          path: path
          createTime: createTime
          fileSize: 5
          fileStatus: 2
          name: name
          repositoryId: 4
          id: 3
          category: 6
          fileUuid: fileUuid
        - fileSuffix: 7
          fileBusinessType: 1
          updateTime: updateTime
          fileMd5: fileMd5
          fileUploadTaskId: 9
          fileContentType: 5
          parentId: 2
          path: path
          createTime: createTime
          fileSize: 5
          fileStatus: 2
          name: name
          repositoryId: 4
          id: 3
          category: 6
          fileUuid: fileUuid
        imgList:
        - fileSuffix: 7
          fileBusinessType: 1
          updateTime: updateTime
          fileMd5: fileMd5
          fileUploadTaskId: 9
          fileContentType: 5
          parentId: 2
          path: path
          createTime: createTime
          fileSize: 5
          fileStatus: 2
          name: name
          repositoryId: 4
          id: 3
          category: 6
          fileUuid: fileUuid
        - fileSuffix: 7
          fileBusinessType: 1
          updateTime: updateTime
          fileMd5: fileMd5
          fileUploadTaskId: 9
          fileContentType: 5
          parentId: 2
          path: path
          createTime: createTime
          fileSize: 5
          fileStatus: 2
          name: name
          repositoryId: 4
          id: 3
          category: 6
          fileUuid: fileUuid
      properties:
        docsList:
          items:
            $ref: '#/components/schemas/DocumentTree'
          type: array
        imgList:
          items:
            $ref: '#/components/schemas/DocumentTree'
          type: array
        noneList:
          items:
            $ref: '#/components/schemas/DocumentTree'
          type: array
        pdfList:
          items:
            $ref: '#/components/schemas/DocumentTree'
          type: array
        pptxList:
          items:
            $ref: '#/components/schemas/DocumentTree'
          type: array
        tableList:
          items:
            $ref: '#/components/schemas/DocumentTree'
          type: array
        txtList:
          items:
            $ref: '#/components/schemas/DocumentTree'
          type: array
      title: DocBusinessTypeVO
      type: object
      x-apifox-orders:
      - docsList
      - imgList
      - noneList
      - pdfList
      - pptxList
      - tableList
      - txtList
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ConversationVo:
      description: ConversationVO
      example:
        createTime: createTime
        conversationId: conversationId
        title: title
        sourceIds: sourceIds
      properties:
        conversationId:
          type: string
        createTime:
          type: string
        sourceIds:
          type: string
        title:
          type: string
      title: ConversationVO
      type: object
      x-apifox-orders:
      - conversationId
      - createTime
      - sourceIds
      - title
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ConversationUpdateRequest:
      description: ConversationUpdateRequest
      example:
        conversationId: conversationId
        title: title
      properties:
        conversationId:
          description: 会话id
          type: string
        title:
          type: string
      required:
      - conversationId
      title: ConversationUpdateRequest
      type: object
      x-apifox-orders:
      - conversationId
      - title
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ConversationRequest:
      description: ConversationRequest
      example:
        firstQuestion: firstQuestion
        type: 0
        sourceIds: sourceIds
        firstAnswer: firstAnswer
      properties:
        firstAnswer:
          type: string
        firstQuestion:
          type: string
        sourceIds:
          description: 保留字段，文档来源，逗号分割，仅文档问答生效
          type: string
        type:
          description: 文档类型:0 智能问答 1文档问答 2智能写作
          format: int32
          type: integer
      required:
      - type
      title: ConversationRequest
      type: object
      x-apifox-orders:
      - firstAnswer
      - firstQuestion
      - sourceIds
      - type
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ConversationDetailVo:
      description: ConversationDetailVO
      example:
        qalist:
        - answer: answer
          question: question
        - answer: answer
          question: question
        conversationId: conversationId
      properties:
        conversationId:
          type: string
        qalist:
          items:
            $ref: '#/components/schemas/ConversationDetail'
          type: array
      title: ConversationDetailVO
      type: object
      x-apifox-orders:
      - conversationId
      - qalist
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ConversationDetail:
      description: ConversationDetail
      example:
        answer: answer
        question: question
      properties:
        answer:
          type: string
        question:
          type: string
      title: ConversationDetail
      type: object
      x-apifox-orders:
      - answer
      - question
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ChunkMetadata:
      description: ChunkMetadata
      example:
        totalPages: 0
        page: page
      properties:
        page:
          type: string
        totalPages:
          format: int32
          type: integer
      title: ChunkMetadata
      type: object
      x-apifox-orders:
      - page
      - totalPages
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    ApproveInfoDuiXiang:
      description: ApproveInfo对象
      example:
        apikey: apikey
        purpose: purpose
        startTime: startTime
        endTime: endTime
        id: 6
        status: 1
        username: username
      properties:
        apikey:
          type: string
        endTime:
          type: string
        id:
          format: int32
          type: integer
        purpose:
          type: string
        startTime:
          type: string
        status:
          format: int32
          type: integer
        username:
          type: string
      title: ApproveInfo对象
      type: object
      x-apifox-orders:
      - apikey
      - endTime
      - id
      - purpose
      - startTime
      - status
      - username
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    AddRepoRequest:
      description: AddRepoRequest
      example:
        password: password
        name: name
        repositoryType: 0
        description: description
        url: url
        sdbConfigureType: 6
        tableName: tableName
        username: username
      properties:
        description:
          description: 知识库描述
          type: string
        name:
          description: 知识库名称
          type: string
        password:
          description: 密码
          type: string
        repositoryType:
          description: 知识库类型 枚举 1 = 文本  2 = sdb
          format: int32
          type: integer
        sdbConfigureType:
          description: 类型 1= tks内部服务 2=本地直连 图谱相关字段仅类型为sdb有效
          format: int32
          type: integer
        tableName:
          description: 表名
          type: string
        url:
          description: URL
          type: string
        username:
          description: 用户名
          type: string
      title: AddRepoRequest
      type: object
      x-apifox-orders:
      - description
      - name
      - password
      - repositoryType
      - sdbConfigureType
      - tableName
      - url
      - username
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    AddDirectoryRequest:
      description: AddDirectoryRequest
      example:
        name: name
        parentId: 0
      properties:
        name:
          description: 文件及名称
          type: string
        parentId:
          description: 父级ID
          format: int64
          type: integer
      title: AddDirectoryRequest
      type: object
      x-apifox-orders:
      - name
      - parentId
      x-apifox-ignore-properties: []
      x-apifox-folder: base/Schemas
    RecallServiceRequest:
      description: RecallServiceRequest
      example:
        reserved_condition_time:
        - reserved_condition_time
        - reserved_condition_time
        table_reformat: true
        hippo_data_sources:
        - hippo_data_sources
        - hippo_data_sources
        sources:
        - kb_id: kb_id
          dir_id: dir_id
          doc_id: doc_id
        - kb_id: kb_id
          dir_id: dir_id
          doc_id: doc_id
        deduplicate: true
        user_queries:
        - user_queries
        - user_queries
        scope_data_sources:
        - scope_data_sources
        - scope_data_sources
        reserved_condition_company:
        - reserved_condition_company
        - reserved_condition_company
        doc_id: doc_id
        rerank: true
        pdf_name: pdf_name
        top_k: 0
        compact_text: true
        fragment_text_filer: true
      properties:
        user_queries:
          default: []
          description: User Queries
          items:
            type: string
          title: User Queries
          type: array
        sources:
          default: []
          description: Sources
          items:
            $ref: '#/components/schemas/KnowledgeSource'
          title: Sources
          type: array
        hippo_data_sources:
          default: []
          description: Hippo Data Sources
          items:
            type: string
          title: Hippo Data Sources
          type: array
        scope_data_sources:
          default: []
          description: Scope Data Sources
          items:
            type: string
          title: Scope Data Sources
          type: array
        reserved_condition_company:
          default: []
          description: Reserved Condition Company
          items:
            type: string
          title: Reserved Condition Company
          type: array
        reserved_condition_time:
          default: []
          description: Reserved Condition Time
          items:
            type: string
          title: Reserved Condition Time
          type: array
        pdf_name:
          default: ""
          description: Pdf Name
          title: Pdf Name
          type: string
        doc_id:
          default: ""
          description: Doc Id
          title: Doc Id
          type: string
        top_k:
          default: 5
          description: Top K
          title: Top K
          type: integer
        fragment_text_filer:
          default: true
          description: Fragment Text Filer
          title: Fragment Text Filer
          type: boolean
        deduplicate:
          default: true
          description: Deduplicate
          title: Deduplicate
          type: boolean
        table_reformat:
          default: true
          description: Table Reformat
          title: Table Reformat
          type: boolean
        rerank:
          default: true
          description: Rerank
          title: Rerank
          type: boolean
        compact_text:
          default: true
          description: Compact Text
          title: Compact Text
          type: boolean
      title: RecallServiceRequest
      type: object
      x-apifox-orders:
      - user_queries
      - sources
      - hippo_data_sources
      - scope_data_sources
      - reserved_condition_company
      - reserved_condition_time
      - pdf_name
      - doc_id
      - top_k
      - fragment_text_filer
      - deduplicate
      - table_reformat
      - rerank
      - compact_text
      x-apifox-ignore-properties: []
      x-apifox-folder: chat/Schemas
    KnowledgeSource:
      description: KnowledgeSource
      example:
        kb_id: kb_id
        dir_id: dir_id
        doc_id: doc_id
      properties:
        kb_id:
          description: Kb Id，知识库id
          title: Kb Id
          type: string
        doc_id:
          description: Doc Id，文档id
          title: Doc Id
          type: string
        dir_id:
          description: Dir Id
          title: Dir Id
          type: string
      title: KnowledgeSource
      type: object
      x-apifox-orders:
      - kb_id
      - doc_id
      - dir_id
      x-apifox-ignore-properties: []
      x-apifox-folder: chat/Schemas
    inline_object:
      properties:
        file:
          description: file
          format: binary
          type: string
      required:
      - file
      type: object
  securitySchemes: {}
