/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// ApproveInfoDuiXiang ApproveInfo对象
type ApproveInfoDuiXiang struct {
	Apikey *string `json:"apikey,omitempty"`
	EndTime *string `json:"endTime,omitempty"`
	Id *int32 `json:"id,omitempty"`
	Purpose *string `json:"purpose,omitempty"`
	StartTime *string `json:"startTime,omitempty"`
	Status *int32 `json:"status,omitempty"`
	Username *string `json:"username,omitempty"`
}

// NewApproveInfoDuiXiang instantiates a new ApproveInfoDuiXiang object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewApproveInfoDuiXiang() *ApproveInfoDuiXiang {
	this := ApproveInfoDuiXiang{}
	return &this
}

// NewApproveInfoDuiXiangWithDefaults instantiates a new ApproveInfoDuiXiang object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewApproveInfoDuiXiangWithDefaults() *ApproveInfoDuiXiang {
	this := ApproveInfoDuiXiang{}
	return &this
}

// GetApikey returns the Apikey field value if set, zero value otherwise.
func (o *ApproveInfoDuiXiang) GetApikey() string {
	if o == nil || o.Apikey == nil {
		var ret string
		return ret
	}
	return *o.Apikey
}

// GetApikeyOk returns a tuple with the Apikey field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ApproveInfoDuiXiang) GetApikeyOk() (*string, bool) {
	if o == nil || o.Apikey == nil {
		return nil, false
	}
	return o.Apikey, true
}

// HasApikey returns a boolean if a field has been set.
func (o *ApproveInfoDuiXiang) HasApikey() bool {
	if o != nil && o.Apikey != nil {
		return true
	}

	return false
}

// SetApikey gets a reference to the given string and assigns it to the Apikey field.
func (o *ApproveInfoDuiXiang) SetApikey(v string) {
	o.Apikey = &v
}

// GetEndTime returns the EndTime field value if set, zero value otherwise.
func (o *ApproveInfoDuiXiang) GetEndTime() string {
	if o == nil || o.EndTime == nil {
		var ret string
		return ret
	}
	return *o.EndTime
}

// GetEndTimeOk returns a tuple with the EndTime field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ApproveInfoDuiXiang) GetEndTimeOk() (*string, bool) {
	if o == nil || o.EndTime == nil {
		return nil, false
	}
	return o.EndTime, true
}

// HasEndTime returns a boolean if a field has been set.
func (o *ApproveInfoDuiXiang) HasEndTime() bool {
	if o != nil && o.EndTime != nil {
		return true
	}

	return false
}

// SetEndTime gets a reference to the given string and assigns it to the EndTime field.
func (o *ApproveInfoDuiXiang) SetEndTime(v string) {
	o.EndTime = &v
}

// GetId returns the Id field value if set, zero value otherwise.
func (o *ApproveInfoDuiXiang) GetId() int32 {
	if o == nil || o.Id == nil {
		var ret int32
		return ret
	}
	return *o.Id
}

// GetIdOk returns a tuple with the Id field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ApproveInfoDuiXiang) GetIdOk() (*int32, bool) {
	if o == nil || o.Id == nil {
		return nil, false
	}
	return o.Id, true
}

// HasId returns a boolean if a field has been set.
func (o *ApproveInfoDuiXiang) HasId() bool {
	if o != nil && o.Id != nil {
		return true
	}

	return false
}

// SetId gets a reference to the given int32 and assigns it to the Id field.
func (o *ApproveInfoDuiXiang) SetId(v int32) {
	o.Id = &v
}

// GetPurpose returns the Purpose field value if set, zero value otherwise.
func (o *ApproveInfoDuiXiang) GetPurpose() string {
	if o == nil || o.Purpose == nil {
		var ret string
		return ret
	}
	return *o.Purpose
}

// GetPurposeOk returns a tuple with the Purpose field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ApproveInfoDuiXiang) GetPurposeOk() (*string, bool) {
	if o == nil || o.Purpose == nil {
		return nil, false
	}
	return o.Purpose, true
}

// HasPurpose returns a boolean if a field has been set.
func (o *ApproveInfoDuiXiang) HasPurpose() bool {
	if o != nil && o.Purpose != nil {
		return true
	}

	return false
}

// SetPurpose gets a reference to the given string and assigns it to the Purpose field.
func (o *ApproveInfoDuiXiang) SetPurpose(v string) {
	o.Purpose = &v
}

// GetStartTime returns the StartTime field value if set, zero value otherwise.
func (o *ApproveInfoDuiXiang) GetStartTime() string {
	if o == nil || o.StartTime == nil {
		var ret string
		return ret
	}
	return *o.StartTime
}

// GetStartTimeOk returns a tuple with the StartTime field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ApproveInfoDuiXiang) GetStartTimeOk() (*string, bool) {
	if o == nil || o.StartTime == nil {
		return nil, false
	}
	return o.StartTime, true
}

// HasStartTime returns a boolean if a field has been set.
func (o *ApproveInfoDuiXiang) HasStartTime() bool {
	if o != nil && o.StartTime != nil {
		return true
	}

	return false
}

// SetStartTime gets a reference to the given string and assigns it to the StartTime field.
func (o *ApproveInfoDuiXiang) SetStartTime(v string) {
	o.StartTime = &v
}

// GetStatus returns the Status field value if set, zero value otherwise.
func (o *ApproveInfoDuiXiang) GetStatus() int32 {
	if o == nil || o.Status == nil {
		var ret int32
		return ret
	}
	return *o.Status
}

// GetStatusOk returns a tuple with the Status field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ApproveInfoDuiXiang) GetStatusOk() (*int32, bool) {
	if o == nil || o.Status == nil {
		return nil, false
	}
	return o.Status, true
}

// HasStatus returns a boolean if a field has been set.
func (o *ApproveInfoDuiXiang) HasStatus() bool {
	if o != nil && o.Status != nil {
		return true
	}

	return false
}

// SetStatus gets a reference to the given int32 and assigns it to the Status field.
func (o *ApproveInfoDuiXiang) SetStatus(v int32) {
	o.Status = &v
}

// GetUsername returns the Username field value if set, zero value otherwise.
func (o *ApproveInfoDuiXiang) GetUsername() string {
	if o == nil || o.Username == nil {
		var ret string
		return ret
	}
	return *o.Username
}

// GetUsernameOk returns a tuple with the Username field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ApproveInfoDuiXiang) GetUsernameOk() (*string, bool) {
	if o == nil || o.Username == nil {
		return nil, false
	}
	return o.Username, true
}

// HasUsername returns a boolean if a field has been set.
func (o *ApproveInfoDuiXiang) HasUsername() bool {
	if o != nil && o.Username != nil {
		return true
	}

	return false
}

// SetUsername gets a reference to the given string and assigns it to the Username field.
func (o *ApproveInfoDuiXiang) SetUsername(v string) {
	o.Username = &v
}

func (o ApproveInfoDuiXiang) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Apikey != nil {
		toSerialize["apikey"] = o.Apikey
	}
	if o.EndTime != nil {
		toSerialize["endTime"] = o.EndTime
	}
	if o.Id != nil {
		toSerialize["id"] = o.Id
	}
	if o.Purpose != nil {
		toSerialize["purpose"] = o.Purpose
	}
	if o.StartTime != nil {
		toSerialize["startTime"] = o.StartTime
	}
	if o.Status != nil {
		toSerialize["status"] = o.Status
	}
	if o.Username != nil {
		toSerialize["username"] = o.Username
	}
	return json.Marshal(toSerialize)
}

type NullableApproveInfoDuiXiang struct {
	value *ApproveInfoDuiXiang
	isSet bool
}

func (v NullableApproveInfoDuiXiang) Get() *ApproveInfoDuiXiang {
	return v.value
}

func (v *NullableApproveInfoDuiXiang) Set(val *ApproveInfoDuiXiang) {
	v.value = val
	v.isSet = true
}

func (v NullableApproveInfoDuiXiang) IsSet() bool {
	return v.isSet
}

func (v *NullableApproveInfoDuiXiang) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableApproveInfoDuiXiang(val *ApproveInfoDuiXiang) *NullableApproveInfoDuiXiang {
	return &NullableApproveInfoDuiXiang{value: val, isSet: true}
}

func (v NullableApproveInfoDuiXiang) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableApproveInfoDuiXiang) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
