/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// DocHighlightRequest DocHighlightRequest
type DocHighlightRequest struct {
	DocUuid *string `json:"docUuid,omitempty"`
	EndPage *int32 `json:"endPage,omitempty"`
	Image *bool `json:"image,omitempty"`
	Rectangles *[]float64 `json:"rectangles,omitempty"`
	StartPage *int32 `json:"startPage,omitempty"`
	Text *string `json:"text,omitempty"`
}

// NewDocHighlightRequest instantiates a new DocHighlightRequest object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDocHighlightRequest() *DocHighlightRequest {
	this := DocHighlightRequest{}
	return &this
}

// NewDocHighlightRequestWithDefaults instantiates a new DocHighlightRequest object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDocHighlightRequestWithDefaults() *DocHighlightRequest {
	this := DocHighlightRequest{}
	return &this
}

// GetDocUuid returns the DocUuid field value if set, zero value otherwise.
func (o *DocHighlightRequest) GetDocUuid() string {
	if o == nil || o.DocUuid == nil {
		var ret string
		return ret
	}
	return *o.DocUuid
}

// GetDocUuidOk returns a tuple with the DocUuid field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocHighlightRequest) GetDocUuidOk() (*string, bool) {
	if o == nil || o.DocUuid == nil {
		return nil, false
	}
	return o.DocUuid, true
}

// HasDocUuid returns a boolean if a field has been set.
func (o *DocHighlightRequest) HasDocUuid() bool {
	if o != nil && o.DocUuid != nil {
		return true
	}

	return false
}

// SetDocUuid gets a reference to the given string and assigns it to the DocUuid field.
func (o *DocHighlightRequest) SetDocUuid(v string) {
	o.DocUuid = &v
}

// GetEndPage returns the EndPage field value if set, zero value otherwise.
func (o *DocHighlightRequest) GetEndPage() int32 {
	if o == nil || o.EndPage == nil {
		var ret int32
		return ret
	}
	return *o.EndPage
}

// GetEndPageOk returns a tuple with the EndPage field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocHighlightRequest) GetEndPageOk() (*int32, bool) {
	if o == nil || o.EndPage == nil {
		return nil, false
	}
	return o.EndPage, true
}

// HasEndPage returns a boolean if a field has been set.
func (o *DocHighlightRequest) HasEndPage() bool {
	if o != nil && o.EndPage != nil {
		return true
	}

	return false
}

// SetEndPage gets a reference to the given int32 and assigns it to the EndPage field.
func (o *DocHighlightRequest) SetEndPage(v int32) {
	o.EndPage = &v
}

// GetImage returns the Image field value if set, zero value otherwise.
func (o *DocHighlightRequest) GetImage() bool {
	if o == nil || o.Image == nil {
		var ret bool
		return ret
	}
	return *o.Image
}

// GetImageOk returns a tuple with the Image field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocHighlightRequest) GetImageOk() (*bool, bool) {
	if o == nil || o.Image == nil {
		return nil, false
	}
	return o.Image, true
}

// HasImage returns a boolean if a field has been set.
func (o *DocHighlightRequest) HasImage() bool {
	if o != nil && o.Image != nil {
		return true
	}

	return false
}

// SetImage gets a reference to the given bool and assigns it to the Image field.
func (o *DocHighlightRequest) SetImage(v bool) {
	o.Image = &v
}

// GetRectangles returns the Rectangles field value if set, zero value otherwise.
func (o *DocHighlightRequest) GetRectangles() []float64 {
	if o == nil || o.Rectangles == nil {
		var ret []float64
		return ret
	}
	return *o.Rectangles
}

// GetRectanglesOk returns a tuple with the Rectangles field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocHighlightRequest) GetRectanglesOk() (*[]float64, bool) {
	if o == nil || o.Rectangles == nil {
		return nil, false
	}
	return o.Rectangles, true
}

// HasRectangles returns a boolean if a field has been set.
func (o *DocHighlightRequest) HasRectangles() bool {
	if o != nil && o.Rectangles != nil {
		return true
	}

	return false
}

// SetRectangles gets a reference to the given []float64 and assigns it to the Rectangles field.
func (o *DocHighlightRequest) SetRectangles(v []float64) {
	o.Rectangles = &v
}

// GetStartPage returns the StartPage field value if set, zero value otherwise.
func (o *DocHighlightRequest) GetStartPage() int32 {
	if o == nil || o.StartPage == nil {
		var ret int32
		return ret
	}
	return *o.StartPage
}

// GetStartPageOk returns a tuple with the StartPage field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocHighlightRequest) GetStartPageOk() (*int32, bool) {
	if o == nil || o.StartPage == nil {
		return nil, false
	}
	return o.StartPage, true
}

// HasStartPage returns a boolean if a field has been set.
func (o *DocHighlightRequest) HasStartPage() bool {
	if o != nil && o.StartPage != nil {
		return true
	}

	return false
}

// SetStartPage gets a reference to the given int32 and assigns it to the StartPage field.
func (o *DocHighlightRequest) SetStartPage(v int32) {
	o.StartPage = &v
}

// GetText returns the Text field value if set, zero value otherwise.
func (o *DocHighlightRequest) GetText() string {
	if o == nil || o.Text == nil {
		var ret string
		return ret
	}
	return *o.Text
}

// GetTextOk returns a tuple with the Text field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocHighlightRequest) GetTextOk() (*string, bool) {
	if o == nil || o.Text == nil {
		return nil, false
	}
	return o.Text, true
}

// HasText returns a boolean if a field has been set.
func (o *DocHighlightRequest) HasText() bool {
	if o != nil && o.Text != nil {
		return true
	}

	return false
}

// SetText gets a reference to the given string and assigns it to the Text field.
func (o *DocHighlightRequest) SetText(v string) {
	o.Text = &v
}

func (o DocHighlightRequest) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.DocUuid != nil {
		toSerialize["docUuid"] = o.DocUuid
	}
	if o.EndPage != nil {
		toSerialize["endPage"] = o.EndPage
	}
	if o.Image != nil {
		toSerialize["image"] = o.Image
	}
	if o.Rectangles != nil {
		toSerialize["rectangles"] = o.Rectangles
	}
	if o.StartPage != nil {
		toSerialize["startPage"] = o.StartPage
	}
	if o.Text != nil {
		toSerialize["text"] = o.Text
	}
	return json.Marshal(toSerialize)
}

type NullableDocHighlightRequest struct {
	value *DocHighlightRequest
	isSet bool
}

func (v NullableDocHighlightRequest) Get() *DocHighlightRequest {
	return v.value
}

func (v *NullableDocHighlightRequest) Set(val *DocHighlightRequest) {
	v.value = val
	v.isSet = true
}

func (v NullableDocHighlightRequest) IsSet() bool {
	return v.isSet
}

func (v *NullableDocHighlightRequest) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDocHighlightRequest(val *DocHighlightRequest) *NullableDocHighlightRequest {
	return &NullableDocHighlightRequest{value: val, isSet: true}
}

func (v NullableDocHighlightRequest) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDocHighlightRequest) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
