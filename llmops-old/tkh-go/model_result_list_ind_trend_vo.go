/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// ResultListIndTrendVo Result«List«IndTrendVO»»
type ResultListIndTrendVo struct {
	Code *int32 `json:"code,omitempty"`
	Data *[]IndTrendVo `json:"data,omitempty"`
	Message *string `json:"message,omitempty"`
}

// NewResultListIndTrendVo instantiates a new ResultListIndTrendVo object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewResultListIndTrendVo() *ResultListIndTrendVo {
	this := ResultListIndTrendVo{}
	return &this
}

// NewResultListIndTrendVoWithDefaults instantiates a new ResultListIndTrendVo object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewResultListIndTrendVoWithDefaults() *ResultListIndTrendVo {
	this := ResultListIndTrendVo{}
	return &this
}

// GetCode returns the Code field value if set, zero value otherwise.
func (o *ResultListIndTrendVo) GetCode() int32 {
	if o == nil || o.Code == nil {
		var ret int32
		return ret
	}
	return *o.Code
}

// GetCodeOk returns a tuple with the Code field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ResultListIndTrendVo) GetCodeOk() (*int32, bool) {
	if o == nil || o.Code == nil {
		return nil, false
	}
	return o.Code, true
}

// HasCode returns a boolean if a field has been set.
func (o *ResultListIndTrendVo) HasCode() bool {
	if o != nil && o.Code != nil {
		return true
	}

	return false
}

// SetCode gets a reference to the given int32 and assigns it to the Code field.
func (o *ResultListIndTrendVo) SetCode(v int32) {
	o.Code = &v
}

// GetData returns the Data field value if set, zero value otherwise.
func (o *ResultListIndTrendVo) GetData() []IndTrendVo {
	if o == nil || o.Data == nil {
		var ret []IndTrendVo
		return ret
	}
	return *o.Data
}

// GetDataOk returns a tuple with the Data field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ResultListIndTrendVo) GetDataOk() (*[]IndTrendVo, bool) {
	if o == nil || o.Data == nil {
		return nil, false
	}
	return o.Data, true
}

// HasData returns a boolean if a field has been set.
func (o *ResultListIndTrendVo) HasData() bool {
	if o != nil && o.Data != nil {
		return true
	}

	return false
}

// SetData gets a reference to the given []IndTrendVo and assigns it to the Data field.
func (o *ResultListIndTrendVo) SetData(v []IndTrendVo) {
	o.Data = &v
}

// GetMessage returns the Message field value if set, zero value otherwise.
func (o *ResultListIndTrendVo) GetMessage() string {
	if o == nil || o.Message == nil {
		var ret string
		return ret
	}
	return *o.Message
}

// GetMessageOk returns a tuple with the Message field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ResultListIndTrendVo) GetMessageOk() (*string, bool) {
	if o == nil || o.Message == nil {
		return nil, false
	}
	return o.Message, true
}

// HasMessage returns a boolean if a field has been set.
func (o *ResultListIndTrendVo) HasMessage() bool {
	if o != nil && o.Message != nil {
		return true
	}

	return false
}

// SetMessage gets a reference to the given string and assigns it to the Message field.
func (o *ResultListIndTrendVo) SetMessage(v string) {
	o.Message = &v
}

func (o ResultListIndTrendVo) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Code != nil {
		toSerialize["code"] = o.Code
	}
	if o.Data != nil {
		toSerialize["data"] = o.Data
	}
	if o.Message != nil {
		toSerialize["message"] = o.Message
	}
	return json.Marshal(toSerialize)
}

type NullableResultListIndTrendVo struct {
	value *ResultListIndTrendVo
	isSet bool
}

func (v NullableResultListIndTrendVo) Get() *ResultListIndTrendVo {
	return v.value
}

func (v *NullableResultListIndTrendVo) Set(val *ResultListIndTrendVo) {
	v.value = val
	v.isSet = true
}

func (v NullableResultListIndTrendVo) IsSet() bool {
	return v.isSet
}

func (v *NullableResultListIndTrendVo) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableResultListIndTrendVo(val *ResultListIndTrendVo) *NullableResultListIndTrendVo {
	return &NullableResultListIndTrendVo{value: val, isSet: true}
}

func (v NullableResultListIndTrendVo) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableResultListIndTrendVo) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
