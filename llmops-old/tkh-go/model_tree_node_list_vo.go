/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// TreeNodeListVo TreeNodeListVO
type TreeNodeListVo struct {
	Children *[]DocTreeDetail `json:"children,omitempty"`
	ParentId *int64 `json:"parentId,omitempty"`
	RepositoryId *int64 `json:"repositoryId,omitempty"`
}

// NewTreeNodeListVo instantiates a new TreeNodeListVo object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewTreeNodeListVo() *TreeNodeListVo {
	this := TreeNodeListVo{}
	return &this
}

// NewTreeNodeListVoWithDefaults instantiates a new TreeNodeListVo object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewTreeNodeListVoWithDefaults() *TreeNodeListVo {
	this := TreeNodeListVo{}
	return &this
}

// GetChildren returns the Children field value if set, zero value otherwise.
func (o *TreeNodeListVo) GetChildren() []DocTreeDetail {
	if o == nil || o.Children == nil {
		var ret []DocTreeDetail
		return ret
	}
	return *o.Children
}

// GetChildrenOk returns a tuple with the Children field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *TreeNodeListVo) GetChildrenOk() (*[]DocTreeDetail, bool) {
	if o == nil || o.Children == nil {
		return nil, false
	}
	return o.Children, true
}

// HasChildren returns a boolean if a field has been set.
func (o *TreeNodeListVo) HasChildren() bool {
	if o != nil && o.Children != nil {
		return true
	}

	return false
}

// SetChildren gets a reference to the given []DocTreeDetail and assigns it to the Children field.
func (o *TreeNodeListVo) SetChildren(v []DocTreeDetail) {
	o.Children = &v
}

// GetParentId returns the ParentId field value if set, zero value otherwise.
func (o *TreeNodeListVo) GetParentId() int64 {
	if o == nil || o.ParentId == nil {
		var ret int64
		return ret
	}
	return *o.ParentId
}

// GetParentIdOk returns a tuple with the ParentId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *TreeNodeListVo) GetParentIdOk() (*int64, bool) {
	if o == nil || o.ParentId == nil {
		return nil, false
	}
	return o.ParentId, true
}

// HasParentId returns a boolean if a field has been set.
func (o *TreeNodeListVo) HasParentId() bool {
	if o != nil && o.ParentId != nil {
		return true
	}

	return false
}

// SetParentId gets a reference to the given int64 and assigns it to the ParentId field.
func (o *TreeNodeListVo) SetParentId(v int64) {
	o.ParentId = &v
}

// GetRepositoryId returns the RepositoryId field value if set, zero value otherwise.
func (o *TreeNodeListVo) GetRepositoryId() int64 {
	if o == nil || o.RepositoryId == nil {
		var ret int64
		return ret
	}
	return *o.RepositoryId
}

// GetRepositoryIdOk returns a tuple with the RepositoryId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *TreeNodeListVo) GetRepositoryIdOk() (*int64, bool) {
	if o == nil || o.RepositoryId == nil {
		return nil, false
	}
	return o.RepositoryId, true
}

// HasRepositoryId returns a boolean if a field has been set.
func (o *TreeNodeListVo) HasRepositoryId() bool {
	if o != nil && o.RepositoryId != nil {
		return true
	}

	return false
}

// SetRepositoryId gets a reference to the given int64 and assigns it to the RepositoryId field.
func (o *TreeNodeListVo) SetRepositoryId(v int64) {
	o.RepositoryId = &v
}

func (o TreeNodeListVo) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Children != nil {
		toSerialize["children"] = o.Children
	}
	if o.ParentId != nil {
		toSerialize["parentId"] = o.ParentId
	}
	if o.RepositoryId != nil {
		toSerialize["repositoryId"] = o.RepositoryId
	}
	return json.Marshal(toSerialize)
}

type NullableTreeNodeListVo struct {
	value *TreeNodeListVo
	isSet bool
}

func (v NullableTreeNodeListVo) Get() *TreeNodeListVo {
	return v.value
}

func (v *NullableTreeNodeListVo) Set(val *TreeNodeListVo) {
	v.value = val
	v.isSet = true
}

func (v NullableTreeNodeListVo) IsSet() bool {
	return v.isSet
}

func (v *NullableTreeNodeListVo) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableTreeNodeListVo(val *TreeNodeListVo) *NullableTreeNodeListVo {
	return &NullableTreeNodeListVo{value: val, isSet: true}
}

func (v NullableTreeNodeListVo) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableTreeNodeListVo) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
