/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// IndOrConceptVo IndOrConceptVO
type IndOrConceptVo struct {
	Name *string `json:"name,omitempty"`
	Type *string `json:"type,omitempty"`
}

// NewIndOrConceptVo instantiates a new IndOrConceptVo object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewIndOrConceptVo() *IndOrConceptVo {
	this := IndOrConceptVo{}
	return &this
}

// NewIndOrConceptVoWithDefaults instantiates a new IndOrConceptVo object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewIndOrConceptVoWithDefaults() *IndOrConceptVo {
	this := IndOrConceptVo{}
	return &this
}

// GetName returns the Name field value if set, zero value otherwise.
func (o *IndOrConceptVo) GetName() string {
	if o == nil || o.Name == nil {
		var ret string
		return ret
	}
	return *o.Name
}

// GetNameOk returns a tuple with the Name field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *IndOrConceptVo) GetNameOk() (*string, bool) {
	if o == nil || o.Name == nil {
		return nil, false
	}
	return o.Name, true
}

// HasName returns a boolean if a field has been set.
func (o *IndOrConceptVo) HasName() bool {
	if o != nil && o.Name != nil {
		return true
	}

	return false
}

// SetName gets a reference to the given string and assigns it to the Name field.
func (o *IndOrConceptVo) SetName(v string) {
	o.Name = &v
}

// GetType returns the Type field value if set, zero value otherwise.
func (o *IndOrConceptVo) GetType() string {
	if o == nil || o.Type == nil {
		var ret string
		return ret
	}
	return *o.Type
}

// GetTypeOk returns a tuple with the Type field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *IndOrConceptVo) GetTypeOk() (*string, bool) {
	if o == nil || o.Type == nil {
		return nil, false
	}
	return o.Type, true
}

// HasType returns a boolean if a field has been set.
func (o *IndOrConceptVo) HasType() bool {
	if o != nil && o.Type != nil {
		return true
	}

	return false
}

// SetType gets a reference to the given string and assigns it to the Type field.
func (o *IndOrConceptVo) SetType(v string) {
	o.Type = &v
}

func (o IndOrConceptVo) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Name != nil {
		toSerialize["name"] = o.Name
	}
	if o.Type != nil {
		toSerialize["type"] = o.Type
	}
	return json.Marshal(toSerialize)
}

type NullableIndOrConceptVo struct {
	value *IndOrConceptVo
	isSet bool
}

func (v NullableIndOrConceptVo) Get() *IndOrConceptVo {
	return v.value
}

func (v *NullableIndOrConceptVo) Set(val *IndOrConceptVo) {
	v.value = val
	v.isSet = true
}

func (v NullableIndOrConceptVo) IsSet() bool {
	return v.isSet
}

func (v *NullableIndOrConceptVo) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableIndOrConceptVo(val *IndOrConceptVo) *NullableIndOrConceptVo {
	return &NullableIndOrConceptVo{value: val, isSet: true}
}

func (v NullableIndOrConceptVo) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableIndOrConceptVo) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
