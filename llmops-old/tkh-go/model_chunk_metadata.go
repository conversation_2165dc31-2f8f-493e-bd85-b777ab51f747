/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// ChunkMetadata ChunkMetadata
type ChunkMetadata struct {
	Page *string `json:"page,omitempty"`
	TotalPages *int32 `json:"totalPages,omitempty"`
}

// NewChunkMetadata instantiates a new ChunkMetadata object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewChunkMetadata() *ChunkMetadata {
	this := ChunkMetadata{}
	return &this
}

// NewChunkMetadataWithDefaults instantiates a new ChunkMetadata object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewChunkMetadataWithDefaults() *ChunkMetadata {
	this := ChunkMetadata{}
	return &this
}

// GetPage returns the Page field value if set, zero value otherwise.
func (o *ChunkMetadata) GetPage() string {
	if o == nil || o.Page == nil {
		var ret string
		return ret
	}
	return *o.Page
}

// GetPageOk returns a tuple with the Page field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ChunkMetadata) GetPageOk() (*string, bool) {
	if o == nil || o.Page == nil {
		return nil, false
	}
	return o.Page, true
}

// HasPage returns a boolean if a field has been set.
func (o *ChunkMetadata) HasPage() bool {
	if o != nil && o.Page != nil {
		return true
	}

	return false
}

// SetPage gets a reference to the given string and assigns it to the Page field.
func (o *ChunkMetadata) SetPage(v string) {
	o.Page = &v
}

// GetTotalPages returns the TotalPages field value if set, zero value otherwise.
func (o *ChunkMetadata) GetTotalPages() int32 {
	if o == nil || o.TotalPages == nil {
		var ret int32
		return ret
	}
	return *o.TotalPages
}

// GetTotalPagesOk returns a tuple with the TotalPages field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ChunkMetadata) GetTotalPagesOk() (*int32, bool) {
	if o == nil || o.TotalPages == nil {
		return nil, false
	}
	return o.TotalPages, true
}

// HasTotalPages returns a boolean if a field has been set.
func (o *ChunkMetadata) HasTotalPages() bool {
	if o != nil && o.TotalPages != nil {
		return true
	}

	return false
}

// SetTotalPages gets a reference to the given int32 and assigns it to the TotalPages field.
func (o *ChunkMetadata) SetTotalPages(v int32) {
	o.TotalPages = &v
}

func (o ChunkMetadata) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Page != nil {
		toSerialize["page"] = o.Page
	}
	if o.TotalPages != nil {
		toSerialize["totalPages"] = o.TotalPages
	}
	return json.Marshal(toSerialize)
}

type NullableChunkMetadata struct {
	value *ChunkMetadata
	isSet bool
}

func (v NullableChunkMetadata) Get() *ChunkMetadata {
	return v.value
}

func (v *NullableChunkMetadata) Set(val *ChunkMetadata) {
	v.value = val
	v.isSet = true
}

func (v NullableChunkMetadata) IsSet() bool {
	return v.isSet
}

func (v *NullableChunkMetadata) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableChunkMetadata(val *ChunkMetadata) *NullableChunkMetadata {
	return &NullableChunkMetadata{value: val, isSet: true}
}

func (v NullableChunkMetadata) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableChunkMetadata) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
