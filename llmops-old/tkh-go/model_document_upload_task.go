/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// DocumentUploadTask DocumentUploadTask，文件上传任务实体
type DocumentUploadTask struct {
	Documents *[]Document `json:"documents,omitempty"`
	// 创建时间
	EndTime *string `json:"endTime,omitempty"`
	// ID
	Id *int64 `json:"id,omitempty"`
	Percent *string `json:"percent,omitempty"`
	// 创建时间
	StartTime *string `json:"startTime,omitempty"`
	SuccessDocs *int32 `json:"successDocs,omitempty"`
	TotalDocs *int32 `json:"totalDocs,omitempty"`
	// 任务状态 1= 任务创建 2=上传中 3=上传成功  4=上传失败
	UploadTaskStatus *int32 `json:"uploadTaskStatus,omitempty"`
	// 上传类型 1 =本地上传 2 知识库上传 default = 1
	UploadType *int32 `json:"uploadType,omitempty"`
}

// NewDocumentUploadTask instantiates a new DocumentUploadTask object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDocumentUploadTask() *DocumentUploadTask {
	this := DocumentUploadTask{}
	return &this
}

// NewDocumentUploadTaskWithDefaults instantiates a new DocumentUploadTask object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDocumentUploadTaskWithDefaults() *DocumentUploadTask {
	this := DocumentUploadTask{}
	return &this
}

// GetDocuments returns the Documents field value if set, zero value otherwise.
func (o *DocumentUploadTask) GetDocuments() []Document {
	if o == nil || o.Documents == nil {
		var ret []Document
		return ret
	}
	return *o.Documents
}

// GetDocumentsOk returns a tuple with the Documents field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentUploadTask) GetDocumentsOk() (*[]Document, bool) {
	if o == nil || o.Documents == nil {
		return nil, false
	}
	return o.Documents, true
}

// HasDocuments returns a boolean if a field has been set.
func (o *DocumentUploadTask) HasDocuments() bool {
	if o != nil && o.Documents != nil {
		return true
	}

	return false
}

// SetDocuments gets a reference to the given []Document and assigns it to the Documents field.
func (o *DocumentUploadTask) SetDocuments(v []Document) {
	o.Documents = &v
}

// GetEndTime returns the EndTime field value if set, zero value otherwise.
func (o *DocumentUploadTask) GetEndTime() string {
	if o == nil || o.EndTime == nil {
		var ret string
		return ret
	}
	return *o.EndTime
}

// GetEndTimeOk returns a tuple with the EndTime field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentUploadTask) GetEndTimeOk() (*string, bool) {
	if o == nil || o.EndTime == nil {
		return nil, false
	}
	return o.EndTime, true
}

// HasEndTime returns a boolean if a field has been set.
func (o *DocumentUploadTask) HasEndTime() bool {
	if o != nil && o.EndTime != nil {
		return true
	}

	return false
}

// SetEndTime gets a reference to the given string and assigns it to the EndTime field.
func (o *DocumentUploadTask) SetEndTime(v string) {
	o.EndTime = &v
}

// GetId returns the Id field value if set, zero value otherwise.
func (o *DocumentUploadTask) GetId() int64 {
	if o == nil || o.Id == nil {
		var ret int64
		return ret
	}
	return *o.Id
}

// GetIdOk returns a tuple with the Id field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentUploadTask) GetIdOk() (*int64, bool) {
	if o == nil || o.Id == nil {
		return nil, false
	}
	return o.Id, true
}

// HasId returns a boolean if a field has been set.
func (o *DocumentUploadTask) HasId() bool {
	if o != nil && o.Id != nil {
		return true
	}

	return false
}

// SetId gets a reference to the given int64 and assigns it to the Id field.
func (o *DocumentUploadTask) SetId(v int64) {
	o.Id = &v
}

// GetPercent returns the Percent field value if set, zero value otherwise.
func (o *DocumentUploadTask) GetPercent() string {
	if o == nil || o.Percent == nil {
		var ret string
		return ret
	}
	return *o.Percent
}

// GetPercentOk returns a tuple with the Percent field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentUploadTask) GetPercentOk() (*string, bool) {
	if o == nil || o.Percent == nil {
		return nil, false
	}
	return o.Percent, true
}

// HasPercent returns a boolean if a field has been set.
func (o *DocumentUploadTask) HasPercent() bool {
	if o != nil && o.Percent != nil {
		return true
	}

	return false
}

// SetPercent gets a reference to the given string and assigns it to the Percent field.
func (o *DocumentUploadTask) SetPercent(v string) {
	o.Percent = &v
}

// GetStartTime returns the StartTime field value if set, zero value otherwise.
func (o *DocumentUploadTask) GetStartTime() string {
	if o == nil || o.StartTime == nil {
		var ret string
		return ret
	}
	return *o.StartTime
}

// GetStartTimeOk returns a tuple with the StartTime field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentUploadTask) GetStartTimeOk() (*string, bool) {
	if o == nil || o.StartTime == nil {
		return nil, false
	}
	return o.StartTime, true
}

// HasStartTime returns a boolean if a field has been set.
func (o *DocumentUploadTask) HasStartTime() bool {
	if o != nil && o.StartTime != nil {
		return true
	}

	return false
}

// SetStartTime gets a reference to the given string and assigns it to the StartTime field.
func (o *DocumentUploadTask) SetStartTime(v string) {
	o.StartTime = &v
}

// GetSuccessDocs returns the SuccessDocs field value if set, zero value otherwise.
func (o *DocumentUploadTask) GetSuccessDocs() int32 {
	if o == nil || o.SuccessDocs == nil {
		var ret int32
		return ret
	}
	return *o.SuccessDocs
}

// GetSuccessDocsOk returns a tuple with the SuccessDocs field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentUploadTask) GetSuccessDocsOk() (*int32, bool) {
	if o == nil || o.SuccessDocs == nil {
		return nil, false
	}
	return o.SuccessDocs, true
}

// HasSuccessDocs returns a boolean if a field has been set.
func (o *DocumentUploadTask) HasSuccessDocs() bool {
	if o != nil && o.SuccessDocs != nil {
		return true
	}

	return false
}

// SetSuccessDocs gets a reference to the given int32 and assigns it to the SuccessDocs field.
func (o *DocumentUploadTask) SetSuccessDocs(v int32) {
	o.SuccessDocs = &v
}

// GetTotalDocs returns the TotalDocs field value if set, zero value otherwise.
func (o *DocumentUploadTask) GetTotalDocs() int32 {
	if o == nil || o.TotalDocs == nil {
		var ret int32
		return ret
	}
	return *o.TotalDocs
}

// GetTotalDocsOk returns a tuple with the TotalDocs field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentUploadTask) GetTotalDocsOk() (*int32, bool) {
	if o == nil || o.TotalDocs == nil {
		return nil, false
	}
	return o.TotalDocs, true
}

// HasTotalDocs returns a boolean if a field has been set.
func (o *DocumentUploadTask) HasTotalDocs() bool {
	if o != nil && o.TotalDocs != nil {
		return true
	}

	return false
}

// SetTotalDocs gets a reference to the given int32 and assigns it to the TotalDocs field.
func (o *DocumentUploadTask) SetTotalDocs(v int32) {
	o.TotalDocs = &v
}

// GetUploadTaskStatus returns the UploadTaskStatus field value if set, zero value otherwise.
func (o *DocumentUploadTask) GetUploadTaskStatus() int32 {
	if o == nil || o.UploadTaskStatus == nil {
		var ret int32
		return ret
	}
	return *o.UploadTaskStatus
}

// GetUploadTaskStatusOk returns a tuple with the UploadTaskStatus field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentUploadTask) GetUploadTaskStatusOk() (*int32, bool) {
	if o == nil || o.UploadTaskStatus == nil {
		return nil, false
	}
	return o.UploadTaskStatus, true
}

// HasUploadTaskStatus returns a boolean if a field has been set.
func (o *DocumentUploadTask) HasUploadTaskStatus() bool {
	if o != nil && o.UploadTaskStatus != nil {
		return true
	}

	return false
}

// SetUploadTaskStatus gets a reference to the given int32 and assigns it to the UploadTaskStatus field.
func (o *DocumentUploadTask) SetUploadTaskStatus(v int32) {
	o.UploadTaskStatus = &v
}

// GetUploadType returns the UploadType field value if set, zero value otherwise.
func (o *DocumentUploadTask) GetUploadType() int32 {
	if o == nil || o.UploadType == nil {
		var ret int32
		return ret
	}
	return *o.UploadType
}

// GetUploadTypeOk returns a tuple with the UploadType field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentUploadTask) GetUploadTypeOk() (*int32, bool) {
	if o == nil || o.UploadType == nil {
		return nil, false
	}
	return o.UploadType, true
}

// HasUploadType returns a boolean if a field has been set.
func (o *DocumentUploadTask) HasUploadType() bool {
	if o != nil && o.UploadType != nil {
		return true
	}

	return false
}

// SetUploadType gets a reference to the given int32 and assigns it to the UploadType field.
func (o *DocumentUploadTask) SetUploadType(v int32) {
	o.UploadType = &v
}

func (o DocumentUploadTask) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Documents != nil {
		toSerialize["documents"] = o.Documents
	}
	if o.EndTime != nil {
		toSerialize["endTime"] = o.EndTime
	}
	if o.Id != nil {
		toSerialize["id"] = o.Id
	}
	if o.Percent != nil {
		toSerialize["percent"] = o.Percent
	}
	if o.StartTime != nil {
		toSerialize["startTime"] = o.StartTime
	}
	if o.SuccessDocs != nil {
		toSerialize["successDocs"] = o.SuccessDocs
	}
	if o.TotalDocs != nil {
		toSerialize["totalDocs"] = o.TotalDocs
	}
	if o.UploadTaskStatus != nil {
		toSerialize["uploadTaskStatus"] = o.UploadTaskStatus
	}
	if o.UploadType != nil {
		toSerialize["uploadType"] = o.UploadType
	}
	return json.Marshal(toSerialize)
}

type NullableDocumentUploadTask struct {
	value *DocumentUploadTask
	isSet bool
}

func (v NullableDocumentUploadTask) Get() *DocumentUploadTask {
	return v.value
}

func (v *NullableDocumentUploadTask) Set(val *DocumentUploadTask) {
	v.value = val
	v.isSet = true
}

func (v NullableDocumentUploadTask) IsSet() bool {
	return v.isSet
}

func (v *NullableDocumentUploadTask) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDocumentUploadTask(val *DocumentUploadTask) *NullableDocumentUploadTask {
	return &NullableDocumentUploadTask{value: val, isSet: true}
}

func (v NullableDocumentUploadTask) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDocumentUploadTask) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
