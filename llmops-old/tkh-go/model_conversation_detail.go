/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// ConversationDetail ConversationDetail
type ConversationDetail struct {
	Answer *string `json:"answer,omitempty"`
	Question *string `json:"question,omitempty"`
}

// NewConversationDetail instantiates a new ConversationDetail object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewConversationDetail() *ConversationDetail {
	this := ConversationDetail{}
	return &this
}

// NewConversationDetailWithDefaults instantiates a new ConversationDetail object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewConversationDetailWithDefaults() *ConversationDetail {
	this := ConversationDetail{}
	return &this
}

// GetAnswer returns the Answer field value if set, zero value otherwise.
func (o *ConversationDetail) GetAnswer() string {
	if o == nil || o.Answer == nil {
		var ret string
		return ret
	}
	return *o.Answer
}

// GetAnswerOk returns a tuple with the Answer field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ConversationDetail) GetAnswerOk() (*string, bool) {
	if o == nil || o.Answer == nil {
		return nil, false
	}
	return o.Answer, true
}

// HasAnswer returns a boolean if a field has been set.
func (o *ConversationDetail) HasAnswer() bool {
	if o != nil && o.Answer != nil {
		return true
	}

	return false
}

// SetAnswer gets a reference to the given string and assigns it to the Answer field.
func (o *ConversationDetail) SetAnswer(v string) {
	o.Answer = &v
}

// GetQuestion returns the Question field value if set, zero value otherwise.
func (o *ConversationDetail) GetQuestion() string {
	if o == nil || o.Question == nil {
		var ret string
		return ret
	}
	return *o.Question
}

// GetQuestionOk returns a tuple with the Question field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ConversationDetail) GetQuestionOk() (*string, bool) {
	if o == nil || o.Question == nil {
		return nil, false
	}
	return o.Question, true
}

// HasQuestion returns a boolean if a field has been set.
func (o *ConversationDetail) HasQuestion() bool {
	if o != nil && o.Question != nil {
		return true
	}

	return false
}

// SetQuestion gets a reference to the given string and assigns it to the Question field.
func (o *ConversationDetail) SetQuestion(v string) {
	o.Question = &v
}

func (o ConversationDetail) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Answer != nil {
		toSerialize["answer"] = o.Answer
	}
	if o.Question != nil {
		toSerialize["question"] = o.Question
	}
	return json.Marshal(toSerialize)
}

type NullableConversationDetail struct {
	value *ConversationDetail
	isSet bool
}

func (v NullableConversationDetail) Get() *ConversationDetail {
	return v.value
}

func (v *NullableConversationDetail) Set(val *ConversationDetail) {
	v.value = val
	v.isSet = true
}

func (v NullableConversationDetail) IsSet() bool {
	return v.isSet
}

func (v *NullableConversationDetail) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableConversationDetail(val *ConversationDetail) *NullableConversationDetail {
	return &NullableConversationDetail{value: val, isSet: true}
}

func (v NullableConversationDetail) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableConversationDetail) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
