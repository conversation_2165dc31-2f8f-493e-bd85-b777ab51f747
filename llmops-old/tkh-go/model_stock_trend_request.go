/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// StockTrendRequest StockTrendRequest
type StockTrendRequest struct {
	DateCode *int32 `json:"dateCode,omitempty"`
	StockCode *string `json:"stockCode,omitempty"`
}

// NewStockTrendRequest instantiates a new StockTrendRequest object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewStockTrendRequest() *StockTrendRequest {
	this := StockTrendRequest{}
	return &this
}

// NewStockTrendRequestWithDefaults instantiates a new StockTrendRequest object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewStockTrendRequestWithDefaults() *StockTrendRequest {
	this := StockTrendRequest{}
	return &this
}

// GetDateCode returns the DateCode field value if set, zero value otherwise.
func (o *StockTrendRequest) GetDateCode() int32 {
	if o == nil || o.DateCode == nil {
		var ret int32
		return ret
	}
	return *o.DateCode
}

// GetDateCodeOk returns a tuple with the DateCode field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *StockTrendRequest) GetDateCodeOk() (*int32, bool) {
	if o == nil || o.DateCode == nil {
		return nil, false
	}
	return o.DateCode, true
}

// HasDateCode returns a boolean if a field has been set.
func (o *StockTrendRequest) HasDateCode() bool {
	if o != nil && o.DateCode != nil {
		return true
	}

	return false
}

// SetDateCode gets a reference to the given int32 and assigns it to the DateCode field.
func (o *StockTrendRequest) SetDateCode(v int32) {
	o.DateCode = &v
}

// GetStockCode returns the StockCode field value if set, zero value otherwise.
func (o *StockTrendRequest) GetStockCode() string {
	if o == nil || o.StockCode == nil {
		var ret string
		return ret
	}
	return *o.StockCode
}

// GetStockCodeOk returns a tuple with the StockCode field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *StockTrendRequest) GetStockCodeOk() (*string, bool) {
	if o == nil || o.StockCode == nil {
		return nil, false
	}
	return o.StockCode, true
}

// HasStockCode returns a boolean if a field has been set.
func (o *StockTrendRequest) HasStockCode() bool {
	if o != nil && o.StockCode != nil {
		return true
	}

	return false
}

// SetStockCode gets a reference to the given string and assigns it to the StockCode field.
func (o *StockTrendRequest) SetStockCode(v string) {
	o.StockCode = &v
}

func (o StockTrendRequest) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.DateCode != nil {
		toSerialize["dateCode"] = o.DateCode
	}
	if o.StockCode != nil {
		toSerialize["stockCode"] = o.StockCode
	}
	return json.Marshal(toSerialize)
}

type NullableStockTrendRequest struct {
	value *StockTrendRequest
	isSet bool
}

func (v NullableStockTrendRequest) Get() *StockTrendRequest {
	return v.value
}

func (v *NullableStockTrendRequest) Set(val *StockTrendRequest) {
	v.value = val
	v.isSet = true
}

func (v NullableStockTrendRequest) IsSet() bool {
	return v.isSet
}

func (v *NullableStockTrendRequest) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableStockTrendRequest(val *StockTrendRequest) *NullableStockTrendRequest {
	return &NullableStockTrendRequest{value: val, isSet: true}
}

func (v NullableStockTrendRequest) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableStockTrendRequest) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
