/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// ConversationDetailVo ConversationDetailVO
type ConversationDetailVo struct {
	ConversationId *string `json:"conversationId,omitempty"`
	Qalist *[]ConversationDetail `json:"qalist,omitempty"`
}

// NewConversationDetailVo instantiates a new ConversationDetailVo object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewConversationDetailVo() *ConversationDetailVo {
	this := ConversationDetailVo{}
	return &this
}

// NewConversationDetailVoWithDefaults instantiates a new ConversationDetailVo object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewConversationDetailVoWithDefaults() *ConversationDetailVo {
	this := ConversationDetailVo{}
	return &this
}

// GetConversationId returns the ConversationId field value if set, zero value otherwise.
func (o *ConversationDetailVo) GetConversationId() string {
	if o == nil || o.ConversationId == nil {
		var ret string
		return ret
	}
	return *o.ConversationId
}

// GetConversationIdOk returns a tuple with the ConversationId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ConversationDetailVo) GetConversationIdOk() (*string, bool) {
	if o == nil || o.ConversationId == nil {
		return nil, false
	}
	return o.ConversationId, true
}

// HasConversationId returns a boolean if a field has been set.
func (o *ConversationDetailVo) HasConversationId() bool {
	if o != nil && o.ConversationId != nil {
		return true
	}

	return false
}

// SetConversationId gets a reference to the given string and assigns it to the ConversationId field.
func (o *ConversationDetailVo) SetConversationId(v string) {
	o.ConversationId = &v
}

// GetQalist returns the Qalist field value if set, zero value otherwise.
func (o *ConversationDetailVo) GetQalist() []ConversationDetail {
	if o == nil || o.Qalist == nil {
		var ret []ConversationDetail
		return ret
	}
	return *o.Qalist
}

// GetQalistOk returns a tuple with the Qalist field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ConversationDetailVo) GetQalistOk() (*[]ConversationDetail, bool) {
	if o == nil || o.Qalist == nil {
		return nil, false
	}
	return o.Qalist, true
}

// HasQalist returns a boolean if a field has been set.
func (o *ConversationDetailVo) HasQalist() bool {
	if o != nil && o.Qalist != nil {
		return true
	}

	return false
}

// SetQalist gets a reference to the given []ConversationDetail and assigns it to the Qalist field.
func (o *ConversationDetailVo) SetQalist(v []ConversationDetail) {
	o.Qalist = &v
}

func (o ConversationDetailVo) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.ConversationId != nil {
		toSerialize["conversationId"] = o.ConversationId
	}
	if o.Qalist != nil {
		toSerialize["qalist"] = o.Qalist
	}
	return json.Marshal(toSerialize)
}

type NullableConversationDetailVo struct {
	value *ConversationDetailVo
	isSet bool
}

func (v NullableConversationDetailVo) Get() *ConversationDetailVo {
	return v.value
}

func (v *NullableConversationDetailVo) Set(val *ConversationDetailVo) {
	v.value = val
	v.isSet = true
}

func (v NullableConversationDetailVo) IsSet() bool {
	return v.isSet
}

func (v *NullableConversationDetailVo) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableConversationDetailVo(val *ConversationDetailVo) *NullableConversationDetailVo {
	return &NullableConversationDetailVo{value: val, isSet: true}
}

func (v NullableConversationDetailVo) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableConversationDetailVo) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
