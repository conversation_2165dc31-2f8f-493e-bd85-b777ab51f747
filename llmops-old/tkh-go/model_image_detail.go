/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// ImageDetail ImageDetail
type ImageDetail struct {
	DirIds *[]string `json:"dir_ids,omitempty"`
	Distance *string `json:"distance,omitempty"`
	DocId *string `json:"doc_id,omitempty"`
	FileName *string `json:"file_name,omitempty"`
	ImageData *string `json:"image_data,omitempty"`
	KbId *string `json:"kb_id,omitempty"`
}

// NewImageDetail instantiates a new ImageDetail object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewImageDetail() *ImageDetail {
	this := ImageDetail{}
	return &this
}

// NewImageDetailWithDefaults instantiates a new ImageDetail object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewImageDetailWithDefaults() *ImageDetail {
	this := ImageDetail{}
	return &this
}

// GetDirIds returns the DirIds field value if set, zero value otherwise.
func (o *ImageDetail) GetDirIds() []string {
	if o == nil || o.DirIds == nil {
		var ret []string
		return ret
	}
	return *o.DirIds
}

// GetDirIdsOk returns a tuple with the DirIds field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ImageDetail) GetDirIdsOk() (*[]string, bool) {
	if o == nil || o.DirIds == nil {
		return nil, false
	}
	return o.DirIds, true
}

// HasDirIds returns a boolean if a field has been set.
func (o *ImageDetail) HasDirIds() bool {
	if o != nil && o.DirIds != nil {
		return true
	}

	return false
}

// SetDirIds gets a reference to the given []string and assigns it to the DirIds field.
func (o *ImageDetail) SetDirIds(v []string) {
	o.DirIds = &v
}

// GetDistance returns the Distance field value if set, zero value otherwise.
func (o *ImageDetail) GetDistance() string {
	if o == nil || o.Distance == nil {
		var ret string
		return ret
	}
	return *o.Distance
}

// GetDistanceOk returns a tuple with the Distance field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ImageDetail) GetDistanceOk() (*string, bool) {
	if o == nil || o.Distance == nil {
		return nil, false
	}
	return o.Distance, true
}

// HasDistance returns a boolean if a field has been set.
func (o *ImageDetail) HasDistance() bool {
	if o != nil && o.Distance != nil {
		return true
	}

	return false
}

// SetDistance gets a reference to the given string and assigns it to the Distance field.
func (o *ImageDetail) SetDistance(v string) {
	o.Distance = &v
}

// GetDocId returns the DocId field value if set, zero value otherwise.
func (o *ImageDetail) GetDocId() string {
	if o == nil || o.DocId == nil {
		var ret string
		return ret
	}
	return *o.DocId
}

// GetDocIdOk returns a tuple with the DocId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ImageDetail) GetDocIdOk() (*string, bool) {
	if o == nil || o.DocId == nil {
		return nil, false
	}
	return o.DocId, true
}

// HasDocId returns a boolean if a field has been set.
func (o *ImageDetail) HasDocId() bool {
	if o != nil && o.DocId != nil {
		return true
	}

	return false
}

// SetDocId gets a reference to the given string and assigns it to the DocId field.
func (o *ImageDetail) SetDocId(v string) {
	o.DocId = &v
}

// GetFileName returns the FileName field value if set, zero value otherwise.
func (o *ImageDetail) GetFileName() string {
	if o == nil || o.FileName == nil {
		var ret string
		return ret
	}
	return *o.FileName
}

// GetFileNameOk returns a tuple with the FileName field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ImageDetail) GetFileNameOk() (*string, bool) {
	if o == nil || o.FileName == nil {
		return nil, false
	}
	return o.FileName, true
}

// HasFileName returns a boolean if a field has been set.
func (o *ImageDetail) HasFileName() bool {
	if o != nil && o.FileName != nil {
		return true
	}

	return false
}

// SetFileName gets a reference to the given string and assigns it to the FileName field.
func (o *ImageDetail) SetFileName(v string) {
	o.FileName = &v
}

// GetImageData returns the ImageData field value if set, zero value otherwise.
func (o *ImageDetail) GetImageData() string {
	if o == nil || o.ImageData == nil {
		var ret string
		return ret
	}
	return *o.ImageData
}

// GetImageDataOk returns a tuple with the ImageData field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ImageDetail) GetImageDataOk() (*string, bool) {
	if o == nil || o.ImageData == nil {
		return nil, false
	}
	return o.ImageData, true
}

// HasImageData returns a boolean if a field has been set.
func (o *ImageDetail) HasImageData() bool {
	if o != nil && o.ImageData != nil {
		return true
	}

	return false
}

// SetImageData gets a reference to the given string and assigns it to the ImageData field.
func (o *ImageDetail) SetImageData(v string) {
	o.ImageData = &v
}

// GetKbId returns the KbId field value if set, zero value otherwise.
func (o *ImageDetail) GetKbId() string {
	if o == nil || o.KbId == nil {
		var ret string
		return ret
	}
	return *o.KbId
}

// GetKbIdOk returns a tuple with the KbId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ImageDetail) GetKbIdOk() (*string, bool) {
	if o == nil || o.KbId == nil {
		return nil, false
	}
	return o.KbId, true
}

// HasKbId returns a boolean if a field has been set.
func (o *ImageDetail) HasKbId() bool {
	if o != nil && o.KbId != nil {
		return true
	}

	return false
}

// SetKbId gets a reference to the given string and assigns it to the KbId field.
func (o *ImageDetail) SetKbId(v string) {
	o.KbId = &v
}

func (o ImageDetail) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.DirIds != nil {
		toSerialize["dir_ids"] = o.DirIds
	}
	if o.Distance != nil {
		toSerialize["distance"] = o.Distance
	}
	if o.DocId != nil {
		toSerialize["doc_id"] = o.DocId
	}
	if o.FileName != nil {
		toSerialize["file_name"] = o.FileName
	}
	if o.ImageData != nil {
		toSerialize["image_data"] = o.ImageData
	}
	if o.KbId != nil {
		toSerialize["kb_id"] = o.KbId
	}
	return json.Marshal(toSerialize)
}

type NullableImageDetail struct {
	value *ImageDetail
	isSet bool
}

func (v NullableImageDetail) Get() *ImageDetail {
	return v.value
}

func (v *NullableImageDetail) Set(val *ImageDetail) {
	v.value = val
	v.isSet = true
}

func (v NullableImageDetail) IsSet() bool {
	return v.isSet
}

func (v *NullableImageDetail) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableImageDetail(val *ImageDetail) *NullableImageDetail {
	return &NullableImageDetail{value: val, isSet: true}
}

func (v NullableImageDetail) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableImageDetail) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
