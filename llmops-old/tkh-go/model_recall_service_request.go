/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// RecallServiceRequest RecallServiceRequest
type RecallServiceRequest struct {
	// User Queries
	UserQueries *[]string `json:"user_queries,omitempty"`
	// Sources
	Sources *[]KnowledgeSource `json:"sources,omitempty"`
	// Hippo Data Sources
	HippoDataSources *[]string `json:"hippo_data_sources,omitempty"`
	// Scope Data Sources
	ScopeDataSources *[]string `json:"scope_data_sources,omitempty"`
	// Reserved Condition Company
	ReservedConditionCompany *[]string `json:"reserved_condition_company,omitempty"`
	// Reserved Condition Time
	ReservedConditionTime *[]string `json:"reserved_condition_time,omitempty"`
	// Pdf Name
	PdfName *string `json:"pdf_name,omitempty"`
	// Doc Id
	DocId *string `json:"doc_id,omitempty"`
	// Top K
	TopK *int32 `json:"top_k,omitempty"`
	// Fragment Text Filer
	FragmentTextFiler *bool `json:"fragment_text_filer,omitempty"`
	// Deduplicate
	Deduplicate *bool `json:"deduplicate,omitempty"`
	// Table Reformat
	TableReformat *bool `json:"table_reformat,omitempty"`
	// Rerank
	Rerank *bool `json:"rerank,omitempty"`
	// Compact Text
	CompactText *bool `json:"compact_text,omitempty"`
}

// NewRecallServiceRequest instantiates a new RecallServiceRequest object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewRecallServiceRequest() *RecallServiceRequest {
	this := RecallServiceRequest{}
	var pdfName string = ""
	this.PdfName = &pdfName
	var docId string = ""
	this.DocId = &docId
	var topK int32 = 5
	this.TopK = &topK
	var fragmentTextFiler bool = true
	this.FragmentTextFiler = &fragmentTextFiler
	var deduplicate bool = true
	this.Deduplicate = &deduplicate
	var tableReformat bool = true
	this.TableReformat = &tableReformat
	var rerank bool = true
	this.Rerank = &rerank
	var compactText bool = true
	this.CompactText = &compactText
	return &this
}

// NewRecallServiceRequestWithDefaults instantiates a new RecallServiceRequest object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewRecallServiceRequestWithDefaults() *RecallServiceRequest {
	this := RecallServiceRequest{}
	var pdfName string = ""
	this.PdfName = &pdfName
	var docId string = ""
	this.DocId = &docId
	var topK int32 = 5
	this.TopK = &topK
	var fragmentTextFiler bool = true
	this.FragmentTextFiler = &fragmentTextFiler
	var deduplicate bool = true
	this.Deduplicate = &deduplicate
	var tableReformat bool = true
	this.TableReformat = &tableReformat
	var rerank bool = true
	this.Rerank = &rerank
	var compactText bool = true
	this.CompactText = &compactText
	return &this
}

// GetUserQueries returns the UserQueries field value if set, zero value otherwise.
func (o *RecallServiceRequest) GetUserQueries() []string {
	if o == nil || o.UserQueries == nil {
		var ret []string
		return ret
	}
	return *o.UserQueries
}

// GetUserQueriesOk returns a tuple with the UserQueries field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *RecallServiceRequest) GetUserQueriesOk() (*[]string, bool) {
	if o == nil || o.UserQueries == nil {
		return nil, false
	}
	return o.UserQueries, true
}

// HasUserQueries returns a boolean if a field has been set.
func (o *RecallServiceRequest) HasUserQueries() bool {
	if o != nil && o.UserQueries != nil {
		return true
	}

	return false
}

// SetUserQueries gets a reference to the given []string and assigns it to the UserQueries field.
func (o *RecallServiceRequest) SetUserQueries(v []string) {
	o.UserQueries = &v
}

// GetSources returns the Sources field value if set, zero value otherwise.
func (o *RecallServiceRequest) GetSources() []KnowledgeSource {
	if o == nil || o.Sources == nil {
		var ret []KnowledgeSource
		return ret
	}
	return *o.Sources
}

// GetSourcesOk returns a tuple with the Sources field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *RecallServiceRequest) GetSourcesOk() (*[]KnowledgeSource, bool) {
	if o == nil || o.Sources == nil {
		return nil, false
	}
	return o.Sources, true
}

// HasSources returns a boolean if a field has been set.
func (o *RecallServiceRequest) HasSources() bool {
	if o != nil && o.Sources != nil {
		return true
	}

	return false
}

// SetSources gets a reference to the given []KnowledgeSource and assigns it to the Sources field.
func (o *RecallServiceRequest) SetSources(v []KnowledgeSource) {
	o.Sources = &v
}

// GetHippoDataSources returns the HippoDataSources field value if set, zero value otherwise.
func (o *RecallServiceRequest) GetHippoDataSources() []string {
	if o == nil || o.HippoDataSources == nil {
		var ret []string
		return ret
	}
	return *o.HippoDataSources
}

// GetHippoDataSourcesOk returns a tuple with the HippoDataSources field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *RecallServiceRequest) GetHippoDataSourcesOk() (*[]string, bool) {
	if o == nil || o.HippoDataSources == nil {
		return nil, false
	}
	return o.HippoDataSources, true
}

// HasHippoDataSources returns a boolean if a field has been set.
func (o *RecallServiceRequest) HasHippoDataSources() bool {
	if o != nil && o.HippoDataSources != nil {
		return true
	}

	return false
}

// SetHippoDataSources gets a reference to the given []string and assigns it to the HippoDataSources field.
func (o *RecallServiceRequest) SetHippoDataSources(v []string) {
	o.HippoDataSources = &v
}

// GetScopeDataSources returns the ScopeDataSources field value if set, zero value otherwise.
func (o *RecallServiceRequest) GetScopeDataSources() []string {
	if o == nil || o.ScopeDataSources == nil {
		var ret []string
		return ret
	}
	return *o.ScopeDataSources
}

// GetScopeDataSourcesOk returns a tuple with the ScopeDataSources field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *RecallServiceRequest) GetScopeDataSourcesOk() (*[]string, bool) {
	if o == nil || o.ScopeDataSources == nil {
		return nil, false
	}
	return o.ScopeDataSources, true
}

// HasScopeDataSources returns a boolean if a field has been set.
func (o *RecallServiceRequest) HasScopeDataSources() bool {
	if o != nil && o.ScopeDataSources != nil {
		return true
	}

	return false
}

// SetScopeDataSources gets a reference to the given []string and assigns it to the ScopeDataSources field.
func (o *RecallServiceRequest) SetScopeDataSources(v []string) {
	o.ScopeDataSources = &v
}

// GetReservedConditionCompany returns the ReservedConditionCompany field value if set, zero value otherwise.
func (o *RecallServiceRequest) GetReservedConditionCompany() []string {
	if o == nil || o.ReservedConditionCompany == nil {
		var ret []string
		return ret
	}
	return *o.ReservedConditionCompany
}

// GetReservedConditionCompanyOk returns a tuple with the ReservedConditionCompany field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *RecallServiceRequest) GetReservedConditionCompanyOk() (*[]string, bool) {
	if o == nil || o.ReservedConditionCompany == nil {
		return nil, false
	}
	return o.ReservedConditionCompany, true
}

// HasReservedConditionCompany returns a boolean if a field has been set.
func (o *RecallServiceRequest) HasReservedConditionCompany() bool {
	if o != nil && o.ReservedConditionCompany != nil {
		return true
	}

	return false
}

// SetReservedConditionCompany gets a reference to the given []string and assigns it to the ReservedConditionCompany field.
func (o *RecallServiceRequest) SetReservedConditionCompany(v []string) {
	o.ReservedConditionCompany = &v
}

// GetReservedConditionTime returns the ReservedConditionTime field value if set, zero value otherwise.
func (o *RecallServiceRequest) GetReservedConditionTime() []string {
	if o == nil || o.ReservedConditionTime == nil {
		var ret []string
		return ret
	}
	return *o.ReservedConditionTime
}

// GetReservedConditionTimeOk returns a tuple with the ReservedConditionTime field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *RecallServiceRequest) GetReservedConditionTimeOk() (*[]string, bool) {
	if o == nil || o.ReservedConditionTime == nil {
		return nil, false
	}
	return o.ReservedConditionTime, true
}

// HasReservedConditionTime returns a boolean if a field has been set.
func (o *RecallServiceRequest) HasReservedConditionTime() bool {
	if o != nil && o.ReservedConditionTime != nil {
		return true
	}

	return false
}

// SetReservedConditionTime gets a reference to the given []string and assigns it to the ReservedConditionTime field.
func (o *RecallServiceRequest) SetReservedConditionTime(v []string) {
	o.ReservedConditionTime = &v
}

// GetPdfName returns the PdfName field value if set, zero value otherwise.
func (o *RecallServiceRequest) GetPdfName() string {
	if o == nil || o.PdfName == nil {
		var ret string
		return ret
	}
	return *o.PdfName
}

// GetPdfNameOk returns a tuple with the PdfName field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *RecallServiceRequest) GetPdfNameOk() (*string, bool) {
	if o == nil || o.PdfName == nil {
		return nil, false
	}
	return o.PdfName, true
}

// HasPdfName returns a boolean if a field has been set.
func (o *RecallServiceRequest) HasPdfName() bool {
	if o != nil && o.PdfName != nil {
		return true
	}

	return false
}

// SetPdfName gets a reference to the given string and assigns it to the PdfName field.
func (o *RecallServiceRequest) SetPdfName(v string) {
	o.PdfName = &v
}

// GetDocId returns the DocId field value if set, zero value otherwise.
func (o *RecallServiceRequest) GetDocId() string {
	if o == nil || o.DocId == nil {
		var ret string
		return ret
	}
	return *o.DocId
}

// GetDocIdOk returns a tuple with the DocId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *RecallServiceRequest) GetDocIdOk() (*string, bool) {
	if o == nil || o.DocId == nil {
		return nil, false
	}
	return o.DocId, true
}

// HasDocId returns a boolean if a field has been set.
func (o *RecallServiceRequest) HasDocId() bool {
	if o != nil && o.DocId != nil {
		return true
	}

	return false
}

// SetDocId gets a reference to the given string and assigns it to the DocId field.
func (o *RecallServiceRequest) SetDocId(v string) {
	o.DocId = &v
}

// GetTopK returns the TopK field value if set, zero value otherwise.
func (o *RecallServiceRequest) GetTopK() int32 {
	if o == nil || o.TopK == nil {
		var ret int32
		return ret
	}
	return *o.TopK
}

// GetTopKOk returns a tuple with the TopK field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *RecallServiceRequest) GetTopKOk() (*int32, bool) {
	if o == nil || o.TopK == nil {
		return nil, false
	}
	return o.TopK, true
}

// HasTopK returns a boolean if a field has been set.
func (o *RecallServiceRequest) HasTopK() bool {
	if o != nil && o.TopK != nil {
		return true
	}

	return false
}

// SetTopK gets a reference to the given int32 and assigns it to the TopK field.
func (o *RecallServiceRequest) SetTopK(v int32) {
	o.TopK = &v
}

// GetFragmentTextFiler returns the FragmentTextFiler field value if set, zero value otherwise.
func (o *RecallServiceRequest) GetFragmentTextFiler() bool {
	if o == nil || o.FragmentTextFiler == nil {
		var ret bool
		return ret
	}
	return *o.FragmentTextFiler
}

// GetFragmentTextFilerOk returns a tuple with the FragmentTextFiler field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *RecallServiceRequest) GetFragmentTextFilerOk() (*bool, bool) {
	if o == nil || o.FragmentTextFiler == nil {
		return nil, false
	}
	return o.FragmentTextFiler, true
}

// HasFragmentTextFiler returns a boolean if a field has been set.
func (o *RecallServiceRequest) HasFragmentTextFiler() bool {
	if o != nil && o.FragmentTextFiler != nil {
		return true
	}

	return false
}

// SetFragmentTextFiler gets a reference to the given bool and assigns it to the FragmentTextFiler field.
func (o *RecallServiceRequest) SetFragmentTextFiler(v bool) {
	o.FragmentTextFiler = &v
}

// GetDeduplicate returns the Deduplicate field value if set, zero value otherwise.
func (o *RecallServiceRequest) GetDeduplicate() bool {
	if o == nil || o.Deduplicate == nil {
		var ret bool
		return ret
	}
	return *o.Deduplicate
}

// GetDeduplicateOk returns a tuple with the Deduplicate field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *RecallServiceRequest) GetDeduplicateOk() (*bool, bool) {
	if o == nil || o.Deduplicate == nil {
		return nil, false
	}
	return o.Deduplicate, true
}

// HasDeduplicate returns a boolean if a field has been set.
func (o *RecallServiceRequest) HasDeduplicate() bool {
	if o != nil && o.Deduplicate != nil {
		return true
	}

	return false
}

// SetDeduplicate gets a reference to the given bool and assigns it to the Deduplicate field.
func (o *RecallServiceRequest) SetDeduplicate(v bool) {
	o.Deduplicate = &v
}

// GetTableReformat returns the TableReformat field value if set, zero value otherwise.
func (o *RecallServiceRequest) GetTableReformat() bool {
	if o == nil || o.TableReformat == nil {
		var ret bool
		return ret
	}
	return *o.TableReformat
}

// GetTableReformatOk returns a tuple with the TableReformat field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *RecallServiceRequest) GetTableReformatOk() (*bool, bool) {
	if o == nil || o.TableReformat == nil {
		return nil, false
	}
	return o.TableReformat, true
}

// HasTableReformat returns a boolean if a field has been set.
func (o *RecallServiceRequest) HasTableReformat() bool {
	if o != nil && o.TableReformat != nil {
		return true
	}

	return false
}

// SetTableReformat gets a reference to the given bool and assigns it to the TableReformat field.
func (o *RecallServiceRequest) SetTableReformat(v bool) {
	o.TableReformat = &v
}

// GetRerank returns the Rerank field value if set, zero value otherwise.
func (o *RecallServiceRequest) GetRerank() bool {
	if o == nil || o.Rerank == nil {
		var ret bool
		return ret
	}
	return *o.Rerank
}

// GetRerankOk returns a tuple with the Rerank field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *RecallServiceRequest) GetRerankOk() (*bool, bool) {
	if o == nil || o.Rerank == nil {
		return nil, false
	}
	return o.Rerank, true
}

// HasRerank returns a boolean if a field has been set.
func (o *RecallServiceRequest) HasRerank() bool {
	if o != nil && o.Rerank != nil {
		return true
	}

	return false
}

// SetRerank gets a reference to the given bool and assigns it to the Rerank field.
func (o *RecallServiceRequest) SetRerank(v bool) {
	o.Rerank = &v
}

// GetCompactText returns the CompactText field value if set, zero value otherwise.
func (o *RecallServiceRequest) GetCompactText() bool {
	if o == nil || o.CompactText == nil {
		var ret bool
		return ret
	}
	return *o.CompactText
}

// GetCompactTextOk returns a tuple with the CompactText field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *RecallServiceRequest) GetCompactTextOk() (*bool, bool) {
	if o == nil || o.CompactText == nil {
		return nil, false
	}
	return o.CompactText, true
}

// HasCompactText returns a boolean if a field has been set.
func (o *RecallServiceRequest) HasCompactText() bool {
	if o != nil && o.CompactText != nil {
		return true
	}

	return false
}

// SetCompactText gets a reference to the given bool and assigns it to the CompactText field.
func (o *RecallServiceRequest) SetCompactText(v bool) {
	o.CompactText = &v
}

func (o RecallServiceRequest) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.UserQueries != nil {
		toSerialize["user_queries"] = o.UserQueries
	}
	if o.Sources != nil {
		toSerialize["sources"] = o.Sources
	}
	if o.HippoDataSources != nil {
		toSerialize["hippo_data_sources"] = o.HippoDataSources
	}
	if o.ScopeDataSources != nil {
		toSerialize["scope_data_sources"] = o.ScopeDataSources
	}
	if o.ReservedConditionCompany != nil {
		toSerialize["reserved_condition_company"] = o.ReservedConditionCompany
	}
	if o.ReservedConditionTime != nil {
		toSerialize["reserved_condition_time"] = o.ReservedConditionTime
	}
	if o.PdfName != nil {
		toSerialize["pdf_name"] = o.PdfName
	}
	if o.DocId != nil {
		toSerialize["doc_id"] = o.DocId
	}
	if o.TopK != nil {
		toSerialize["top_k"] = o.TopK
	}
	if o.FragmentTextFiler != nil {
		toSerialize["fragment_text_filer"] = o.FragmentTextFiler
	}
	if o.Deduplicate != nil {
		toSerialize["deduplicate"] = o.Deduplicate
	}
	if o.TableReformat != nil {
		toSerialize["table_reformat"] = o.TableReformat
	}
	if o.Rerank != nil {
		toSerialize["rerank"] = o.Rerank
	}
	if o.CompactText != nil {
		toSerialize["compact_text"] = o.CompactText
	}
	return json.Marshal(toSerialize)
}

type NullableRecallServiceRequest struct {
	value *RecallServiceRequest
	isSet bool
}

func (v NullableRecallServiceRequest) Get() *RecallServiceRequest {
	return v.value
}

func (v *NullableRecallServiceRequest) Set(val *RecallServiceRequest) {
	v.value = val
	v.isSet = true
}

func (v NullableRecallServiceRequest) IsSet() bool {
	return v.isSet
}

func (v *NullableRecallServiceRequest) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableRecallServiceRequest(val *RecallServiceRequest) *NullableRecallServiceRequest {
	return &NullableRecallServiceRequest{value: val, isSet: true}
}

func (v NullableRecallServiceRequest) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableRecallServiceRequest) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
