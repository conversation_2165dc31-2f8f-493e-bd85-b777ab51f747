/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// ImageQueryRequest ImageQueryRequest
type ImageQueryRequest struct {
	KbIdParams *[]KnowledgeBaseDirParams `json:"kb_id_params,omitempty"`
	Mode *string `json:"mode,omitempty"`
	ReturnImage *bool `json:"return_image,omitempty"`
	Text *string `json:"text,omitempty"`
	TopK *int32 `json:"top_k,omitempty"`
}

// NewImageQueryRequest instantiates a new ImageQueryRequest object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewImageQueryRequest() *ImageQueryRequest {
	this := ImageQueryRequest{}
	return &this
}

// NewImageQueryRequestWithDefaults instantiates a new ImageQueryRequest object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewImageQueryRequestWithDefaults() *ImageQueryRequest {
	this := ImageQueryRequest{}
	return &this
}

// GetKbIdParams returns the KbIdParams field value if set, zero value otherwise.
func (o *ImageQueryRequest) GetKbIdParams() []KnowledgeBaseDirParams {
	if o == nil || o.KbIdParams == nil {
		var ret []KnowledgeBaseDirParams
		return ret
	}
	return *o.KbIdParams
}

// GetKbIdParamsOk returns a tuple with the KbIdParams field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ImageQueryRequest) GetKbIdParamsOk() (*[]KnowledgeBaseDirParams, bool) {
	if o == nil || o.KbIdParams == nil {
		return nil, false
	}
	return o.KbIdParams, true
}

// HasKbIdParams returns a boolean if a field has been set.
func (o *ImageQueryRequest) HasKbIdParams() bool {
	if o != nil && o.KbIdParams != nil {
		return true
	}

	return false
}

// SetKbIdParams gets a reference to the given []KnowledgeBaseDirParams and assigns it to the KbIdParams field.
func (o *ImageQueryRequest) SetKbIdParams(v []KnowledgeBaseDirParams) {
	o.KbIdParams = &v
}

// GetMode returns the Mode field value if set, zero value otherwise.
func (o *ImageQueryRequest) GetMode() string {
	if o == nil || o.Mode == nil {
		var ret string
		return ret
	}
	return *o.Mode
}

// GetModeOk returns a tuple with the Mode field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ImageQueryRequest) GetModeOk() (*string, bool) {
	if o == nil || o.Mode == nil {
		return nil, false
	}
	return o.Mode, true
}

// HasMode returns a boolean if a field has been set.
func (o *ImageQueryRequest) HasMode() bool {
	if o != nil && o.Mode != nil {
		return true
	}

	return false
}

// SetMode gets a reference to the given string and assigns it to the Mode field.
func (o *ImageQueryRequest) SetMode(v string) {
	o.Mode = &v
}

// GetReturnImage returns the ReturnImage field value if set, zero value otherwise.
func (o *ImageQueryRequest) GetReturnImage() bool {
	if o == nil || o.ReturnImage == nil {
		var ret bool
		return ret
	}
	return *o.ReturnImage
}

// GetReturnImageOk returns a tuple with the ReturnImage field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ImageQueryRequest) GetReturnImageOk() (*bool, bool) {
	if o == nil || o.ReturnImage == nil {
		return nil, false
	}
	return o.ReturnImage, true
}

// HasReturnImage returns a boolean if a field has been set.
func (o *ImageQueryRequest) HasReturnImage() bool {
	if o != nil && o.ReturnImage != nil {
		return true
	}

	return false
}

// SetReturnImage gets a reference to the given bool and assigns it to the ReturnImage field.
func (o *ImageQueryRequest) SetReturnImage(v bool) {
	o.ReturnImage = &v
}

// GetText returns the Text field value if set, zero value otherwise.
func (o *ImageQueryRequest) GetText() string {
	if o == nil || o.Text == nil {
		var ret string
		return ret
	}
	return *o.Text
}

// GetTextOk returns a tuple with the Text field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ImageQueryRequest) GetTextOk() (*string, bool) {
	if o == nil || o.Text == nil {
		return nil, false
	}
	return o.Text, true
}

// HasText returns a boolean if a field has been set.
func (o *ImageQueryRequest) HasText() bool {
	if o != nil && o.Text != nil {
		return true
	}

	return false
}

// SetText gets a reference to the given string and assigns it to the Text field.
func (o *ImageQueryRequest) SetText(v string) {
	o.Text = &v
}

// GetTopK returns the TopK field value if set, zero value otherwise.
func (o *ImageQueryRequest) GetTopK() int32 {
	if o == nil || o.TopK == nil {
		var ret int32
		return ret
	}
	return *o.TopK
}

// GetTopKOk returns a tuple with the TopK field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ImageQueryRequest) GetTopKOk() (*int32, bool) {
	if o == nil || o.TopK == nil {
		return nil, false
	}
	return o.TopK, true
}

// HasTopK returns a boolean if a field has been set.
func (o *ImageQueryRequest) HasTopK() bool {
	if o != nil && o.TopK != nil {
		return true
	}

	return false
}

// SetTopK gets a reference to the given int32 and assigns it to the TopK field.
func (o *ImageQueryRequest) SetTopK(v int32) {
	o.TopK = &v
}

func (o ImageQueryRequest) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.KbIdParams != nil {
		toSerialize["kb_id_params"] = o.KbIdParams
	}
	if o.Mode != nil {
		toSerialize["mode"] = o.Mode
	}
	if o.ReturnImage != nil {
		toSerialize["return_image"] = o.ReturnImage
	}
	if o.Text != nil {
		toSerialize["text"] = o.Text
	}
	if o.TopK != nil {
		toSerialize["top_k"] = o.TopK
	}
	return json.Marshal(toSerialize)
}

type NullableImageQueryRequest struct {
	value *ImageQueryRequest
	isSet bool
}

func (v NullableImageQueryRequest) Get() *ImageQueryRequest {
	return v.value
}

func (v *NullableImageQueryRequest) Set(val *ImageQueryRequest) {
	v.value = val
	v.isSet = true
}

func (v NullableImageQueryRequest) IsSet() bool {
	return v.isSet
}

func (v *NullableImageQueryRequest) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableImageQueryRequest(val *ImageQueryRequest) *NullableImageQueryRequest {
	return &NullableImageQueryRequest{value: val, isSet: true}
}

func (v NullableImageQueryRequest) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableImageQueryRequest) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
