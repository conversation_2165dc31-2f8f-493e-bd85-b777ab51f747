/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// AddRepoRequest AddRepoRequest
type AddRepoRequest struct {
	// 知识库描述
	Description *string `json:"description,omitempty"`
	// 知识库名称
	Name *string `json:"name,omitempty"`
	// 密码
	Password *string `json:"password,omitempty"`
	// 知识库类型 枚举 1 = 文本  2 = sdb
	RepositoryType *int32 `json:"repositoryType,omitempty"`
	// 类型 1= tks内部服务 2=本地直连 图谱相关字段仅类型为sdb有效
	SdbConfigureType *int32 `json:"sdbConfigureType,omitempty"`
	// 表名
	TableName *string `json:"tableName,omitempty"`
	// URL
	Url *string `json:"url,omitempty"`
	// 用户名
	Username *string `json:"username,omitempty"`
}

// NewAddRepoRequest instantiates a new AddRepoRequest object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewAddRepoRequest() *AddRepoRequest {
	this := AddRepoRequest{}
	return &this
}

// NewAddRepoRequestWithDefaults instantiates a new AddRepoRequest object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewAddRepoRequestWithDefaults() *AddRepoRequest {
	this := AddRepoRequest{}
	return &this
}

// GetDescription returns the Description field value if set, zero value otherwise.
func (o *AddRepoRequest) GetDescription() string {
	if o == nil || o.Description == nil {
		var ret string
		return ret
	}
	return *o.Description
}

// GetDescriptionOk returns a tuple with the Description field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AddRepoRequest) GetDescriptionOk() (*string, bool) {
	if o == nil || o.Description == nil {
		return nil, false
	}
	return o.Description, true
}

// HasDescription returns a boolean if a field has been set.
func (o *AddRepoRequest) HasDescription() bool {
	if o != nil && o.Description != nil {
		return true
	}

	return false
}

// SetDescription gets a reference to the given string and assigns it to the Description field.
func (o *AddRepoRequest) SetDescription(v string) {
	o.Description = &v
}

// GetName returns the Name field value if set, zero value otherwise.
func (o *AddRepoRequest) GetName() string {
	if o == nil || o.Name == nil {
		var ret string
		return ret
	}
	return *o.Name
}

// GetNameOk returns a tuple with the Name field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AddRepoRequest) GetNameOk() (*string, bool) {
	if o == nil || o.Name == nil {
		return nil, false
	}
	return o.Name, true
}

// HasName returns a boolean if a field has been set.
func (o *AddRepoRequest) HasName() bool {
	if o != nil && o.Name != nil {
		return true
	}

	return false
}

// SetName gets a reference to the given string and assigns it to the Name field.
func (o *AddRepoRequest) SetName(v string) {
	o.Name = &v
}

// GetPassword returns the Password field value if set, zero value otherwise.
func (o *AddRepoRequest) GetPassword() string {
	if o == nil || o.Password == nil {
		var ret string
		return ret
	}
	return *o.Password
}

// GetPasswordOk returns a tuple with the Password field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AddRepoRequest) GetPasswordOk() (*string, bool) {
	if o == nil || o.Password == nil {
		return nil, false
	}
	return o.Password, true
}

// HasPassword returns a boolean if a field has been set.
func (o *AddRepoRequest) HasPassword() bool {
	if o != nil && o.Password != nil {
		return true
	}

	return false
}

// SetPassword gets a reference to the given string and assigns it to the Password field.
func (o *AddRepoRequest) SetPassword(v string) {
	o.Password = &v
}

// GetRepositoryType returns the RepositoryType field value if set, zero value otherwise.
func (o *AddRepoRequest) GetRepositoryType() int32 {
	if o == nil || o.RepositoryType == nil {
		var ret int32
		return ret
	}
	return *o.RepositoryType
}

// GetRepositoryTypeOk returns a tuple with the RepositoryType field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AddRepoRequest) GetRepositoryTypeOk() (*int32, bool) {
	if o == nil || o.RepositoryType == nil {
		return nil, false
	}
	return o.RepositoryType, true
}

// HasRepositoryType returns a boolean if a field has been set.
func (o *AddRepoRequest) HasRepositoryType() bool {
	if o != nil && o.RepositoryType != nil {
		return true
	}

	return false
}

// SetRepositoryType gets a reference to the given int32 and assigns it to the RepositoryType field.
func (o *AddRepoRequest) SetRepositoryType(v int32) {
	o.RepositoryType = &v
}

// GetSdbConfigureType returns the SdbConfigureType field value if set, zero value otherwise.
func (o *AddRepoRequest) GetSdbConfigureType() int32 {
	if o == nil || o.SdbConfigureType == nil {
		var ret int32
		return ret
	}
	return *o.SdbConfigureType
}

// GetSdbConfigureTypeOk returns a tuple with the SdbConfigureType field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AddRepoRequest) GetSdbConfigureTypeOk() (*int32, bool) {
	if o == nil || o.SdbConfigureType == nil {
		return nil, false
	}
	return o.SdbConfigureType, true
}

// HasSdbConfigureType returns a boolean if a field has been set.
func (o *AddRepoRequest) HasSdbConfigureType() bool {
	if o != nil && o.SdbConfigureType != nil {
		return true
	}

	return false
}

// SetSdbConfigureType gets a reference to the given int32 and assigns it to the SdbConfigureType field.
func (o *AddRepoRequest) SetSdbConfigureType(v int32) {
	o.SdbConfigureType = &v
}

// GetTableName returns the TableName field value if set, zero value otherwise.
func (o *AddRepoRequest) GetTableName() string {
	if o == nil || o.TableName == nil {
		var ret string
		return ret
	}
	return *o.TableName
}

// GetTableNameOk returns a tuple with the TableName field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AddRepoRequest) GetTableNameOk() (*string, bool) {
	if o == nil || o.TableName == nil {
		return nil, false
	}
	return o.TableName, true
}

// HasTableName returns a boolean if a field has been set.
func (o *AddRepoRequest) HasTableName() bool {
	if o != nil && o.TableName != nil {
		return true
	}

	return false
}

// SetTableName gets a reference to the given string and assigns it to the TableName field.
func (o *AddRepoRequest) SetTableName(v string) {
	o.TableName = &v
}

// GetUrl returns the Url field value if set, zero value otherwise.
func (o *AddRepoRequest) GetUrl() string {
	if o == nil || o.Url == nil {
		var ret string
		return ret
	}
	return *o.Url
}

// GetUrlOk returns a tuple with the Url field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AddRepoRequest) GetUrlOk() (*string, bool) {
	if o == nil || o.Url == nil {
		return nil, false
	}
	return o.Url, true
}

// HasUrl returns a boolean if a field has been set.
func (o *AddRepoRequest) HasUrl() bool {
	if o != nil && o.Url != nil {
		return true
	}

	return false
}

// SetUrl gets a reference to the given string and assigns it to the Url field.
func (o *AddRepoRequest) SetUrl(v string) {
	o.Url = &v
}

// GetUsername returns the Username field value if set, zero value otherwise.
func (o *AddRepoRequest) GetUsername() string {
	if o == nil || o.Username == nil {
		var ret string
		return ret
	}
	return *o.Username
}

// GetUsernameOk returns a tuple with the Username field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AddRepoRequest) GetUsernameOk() (*string, bool) {
	if o == nil || o.Username == nil {
		return nil, false
	}
	return o.Username, true
}

// HasUsername returns a boolean if a field has been set.
func (o *AddRepoRequest) HasUsername() bool {
	if o != nil && o.Username != nil {
		return true
	}

	return false
}

// SetUsername gets a reference to the given string and assigns it to the Username field.
func (o *AddRepoRequest) SetUsername(v string) {
	o.Username = &v
}

func (o AddRepoRequest) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Description != nil {
		toSerialize["description"] = o.Description
	}
	if o.Name != nil {
		toSerialize["name"] = o.Name
	}
	if o.Password != nil {
		toSerialize["password"] = o.Password
	}
	if o.RepositoryType != nil {
		toSerialize["repositoryType"] = o.RepositoryType
	}
	if o.SdbConfigureType != nil {
		toSerialize["sdbConfigureType"] = o.SdbConfigureType
	}
	if o.TableName != nil {
		toSerialize["tableName"] = o.TableName
	}
	if o.Url != nil {
		toSerialize["url"] = o.Url
	}
	if o.Username != nil {
		toSerialize["username"] = o.Username
	}
	return json.Marshal(toSerialize)
}

type NullableAddRepoRequest struct {
	value *AddRepoRequest
	isSet bool
}

func (v NullableAddRepoRequest) Get() *AddRepoRequest {
	return v.value
}

func (v *NullableAddRepoRequest) Set(val *AddRepoRequest) {
	v.value = val
	v.isSet = true
}

func (v NullableAddRepoRequest) IsSet() bool {
	return v.isSet
}

func (v *NullableAddRepoRequest) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableAddRepoRequest(val *AddRepoRequest) *NullableAddRepoRequest {
	return &NullableAddRepoRequest{value: val, isSet: true}
}

func (v NullableAddRepoRequest) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableAddRepoRequest) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
