/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// ImageMatchRequest ImageMatchRequest
type ImageMatchRequest struct {
	ImageData *string `json:"image_data,omitempty"`
	KbIdParams *[]KnowledgeBaseDirParams `json:"kb_id_params,omitempty"`
	ReturnImage *bool `json:"return_image,omitempty"`
	TopK *int32 `json:"top_k,omitempty"`
}

// NewImageMatchRequest instantiates a new ImageMatchRequest object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewImageMatchRequest() *ImageMatchRequest {
	this := ImageMatchRequest{}
	return &this
}

// NewImageMatchRequestWithDefaults instantiates a new ImageMatchRequest object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewImageMatchRequestWithDefaults() *ImageMatchRequest {
	this := ImageMatchRequest{}
	return &this
}

// GetImageData returns the ImageData field value if set, zero value otherwise.
func (o *ImageMatchRequest) GetImageData() string {
	if o == nil || o.ImageData == nil {
		var ret string
		return ret
	}
	return *o.ImageData
}

// GetImageDataOk returns a tuple with the ImageData field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ImageMatchRequest) GetImageDataOk() (*string, bool) {
	if o == nil || o.ImageData == nil {
		return nil, false
	}
	return o.ImageData, true
}

// HasImageData returns a boolean if a field has been set.
func (o *ImageMatchRequest) HasImageData() bool {
	if o != nil && o.ImageData != nil {
		return true
	}

	return false
}

// SetImageData gets a reference to the given string and assigns it to the ImageData field.
func (o *ImageMatchRequest) SetImageData(v string) {
	o.ImageData = &v
}

// GetKbIdParams returns the KbIdParams field value if set, zero value otherwise.
func (o *ImageMatchRequest) GetKbIdParams() []KnowledgeBaseDirParams {
	if o == nil || o.KbIdParams == nil {
		var ret []KnowledgeBaseDirParams
		return ret
	}
	return *o.KbIdParams
}

// GetKbIdParamsOk returns a tuple with the KbIdParams field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ImageMatchRequest) GetKbIdParamsOk() (*[]KnowledgeBaseDirParams, bool) {
	if o == nil || o.KbIdParams == nil {
		return nil, false
	}
	return o.KbIdParams, true
}

// HasKbIdParams returns a boolean if a field has been set.
func (o *ImageMatchRequest) HasKbIdParams() bool {
	if o != nil && o.KbIdParams != nil {
		return true
	}

	return false
}

// SetKbIdParams gets a reference to the given []KnowledgeBaseDirParams and assigns it to the KbIdParams field.
func (o *ImageMatchRequest) SetKbIdParams(v []KnowledgeBaseDirParams) {
	o.KbIdParams = &v
}

// GetReturnImage returns the ReturnImage field value if set, zero value otherwise.
func (o *ImageMatchRequest) GetReturnImage() bool {
	if o == nil || o.ReturnImage == nil {
		var ret bool
		return ret
	}
	return *o.ReturnImage
}

// GetReturnImageOk returns a tuple with the ReturnImage field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ImageMatchRequest) GetReturnImageOk() (*bool, bool) {
	if o == nil || o.ReturnImage == nil {
		return nil, false
	}
	return o.ReturnImage, true
}

// HasReturnImage returns a boolean if a field has been set.
func (o *ImageMatchRequest) HasReturnImage() bool {
	if o != nil && o.ReturnImage != nil {
		return true
	}

	return false
}

// SetReturnImage gets a reference to the given bool and assigns it to the ReturnImage field.
func (o *ImageMatchRequest) SetReturnImage(v bool) {
	o.ReturnImage = &v
}

// GetTopK returns the TopK field value if set, zero value otherwise.
func (o *ImageMatchRequest) GetTopK() int32 {
	if o == nil || o.TopK == nil {
		var ret int32
		return ret
	}
	return *o.TopK
}

// GetTopKOk returns a tuple with the TopK field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ImageMatchRequest) GetTopKOk() (*int32, bool) {
	if o == nil || o.TopK == nil {
		return nil, false
	}
	return o.TopK, true
}

// HasTopK returns a boolean if a field has been set.
func (o *ImageMatchRequest) HasTopK() bool {
	if o != nil && o.TopK != nil {
		return true
	}

	return false
}

// SetTopK gets a reference to the given int32 and assigns it to the TopK field.
func (o *ImageMatchRequest) SetTopK(v int32) {
	o.TopK = &v
}

func (o ImageMatchRequest) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.ImageData != nil {
		toSerialize["image_data"] = o.ImageData
	}
	if o.KbIdParams != nil {
		toSerialize["kb_id_params"] = o.KbIdParams
	}
	if o.ReturnImage != nil {
		toSerialize["return_image"] = o.ReturnImage
	}
	if o.TopK != nil {
		toSerialize["top_k"] = o.TopK
	}
	return json.Marshal(toSerialize)
}

type NullableImageMatchRequest struct {
	value *ImageMatchRequest
	isSet bool
}

func (v NullableImageMatchRequest) Get() *ImageMatchRequest {
	return v.value
}

func (v *NullableImageMatchRequest) Set(val *ImageMatchRequest) {
	v.value = val
	v.isSet = true
}

func (v NullableImageMatchRequest) IsSet() bool {
	return v.isSet
}

func (v *NullableImageMatchRequest) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableImageMatchRequest(val *ImageMatchRequest) *NullableImageMatchRequest {
	return &NullableImageMatchRequest{value: val, isSet: true}
}

func (v NullableImageMatchRequest) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableImageMatchRequest) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
