/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// Repository Repository，Repository实体类
type Repository struct {
	// 创建时间
	CreateTime *string `json:"createTime,omitempty"`
	// 创建者
	Creator *string `json:"creator,omitempty"`
	// 关联数据源id,仅repository_type=2才有效
	DatasourceId *int64 `json:"datasourceId,omitempty"`
	// 描述
	Description *string `json:"description,omitempty"`
	// 文档树ID
	DocumentTreeId *int64 `json:"documentTreeId,omitempty"`
	Id *int64 `json:"id,omitempty"`
	// 名称
	Name *string `json:"name,omitempty"`
	// 仓库所属人类型  1 =公共 2= 私有
	OwnerType *int32 `json:"ownerType,omitempty"`
	// 知识库存储类型 枚举 1 = 网络存储系统  2 = 本地文件系统
	RepositoryStorageType *int32 `json:"repositoryStorageType,omitempty"`
	// 知识库类型 枚举 1 = 文本  2 = sdb
	RepositoryType *int32 `json:"repositoryType,omitempty"`
	// 更新时间
	UpdateTime *string `json:"updateTime,omitempty"`
	// 用户是否有权限执行上传
	UploadEnable *bool `json:"uploadEnable,omitempty"`
	// uuid
	Uuid *string `json:"uuid,omitempty"`
}

// NewRepository instantiates a new Repository object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewRepository() *Repository {
	this := Repository{}
	return &this
}

// NewRepositoryWithDefaults instantiates a new Repository object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewRepositoryWithDefaults() *Repository {
	this := Repository{}
	return &this
}

// GetCreateTime returns the CreateTime field value if set, zero value otherwise.
func (o *Repository) GetCreateTime() string {
	if o == nil || o.CreateTime == nil {
		var ret string
		return ret
	}
	return *o.CreateTime
}

// GetCreateTimeOk returns a tuple with the CreateTime field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Repository) GetCreateTimeOk() (*string, bool) {
	if o == nil || o.CreateTime == nil {
		return nil, false
	}
	return o.CreateTime, true
}

// HasCreateTime returns a boolean if a field has been set.
func (o *Repository) HasCreateTime() bool {
	if o != nil && o.CreateTime != nil {
		return true
	}

	return false
}

// SetCreateTime gets a reference to the given string and assigns it to the CreateTime field.
func (o *Repository) SetCreateTime(v string) {
	o.CreateTime = &v
}

// GetCreator returns the Creator field value if set, zero value otherwise.
func (o *Repository) GetCreator() string {
	if o == nil || o.Creator == nil {
		var ret string
		return ret
	}
	return *o.Creator
}

// GetCreatorOk returns a tuple with the Creator field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Repository) GetCreatorOk() (*string, bool) {
	if o == nil || o.Creator == nil {
		return nil, false
	}
	return o.Creator, true
}

// HasCreator returns a boolean if a field has been set.
func (o *Repository) HasCreator() bool {
	if o != nil && o.Creator != nil {
		return true
	}

	return false
}

// SetCreator gets a reference to the given string and assigns it to the Creator field.
func (o *Repository) SetCreator(v string) {
	o.Creator = &v
}

// GetDatasourceId returns the DatasourceId field value if set, zero value otherwise.
func (o *Repository) GetDatasourceId() int64 {
	if o == nil || o.DatasourceId == nil {
		var ret int64
		return ret
	}
	return *o.DatasourceId
}

// GetDatasourceIdOk returns a tuple with the DatasourceId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Repository) GetDatasourceIdOk() (*int64, bool) {
	if o == nil || o.DatasourceId == nil {
		return nil, false
	}
	return o.DatasourceId, true
}

// HasDatasourceId returns a boolean if a field has been set.
func (o *Repository) HasDatasourceId() bool {
	if o != nil && o.DatasourceId != nil {
		return true
	}

	return false
}

// SetDatasourceId gets a reference to the given int64 and assigns it to the DatasourceId field.
func (o *Repository) SetDatasourceId(v int64) {
	o.DatasourceId = &v
}

// GetDescription returns the Description field value if set, zero value otherwise.
func (o *Repository) GetDescription() string {
	if o == nil || o.Description == nil {
		var ret string
		return ret
	}
	return *o.Description
}

// GetDescriptionOk returns a tuple with the Description field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Repository) GetDescriptionOk() (*string, bool) {
	if o == nil || o.Description == nil {
		return nil, false
	}
	return o.Description, true
}

// HasDescription returns a boolean if a field has been set.
func (o *Repository) HasDescription() bool {
	if o != nil && o.Description != nil {
		return true
	}

	return false
}

// SetDescription gets a reference to the given string and assigns it to the Description field.
func (o *Repository) SetDescription(v string) {
	o.Description = &v
}

// GetDocumentTreeId returns the DocumentTreeId field value if set, zero value otherwise.
func (o *Repository) GetDocumentTreeId() int64 {
	if o == nil || o.DocumentTreeId == nil {
		var ret int64
		return ret
	}
	return *o.DocumentTreeId
}

// GetDocumentTreeIdOk returns a tuple with the DocumentTreeId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Repository) GetDocumentTreeIdOk() (*int64, bool) {
	if o == nil || o.DocumentTreeId == nil {
		return nil, false
	}
	return o.DocumentTreeId, true
}

// HasDocumentTreeId returns a boolean if a field has been set.
func (o *Repository) HasDocumentTreeId() bool {
	if o != nil && o.DocumentTreeId != nil {
		return true
	}

	return false
}

// SetDocumentTreeId gets a reference to the given int64 and assigns it to the DocumentTreeId field.
func (o *Repository) SetDocumentTreeId(v int64) {
	o.DocumentTreeId = &v
}

// GetId returns the Id field value if set, zero value otherwise.
func (o *Repository) GetId() int64 {
	if o == nil || o.Id == nil {
		var ret int64
		return ret
	}
	return *o.Id
}

// GetIdOk returns a tuple with the Id field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Repository) GetIdOk() (*int64, bool) {
	if o == nil || o.Id == nil {
		return nil, false
	}
	return o.Id, true
}

// HasId returns a boolean if a field has been set.
func (o *Repository) HasId() bool {
	if o != nil && o.Id != nil {
		return true
	}

	return false
}

// SetId gets a reference to the given int64 and assigns it to the Id field.
func (o *Repository) SetId(v int64) {
	o.Id = &v
}

// GetName returns the Name field value if set, zero value otherwise.
func (o *Repository) GetName() string {
	if o == nil || o.Name == nil {
		var ret string
		return ret
	}
	return *o.Name
}

// GetNameOk returns a tuple with the Name field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Repository) GetNameOk() (*string, bool) {
	if o == nil || o.Name == nil {
		return nil, false
	}
	return o.Name, true
}

// HasName returns a boolean if a field has been set.
func (o *Repository) HasName() bool {
	if o != nil && o.Name != nil {
		return true
	}

	return false
}

// SetName gets a reference to the given string and assigns it to the Name field.
func (o *Repository) SetName(v string) {
	o.Name = &v
}

// GetOwnerType returns the OwnerType field value if set, zero value otherwise.
func (o *Repository) GetOwnerType() int32 {
	if o == nil || o.OwnerType == nil {
		var ret int32
		return ret
	}
	return *o.OwnerType
}

// GetOwnerTypeOk returns a tuple with the OwnerType field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Repository) GetOwnerTypeOk() (*int32, bool) {
	if o == nil || o.OwnerType == nil {
		return nil, false
	}
	return o.OwnerType, true
}

// HasOwnerType returns a boolean if a field has been set.
func (o *Repository) HasOwnerType() bool {
	if o != nil && o.OwnerType != nil {
		return true
	}

	return false
}

// SetOwnerType gets a reference to the given int32 and assigns it to the OwnerType field.
func (o *Repository) SetOwnerType(v int32) {
	o.OwnerType = &v
}

// GetRepositoryStorageType returns the RepositoryStorageType field value if set, zero value otherwise.
func (o *Repository) GetRepositoryStorageType() int32 {
	if o == nil || o.RepositoryStorageType == nil {
		var ret int32
		return ret
	}
	return *o.RepositoryStorageType
}

// GetRepositoryStorageTypeOk returns a tuple with the RepositoryStorageType field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Repository) GetRepositoryStorageTypeOk() (*int32, bool) {
	if o == nil || o.RepositoryStorageType == nil {
		return nil, false
	}
	return o.RepositoryStorageType, true
}

// HasRepositoryStorageType returns a boolean if a field has been set.
func (o *Repository) HasRepositoryStorageType() bool {
	if o != nil && o.RepositoryStorageType != nil {
		return true
	}

	return false
}

// SetRepositoryStorageType gets a reference to the given int32 and assigns it to the RepositoryStorageType field.
func (o *Repository) SetRepositoryStorageType(v int32) {
	o.RepositoryStorageType = &v
}

// GetRepositoryType returns the RepositoryType field value if set, zero value otherwise.
func (o *Repository) GetRepositoryType() int32 {
	if o == nil || o.RepositoryType == nil {
		var ret int32
		return ret
	}
	return *o.RepositoryType
}

// GetRepositoryTypeOk returns a tuple with the RepositoryType field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Repository) GetRepositoryTypeOk() (*int32, bool) {
	if o == nil || o.RepositoryType == nil {
		return nil, false
	}
	return o.RepositoryType, true
}

// HasRepositoryType returns a boolean if a field has been set.
func (o *Repository) HasRepositoryType() bool {
	if o != nil && o.RepositoryType != nil {
		return true
	}

	return false
}

// SetRepositoryType gets a reference to the given int32 and assigns it to the RepositoryType field.
func (o *Repository) SetRepositoryType(v int32) {
	o.RepositoryType = &v
}

// GetUpdateTime returns the UpdateTime field value if set, zero value otherwise.
func (o *Repository) GetUpdateTime() string {
	if o == nil || o.UpdateTime == nil {
		var ret string
		return ret
	}
	return *o.UpdateTime
}

// GetUpdateTimeOk returns a tuple with the UpdateTime field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Repository) GetUpdateTimeOk() (*string, bool) {
	if o == nil || o.UpdateTime == nil {
		return nil, false
	}
	return o.UpdateTime, true
}

// HasUpdateTime returns a boolean if a field has been set.
func (o *Repository) HasUpdateTime() bool {
	if o != nil && o.UpdateTime != nil {
		return true
	}

	return false
}

// SetUpdateTime gets a reference to the given string and assigns it to the UpdateTime field.
func (o *Repository) SetUpdateTime(v string) {
	o.UpdateTime = &v
}

// GetUploadEnable returns the UploadEnable field value if set, zero value otherwise.
func (o *Repository) GetUploadEnable() bool {
	if o == nil || o.UploadEnable == nil {
		var ret bool
		return ret
	}
	return *o.UploadEnable
}

// GetUploadEnableOk returns a tuple with the UploadEnable field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Repository) GetUploadEnableOk() (*bool, bool) {
	if o == nil || o.UploadEnable == nil {
		return nil, false
	}
	return o.UploadEnable, true
}

// HasUploadEnable returns a boolean if a field has been set.
func (o *Repository) HasUploadEnable() bool {
	if o != nil && o.UploadEnable != nil {
		return true
	}

	return false
}

// SetUploadEnable gets a reference to the given bool and assigns it to the UploadEnable field.
func (o *Repository) SetUploadEnable(v bool) {
	o.UploadEnable = &v
}

// GetUuid returns the Uuid field value if set, zero value otherwise.
func (o *Repository) GetUuid() string {
	if o == nil || o.Uuid == nil {
		var ret string
		return ret
	}
	return *o.Uuid
}

// GetUuidOk returns a tuple with the Uuid field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Repository) GetUuidOk() (*string, bool) {
	if o == nil || o.Uuid == nil {
		return nil, false
	}
	return o.Uuid, true
}

// HasUuid returns a boolean if a field has been set.
func (o *Repository) HasUuid() bool {
	if o != nil && o.Uuid != nil {
		return true
	}

	return false
}

// SetUuid gets a reference to the given string and assigns it to the Uuid field.
func (o *Repository) SetUuid(v string) {
	o.Uuid = &v
}

func (o Repository) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.CreateTime != nil {
		toSerialize["createTime"] = o.CreateTime
	}
	if o.Creator != nil {
		toSerialize["creator"] = o.Creator
	}
	if o.DatasourceId != nil {
		toSerialize["datasourceId"] = o.DatasourceId
	}
	if o.Description != nil {
		toSerialize["description"] = o.Description
	}
	if o.DocumentTreeId != nil {
		toSerialize["documentTreeId"] = o.DocumentTreeId
	}
	if o.Id != nil {
		toSerialize["id"] = o.Id
	}
	if o.Name != nil {
		toSerialize["name"] = o.Name
	}
	if o.OwnerType != nil {
		toSerialize["ownerType"] = o.OwnerType
	}
	if o.RepositoryStorageType != nil {
		toSerialize["repositoryStorageType"] = o.RepositoryStorageType
	}
	if o.RepositoryType != nil {
		toSerialize["repositoryType"] = o.RepositoryType
	}
	if o.UpdateTime != nil {
		toSerialize["updateTime"] = o.UpdateTime
	}
	if o.UploadEnable != nil {
		toSerialize["uploadEnable"] = o.UploadEnable
	}
	if o.Uuid != nil {
		toSerialize["uuid"] = o.Uuid
	}
	return json.Marshal(toSerialize)
}

type NullableRepository struct {
	value *Repository
	isSet bool
}

func (v NullableRepository) Get() *Repository {
	return v.value
}

func (v *NullableRepository) Set(val *Repository) {
	v.value = val
	v.isSet = true
}

func (v NullableRepository) IsSet() bool {
	return v.isSet
}

func (v *NullableRepository) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableRepository(val *Repository) *NullableRepository {
	return &NullableRepository{value: val, isSet: true}
}

func (v NullableRepository) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableRepository) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
