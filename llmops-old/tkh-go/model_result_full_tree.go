/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// ResultFullTree Result«FullTree»
type ResultFullTree struct {
	Code *int32 `json:"code,omitempty"`
	Data *FullTree `json:"data,omitempty"`
	Message *string `json:"message,omitempty"`
}

// NewResultFullTree instantiates a new ResultFullTree object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewResultFullTree() *ResultFullTree {
	this := ResultFullTree{}
	return &this
}

// NewResultFullTreeWithDefaults instantiates a new ResultFullTree object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewResultFullTreeWithDefaults() *ResultFullTree {
	this := ResultFullTree{}
	return &this
}

// GetCode returns the Code field value if set, zero value otherwise.
func (o *ResultFullTree) GetCode() int32 {
	if o == nil || o.Code == nil {
		var ret int32
		return ret
	}
	return *o.Code
}

// GetCodeOk returns a tuple with the Code field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ResultFullTree) GetCodeOk() (*int32, bool) {
	if o == nil || o.Code == nil {
		return nil, false
	}
	return o.Code, true
}

// HasCode returns a boolean if a field has been set.
func (o *ResultFullTree) HasCode() bool {
	if o != nil && o.Code != nil {
		return true
	}

	return false
}

// SetCode gets a reference to the given int32 and assigns it to the Code field.
func (o *ResultFullTree) SetCode(v int32) {
	o.Code = &v
}

// GetData returns the Data field value if set, zero value otherwise.
func (o *ResultFullTree) GetData() FullTree {
	if o == nil || o.Data == nil {
		var ret FullTree
		return ret
	}
	return *o.Data
}

// GetDataOk returns a tuple with the Data field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ResultFullTree) GetDataOk() (*FullTree, bool) {
	if o == nil || o.Data == nil {
		return nil, false
	}
	return o.Data, true
}

// HasData returns a boolean if a field has been set.
func (o *ResultFullTree) HasData() bool {
	if o != nil && o.Data != nil {
		return true
	}

	return false
}

// SetData gets a reference to the given FullTree and assigns it to the Data field.
func (o *ResultFullTree) SetData(v FullTree) {
	o.Data = &v
}

// GetMessage returns the Message field value if set, zero value otherwise.
func (o *ResultFullTree) GetMessage() string {
	if o == nil || o.Message == nil {
		var ret string
		return ret
	}
	return *o.Message
}

// GetMessageOk returns a tuple with the Message field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ResultFullTree) GetMessageOk() (*string, bool) {
	if o == nil || o.Message == nil {
		return nil, false
	}
	return o.Message, true
}

// HasMessage returns a boolean if a field has been set.
func (o *ResultFullTree) HasMessage() bool {
	if o != nil && o.Message != nil {
		return true
	}

	return false
}

// SetMessage gets a reference to the given string and assigns it to the Message field.
func (o *ResultFullTree) SetMessage(v string) {
	o.Message = &v
}

func (o ResultFullTree) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Code != nil {
		toSerialize["code"] = o.Code
	}
	if o.Data != nil {
		toSerialize["data"] = o.Data
	}
	if o.Message != nil {
		toSerialize["message"] = o.Message
	}
	return json.Marshal(toSerialize)
}

type NullableResultFullTree struct {
	value *ResultFullTree
	isSet bool
}

func (v NullableResultFullTree) Get() *ResultFullTree {
	return v.value
}

func (v *NullableResultFullTree) Set(val *ResultFullTree) {
	v.value = val
	v.isSet = true
}

func (v NullableResultFullTree) IsSet() bool {
	return v.isSet
}

func (v *NullableResultFullTree) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableResultFullTree(val *ResultFullTree) *NullableResultFullTree {
	return &NullableResultFullTree{value: val, isSet: true}
}

func (v NullableResultFullTree) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableResultFullTree) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
