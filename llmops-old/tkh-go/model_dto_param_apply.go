/*
 * tkh3
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package tkh

import (
	"encoding/json"
)

// DtoParamApply DtoParamApply
type DtoParamApply struct {
	EndTime *string `json:"endTime,omitempty"`
	Purpose *string `json:"purpose,omitempty"`
}

// NewDtoParamApply instantiates a new DtoParamApply object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDtoParamApply() *DtoParamApply {
	this := DtoParamApply{}
	return &this
}

// NewDtoParamApplyWithDefaults instantiates a new DtoParamApply object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDtoParamApplyWithDefaults() *DtoParamApply {
	this := DtoParamApply{}
	return &this
}

// GetEndTime returns the EndTime field value if set, zero value otherwise.
func (o *DtoParamApply) GetEndTime() string {
	if o == nil || o.EndTime == nil {
		var ret string
		return ret
	}
	return *o.EndTime
}

// GetEndTimeOk returns a tuple with the EndTime field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DtoParamApply) GetEndTimeOk() (*string, bool) {
	if o == nil || o.EndTime == nil {
		return nil, false
	}
	return o.EndTime, true
}

// HasEndTime returns a boolean if a field has been set.
func (o *DtoParamApply) HasEndTime() bool {
	if o != nil && o.EndTime != nil {
		return true
	}

	return false
}

// SetEndTime gets a reference to the given string and assigns it to the EndTime field.
func (o *DtoParamApply) SetEndTime(v string) {
	o.EndTime = &v
}

// GetPurpose returns the Purpose field value if set, zero value otherwise.
func (o *DtoParamApply) GetPurpose() string {
	if o == nil || o.Purpose == nil {
		var ret string
		return ret
	}
	return *o.Purpose
}

// GetPurposeOk returns a tuple with the Purpose field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DtoParamApply) GetPurposeOk() (*string, bool) {
	if o == nil || o.Purpose == nil {
		return nil, false
	}
	return o.Purpose, true
}

// HasPurpose returns a boolean if a field has been set.
func (o *DtoParamApply) HasPurpose() bool {
	if o != nil && o.Purpose != nil {
		return true
	}

	return false
}

// SetPurpose gets a reference to the given string and assigns it to the Purpose field.
func (o *DtoParamApply) SetPurpose(v string) {
	o.Purpose = &v
}

func (o DtoParamApply) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.EndTime != nil {
		toSerialize["endTime"] = o.EndTime
	}
	if o.Purpose != nil {
		toSerialize["purpose"] = o.Purpose
	}
	return json.Marshal(toSerialize)
}

type NullableDtoParamApply struct {
	value *DtoParamApply
	isSet bool
}

func (v NullableDtoParamApply) Get() *DtoParamApply {
	return v.value
}

func (v *NullableDtoParamApply) Set(val *DtoParamApply) {
	v.value = val
	v.isSet = true
}

func (v NullableDtoParamApply) IsSet() bool {
	return v.isSet
}

func (v *NullableDtoParamApply) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDtoParamApply(val *DtoParamApply) *NullableDtoParamApply {
	return &NullableDtoParamApply{value: val, isSet: true}
}

func (v NullableDtoParamApply) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDtoParamApply) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
