package main

import (
	"com/trs/hybase/client"
	"com/trs/hybase/client/params"
	"fmt"
	"log"
)

func getConn() *client.TRSConnection {
	connectParams := params.NewConnectParams()
	trsConn := client.NewTRSConnection("http://172.17.120.207:31437", "admin", "trsadmin", connectParams)
	return trsConn
}

func testDeleteQuery() {
	trsConn := getConn()

	DBName := "vectest"
	query := "id#LIST:aabbcc,bbccaa" // Your delete query
	params := params.NewSearchParams()
	report := client.NewTRSReport(nil)

	// Call the ExecuteDeleteQuery method
	deletedCount, err := trsConn.ExecuteDeleteQuery(DBName, query, params, report)
	if err != nil {
		log.Fatalf("Error executing delete query: %v", err)
	}

	fmt.Printf("Successfully deleted %d records.\n", deletedCount)
}

func testInsert() {
	trsConn := getConn()
	DBName := "vectest"
	column2 := client.NewTRSInputColumn("vec", "")
	column2.SetFloatVector([]float32{0.1, 0.1, 0.2})

	// Create an instance of TRSInputRecord with the columns
	rec := client.NewTRSInputRecord()
	rec.AddColumn(client.NewTRSInputColumn("text", "中航光电科技股份有限公司2023年年度报告全文\n识产权示范企业等多项荣誉，IO矩形、射频、圆形、光纤、重载、大电流连接器及其他等7个产品专业进入国际前10名。\n（二）市场引领快速响应优势\n公司坚持市场导向客户至上，以市场引领、敏捷交付、流程优化、顾客满意为基本原则，从前期技术服务到联合设计，从全程支持到全方位互动，始终为客户提供专业化互连解决方案，以“更精、更快、更周到”的理念贯穿全价值链服务，为客户发展保驾护航。公司建立满足客户需求的快速响应机制，面对客户需求，公司秉持“效率”第一，畅通高效信息沟通渠道和服务保障体系，在国内重点城市设立研发中心及综合保障团队，在国际重点地区和国家设立分支机构通过“全球一体布局、业务融入区域、最优综合性价比”顶层规划，持续推动国际化布局，实现全球范围的高效本地化服务。"))
	rec.AddColumn(column2)
	rec.AddColumn(client.NewTRSInputColumn("id", "aabbcc"))
	rec.AddColumn(client.NewTRSInputColumn("ori_id", "abc"))

	rec2 := client.NewTRSInputRecord()
	rec2.AddColumn(client.NewTRSInputColumn("text", "顶层规划，持续推动国际化布局，实现全球范围的高效本地化服务。\n（三）品牌与产品质量优势\n公司深耕连接器行业50余年，树立了“专业、可靠、高端”的品牌形象，践行“以客户为关注焦点的市场观”和“以顾客满意为决策原则的客户观”，致力于打造互连领域领军企业。公司拥有国家和国防认可实验室及行业先进的试验检测平台，构建了“一次做好、零缺陷”为核心的全价值链质量管理模式，系统规划质量发展，建立适应不同领域的质量管理体系，已通过GB/T19001和GJB9001C标准的管理质量体系认证、GJB546B国军标生产线认证、AS9100D国际航空航天质量管理体系认证、IATF16949汽车行业质量管理体系认证、ISO/TS22163轨道交通业质量管理体系认证、IS013485医疗器械质量管理体系认证等，并选择代表性产品先后通过了UL、CUL、CE、TUV、CB等安规认证，为质量管理体系建设与发展提供全面的制度文件和流程保障。公司不断提升的质量管理能力和产品实物质量水平，为客户带来值得信赖的产品。近年来，公司先后获得河南省省长质量奖、第四届中国质量奖提名奖。\n（四）持续提升的智能制造水平"))
	rec2.AddColumn(column2)
	rec2.AddColumn(client.NewTRSInputColumn("id", "bbccaa"))
	rec2.AddColumn(client.NewTRSInputColumn("ori_id", "bca"))
	// Populate the records slice with TRSInputRecord instances
	var records []client.TRSInputRecord
	records = append(records, *rec)
	records = append(records, *rec2)

	report := client.NewTRSReport(nil)
	params := params.NewOperationParams()
	err := trsConn.ExecuteInsert(DBName, records, params, report, nil)
	if err != nil {
		log.Fatalf("Error executing insert: %v", err)
	}

	fmt.Println("Insert operation completed successfully.")
}

func testCreateDatabase() {
	trsConn := getConn()
	db := client.NewTRSDatabase("vectest", client.TYPE_DATABASE)
	db.SetEngineType("common")
	db.AddColumn(client.NewTRSDatabaseColumn("id", client.TYPE_CHAR))
	db.AddColumn(client.NewTRSDatabaseColumn("ori_id", client.TYPE_CHAR))
	db.AddColumn(client.NewTRSDatabaseColumn("text", client.TYPE_DOCUMENT))
	co := client.NewTRSDatabaseColumn("vec", client.TYPE_VECTOR)
	co.SetProperty("index.vector.dims", "3")
	db.AddColumn(co)
	success, err := trsConn.CreateDatabase(db)
	if err != nil {
		log.Fatalf("Error creating database: %v", err)
	}

	if success {
		fmt.Println("Database created successfully!")
	} else {
		fmt.Println("Failed to create database.")
	}
}

func testCategoryQuery() {
	trsConn := getConn()
	resultSet, err := trsConn.CategoryQuery("demo", "中国", "", "版名", 10, nil)
	if err != nil {
		fmt.Println("Error executing select:", err.Message)
		return
	}

	categoryMap := resultSet.GetCategoryMap("版名")
	for key, value := range categoryMap {
		fmt.Printf("Key: %s, Value: %v\n", key, value) // Adjust value type as needed
	}
}

func testSearch() {
	trsConn := getConn()
	strSources := "vectest"                                 // Replace with actual source string
	strWhere := "id#LIST:aabbcc,bbccaa AND vec:0.1,0.2,0.2" // Replace with actual condition string
	start := int64(0)                                       // Starting index
	recordNum := int64(10)                                  // Number of records to fetch
	searchParams := params.NewSearchParams()
	//searchParams.SetColorColumns("标题;正文")
	searchParams.SetCutSize(100)
	searchParams.SetSortMethod("RELEVANCE")
	searchParams.SetReadColumns("id;ori_id")
	// Call the ExecuteSelect method
	resultSet, err := trsConn.ExecuteSelect(strSources, strWhere, start, recordNum, searchParams)
	if err != nil {
		fmt.Println("Error executing select:", err.Error())
		return
	}

	// Use the resultSet as needed for assertions
	fmt.Println("Result set:", resultSet.GetNumFound())
	for i := 0; i < resultSet.Size(); i++ {
		record, err := resultSet.Get()
		if err != nil {
			fmt.Println("Error getting record:", err.Message)
			return
		}
		uid := record.GetUid()
		fmt.Println("uid:", uid)

		score := record.GetRelevance()
		fmt.Println("score: ", score)

		// Capture both return values from GetString
		content, err := record.GetString("id")
		if err != nil {
			fmt.Println("Error getting title:", err) // Handle the error appropriately
			return                                   // or handle the error as needed
		}
		fmt.Println("content:", content)
		resultSet.MoveNext()
	}
}

func testDeleteDatabase() {
	trsConn := getConn()
	dbName := "vectest"
	recordOnly := false

	success, err := trsConn.DeleteDatabase(dbName, recordOnly)
	if err != nil {
		log.Fatalf("Failed to delete database: %v", err)
	}

	if success {
		fmt.Println("Database deleted successfully.")
	} else {
		fmt.Println("Failed to delete the database.")
	}
}

func testDeleteRecord() {
	trsConn := getConn()

	searchParams := params.NewSearchParams()
	resultSet, err := trsConn.ExecuteSelect("wzj", "", 0, 1, searchParams)
	if err != nil {
		fmt.Println("Error executing select:", err.Error())
		return
	}

	resultSet.MoveFirst()
	record, err := resultSet.Get()
	if err != nil {
		fmt.Println("Error getting record:", err.Error())
		return
	}
	uid := record.GetUid()

	uids := []string{uid}
	report := client.NewTRSReport(nil)
	trsConn.ExecuteDelete("wzj", uids, report)
}

func testUpdateRecord() {
	trsConn := getConn()

	searchParams := params.NewSearchParams()
	resultSet, err := trsConn.ExecuteSelect("wzj", "", 0, 1, searchParams)
	if err != nil {
		fmt.Println("Error executing select:", err.Error())
		return
	}

	resultSet.MoveFirst()
	record, err := resultSet.Get()
	if err != nil {
		fmt.Println("Error getting record:", err.Error())
		return
	}
	uid := record.GetUid()

	rec := client.NewTRSInputRecord()
	rec.SetUid(uid)
	rec.AddColumn(client.NewTRSInputColumn("author", "trs"))
	// Populate the records slice with TRSInputRecord instances
	var records []client.TRSInputRecord
	records = append(records, *rec)

	params := params.NewOperationParams()
	report := client.NewTRSReport(nil)

	trsConn.ExecuteUpdate("wzj", records, params, report)
}

func testGetDatabases() {
	trsConn := getConn()

	dbs, err := trsConn.GetDatabases("wzj,entity", nil)
	if err != nil {
		fmt.Println("Error executing getdatabase:", err.Message)
		return
	}

	if len(dbs) == 0 {
		fmt.Println("db not exists")
		return
	}
	for _, db := range dbs {
		fmt.Println("dbname: ", db.GetName())
	}
}

func main() {
	//testDeleteQuery()
	//testInsert()
	//testCreateDatabase()
	// testCategoryQuery()
	testSearch()
	//testDeleteDatabase()
	// testDeleteRecord()
	// testUpdateRecord()
	//testGetDatabases()
}
