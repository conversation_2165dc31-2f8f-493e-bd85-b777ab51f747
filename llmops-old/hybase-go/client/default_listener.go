package client

type defaultListener struct{}

func (l *defaultListener) Report(tag string, report []string) {}

func (l *defaultListener) OnInsertedRec(recordIndex int64) {}

func (l *defaultListener) OnUpdatedRec(recordIndex int64) {}

func (l *defaultListener) OnDelRec(uuid string, unique string) {}

func (l *defaultListener) OnGenUUID(recordIndex int64, UUID string) {}

func (l *defaultListener) OnUpdateUUID(recordIndex int64, UUID string) {}

func (l *defaultListener) OnDelUUID(recordIndex int64, UUIDs []string) {}

func (l *defaultListener) OnSkip(recordIndex int64, skipID int, reason string) {}

func (l *defaultListener) OnError(recordIndex int64, errorID int, errorMessage string) {}

func (l *defaultListener) OnDelError(uuid string, errorID int, errorMessage string) {}

func (l *defaultListener) OnInsertFinish(totalNum int64) {}

func (l *defaultListener) OnUpdateFinish(totalNum int64) {}

func (l *defaultListener) OnDeleteFinish(totalNum int64) {}

func (l *defaultListener) Clear() {}
