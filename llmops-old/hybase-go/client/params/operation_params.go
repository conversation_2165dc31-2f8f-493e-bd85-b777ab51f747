package params

import (
	"com/trs/hybase/client/common"
)

type OperationParams struct {
	WaitFlush    bool
	WaitSearcher bool
	SoftCommit   bool
	*common.TRSObject
}

func NewOperationParams() *OperationParams {
	return &OperationParams{
		WaitFlush:    true,
		WaitSearcher: true,
		SoftCommit:   false,
		TRSObject:    common.NewTRSObject(),
	}
}

func (op *OperationParams) SetWaitFlush(waitFlush bool) *OperationParams {
	op.WaitFlush = waitFlush
	return op
}

func (op *OperationParams) IsWaitFlush() bool {
	return op.WaitFlush
}

func (op *OperationParams) SetWaitSearcher(waitSearcher bool) *OperationParams {
	op.WaitSearcher = waitSearcher
	return op
}

func (op *OperationParams) IsWaitSearcher() bool {
	return op.WaitSearcher
}

func (op *OperationParams) SetSoftCommit(softCommit bool) *OperationParams {
	op.SoftCommit = softCommit
	return op
}

func (op *OperationParams) IsSoftCommit() bool {
	return op.SoftCommit
}
