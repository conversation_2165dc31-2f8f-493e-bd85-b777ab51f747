package params

import (
	"com/trs/hybase/client/common"
)

// Constants for ConnectParams
const (
	// If connection is not successful within timeout, abandon this connection
	TIMEOUT = "TIMEOUT"
	// Use the default timeout from constants
	defaultTimeout = common.DefaultTimeout

	// Request encryption enable property
	RequestEncryptEnable = "request.encrypt.enable"
)

// ConnectParams represents connection parameters
type ConnectParams struct {
	*common.TRSObject
}

// NewConnectParams creates a new ConnectParams instance
func NewConnectParams() *ConnectParams {
	return &ConnectParams{
		TRSObject: common.NewTRSObject(),
	}
}

// SetTimeout sets the connection timeout
// timeout: in milliseconds, if less than or equal to 0, use system default value
// Returns: ConnectParams object itself
// Note: Timeout setting is not effective for data loading and exporting operations
func (c *ConnectParams) SetTimeout(timeout int64) *ConnectParams {
	if timeout <= 0 {
		c.TRSObject.SetLongProperty(TIMEOUT, defaultTimeout)
	} else {
		c.TRSObject.SetLongProperty(TIMEOUT, timeout)
	}
	return c
}

// GetTimeout gets the connection timeout value
// Returns: connection timeout value, if not set, returns default value
func (c *ConnectParams) GetTimeout() int64 {
	value := c.TRSObject.GetProperty(TIMEOUT)
	if value == "" {
		return defaultTimeout
	}

	// Using GetLongProperty from parent TRSObject
	return c.TRSObject.GetLongProperty(TIMEOUT)
}

// SetProperty sets other connection options
func (c *ConnectParams) SetProperty(key, value string) *ConnectParams {
	c.TRSObject.SetProperty(key, value)
	return c
}
