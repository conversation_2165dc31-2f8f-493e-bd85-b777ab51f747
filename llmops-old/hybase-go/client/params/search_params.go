package params

import (
	"com/trs/hybase/client/common"
	"fmt"
	"strconv"
	"strings"
)

// Constants for property keys
const (
	// Result record sorting method
	sortMethod = "SORTMETHOD"

	// Default search field list
	defaultCol = "DEFAULTCOL"

	// Fields to return
	readColumns = "READCOLUMNS"

	// Fields that need highlighting for hit points
	colorColumns = "COLORCOLUMNS"

	// Truncation length of field values
	cutSize = "CUTSIZE"

	estimateUnit = "EstimateUnit"
	quickSearch  = "QuickSearch"
	timeOut      = "TimeOut"

	defaultSortMethod  = ""
	defaultDefaultCols = ""

	textualSimilarPropertyKey = "search.similar.json"
)

// SearchParams represents search parameters for HyBase queries
type SearchParams struct {
	*common.TRSObject
	tmpfileLocalDir    string
	tmpfileKeepOnClose bool
	similarQueries     []TextualSimilarQuery
}

func NewSearchParams() *SearchParams {
	return &SearchParams{
		TRSObject: common.NewTRSObject(),
	}
}

// TextualSimilarQuery represents a query for textual similarity search
type TextualSimilarQuery struct {
	Column string `json:"column"`
	Text   string `json:"text"`
}

// Method signatures (to be implemented)

// SetSortMethod sets the sorting method for search results
func (s *SearchParams) SetSortMethod(method string) *SearchParams {
	s.SetProperty(sortMethod, method)
	return s
}

// GetSortMethod gets the current sorting method
func (s *SearchParams) GetSortMethod() string {
	ret := s.TRSObject.GetProperty(sortMethod)
	if ret == "" {
		return defaultSortMethod
	}
	return ret
}

// SetDefaultColumn sets the default column for search expressions
func (s *SearchParams) SetDefaultColumn(defaultCol string) *SearchParams {
	s.SetProperty(defaultCol, defaultCol)
	return s
}

// GetDefaultColumn gets the default column
func (s *SearchParams) GetDefaultColumn() string {
	ret := s.TRSObject.GetProperty(defaultCol)
	if ret == "" {
		return defaultDefaultCols
	}
	return ret
}

// SetTimeOut sets the search timeout in seconds
func (s *SearchParams) SetTimeOut(timeout int64) *SearchParams {
	s.SetProperty(timeOut, strconv.FormatInt(timeout, 10))
	return s
}

// GetTimeOut gets the search timeout in seconds
func (s *SearchParams) GetTimeOut() int64 {
	value := s.TRSObject.GetProperty(timeOut)
	if value == "" {
		return 60
	}
	timeout, err := strconv.ParseInt(value, 10, 64)
	if err != nil {
		return 60
	}
	return timeout
}

// SetReadColumns sets the columns to be returned in search results
func (s *SearchParams) SetReadColumns(cols string) *SearchParams {
	s.SetProperty(readColumns, cols)
	return s
}

// GetReadColumns gets the columns to be returned
func (s *SearchParams) GetReadColumns() string {
	return s.TRSObject.GetProperty(readColumns)
}

// SetColorColumns sets the columns to be highlighted in search results
func (s *SearchParams) SetColorColumns(cols string) *SearchParams {
	s.SetProperty(colorColumns, cols)
	return s
}

// GetColorColumns gets the columns to be highlighted
func (s *SearchParams) GetColorColumns() string {
	return s.TRSObject.GetProperty(colorColumns)
}

// SetCutSize sets the truncation length for field values
func (s *SearchParams) SetCutSize(size int64) *SearchParams {
	s.SetProperty(cutSize, strconv.FormatInt(size, 10))
	return s
}

// GetCutSize gets the truncation length for field values
func (s *SearchParams) GetCutSize() int64 {
	value := s.TRSObject.GetProperty(cutSize)
	if value == "" {
		return 0
	}
	size, err := strconv.ParseInt(value, 10, 64)
	if err != nil {
		return 0
	}
	return size
}

// SetQuickSearch sets whether to use quick search
func (s *SearchParams) SetQuickSearch(isQuickSearch bool) *SearchParams {
	s.SetProperty(quickSearch, strconv.FormatBool(isQuickSearch))
	return s
}

// IsQuickSearch checks if quick search is enabled
func (s *SearchParams) IsQuickSearch() bool {
	value := s.TRSObject.GetProperty(quickSearch)
	if value == "" {
		return false
	}
	return value == "true"
}

// SetTempDir sets the temporary file storage path
func (s *SearchParams) SetTempDir(localDir string, keepOnClose bool) *SearchParams {
	s.tmpfileLocalDir = localDir
	s.tmpfileKeepOnClose = keepOnClose
	return s
}

// GetTempLocalDir gets the temporary file storage path
func (s *SearchParams) GetTempLocalDir() string {
	return s.tmpfileLocalDir
}

// GetTmpKeepOnClose gets the temporary file deletion policy
func (s *SearchParams) GetTmpKeepOnClose() bool {
	return s.tmpfileKeepOnClose
}

// SetEstimateUnit sets the number of search records for quick search
func (s *SearchParams) SetEstimateUnit(unit int64) *SearchParams {
	s.SetProperty(estimateUnit, strconv.FormatInt(unit, 10))
	return s
}

// GetEstimateUnit gets the number of search records for quick search
func (s *SearchParams) GetEstimateUnit() int64 {
	value := s.TRSObject.GetProperty(estimateUnit)
	if value == "" {
		return 0
	}
	unit, err := strconv.ParseInt(value, 10, 64)
	if err != nil {
		return 0
	}
	return unit
}

// SetProperty sets a property in the parent TRSObject
func (s *SearchParams) SetProperty(key, value string) *SearchParams {
	s.TRSObject.SetProperty(key, value)
	return s
}

// SetFilterBuilder sets the search filter
func (s *SearchParams) SetFilterBuilder(filter BaseFilterBuilder) *SearchParams {
	// s.SetProperty(filterBuilder, filter.Build())
	return s
}

// GetExtendParams gets the list of extended parameter names
func (s *SearchParams) GetExtendParams() []string {
	ret := make([]string, 0)

	for key := range s.TRSObject.Properties { // Assuming Properties() returns the properties map
		if strings.Contains(key, ".") {
			ret = append(ret, key)
		}
	}

	return ret
}

// AndTextualSimilarQuery adds a textual similarity query
func (s *SearchParams) AndTextualSimilarQuery(column, queryText string) *SearchParams {
	s.SetProperty(textualSimilarPropertyKey, fmt.Sprintf("{\"column\":\"%s\",\"text\":\"%s\"}", column, queryText))
	return s
}

// GetTextualSimilarQuery returns the list of textual similarity queries
func (s *SearchParams) GetTextualSimilarQuery() []TextualSimilarQuery {
	return s.similarQueries
}
