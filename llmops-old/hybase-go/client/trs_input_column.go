package client

import (
	"fmt"
	"os"
	"time"
)

type TRSInputColumn struct {
	name        string
	value       string
	boost       float32
	typ         int
	floatVector []float32
	bytesVector []byte
}

func NewTRSInputColumn(name, value string) *TRSInputColumn {
	return &TRSInputColumn{name: name, value: value}
}

func (c *TRSInputColumn) GetName() string {
	return c.name
}

func (c *TRSInputColumn) SetName(name string) {
	c.name = name
}

func (c *TRSInputColumn) SetValue(val string) {
	c.value = val
}

func (c *TRSInputColumn) GetBoost() float32 {
	return c.boost
}

func (c *TRSInputColumn) SetBoost(boost float32) {
	c.boost = boost
}

func (c *TRSInputColumn) setType(typ int) {
	c.typ = typ
}

func (c *TRSInputColumn) GetColumnType() int {
	return c.typ
}

func (c *TRSInputColumn) GetValue() string {
	return c.value
}

func (c *TRSInputColumn) SetIntValue(iValue int) {
	c.setType(1) // Assuming 1 is the type for int
	c.SetValue(fmt.Sprintf("%d", iValue))
}

func (c *TRSInputColumn) SetLongValue(lValue int64) {
	c.setType(2) // Assuming 2 is the type for long
	c.SetValue(fmt.Sprintf("%d", lValue))
}

func (c *TRSInputColumn) SetFloatValue(fValue float32) {
	c.setType(3) // Assuming 3 is the type for float
	c.SetValue(fmt.Sprintf("%f", fValue))
}

func (c *TRSInputColumn) SetDoubleValue(dValue float64) {
	c.setType(4) // Assuming 4 is the type for double
	c.SetValue(fmt.Sprintf("%f", dValue))
}

func (c *TRSInputColumn) SetDateValue(dValue time.Time) {
	c.setType(5) // Assuming 5 is the type for date
	c.SetValue(dValue.Format("2006-01-02 15:04:05"))
}

func (c *TRSInputColumn) SetFileValue(fPath string) {
	c.setType(6) // Assuming 6 is the type for file
	c.SetValue(fPath)
}

func (c *TRSInputColumn) SetFileValueSingle(fvalue *os.File) {
	c.setType(6) // Assuming 6 is the type for file
	c.SetValue(fvalue.Name())
}

func (c *TRSInputColumn) SetFileValueMultiple(fvalues []*os.File) {
	c.value = ""
	for _, f := range fvalues {
		c.addValue(f.Name())
	}
	c.SetFileValue(c.value)
}

func (c *TRSInputColumn) SetDocumentValue(fPath string) {
	c.setType(7) // Assuming 7 is the type for document
	c.SetValue(fPath)
}

func (c *TRSInputColumn) SetDocumentValueSingle(fvalue *os.File) {
	c.setType(7) // Assuming 7 is the type for document
	c.SetValue(fvalue.Name())
}

func (c *TRSInputColumn) SetDocumentValueMultiple(fvalues []*os.File) {
	c.value = ""
	for _, f := range fvalues {
		c.addValue(f.Name())
	}
	c.SetDocumentValue(c.value)
}

func (c *TRSInputColumn) SetValueByType(value interface{}) error {
	switch v := value.(type) {
	case string:
		c.SetValue(v)
	case int:
		c.SetIntValue(v)
	case int64:
		c.SetLongValue(v)
	case float32:
		c.SetFloatValue(v)
	case float64:
		c.SetDoubleValue(v)
	case time.Time:
		c.SetDateValue(v)
	case *os.File:
		c.SetFileValueSingle(v)
	case []*os.File:
		c.SetFileValueMultiple(v)
	default:
		return fmt.Errorf("invalid field value type")
	}
	return nil
}

func (c *TRSInputColumn) addValue(val string) {
	if c.value == "" {
		c.SetValue(val)
	} else {
		c.value += ";" + val
	}
}

func (c *TRSInputColumn) SetFloatVector(vector []float32) {
	c.floatVector = vector
}

func (c *TRSInputColumn) GetFloatVector() []float32 {
	return c.floatVector
}

func (c *TRSInputColumn) AddFloatVector(vector float32) {
	c.floatVector = append(c.floatVector, vector)
}

func (c *TRSInputColumn) SetByteVector(vector []byte) {
	c.bytesVector = vector
}

func (c *TRSInputColumn) GetByteVector() []byte {
	return c.bytesVector
}
