package client

import (
	"com/trs/hybase/client/common"
	"fmt"
	"strconv"
	"strings"
)

type ResultSetParser struct{}

func (p *ResultSetParser) ProcessSearchResponse(searchResponse *SearchRPC_SearchRecordResponse, conn *TRSConnection) (*TRSResultSet, error) {
	resultSet := NewTRSResultSet(conn)
	if searchResponse.GetStatusCode() != 0 {
		return nil, nil
	}

	resultSet.searchID = searchResponse.GetSearchid()
	resultSet.SetNumFound(int64(searchResponse.GetNumFound()))
	resultList := searchResponse.GetRecord()
	if resultList != nil {
		for _, re := range resultList {
			record := NewTRSRecord(conn)
			record.SetUid(re.GetUid())
			record.SetRelevance(re.GetRelevance())
			record.SetDbName(re.GetDbName())
			record.SetTimestamp(re.GetTimestamp())
			record.SetCreateTimestamp(re.GetCreatetimestamp())
			for _, col := range re.GetColumns() {
				record.AddValue(col.GetName(), col.GetStrValue())
			}
			resultSet.Add(*record)
		}
	}

	if searchResponse.GetSearchProgress() != nil {
		mergeNum := 0
		totalNum := 1
		for _, progressInfo := range searchResponse.GetSearchProgress() {
			if progressInfo.GetKey() == common.MergeNum {
				mergeNum, _ = strconv.Atoi(progressInfo.GetValue())
				continue
			}
			if progressInfo.GetKey() == common.TotalNum {
				totalNum, _ = strconv.Atoi(progressInfo.GetValue())
				continue
			}
			resultSet.dbHitsNum[progressInfo.GetKey()] = parseInt(progressInfo.GetValue())
		}
		resultSet.progress = fmt.Sprintf("%.0f%%", float64(mergeNum)/float64(totalNum)*100)
	}

	if searchResponse.GetDbHitsMap() != nil {
		for _, pty := range searchResponse.GetDbHitsMap() {
			dbName := pty.GetKey()
			hit := parseInt(pty.GetValue())
			if idx := strings.Index(dbName, "#"); idx > 0 {
				view := dbName[:idx]
				num, exists := resultSet.dbHitsNum[view]
				if !exists {
					resultSet.dbHitsNum[view] = hit
				} else {
					resultSet.dbHitsNum[view] = hit + num
				}
			}
			resultSet.dbHitsNum[pty.GetKey()] = hit
		}
	}

	return resultSet, nil
}

func (p *ResultSetParser) ProcessCategoryResponse(categoryResponse *SearchRPC_CategoryQueryResponse) (*TRSResultSet, error) {
	if categoryResponse.GetStatusCode() != 0 {
		return nil, fmt.Errorf("unexpected status: %d", categoryResponse.GetStatusCode())
	}

	categoryMap := make(map[string]map[string]int64)
	for _, re := range categoryResponse.GetResult() {
		if re.GetProperty() == "" {
			defaultProperty := "#0"
			re.Property = &defaultProperty
		}
		if _, exists := categoryMap[re.GetProperty()]; exists {
			categoryMap[re.GetProperty()][re.GetKey()] = re.GetValue()
		} else {
			resMap := make(map[string]int64)
			resMap[re.GetKey()] = re.GetValue()
			categoryMap[re.GetProperty()] = resMap
		}
	}

	totalHits := int64(0)
	if totalMap, exists := categoryMap["$total"]; exists {
		totalHits = totalMap["$total"]
		delete(categoryMap, "$total")
	} else {
		for _, next := range categoryMap {
			if val, exists := next["$total"]; exists {
				totalHits = val
				delete(next, "$total")
			}
		}
	}
	totalHits = totalHits - 1

	result := NewTRSResultSetWithCategories(categoryMap, nil, nil, totalHits)
	return result, nil
}

func (p *ResultSetParser) ProcessFacetResponse(facetResponse *SearchRPC_CategoryQueryResponse) (*TRSResultSet, error) {
	if facetResponse.GetStatusCode() != 0 {
		return nil, fmt.Errorf("unexpected status: %d", facetResponse.GetStatusCode())
	}

	facetMap := make(map[string]map[string]float64)
	for _, re := range facetResponse.GetResult() {
		if re.GetProperty() == "" {
			defaultProperty := "#0"
			re.Property = &defaultProperty
		}
		property := re.GetProperty()
		if _, exists := facetMap[property]; exists {
			facetMap[property][re.GetKey()] = re.GetDoubleValue()
		} else {
			resMap := make(map[string]float64)
			resMap[re.GetKey()] = re.GetDoubleValue()
			facetMap[property] = resMap
		}
	}

	totalHits := facetMap["$total"]["$total"]
	totalHits = totalHits - 1

	result := NewTRSResultSetWithFacets(nil, nil, facetMap, int64(totalHits))
	return result, nil
}

func (p *ResultSetParser) ProcessExpressionResponse(expressionResponse *SearchRPC_ExpressionQueryResponse) (*TRSResultSet, error) {
	if expressionResponse.GetStatusCode() != 0 {
		return nil, fmt.Errorf("unexpected status: %d", expressionResponse.GetStatusCode())
	}

	expressionMap := make(map[string]*ResultField)
	for _, re := range expressionResponse.GetResult() {
		expressionMap[re.GetName()] = re
	}
	numFound := expressionMap["$total"].GetNumValue()
	delete(expressionMap, "$total")

	result := NewTRSResultSetWithExpressions(nil, expressionMap, nil, numFound)
	return result, nil
}

// func (p *ResultSetParser) ParserExportRecordResponse(result *ExportRecordResponse, conn *TRSConnection, exportVersion int, exportAsync bool) (*TRSResultNoSortSet, error) {
// 	exportID := result.GetExportID()
// 	resultSet := NewTRSResultNoSortSet(conn)
// 	if result.GetStatus() == "" {
// 		return nil, fmt.Errorf("export dbStatus is empty!")
// 	}
// 	exportStatus := ExportStatus{}
// 	if err := exportStatus.Unmarshal(result.GetStatus()); err != nil {
// 		return nil, err
// 	}
// 	resultSet.searchID = exportID
// 	numFound := int64(0)
// 	for _, subDBInfo := range exportStatus.GetSubDBList() {
// 		hitsNum := subDBInfo.GetSize()
// 		dbName := strings.Split(subDBInfo.GetSubID(), "@")[1]
// 		view := ""
// 		if idx := strings.Index(dbName, "#"); idx > 0 {
// 			view = dbName[:idx]
// 		}
// 		num, exists := resultSet.dbHitsNum[dbName]
// 		if !exists {
// 			resultSet.dbHitsNum[dbName] = hitsNum
// 		} else {
// 			resultSet.dbHitsNum[dbName] = num + hitsNum
// 		}

// 		if view != "" {
// 			num, exists = resultSet.dbHitsNum[view]
// 			if !exists {
// 				resultSet.dbHitsNum[view] = hitsNum
// 			} else {
// 				resultSet.dbHitsNum[view] = num + hitsNum
// 			}
// 		}
// 		numFound += hitsNum
// 	}
// 	resultSet.SetNumFound(numFound)
// 	resultSet.SetSearchRequest(exportStatus.GetSearchRequest())
// 	resultSet.SetExportVersion(exportVersion)
// 	resultSet.SetAsync(exportAsync)
// 	resultSet.InitExport()

// 	return resultSet, nil
// }

func parseInt(value string) int64 {
	result, _ := strconv.ParseInt(value, 10, 64)
	return result
}
