package client

import (
	"bytes"
	"fmt"
	"io"
	"os"
	"sync/atomic"
)

// State defines the possible states of the data stream.
type State int

const (
	HEADER_LEN State = iota
	HEADER_DATA
	FILE_DATA
	FILE_CRC
	FILE_DONE
)

// RequestMultiFileStream handles reading from multiple files.
type RequestMultiFileStream struct {
	data      []byte
	files     []*InputFile
	nextIdx   int
	input     io.ReadCloser
	lastError error
	skipError bool
}

// NewRequestMultiFileStream creates a new RequestMultiFileStream.
func NewRequestMultiFileStream(data []byte, files []*InputFile, skipError bool) *RequestMultiFileStream {
	return &RequestMultiFileStream{
		data:      data,
		input:     io.NopCloser(bytes.NewReader(data)),
		files:     files,
		skipError: skipError,
	}
}

// Read reads data from the stream.
func (r *RequestMultiFileStream) Read(p []byte) (n int, err error) {
	if r.lastError != nil {
		return 0, r.lastError
	}
	if r.input == nil {
		return 0, io.EOF
	}

	n, err = r.input.Read(p)
	if n == 0 && err == io.EOF {
		r.input.Close()
		r.input = nil
		for r.nextIdx < len(r.files) {
			if *r.files[r.nextIdx].IsDir {
				r.nextIdx++
				continue
			}
			file, err := r.getFile(r.files[r.nextIdx])
			if err != nil {
				r.input = io.NopCloser(NewErrorFileInputStream(*r.files[r.nextIdx].Name))
			} else {
				r.input = io.NopCloser(NewFileWithCRCInputStream(file))
			}
			n, err = r.input.Read(p)
			if n > 0 || err != io.EOF {
				break
			}
		}
	}
	return n, err
}

// getFile retrieves the file based on the InputFile struct.
func (r *RequestMultiFileStream) getFile(file *InputFile) (*os.File, error) {
	f, err := os.Open(file.GetUri())
	if err != nil {
		return nil, err
	}

	fileInfo, err := f.Stat()
	if err != nil {
		return nil, err
	}
	if !fileInfo.Mode().IsRegular() || fileInfo.Size() != int64(file.GetFileLength()) {
		if r.skipError {
			return nil, nil
		}
		r.lastError = fmt.Errorf("File[%s] not found or unreadable", file.GetUri())
		return nil, r.lastError
	}
	return f, nil
}

// Close closes the stream.
func (r *RequestMultiFileStream) Close() error {
	if r.input != nil {
		return r.input.Close()
	}
	return nil
}

// ErrorFileInputStream simulates an error file input stream.
type ErrorFileInputStream struct {
	state  atomic.Value
	f      string
	header []byte
	input  io.Reader
}

// NewErrorFileInputStream creates a new ErrorFileInputStream.
func NewErrorFileInputStream(fileName string) *ErrorFileInputStream {
	header := []byte(fileName) // Replace with actual header logic
	input := bytes.NewReader(header)
	efs := &ErrorFileInputStream{
		f:     fileName,
		input: input,
	}
	efs.state.Store(HEADER_LEN)
	return efs
}

// Read reads data from the error file input stream.
func (e *ErrorFileInputStream) Read(p []byte) (n int, err error) {
	n, err = e.input.Read(p)
	if n == 0 && err == io.EOF {
		return 0, io.EOF
	}
	return n, err
}

// FileWithCRCInputStream reads a file and calculates its CRC32.
type FileWithCRCInputStream struct {
	state atomic.Value
	file  *os.File
	crc   *CRC32InputStream
}

// NewFileWithCRCInputStream creates a new FileWithCRCInputStream.
func NewFileWithCRCInputStream(file *os.File) *FileWithCRCInputStream {
	fwcs := &FileWithCRCInputStream{
		file: file,
		crc:  NewCRC32InputStream(io.NopCloser(file)),
	}
	fwcs.state.Store(HEADER_LEN)
	return fwcs
}

// Read reads data from the file and updates the CRC32.
func (f *FileWithCRCInputStream) Read(p []byte) (n int, err error) {
	n, err = f.crc.Read(p)
	if n == 0 && err == io.EOF {
		return 0, io.EOF
	}
	return n, err
}

// Close closes the file input stream.
func (f *FileWithCRCInputStream) Close() error {
	if f.file != nil {
		return f.file.Close()
	}
	return nil
}

// toBytes converts a long value to a byte array using big-endian.
func toBytes(val int64) []byte {
	b := make([]byte, 8)
	for i := 7; i >= 0; i-- {
		b[i] = byte(val)
		val >>= 8
	}
	return b
}
