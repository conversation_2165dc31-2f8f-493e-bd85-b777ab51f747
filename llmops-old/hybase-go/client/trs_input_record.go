package client

type TRSInputRecord struct {
	uid     string
	columns map[string]*TRSInputColumn
	boost   float32
}

func NewTRSInputRecord() *TRSInputRecord {
	return &TRSInputRecord{
		columns: make(map[string]*TRSInputColumn),
	}
}

func (r *TRSInputRecord) SetUid(uid string) {
	r.uid = uid
}

func (r *TRSInputRecord) GetUid() string {
	return r.uid
}

func (r *TRSInputRecord) GetBoost() float32 {
	return r.boost
}

func (r *TRSInputRecord) SetBoost(boost float32) {
	r.boost = boost
}

func (r *TRSInputRecord) AddColumn(col *TRSInputColumn) {
	r.columns[col.GetName()] = col
}

func (r *TRSInputRecord) AddColumnWithValue(name string, value interface{}) error {
	return r.AddColumnWithValueAndBoost(name, value, 1.0)
}

func (r *TRSInputRecord) AddColumnWithValueAndBoost(name string, value interface{}, boost float32) error {
	column := NewTRSInputColumn(name, "")
	if err := column.SetValueByType(value); err != nil {
		return err
	}
	column.SetBoost(boost)
	r.AddColumn(column)
	return nil
}

func (r *TRSInputRecord) GetColumn(name string) *TRSInputColumn {
	return r.columns[name]
}

func (r *TRSInputRecord) GetColumnValue(name string) string {
	col := r.GetColumn(name)
	if col != nil {
		return col.GetValue()
	}
	return ""
}

func (r *TRSInputRecord) GetColumns() []*TRSInputColumn {
	cols := make([]*TRSInputColumn, 0, len(r.columns))
	for _, col := range r.columns {
		cols = append(cols, col)
	}
	return cols
}

func (r *TRSInputRecord) GetColumnCount() int {
	return len(r.columns)
}

func (r *TRSInputRecord) GetAllColumnsName() []string {
	names := make([]string, 0, len(r.columns))
	for name := range r.columns {
		names = append(names, name)
	}
	return names
}

func (r *TRSInputRecord) RemoveColumn(name string) *TRSInputColumn {
	col := r.columns[name]
	delete(r.columns, name)
	return col
}

func (r *TRSInputRecord) Clear() {
	r.columns = make(map[string]*TRSInputColumn)
}
