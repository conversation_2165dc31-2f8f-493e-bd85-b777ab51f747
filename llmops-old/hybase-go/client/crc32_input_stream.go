package client

import (
	"hash"
	"hash/crc32"
	"io"
)

// CRC32InputStream wraps an io.Reader and calculates the CRC32 checksum.
type CRC32InputStream struct {
	crc   hash.Hash32
	input io.Reader
}

// NewCRC32InputStream creates a new CRC32InputStream.
func NewCRC32InputStream(input io.Reader) *CRC32InputStream {
	return &CRC32InputStream{
		crc:   crc32.NewIEEE(), // Create a new CRC32 hasher
		input: input,
	}
}

// Read reads data from the underlying input stream and updates the CRC32 checksum.
func (c *CRC32InputStream) Read(p []byte) (n int, err error) {
	n, err = c.input.Read(p)
	if n > 0 {
		c.crc.Write(p[:n]) // Update the CRC32 checksum with the read bytes
	}
	return n, err
}

// Close closes the underlying input stream.
func (c *CRC32InputStream) Close() error {
	if closer, ok := c.input.(io.Closer); ok {
		return closer.Close()
	}
	return nil
}

// GetCrc returns the current CRC32 checksum.
func (c *CRC32InputStream) GetCrc() uint32 {
	return c.crc.Sum32() // Return the current CRC32 value
}
