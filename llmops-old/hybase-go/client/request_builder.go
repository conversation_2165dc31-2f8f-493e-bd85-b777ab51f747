package client

import (
	"com/trs/hybase/client/common"
	"com/trs/hybase/client/params"
	"errors"
	"fmt"
	"sync/atomic"

	"google.golang.org/protobuf/proto"
)

type RequestBuilder struct{}

func NewRequestBuilder() *RequestBuilder {
	return &RequestBuilder{}
}

func (rb *RequestBuilder) BuildDeleteReqByID(DBName string, uids []string) (*MaintRPC_DeleteRecordRequest, error) {
	return rb.BuildDeleteReqByIDWithOptions(DBName, uids, nil)
}

func (rb *RequestBuilder) BuildDeleteReqByIDWithOptions(DBName string, uids []string, options map[string]string) (*MaintRPC_DeleteRecordRequest, error) {
	if DBName == "" {
		return nil, errors.New("DBName cannot be null")
	}
	builder := MaintRPC_DeleteRecordRequest{}
	builder.Source = &DBName
	if uids != nil {
		builder.Id = uids
	}

	if options != nil {
		for key, value := range options {
			builder.Options = append(builder.Options, buildProperty(key, value))
		}
	}
	return &builder, nil
}

func (rb *RequestBuilder) BuildDeleteReqByQuery(DBName string, query []string, param *params.SearchParams) (*MaintRPC_DeleteRecordRequest, error) {
	builder := MaintRPC_DeleteRecordRequest{}
	builder.Source = &DBName
	if query != nil {
		builder.Query = query
	}

	if param != nil {
		for key, value := range param.Properties {
			builder.Options = append(builder.Options, buildProperty(key, value))
		}
	}
	return &builder, nil
}

func (rb *RequestBuilder) BuildSearchReq(strSources string, strWhere string, start int64, num int64, params *params.SearchParams) (*SearchRPC_SearchRecordRequest, error) {
	builder := SearchRPC_SearchRecordRequest{}
	builder.Source = &strSources
	builder.Start = &start
	builder.Num = &num
	if strWhere != "" {
		builder.Query = &strWhere
	}

	if params != nil {
		if sortMethod := params.GetSortMethod(); sortMethod != "" {
			builder.SortMethod = &sortMethod
		}
		if readColumns := params.GetReadColumns(); readColumns != "" {
			builder.ReadColumns = &readColumns
		}
		if colorColumns := params.GetColorColumns(); colorColumns != "" {
			builder.ColorColumns = &colorColumns
		}
		if quickSearch := params.IsQuickSearch(); quickSearch {
			builder.QuickSearch = &quickSearch
		}
		estimateUnit := params.GetEstimateUnit()
		builder.EstimateUnit = &estimateUnit
		defaultColumn := params.GetDefaultColumn()
		builder.DefaultColumn = &defaultColumn
		cutSize := params.GetCutSize()
		builder.CutSize = &cutSize
		timeout := params.GetTimeOut()
		builder.Timeout = &timeout

		for _, key := range params.GetExtendParams() {
			if val := params.GetProperty(key); val != "" {
				builder.Opts = append(builder.Opts, buildProperty(key, val))
			}
		}
	}
	return &builder, nil
}

func (rb *RequestBuilder) BuildCategoryQueryReq(sources string, query string, defaultColumn string, categoryColumn string, topNum int64, params *params.SearchParams) (*SearchRPC_CategoryQueryRequest, error) {
	builder := SearchRPC_CategoryQueryRequest{}
	builder.Source = &sources
	if query != "" {
		builder.Query = &query
	}
	builder.CategoryColumn = &categoryColumn
	if defaultColumn != "" {
		builder.DefaultColumn = &defaultColumn
	}
	if topNum > 256000 {
		topNum = 256000
	}
	builder.Num = &topNum

	if params != nil {
		for key, value := range params.Properties {
			builder.Opts = append(builder.Opts, buildProperty(key, value))
		}
	}
	return &builder, nil
}

func (rb *RequestBuilder) BuildFacetQueryReq(sources string, query string, defaultColumn string, categoryColumns []TRSFacetColumn, topNum int64, params *params.SearchParams) (*SearchRPC_FacetQueryRequest, error) {
	builder := SearchRPC_FacetQueryRequest{}
	builder.Source = &sources
	if query != "" {
		builder.Query = &query
	}
	if defaultColumn != "" {
		builder.DefaultColumn = &defaultColumn
	}
	if topNum > 256000 {
		topNum = 256000
	}
	builder.Num = &topNum

	for _, col := range categoryColumns {
		facetColumn := SearchRPC_FacetQueryRequest_InputFacetColumn{}
		facetColumn.Name = &col.name
		facetColumn.StatColumn = &col.statColumn
		facetColumn.Function = &col.statFunction
		if col.HasFacetLabels() {
			facetColumn.Facetlabels = &col.facetLabels
		}
		if col.HasMaxSize() {
			maxSize := int32(col.maxSize)
			facetColumn.MaxResultsetSize = &maxSize
		}
		for key, value := range col.GetAllOptions() {
			facetColumn.Opts = append(facetColumn.Opts, buildProperty(key, value))
		}
		builder.FacetColumn = append(builder.FacetColumn, &facetColumn)
	}

	if params != nil {
		for key, value := range params.Properties {
			builder.Opts = append(builder.Opts, buildProperty(key, value))
		}
	}
	return &builder, nil
}

func (rb *RequestBuilder) BuildRecord(input *TRSInputRecord, isNew bool, size *atomic.Int64, params *params.OperationParams) (*InputRecord, error) {
	if input == nil {
		return nil, nil
	}

	builder := &InputRecord{
		Boost: proto.Float32(input.GetBoost()),
	}

	if !isNew {
		uid := input.GetUid()
		if uid != "" {
			builder.Uid = proto.String(uid)
			size.Add(int64(len(uid)))
		} else {
			if !params.GetBoolProperty("unique.column.search", false) {
				return nil, fmt.Errorf("更新记录没有指定uid")
			}
		}
	}

	fieldNum := input.GetColumnCount()
	fields := input.GetColumns()
	// skipFileError := params.GetBoolProperty("skip.file.error", false)
	// skipErrorRec := params.GetBoolProperty("insert.skip.error", false)

	for i := 0; i < fieldNum; i++ {
		f := fields[i]
		valueString := f.GetValue()
		fieldBuilder := &InputRecord_InputColumn{
			Boost: proto.Float32(f.GetBoost()),
			Name:  proto.String(f.GetName()),
		}
		size.Add(int64(len(f.GetName())))

		byteVector := f.GetByteVector()
		floatVector := f.GetFloatVector()

		if byteVector != nil {
			fieldBuilder.BytesValue = byteVector
		} else if floatVector != nil {
			fieldBuilder.VectorValue = floatVector
		} else if valueString == "" {
			// Null value
			fieldBuilder.NullValue = proto.Bool(true)
		} else if f.GetColumnType() == common.TypeFile || f.GetColumnType() == common.TypeDocument {
			// Handle files
			fileList := &FileList{}
			// paths := strings.Split(valueString, TRSConstants_DEFALT_COLUMN_SEPCHAR)

			// for _, path := range paths {
			// 	file := &os.File{}
			// 	if _, err := os.Stat(path); os.IsNotExist(err) || file.IsDir() {
			// 		if !skipFileError {
			// 			if skipErrorRec {
			// 				return nil, nil
			// 			} else {
			// 				return nil, errors.New("File[" + path + "] not exist or it's a directory!")
			// 			}
			// 		}
			// 		continue
			// 	}

			// 	inputFile, err := parseInputFile(file)
			// 	if err == nil && inputFile != nil {
			// 		fileList.Files = append(fileList.Files, inputFile)
			// 	}
			// }

			if len(fileList.Files) > 0 {
				fieldBuilder.Files = fileList
			}

			// if f.GetColumnType() == TRSConstants_DOCUMENT_TYPE && factory != nil {
			// 	txtContent, err := extractFiles(fileList.Files, factory, textExtractFilter)
			// 	if err == nil && txtContent != "" {
			// 		if len(txtContent) > 5000000 {
			// 			txtContent = txtContent[:5000000]
			// 		}
			// 		fieldBuilder.StrValue = txtContent
			// 		size.Add(int64(len(txtContent)))
			// 	}
			// }
		} else {
			// String value
			fieldBuilder.StrValue = proto.String(valueString)
			size.Add(int64(len(valueString)))
		}

		builder.Columns = append(builder.Columns, fieldBuilder)
	}

	return builder, nil
}

func buildProperty(key, value string) *Property {
	return &Property{Key: &key, Value: &value}
}
