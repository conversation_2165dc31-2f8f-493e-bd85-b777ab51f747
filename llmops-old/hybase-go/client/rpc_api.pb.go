// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.3
// 	protoc        v3.17.2
// source: rpc_api.proto

package client

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MaintRPC_AdminAction int32

const (
	MaintRPC_ENABLE   MaintRPC_AdminAction = 1
	MaintRPC_DISABLE  MaintRPC_AdminAction = 2
	MaintRPC_COMMIT   MaintRPC_AdminAction = 3
	MaintRPC_OPTIMIZE MaintRPC_AdminAction = 4
)

// Enum value maps for MaintRPC_AdminAction.
var (
	MaintRPC_AdminAction_name = map[int32]string{
		1: "ENABLE",
		2: "DISABLE",
		3: "COMMIT",
		4: "OPTIMIZE",
	}
	MaintRPC_AdminAction_value = map[string]int32{
		"ENABLE":   1,
		"DISABLE":  2,
		"COMMIT":   3,
		"OPTIMIZE": 4,
	}
)

func (x MaintRPC_AdminAction) Enum() *MaintRPC_AdminAction {
	p := new(MaintRPC_AdminAction)
	*p = x
	return p
}

func (x MaintRPC_AdminAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MaintRPC_AdminAction) Descriptor() protoreflect.EnumDescriptor {
	return file_rpc_api_proto_enumTypes[0].Descriptor()
}

func (MaintRPC_AdminAction) Type() protoreflect.EnumType {
	return &file_rpc_api_proto_enumTypes[0]
}

func (x MaintRPC_AdminAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *MaintRPC_AdminAction) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = MaintRPC_AdminAction(num)
	return nil
}

// Deprecated: Use MaintRPC_AdminAction.Descriptor instead.
func (MaintRPC_AdminAction) EnumDescriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{16, 0}
}

// searchid
type SearchID struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Md5Hash       *string                `protobuf:"bytes,1,req,name=md5hash" json:"md5hash,omitempty"`
	Updatetime    *int64                 `protobuf:"varint,2,opt,name=updatetime" json:"updatetime,omitempty"`
	SubStart      *int32                 `protobuf:"varint,3,opt,name=subStart" json:"subStart,omitempty"`
	SubEnd        *int32                 `protobuf:"varint,4,opt,name=subEnd" json:"subEnd,omitempty"`
	AgentHost     *string                `protobuf:"bytes,5,opt,name=agentHost" json:"agentHost,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchID) Reset() {
	*x = SearchID{}
	mi := &file_rpc_api_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchID) ProtoMessage() {}

func (x *SearchID) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchID.ProtoReflect.Descriptor instead.
func (*SearchID) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{0}
}

func (x *SearchID) GetMd5Hash() string {
	if x != nil && x.Md5Hash != nil {
		return *x.Md5Hash
	}
	return ""
}

func (x *SearchID) GetUpdatetime() int64 {
	if x != nil && x.Updatetime != nil {
		return *x.Updatetime
	}
	return 0
}

func (x *SearchID) GetSubStart() int32 {
	if x != nil && x.SubStart != nil {
		return *x.SubStart
	}
	return 0
}

func (x *SearchID) GetSubEnd() int32 {
	if x != nil && x.SubEnd != nil {
		return *x.SubEnd
	}
	return 0
}

func (x *SearchID) GetAgentHost() string {
	if x != nil && x.AgentHost != nil {
		return *x.AgentHost
	}
	return ""
}

// recordid
type RecordID struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Docid         *int64                 `protobuf:"varint,1,opt,name=docid" json:"docid,omitempty"`
	Uuid          *int64                 `protobuf:"varint,2,req,name=uuid" json:"uuid,omitempty"`
	SubID         *string                `protobuf:"bytes,3,opt,name=subID" json:"subID,omitempty"`
	DbName        *string                `protobuf:"bytes,4,opt,name=dbName" json:"dbName,omitempty"`
	DbHost        *string                `protobuf:"bytes,5,opt,name=dbHost" json:"dbHost,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecordID) Reset() {
	*x = RecordID{}
	mi := &file_rpc_api_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecordID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordID) ProtoMessage() {}

func (x *RecordID) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordID.ProtoReflect.Descriptor instead.
func (*RecordID) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{1}
}

func (x *RecordID) GetDocid() int64 {
	if x != nil && x.Docid != nil {
		return *x.Docid
	}
	return 0
}

func (x *RecordID) GetUuid() int64 {
	if x != nil && x.Uuid != nil {
		return *x.Uuid
	}
	return 0
}

func (x *RecordID) GetSubID() string {
	if x != nil && x.SubID != nil {
		return *x.SubID
	}
	return ""
}

func (x *RecordID) GetDbName() string {
	if x != nil && x.DbName != nil {
		return *x.DbName
	}
	return ""
}

func (x *RecordID) GetDbHost() string {
	if x != nil && x.DbHost != nil {
		return *x.DbHost
	}
	return ""
}

type InputFile struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          *string                `protobuf:"bytes,1,req,name=name" json:"name,omitempty"`
	IsDir         *bool                  `protobuf:"varint,2,req,name=isDir" json:"isDir,omitempty"`
	Suffix        *string                `protobuf:"bytes,3,opt,name=suffix" json:"suffix,omitempty"`
	Content       []byte                 `protobuf:"bytes,4,opt,name=content" json:"content,omitempty"`
	FileLength    *uint64                `protobuf:"varint,5,opt,name=fileLength" json:"fileLength,omitempty"`
	Uri           *string                `protobuf:"bytes,6,opt,name=uri" json:"uri,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InputFile) Reset() {
	*x = InputFile{}
	mi := &file_rpc_api_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InputFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputFile) ProtoMessage() {}

func (x *InputFile) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputFile.ProtoReflect.Descriptor instead.
func (*InputFile) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{2}
}

func (x *InputFile) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *InputFile) GetIsDir() bool {
	if x != nil && x.IsDir != nil {
		return *x.IsDir
	}
	return false
}

func (x *InputFile) GetSuffix() string {
	if x != nil && x.Suffix != nil {
		return *x.Suffix
	}
	return ""
}

func (x *InputFile) GetContent() []byte {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *InputFile) GetFileLength() uint64 {
	if x != nil && x.FileLength != nil {
		return *x.FileLength
	}
	return 0
}

func (x *InputFile) GetUri() string {
	if x != nil && x.Uri != nil {
		return *x.Uri
	}
	return ""
}

type InputFileHeader struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FileName      *string                `protobuf:"bytes,1,req,name=fileName" json:"fileName,omitempty"`
	FLength       *int64                 `protobuf:"varint,2,opt,name=fLength" json:"fLength,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InputFileHeader) Reset() {
	*x = InputFileHeader{}
	mi := &file_rpc_api_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InputFileHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputFileHeader) ProtoMessage() {}

func (x *InputFileHeader) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputFileHeader.ProtoReflect.Descriptor instead.
func (*InputFileHeader) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{3}
}

func (x *InputFileHeader) GetFileName() string {
	if x != nil && x.FileName != nil {
		return *x.FileName
	}
	return ""
}

func (x *InputFileHeader) GetFLength() int64 {
	if x != nil && x.FLength != nil {
		return *x.FLength
	}
	return 0
}

type FileList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Files         []*InputFile           `protobuf:"bytes,1,rep,name=files" json:"files,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FileList) Reset() {
	*x = FileList{}
	mi := &file_rpc_api_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileList) ProtoMessage() {}

func (x *FileList) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileList.ProtoReflect.Descriptor instead.
func (*FileList) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{4}
}

func (x *FileList) GetFiles() []*InputFile {
	if x != nil {
		return x.Files
	}
	return nil
}

// input
type InputRecord struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Uid           *string                    `protobuf:"bytes,1,opt,name=uid" json:"uid,omitempty"`
	Boost         *float32                   `protobuf:"fixed32,2,opt,name=boost,def=1" json:"boost,omitempty"`
	Columns       []*InputRecord_InputColumn `protobuf:"bytes,3,rep,name=columns" json:"columns,omitempty"`
	Tag           *int64                     `protobuf:"varint,4,opt,name=tag" json:"tag,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

// Default values for InputRecord fields.
const (
	Default_InputRecord_Boost = float32(1)
)

func (x *InputRecord) Reset() {
	*x = InputRecord{}
	mi := &file_rpc_api_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InputRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputRecord) ProtoMessage() {}

func (x *InputRecord) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputRecord.ProtoReflect.Descriptor instead.
func (*InputRecord) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{5}
}

func (x *InputRecord) GetUid() string {
	if x != nil && x.Uid != nil {
		return *x.Uid
	}
	return ""
}

func (x *InputRecord) GetBoost() float32 {
	if x != nil && x.Boost != nil {
		return *x.Boost
	}
	return Default_InputRecord_Boost
}

func (x *InputRecord) GetColumns() []*InputRecord_InputColumn {
	if x != nil {
		return x.Columns
	}
	return nil
}

func (x *InputRecord) GetTag() int64 {
	if x != nil && x.Tag != nil {
		return *x.Tag
	}
	return 0
}

// result
type ResultRecord struct {
	state           protoimpl.MessageState       `protogen:"open.v1"`
	Uid             *string                      `protobuf:"bytes,1,req,name=uid" json:"uid,omitempty"`
	Relevance       *float64                     `protobuf:"fixed64,2,opt,name=relevance" json:"relevance,omitempty"`
	Columns         []*ResultRecord_ResultColumn `protobuf:"bytes,3,rep,name=columns" json:"columns,omitempty"`
	DbName          *string                      `protobuf:"bytes,4,opt,name=dbName" json:"dbName,omitempty"`
	Timestamp       *int64                       `protobuf:"varint,5,opt,name=timestamp" json:"timestamp,omitempty"`
	Createtimestamp *int64                       `protobuf:"varint,6,opt,name=createtimestamp" json:"createtimestamp,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ResultRecord) Reset() {
	*x = ResultRecord{}
	mi := &file_rpc_api_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResultRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResultRecord) ProtoMessage() {}

func (x *ResultRecord) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResultRecord.ProtoReflect.Descriptor instead.
func (*ResultRecord) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{6}
}

func (x *ResultRecord) GetUid() string {
	if x != nil && x.Uid != nil {
		return *x.Uid
	}
	return ""
}

func (x *ResultRecord) GetRelevance() float64 {
	if x != nil && x.Relevance != nil {
		return *x.Relevance
	}
	return 0
}

func (x *ResultRecord) GetColumns() []*ResultRecord_ResultColumn {
	if x != nil {
		return x.Columns
	}
	return nil
}

func (x *ResultRecord) GetDbName() string {
	if x != nil && x.DbName != nil {
		return *x.DbName
	}
	return ""
}

func (x *ResultRecord) GetTimestamp() int64 {
	if x != nil && x.Timestamp != nil {
		return *x.Timestamp
	}
	return 0
}

func (x *ResultRecord) GetCreatetimestamp() int64 {
	if x != nil && x.Createtimestamp != nil {
		return *x.Createtimestamp
	}
	return 0
}

// property
type Property struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           *string                `protobuf:"bytes,1,req,name=key" json:"key,omitempty"`
	Value         *string                `protobuf:"bytes,2,req,name=value" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Property) Reset() {
	*x = Property{}
	mi := &file_rpc_api_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Property) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Property) ProtoMessage() {}

func (x *Property) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Property.ProtoReflect.Descriptor instead.
func (*Property) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{7}
}

func (x *Property) GetKey() string {
	if x != nil && x.Key != nil {
		return *x.Key
	}
	return ""
}

func (x *Property) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

// propertyList
type PropertyList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Property      []*Property            `protobuf:"bytes,1,rep,name=property" json:"property,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PropertyList) Reset() {
	*x = PropertyList{}
	mi := &file_rpc_api_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PropertyList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PropertyList) ProtoMessage() {}

func (x *PropertyList) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PropertyList.ProtoReflect.Descriptor instead.
func (*PropertyList) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{8}
}

func (x *PropertyList) GetProperty() []*Property {
	if x != nil {
		return x.Property
	}
	return nil
}

type PropertyTable struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PropList      []*PropertyList        `protobuf:"bytes,1,rep,name=propList" json:"propList,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PropertyTable) Reset() {
	*x = PropertyTable{}
	mi := &file_rpc_api_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PropertyTable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PropertyTable) ProtoMessage() {}

func (x *PropertyTable) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PropertyTable.ProtoReflect.Descriptor instead.
func (*PropertyTable) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{9}
}

func (x *PropertyTable) GetPropList() []*PropertyList {
	if x != nil {
		return x.PropList
	}
	return nil
}

// stringList
type StringList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Str           []string               `protobuf:"bytes,1,rep,name=str" json:"str,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StringList) Reset() {
	*x = StringList{}
	mi := &file_rpc_api_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StringList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringList) ProtoMessage() {}

func (x *StringList) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringList.ProtoReflect.Descriptor instead.
func (*StringList) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{10}
}

func (x *StringList) GetStr() []string {
	if x != nil {
		return x.Str
	}
	return nil
}

type ResultField struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          *string                `protobuf:"bytes,1,req,name=name" json:"name,omitempty"`
	Type          *int32                 `protobuf:"varint,2,req,name=type" json:"type,omitempty"`
	StrValue      *string                `protobuf:"bytes,3,opt,name=strValue" json:"strValue,omitempty"`
	NumValue      *int64                 `protobuf:"varint,4,opt,name=numValue" json:"numValue,omitempty"`
	FloatValue    *float32               `protobuf:"fixed32,5,opt,name=floatValue" json:"floatValue,omitempty"`
	BytesValue    []byte                 `protobuf:"bytes,6,opt,name=bytesValue" json:"bytesValue,omitempty"`
	DoubleValue   *float64               `protobuf:"fixed64,7,opt,name=doubleValue" json:"doubleValue,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResultField) Reset() {
	*x = ResultField{}
	mi := &file_rpc_api_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResultField) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResultField) ProtoMessage() {}

func (x *ResultField) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResultField.ProtoReflect.Descriptor instead.
func (*ResultField) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{11}
}

func (x *ResultField) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *ResultField) GetType() int32 {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return 0
}

func (x *ResultField) GetStrValue() string {
	if x != nil && x.StrValue != nil {
		return *x.StrValue
	}
	return ""
}

func (x *ResultField) GetNumValue() int64 {
	if x != nil && x.NumValue != nil {
		return *x.NumValue
	}
	return 0
}

func (x *ResultField) GetFloatValue() float32 {
	if x != nil && x.FloatValue != nil {
		return *x.FloatValue
	}
	return 0
}

func (x *ResultField) GetBytesValue() []byte {
	if x != nil {
		return x.BytesValue
	}
	return nil
}

func (x *ResultField) GetDoubleValue() float64 {
	if x != nil && x.DoubleValue != nil {
		return *x.DoubleValue
	}
	return 0
}

// int64 property
type NumProperty struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           *string                `protobuf:"bytes,1,req,name=key" json:"key,omitempty"`
	Value         *int64                 `protobuf:"varint,2,req,name=value" json:"value,omitempty"`
	Property      *string                `protobuf:"bytes,3,opt,name=property" json:"property,omitempty"`
	DoubleValue   *float64               `protobuf:"fixed64,4,opt,name=doubleValue" json:"doubleValue,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NumProperty) Reset() {
	*x = NumProperty{}
	mi := &file_rpc_api_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NumProperty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NumProperty) ProtoMessage() {}

func (x *NumProperty) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NumProperty.ProtoReflect.Descriptor instead.
func (*NumProperty) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{12}
}

func (x *NumProperty) GetKey() string {
	if x != nil && x.Key != nil {
		return *x.Key
	}
	return ""
}

func (x *NumProperty) GetValue() int64 {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return 0
}

func (x *NumProperty) GetProperty() string {
	if x != nil && x.Property != nil {
		return *x.Property
	}
	return ""
}

func (x *NumProperty) GetDoubleValue() float64 {
	if x != nil && x.DoubleValue != nil {
		return *x.DoubleValue
	}
	return 0
}

// float FloatProperty
type FloatProperty struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           *string                `protobuf:"bytes,1,req,name=key" json:"key,omitempty"`
	Value         *float32               `protobuf:"fixed32,2,req,name=value" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FloatProperty) Reset() {
	*x = FloatProperty{}
	mi := &file_rpc_api_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FloatProperty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FloatProperty) ProtoMessage() {}

func (x *FloatProperty) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FloatProperty.ProtoReflect.Descriptor instead.
func (*FloatProperty) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{13}
}

func (x *FloatProperty) GetKey() string {
	if x != nil && x.Key != nil {
		return *x.Key
	}
	return ""
}

func (x *FloatProperty) GetValue() float32 {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return 0
}

// bytes BytesProperty
type BytesProperty struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           *string                `protobuf:"bytes,1,req,name=key" json:"key,omitempty"`
	Value         []byte                 `protobuf:"bytes,2,req,name=value" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BytesProperty) Reset() {
	*x = BytesProperty{}
	mi := &file_rpc_api_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BytesProperty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BytesProperty) ProtoMessage() {}

func (x *BytesProperty) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BytesProperty.ProtoReflect.Descriptor instead.
func (*BytesProperty) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{14}
}

func (x *BytesProperty) GetKey() string {
	if x != nil && x.Key != nil {
		return *x.Key
	}
	return ""
}

func (x *BytesProperty) GetValue() []byte {
	if x != nil {
		return x.Value
	}
	return nil
}

type FloatPropertyList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Property      []*FloatProperty       `protobuf:"bytes,1,rep,name=property" json:"property,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FloatPropertyList) Reset() {
	*x = FloatPropertyList{}
	mi := &file_rpc_api_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FloatPropertyList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FloatPropertyList) ProtoMessage() {}

func (x *FloatPropertyList) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FloatPropertyList.ProtoReflect.Descriptor instead.
func (*FloatPropertyList) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{15}
}

func (x *FloatPropertyList) GetProperty() []*FloatProperty {
	if x != nil {
		return x.Property
	}
	return nil
}

type MaintRPC struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MaintRPC) Reset() {
	*x = MaintRPC{}
	mi := &file_rpc_api_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MaintRPC) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintRPC) ProtoMessage() {}

func (x *MaintRPC) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintRPC.ProtoReflect.Descriptor instead.
func (*MaintRPC) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{16}
}

type SearchRPC struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchRPC) Reset() {
	*x = SearchRPC{}
	mi := &file_rpc_api_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchRPC) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRPC) ProtoMessage() {}

func (x *SearchRPC) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRPC.ProtoReflect.Descriptor instead.
func (*SearchRPC) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{17}
}

type SchemaRPC struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SchemaRPC) Reset() {
	*x = SchemaRPC{}
	mi := &file_rpc_api_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SchemaRPC) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SchemaRPC) ProtoMessage() {}

func (x *SchemaRPC) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SchemaRPC.ProtoReflect.Descriptor instead.
func (*SchemaRPC) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{18}
}

// report
type Report struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           *string                `protobuf:"bytes,1,req,name=key" json:"key,omitempty"`
	Value         *string                `protobuf:"bytes,2,req,name=value" json:"value,omitempty"`
	Items         *PropertyList          `protobuf:"bytes,3,opt,name=items" json:"items,omitempty"`
	InsertedNum   *int64                 `protobuf:"varint,4,opt,name=insertedNum" json:"insertedNum,omitempty"`
	UpdatedNum    *int64                 `protobuf:"varint,5,opt,name=updatedNum" json:"updatedNum,omitempty"`
	DeletedNum    *int64                 `protobuf:"varint,6,opt,name=deletedNum" json:"deletedNum,omitempty"`
	InsertedRec   []int64                `protobuf:"varint,7,rep,name=insertedRec" json:"insertedRec,omitempty"`
	UpdatedRec    []int64                `protobuf:"varint,8,rep,name=updatedRec" json:"updatedRec,omitempty"`
	DeletedRec    []*Property            `protobuf:"bytes,9,rep,name=deletedRec" json:"deletedRec,omitempty"`
	GenUUID       []*Property            `protobuf:"bytes,10,rep,name=genUUID" json:"genUUID,omitempty"`
	UpdateUUID    []*Property            `protobuf:"bytes,11,rep,name=updateUUID" json:"updateUUID,omitempty"`
	DelUUID       []*Report_RecordItem   `protobuf:"bytes,12,rep,name=delUUID" json:"delUUID,omitempty"`
	Skip          []*Report_RecordPro    `protobuf:"bytes,13,rep,name=skip" json:"skip,omitempty"`
	Error         []*Report_RecordPro    `protobuf:"bytes,14,rep,name=error" json:"error,omitempty"`
	DelError      []*Report_DelErrorPro  `protobuf:"bytes,15,rep,name=delError" json:"delError,omitempty"`
	Marking       []*Report_RecordItem   `protobuf:"bytes,16,rep,name=marking" json:"marking,omitempty"`
	SubError      []*SubDatabaseError    `protobuf:"bytes,17,rep,name=subError" json:"subError,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Report) Reset() {
	*x = Report{}
	mi := &file_rpc_api_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Report) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Report) ProtoMessage() {}

func (x *Report) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Report.ProtoReflect.Descriptor instead.
func (*Report) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{19}
}

func (x *Report) GetKey() string {
	if x != nil && x.Key != nil {
		return *x.Key
	}
	return ""
}

func (x *Report) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

func (x *Report) GetItems() *PropertyList {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *Report) GetInsertedNum() int64 {
	if x != nil && x.InsertedNum != nil {
		return *x.InsertedNum
	}
	return 0
}

func (x *Report) GetUpdatedNum() int64 {
	if x != nil && x.UpdatedNum != nil {
		return *x.UpdatedNum
	}
	return 0
}

func (x *Report) GetDeletedNum() int64 {
	if x != nil && x.DeletedNum != nil {
		return *x.DeletedNum
	}
	return 0
}

func (x *Report) GetInsertedRec() []int64 {
	if x != nil {
		return x.InsertedRec
	}
	return nil
}

func (x *Report) GetUpdatedRec() []int64 {
	if x != nil {
		return x.UpdatedRec
	}
	return nil
}

func (x *Report) GetDeletedRec() []*Property {
	if x != nil {
		return x.DeletedRec
	}
	return nil
}

func (x *Report) GetGenUUID() []*Property {
	if x != nil {
		return x.GenUUID
	}
	return nil
}

func (x *Report) GetUpdateUUID() []*Property {
	if x != nil {
		return x.UpdateUUID
	}
	return nil
}

func (x *Report) GetDelUUID() []*Report_RecordItem {
	if x != nil {
		return x.DelUUID
	}
	return nil
}

func (x *Report) GetSkip() []*Report_RecordPro {
	if x != nil {
		return x.Skip
	}
	return nil
}

func (x *Report) GetError() []*Report_RecordPro {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *Report) GetDelError() []*Report_DelErrorPro {
	if x != nil {
		return x.DelError
	}
	return nil
}

func (x *Report) GetMarking() []*Report_RecordItem {
	if x != nil {
		return x.Marking
	}
	return nil
}

func (x *Report) GetSubError() []*SubDatabaseError {
	if x != nil {
		return x.SubError
	}
	return nil
}

type SubDatabaseError struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SubDatabaseId *string                `protobuf:"bytes,1,req,name=subDatabaseId" json:"subDatabaseId,omitempty"`
	ErrorResponse *ErrorResponse         `protobuf:"bytes,2,opt,name=errorResponse" json:"errorResponse,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubDatabaseError) Reset() {
	*x = SubDatabaseError{}
	mi := &file_rpc_api_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubDatabaseError) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubDatabaseError) ProtoMessage() {}

func (x *SubDatabaseError) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubDatabaseError.ProtoReflect.Descriptor instead.
func (*SubDatabaseError) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{20}
}

func (x *SubDatabaseError) GetSubDatabaseId() string {
	if x != nil && x.SubDatabaseId != nil {
		return *x.SubDatabaseId
	}
	return ""
}

func (x *SubDatabaseError) GetErrorResponse() *ErrorResponse {
	if x != nil {
		return x.ErrorResponse
	}
	return nil
}

type ErrorResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *int32                 `protobuf:"varint,1,opt,name=code" json:"code,omitempty"`
	Message       *string                `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ErrorResponse) Reset() {
	*x = ErrorResponse{}
	mi := &file_rpc_api_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ErrorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorResponse) ProtoMessage() {}

func (x *ErrorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorResponse.ProtoReflect.Descriptor instead.
func (*ErrorResponse) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{21}
}

func (x *ErrorResponse) GetCode() int32 {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return 0
}

func (x *ErrorResponse) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

type SuggestRPC struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SuggestRPC) Reset() {
	*x = SuggestRPC{}
	mi := &file_rpc_api_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SuggestRPC) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuggestRPC) ProtoMessage() {}

func (x *SuggestRPC) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuggestRPC.ProtoReflect.Descriptor instead.
func (*SuggestRPC) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{22}
}

type FilterSplit struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DbName        *string                `protobuf:"bytes,1,req,name=dbName" json:"dbName,omitempty"`
	Split         *string                `protobuf:"bytes,2,opt,name=split" json:"split,omitempty"`
	SubSplit      *string                `protobuf:"bytes,3,opt,name=subSplit" json:"subSplit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterSplit) Reset() {
	*x = FilterSplit{}
	mi := &file_rpc_api_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterSplit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterSplit) ProtoMessage() {}

func (x *FilterSplit) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterSplit.ProtoReflect.Descriptor instead.
func (*FilterSplit) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{23}
}

func (x *FilterSplit) GetDbName() string {
	if x != nil && x.DbName != nil {
		return *x.DbName
	}
	return ""
}

func (x *FilterSplit) GetSplit() string {
	if x != nil && x.Split != nil {
		return *x.Split
	}
	return ""
}

func (x *FilterSplit) GetSubSplit() string {
	if x != nil && x.SubSplit != nil {
		return *x.SubSplit
	}
	return ""
}

type FilterQuery struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Query         *string                `protobuf:"bytes,1,req,name=query" json:"query,omitempty"`
	DbSplit       []*FilterSplit         `protobuf:"bytes,2,rep,name=dbSplit" json:"dbSplit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterQuery) Reset() {
	*x = FilterQuery{}
	mi := &file_rpc_api_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterQuery) ProtoMessage() {}

func (x *FilterQuery) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterQuery.ProtoReflect.Descriptor instead.
func (*FilterQuery) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{24}
}

func (x *FilterQuery) GetQuery() string {
	if x != nil && x.Query != nil {
		return *x.Query
	}
	return ""
}

func (x *FilterQuery) GetDbSplit() []*FilterSplit {
	if x != nil {
		return x.DbSplit
	}
	return nil
}

type OptimizeQuery1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FilterQuery   []*FilterQuery         `protobuf:"bytes,1,rep,name=filterQuery" json:"filterQuery,omitempty"`
	MustQuery     *string                `protobuf:"bytes,2,opt,name=mustQuery" json:"mustQuery,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OptimizeQuery1) Reset() {
	*x = OptimizeQuery1{}
	mi := &file_rpc_api_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OptimizeQuery1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OptimizeQuery1) ProtoMessage() {}

func (x *OptimizeQuery1) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OptimizeQuery1.ProtoReflect.Descriptor instead.
func (*OptimizeQuery1) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{25}
}

func (x *OptimizeQuery1) GetFilterQuery() []*FilterQuery {
	if x != nil {
		return x.FilterQuery
	}
	return nil
}

func (x *OptimizeQuery1) GetMustQuery() string {
	if x != nil && x.MustQuery != nil {
		return *x.MustQuery
	}
	return ""
}

type PermissionRPC struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PermissionRPC) Reset() {
	*x = PermissionRPC{}
	mi := &file_rpc_api_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PermissionRPC) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PermissionRPC) ProtoMessage() {}

func (x *PermissionRPC) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PermissionRPC.ProtoReflect.Descriptor instead.
func (*PermissionRPC) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{26}
}

type InputRecord_InputColumn struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Boost         *float32                   `protobuf:"fixed32,1,opt,name=boost,def=1" json:"boost,omitempty"`
	Name          *string                    `protobuf:"bytes,2,req,name=name" json:"name,omitempty"`
	StrValue      *string                    `protobuf:"bytes,3,opt,name=strValue" json:"strValue,omitempty"`
	NumValue      *int64                     `protobuf:"varint,4,opt,name=numValue" json:"numValue,omitempty"`
	FloatValue    *float32                   `protobuf:"fixed32,5,opt,name=floatValue" json:"floatValue,omitempty"`
	BytesValue    []byte                     `protobuf:"bytes,6,opt,name=bytesValue" json:"bytesValue,omitempty"`
	Files         *FileList                  `protobuf:"bytes,7,opt,name=files" json:"files,omitempty"`
	Payload       []byte                     `protobuf:"bytes,8,opt,name=payload" json:"payload,omitempty"`
	NullValue     *bool                      `protobuf:"varint,9,opt,name=nullValue" json:"nullValue,omitempty"`
	VectorValue   []float32                  `protobuf:"fixed32,10,rep,name=vectorValue" json:"vectorValue,omitempty"`
	RedunColumns  []*InputRecord_InputColumn `protobuf:"bytes,11,rep,name=redunColumns" json:"redunColumns,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

// Default values for InputRecord_InputColumn fields.
const (
	Default_InputRecord_InputColumn_Boost = float32(1)
)

func (x *InputRecord_InputColumn) Reset() {
	*x = InputRecord_InputColumn{}
	mi := &file_rpc_api_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InputRecord_InputColumn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputRecord_InputColumn) ProtoMessage() {}

func (x *InputRecord_InputColumn) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputRecord_InputColumn.ProtoReflect.Descriptor instead.
func (*InputRecord_InputColumn) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{5, 0}
}

func (x *InputRecord_InputColumn) GetBoost() float32 {
	if x != nil && x.Boost != nil {
		return *x.Boost
	}
	return Default_InputRecord_InputColumn_Boost
}

func (x *InputRecord_InputColumn) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *InputRecord_InputColumn) GetStrValue() string {
	if x != nil && x.StrValue != nil {
		return *x.StrValue
	}
	return ""
}

func (x *InputRecord_InputColumn) GetNumValue() int64 {
	if x != nil && x.NumValue != nil {
		return *x.NumValue
	}
	return 0
}

func (x *InputRecord_InputColumn) GetFloatValue() float32 {
	if x != nil && x.FloatValue != nil {
		return *x.FloatValue
	}
	return 0
}

func (x *InputRecord_InputColumn) GetBytesValue() []byte {
	if x != nil {
		return x.BytesValue
	}
	return nil
}

func (x *InputRecord_InputColumn) GetFiles() *FileList {
	if x != nil {
		return x.Files
	}
	return nil
}

func (x *InputRecord_InputColumn) GetPayload() []byte {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *InputRecord_InputColumn) GetNullValue() bool {
	if x != nil && x.NullValue != nil {
		return *x.NullValue
	}
	return false
}

func (x *InputRecord_InputColumn) GetVectorValue() []float32 {
	if x != nil {
		return x.VectorValue
	}
	return nil
}

func (x *InputRecord_InputColumn) GetRedunColumns() []*InputRecord_InputColumn {
	if x != nil {
		return x.RedunColumns
	}
	return nil
}

type ResultRecord_ResultColumn struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          *string                `protobuf:"bytes,1,req,name=name" json:"name,omitempty"`
	NumValue      *int64                 `protobuf:"varint,2,opt,name=numValue" json:"numValue,omitempty"`
	StrValue      *string                `protobuf:"bytes,3,opt,name=strValue" json:"strValue,omitempty"`
	FloatValue    *float32               `protobuf:"fixed32,4,opt,name=floatValue" json:"floatValue,omitempty"`
	BytesValue    []byte                 `protobuf:"bytes,5,opt,name=bytesValue" json:"bytesValue,omitempty"`
	Files         *FileList              `protobuf:"bytes,6,opt,name=files" json:"files,omitempty"`
	Payload       []byte                 `protobuf:"bytes,7,opt,name=payload" json:"payload,omitempty"`
	FloatVectors  []float32              `protobuf:"fixed32,8,rep,name=floatVectors" json:"floatVectors,omitempty"`
	ByteVector    []byte                 `protobuf:"bytes,9,opt,name=byteVector" json:"byteVector,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResultRecord_ResultColumn) Reset() {
	*x = ResultRecord_ResultColumn{}
	mi := &file_rpc_api_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResultRecord_ResultColumn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResultRecord_ResultColumn) ProtoMessage() {}

func (x *ResultRecord_ResultColumn) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResultRecord_ResultColumn.ProtoReflect.Descriptor instead.
func (*ResultRecord_ResultColumn) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{6, 0}
}

func (x *ResultRecord_ResultColumn) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *ResultRecord_ResultColumn) GetNumValue() int64 {
	if x != nil && x.NumValue != nil {
		return *x.NumValue
	}
	return 0
}

func (x *ResultRecord_ResultColumn) GetStrValue() string {
	if x != nil && x.StrValue != nil {
		return *x.StrValue
	}
	return ""
}

func (x *ResultRecord_ResultColumn) GetFloatValue() float32 {
	if x != nil && x.FloatValue != nil {
		return *x.FloatValue
	}
	return 0
}

func (x *ResultRecord_ResultColumn) GetBytesValue() []byte {
	if x != nil {
		return x.BytesValue
	}
	return nil
}

func (x *ResultRecord_ResultColumn) GetFiles() *FileList {
	if x != nil {
		return x.Files
	}
	return nil
}

func (x *ResultRecord_ResultColumn) GetPayload() []byte {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *ResultRecord_ResultColumn) GetFloatVectors() []float32 {
	if x != nil {
		return x.FloatVectors
	}
	return nil
}

func (x *ResultRecord_ResultColumn) GetByteVector() []byte {
	if x != nil {
		return x.ByteVector
	}
	return nil
}

// add records
type MaintRPC_InsertRecordRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Source        *string                `protobuf:"bytes,1,req,name=source" json:"source,omitempty"`
	Overwrite     *bool                  `protobuf:"varint,2,opt,name=overwrite,def=1" json:"overwrite,omitempty"`
	Record        []*InputRecord         `protobuf:"bytes,3,rep,name=record" json:"record,omitempty"`
	Options       []*Property            `protobuf:"bytes,4,rep,name=options" json:"options,omitempty"`
	SkipFileError *bool                  `protobuf:"varint,5,opt,name=skipFileError,def=0" json:"skipFileError,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

// Default values for MaintRPC_InsertRecordRequest fields.
const (
	Default_MaintRPC_InsertRecordRequest_Overwrite     = bool(true)
	Default_MaintRPC_InsertRecordRequest_SkipFileError = bool(false)
)

func (x *MaintRPC_InsertRecordRequest) Reset() {
	*x = MaintRPC_InsertRecordRequest{}
	mi := &file_rpc_api_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MaintRPC_InsertRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintRPC_InsertRecordRequest) ProtoMessage() {}

func (x *MaintRPC_InsertRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintRPC_InsertRecordRequest.ProtoReflect.Descriptor instead.
func (*MaintRPC_InsertRecordRequest) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{16, 0}
}

func (x *MaintRPC_InsertRecordRequest) GetSource() string {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return ""
}

func (x *MaintRPC_InsertRecordRequest) GetOverwrite() bool {
	if x != nil && x.Overwrite != nil {
		return *x.Overwrite
	}
	return Default_MaintRPC_InsertRecordRequest_Overwrite
}

func (x *MaintRPC_InsertRecordRequest) GetRecord() []*InputRecord {
	if x != nil {
		return x.Record
	}
	return nil
}

func (x *MaintRPC_InsertRecordRequest) GetOptions() []*Property {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *MaintRPC_InsertRecordRequest) GetSkipFileError() bool {
	if x != nil && x.SkipFileError != nil {
		return *x.SkipFileError
	}
	return Default_MaintRPC_InsertRecordRequest_SkipFileError
}

// load records from file
type MaintRPC_LoadRecordRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Source        *string                `protobuf:"bytes,1,req,name=source" json:"source,omitempty"`
	Overwrite     *bool                  `protobuf:"varint,2,opt,name=overwrite,def=1" json:"overwrite,omitempty"`
	Record        []*InputRecord         `protobuf:"bytes,3,rep,name=record" json:"record,omitempty"`
	Options       []*Property            `protobuf:"bytes,4,rep,name=options" json:"options,omitempty"`
	SkipFileError *bool                  `protobuf:"varint,5,opt,name=skipFileError,def=0" json:"skipFileError,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

// Default values for MaintRPC_LoadRecordRequest fields.
const (
	Default_MaintRPC_LoadRecordRequest_Overwrite     = bool(true)
	Default_MaintRPC_LoadRecordRequest_SkipFileError = bool(false)
)

func (x *MaintRPC_LoadRecordRequest) Reset() {
	*x = MaintRPC_LoadRecordRequest{}
	mi := &file_rpc_api_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MaintRPC_LoadRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintRPC_LoadRecordRequest) ProtoMessage() {}

func (x *MaintRPC_LoadRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintRPC_LoadRecordRequest.ProtoReflect.Descriptor instead.
func (*MaintRPC_LoadRecordRequest) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{16, 1}
}

func (x *MaintRPC_LoadRecordRequest) GetSource() string {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return ""
}

func (x *MaintRPC_LoadRecordRequest) GetOverwrite() bool {
	if x != nil && x.Overwrite != nil {
		return *x.Overwrite
	}
	return Default_MaintRPC_LoadRecordRequest_Overwrite
}

func (x *MaintRPC_LoadRecordRequest) GetRecord() []*InputRecord {
	if x != nil {
		return x.Record
	}
	return nil
}

func (x *MaintRPC_LoadRecordRequest) GetOptions() []*Property {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *MaintRPC_LoadRecordRequest) GetSkipFileError() bool {
	if x != nil && x.SkipFileError != nil {
		return *x.SkipFileError
	}
	return Default_MaintRPC_LoadRecordRequest_SkipFileError
}

// delete records
type MaintRPC_DeleteRecordRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Source        *string                `protobuf:"bytes,1,req,name=source" json:"source,omitempty"`
	Id            []string               `protobuf:"bytes,2,rep,name=id" json:"id,omitempty"`
	Query         []string               `protobuf:"bytes,3,rep,name=query" json:"query,omitempty"`
	Options       []*Property            `protobuf:"bytes,4,rep,name=options" json:"options,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MaintRPC_DeleteRecordRequest) Reset() {
	*x = MaintRPC_DeleteRecordRequest{}
	mi := &file_rpc_api_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MaintRPC_DeleteRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintRPC_DeleteRecordRequest) ProtoMessage() {}

func (x *MaintRPC_DeleteRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintRPC_DeleteRecordRequest.ProtoReflect.Descriptor instead.
func (*MaintRPC_DeleteRecordRequest) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{16, 2}
}

func (x *MaintRPC_DeleteRecordRequest) GetSource() string {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return ""
}

func (x *MaintRPC_DeleteRecordRequest) GetId() []string {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *MaintRPC_DeleteRecordRequest) GetQuery() []string {
	if x != nil {
		return x.Query
	}
	return nil
}

func (x *MaintRPC_DeleteRecordRequest) GetOptions() []*Property {
	if x != nil {
		return x.Options
	}
	return nil
}

// update records
type MaintRPC_UpdateRecordRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Source        *string                `protobuf:"bytes,1,req,name=source" json:"source,omitempty"`
	Overwrite     *bool                  `protobuf:"varint,2,opt,name=overwrite,def=1" json:"overwrite,omitempty"`
	Record        []*InputRecord         `protobuf:"bytes,3,rep,name=record" json:"record,omitempty"`
	Options       []*Property            `protobuf:"bytes,4,rep,name=options" json:"options,omitempty"`
	SkipFileError *bool                  `protobuf:"varint,5,opt,name=skipFileError,def=0" json:"skipFileError,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

// Default values for MaintRPC_UpdateRecordRequest fields.
const (
	Default_MaintRPC_UpdateRecordRequest_Overwrite     = bool(true)
	Default_MaintRPC_UpdateRecordRequest_SkipFileError = bool(false)
)

func (x *MaintRPC_UpdateRecordRequest) Reset() {
	*x = MaintRPC_UpdateRecordRequest{}
	mi := &file_rpc_api_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MaintRPC_UpdateRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintRPC_UpdateRecordRequest) ProtoMessage() {}

func (x *MaintRPC_UpdateRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintRPC_UpdateRecordRequest.ProtoReflect.Descriptor instead.
func (*MaintRPC_UpdateRecordRequest) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{16, 3}
}

func (x *MaintRPC_UpdateRecordRequest) GetSource() string {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return ""
}

func (x *MaintRPC_UpdateRecordRequest) GetOverwrite() bool {
	if x != nil && x.Overwrite != nil {
		return *x.Overwrite
	}
	return Default_MaintRPC_UpdateRecordRequest_Overwrite
}

func (x *MaintRPC_UpdateRecordRequest) GetRecord() []*InputRecord {
	if x != nil {
		return x.Record
	}
	return nil
}

func (x *MaintRPC_UpdateRecordRequest) GetOptions() []*Property {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *MaintRPC_UpdateRecordRequest) GetSkipFileError() bool {
	if x != nil && x.SkipFileError != nil {
		return *x.SkipFileError
	}
	return Default_MaintRPC_UpdateRecordRequest_SkipFileError
}

type MaintRPC_CommitResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Info          []*Property            `protobuf:"bytes,1,rep,name=info" json:"info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MaintRPC_CommitResponse) Reset() {
	*x = MaintRPC_CommitResponse{}
	mi := &file_rpc_api_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MaintRPC_CommitResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintRPC_CommitResponse) ProtoMessage() {}

func (x *MaintRPC_CommitResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintRPC_CommitResponse.ProtoReflect.Descriptor instead.
func (*MaintRPC_CommitResponse) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{16, 4}
}

func (x *MaintRPC_CommitResponse) GetInfo() []*Property {
	if x != nil {
		return x.Info
	}
	return nil
}

// admin
type MaintRPC_AdminRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Source        *string                `protobuf:"bytes,1,req,name=source" json:"source,omitempty"`
	Action        *MaintRPC_AdminAction  `protobuf:"varint,2,req,name=action,enum=MaintRPC_AdminAction" json:"action,omitempty"`
	Options       []*Property            `protobuf:"bytes,3,rep,name=options" json:"options,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MaintRPC_AdminRequest) Reset() {
	*x = MaintRPC_AdminRequest{}
	mi := &file_rpc_api_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MaintRPC_AdminRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintRPC_AdminRequest) ProtoMessage() {}

func (x *MaintRPC_AdminRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintRPC_AdminRequest.ProtoReflect.Descriptor instead.
func (*MaintRPC_AdminRequest) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{16, 5}
}

func (x *MaintRPC_AdminRequest) GetSource() string {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return ""
}

func (x *MaintRPC_AdminRequest) GetAction() MaintRPC_AdminAction {
	if x != nil && x.Action != nil {
		return *x.Action
	}
	return MaintRPC_ENABLE
}

func (x *MaintRPC_AdminRequest) GetOptions() []*Property {
	if x != nil {
		return x.Options
	}
	return nil
}

// optimize
type MaintRPC_OptimizeIndexRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Source        *string                `protobuf:"bytes,1,req,name=source" json:"source,omitempty"`
	WaitFlush     *bool                  `protobuf:"varint,2,opt,name=waitFlush,def=1" json:"waitFlush,omitempty"`
	WaitSearcher  *bool                  `protobuf:"varint,3,opt,name=waitSearcher,def=1" json:"waitSearcher,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

// Default values for MaintRPC_OptimizeIndexRequest fields.
const (
	Default_MaintRPC_OptimizeIndexRequest_WaitFlush    = bool(true)
	Default_MaintRPC_OptimizeIndexRequest_WaitSearcher = bool(true)
)

func (x *MaintRPC_OptimizeIndexRequest) Reset() {
	*x = MaintRPC_OptimizeIndexRequest{}
	mi := &file_rpc_api_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MaintRPC_OptimizeIndexRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintRPC_OptimizeIndexRequest) ProtoMessage() {}

func (x *MaintRPC_OptimizeIndexRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintRPC_OptimizeIndexRequest.ProtoReflect.Descriptor instead.
func (*MaintRPC_OptimizeIndexRequest) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{16, 6}
}

func (x *MaintRPC_OptimizeIndexRequest) GetSource() string {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return ""
}

func (x *MaintRPC_OptimizeIndexRequest) GetWaitFlush() bool {
	if x != nil && x.WaitFlush != nil {
		return *x.WaitFlush
	}
	return Default_MaintRPC_OptimizeIndexRequest_WaitFlush
}

func (x *MaintRPC_OptimizeIndexRequest) GetWaitSearcher() bool {
	if x != nil && x.WaitSearcher != nil {
		return *x.WaitSearcher
	}
	return Default_MaintRPC_OptimizeIndexRequest_WaitSearcher
}

// export
type MaintRPC_ExportRecordRequest struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	Source        *string                        `protobuf:"bytes,1,req,name=source" json:"source,omitempty"`
	Columns       *string                        `protobuf:"bytes,2,opt,name=columns" json:"columns,omitempty"`
	Query         *string                        `protobuf:"bytes,3,opt,name=query" json:"query,omitempty"`
	Start         *uint64                        `protobuf:"varint,4,opt,name=start" json:"start,omitempty"`
	Num           *uint64                        `protobuf:"varint,5,opt,name=num" json:"num,omitempty"`
	SearchRequest *SearchRPC_SearchRecordRequest `protobuf:"bytes,6,opt,name=searchRequest" json:"searchRequest,omitempty"`
	Options       []*Property                    `protobuf:"bytes,7,rep,name=options" json:"options,omitempty"`
	ExportID      *string                        `protobuf:"bytes,8,opt,name=exportID" json:"exportID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MaintRPC_ExportRecordRequest) Reset() {
	*x = MaintRPC_ExportRecordRequest{}
	mi := &file_rpc_api_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MaintRPC_ExportRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintRPC_ExportRecordRequest) ProtoMessage() {}

func (x *MaintRPC_ExportRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintRPC_ExportRecordRequest.ProtoReflect.Descriptor instead.
func (*MaintRPC_ExportRecordRequest) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{16, 7}
}

func (x *MaintRPC_ExportRecordRequest) GetSource() string {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return ""
}

func (x *MaintRPC_ExportRecordRequest) GetColumns() string {
	if x != nil && x.Columns != nil {
		return *x.Columns
	}
	return ""
}

func (x *MaintRPC_ExportRecordRequest) GetQuery() string {
	if x != nil && x.Query != nil {
		return *x.Query
	}
	return ""
}

func (x *MaintRPC_ExportRecordRequest) GetStart() uint64 {
	if x != nil && x.Start != nil {
		return *x.Start
	}
	return 0
}

func (x *MaintRPC_ExportRecordRequest) GetNum() uint64 {
	if x != nil && x.Num != nil {
		return *x.Num
	}
	return 0
}

func (x *MaintRPC_ExportRecordRequest) GetSearchRequest() *SearchRPC_SearchRecordRequest {
	if x != nil {
		return x.SearchRequest
	}
	return nil
}

func (x *MaintRPC_ExportRecordRequest) GetOptions() []*Property {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *MaintRPC_ExportRecordRequest) GetExportID() string {
	if x != nil && x.ExportID != nil {
		return *x.ExportID
	}
	return ""
}

type MaintRPC_ExportRecordResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Source        *string                `protobuf:"bytes,1,req,name=source" json:"source,omitempty"`
	Record        []*ResultRecord        `protobuf:"bytes,2,rep,name=record" json:"record,omitempty"`
	ExportID      *string                `protobuf:"bytes,3,opt,name=exportID" json:"exportID,omitempty"`
	AppendixNum   *int32                 `protobuf:"varint,4,opt,name=appendixNum" json:"appendixNum,omitempty"`
	Status        *string                `protobuf:"bytes,5,opt,name=status" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MaintRPC_ExportRecordResponse) Reset() {
	*x = MaintRPC_ExportRecordResponse{}
	mi := &file_rpc_api_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MaintRPC_ExportRecordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintRPC_ExportRecordResponse) ProtoMessage() {}

func (x *MaintRPC_ExportRecordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintRPC_ExportRecordResponse.ProtoReflect.Descriptor instead.
func (*MaintRPC_ExportRecordResponse) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{16, 8}
}

func (x *MaintRPC_ExportRecordResponse) GetSource() string {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return ""
}

func (x *MaintRPC_ExportRecordResponse) GetRecord() []*ResultRecord {
	if x != nil {
		return x.Record
	}
	return nil
}

func (x *MaintRPC_ExportRecordResponse) GetExportID() string {
	if x != nil && x.ExportID != nil {
		return *x.ExportID
	}
	return ""
}

func (x *MaintRPC_ExportRecordResponse) GetAppendixNum() int32 {
	if x != nil && x.AppendixNum != nil {
		return *x.AppendixNum
	}
	return 0
}

func (x *MaintRPC_ExportRecordResponse) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

type MaintRPC_ExportFileHeader struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FileName      *string                `protobuf:"bytes,1,req,name=fileName" json:"fileName,omitempty"`
	FLength       *int64                 `protobuf:"varint,2,opt,name=fLength" json:"fLength,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MaintRPC_ExportFileHeader) Reset() {
	*x = MaintRPC_ExportFileHeader{}
	mi := &file_rpc_api_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MaintRPC_ExportFileHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintRPC_ExportFileHeader) ProtoMessage() {}

func (x *MaintRPC_ExportFileHeader) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintRPC_ExportFileHeader.ProtoReflect.Descriptor instead.
func (*MaintRPC_ExportFileHeader) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{16, 9}
}

func (x *MaintRPC_ExportFileHeader) GetFileName() string {
	if x != nil && x.FileName != nil {
		return *x.FileName
	}
	return ""
}

func (x *MaintRPC_ExportFileHeader) GetFLength() int64 {
	if x != nil && x.FLength != nil {
		return *x.FLength
	}
	return 0
}

type MaintRPC_ExportStatus struct {
	state         protoimpl.MessageState             `protogen:"open.v1"`
	SearchRequest *SearchRPC_SearchRecordRequest     `protobuf:"bytes,1,req,name=searchRequest" json:"searchRequest,omitempty"`
	SubDB         []*MaintRPC_ExportStatus_SubDBInfo `protobuf:"bytes,2,rep,name=subDB" json:"subDB,omitempty"`
	ModifyTime    *int64                             `protobuf:"varint,3,opt,name=modifyTime" json:"modifyTime,omitempty"`
	Completed     *bool                              `protobuf:"varint,4,opt,name=completed,def=0" json:"completed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

// Default values for MaintRPC_ExportStatus fields.
const (
	Default_MaintRPC_ExportStatus_Completed = bool(false)
)

func (x *MaintRPC_ExportStatus) Reset() {
	*x = MaintRPC_ExportStatus{}
	mi := &file_rpc_api_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MaintRPC_ExportStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintRPC_ExportStatus) ProtoMessage() {}

func (x *MaintRPC_ExportStatus) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintRPC_ExportStatus.ProtoReflect.Descriptor instead.
func (*MaintRPC_ExportStatus) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{16, 10}
}

func (x *MaintRPC_ExportStatus) GetSearchRequest() *SearchRPC_SearchRecordRequest {
	if x != nil {
		return x.SearchRequest
	}
	return nil
}

func (x *MaintRPC_ExportStatus) GetSubDB() []*MaintRPC_ExportStatus_SubDBInfo {
	if x != nil {
		return x.SubDB
	}
	return nil
}

func (x *MaintRPC_ExportStatus) GetModifyTime() int64 {
	if x != nil && x.ModifyTime != nil {
		return *x.ModifyTime
	}
	return 0
}

func (x *MaintRPC_ExportStatus) GetCompleted() bool {
	if x != nil && x.Completed != nil {
		return *x.Completed
	}
	return Default_MaintRPC_ExportStatus_Completed
}

type MaintRPC_CollisionRequest struct {
	state         protoimpl.MessageState               `protogen:"open.v1"`
	HistoryResult []*MaintRPC_CollisionRequest_History `protobuf:"bytes,1,rep,name=historyResult" json:"historyResult,omitempty"`
	SearchRequest []*SearchRPC_SearchRecordRequest     `protobuf:"bytes,2,rep,name=searchRequest" json:"searchRequest,omitempty"`
	Options       []*Property                          `protobuf:"bytes,3,rep,name=options" json:"options,omitempty"`
	CollisionID   *int64                               `protobuf:"varint,4,opt,name=collisionID" json:"collisionID,omitempty"`
	Start         *uint64                              `protobuf:"varint,5,opt,name=start" json:"start,omitempty"`
	Num           *uint64                              `protobuf:"varint,6,opt,name=num" json:"num,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MaintRPC_CollisionRequest) Reset() {
	*x = MaintRPC_CollisionRequest{}
	mi := &file_rpc_api_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MaintRPC_CollisionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintRPC_CollisionRequest) ProtoMessage() {}

func (x *MaintRPC_CollisionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintRPC_CollisionRequest.ProtoReflect.Descriptor instead.
func (*MaintRPC_CollisionRequest) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{16, 11}
}

func (x *MaintRPC_CollisionRequest) GetHistoryResult() []*MaintRPC_CollisionRequest_History {
	if x != nil {
		return x.HistoryResult
	}
	return nil
}

func (x *MaintRPC_CollisionRequest) GetSearchRequest() []*SearchRPC_SearchRecordRequest {
	if x != nil {
		return x.SearchRequest
	}
	return nil
}

func (x *MaintRPC_CollisionRequest) GetOptions() []*Property {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *MaintRPC_CollisionRequest) GetCollisionID() int64 {
	if x != nil && x.CollisionID != nil {
		return *x.CollisionID
	}
	return 0
}

func (x *MaintRPC_CollisionRequest) GetStart() uint64 {
	if x != nil && x.Start != nil {
		return *x.Start
	}
	return 0
}

func (x *MaintRPC_CollisionRequest) GetNum() uint64 {
	if x != nil && x.Num != nil {
		return *x.Num
	}
	return 0
}

type MaintRPC_CollisionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CollisionID   *string                `protobuf:"bytes,1,opt,name=collisionID" json:"collisionID,omitempty"`
	Record        []string               `protobuf:"bytes,2,rep,name=record" json:"record,omitempty"`
	Progress      *int64                 `protobuf:"varint,3,req,name=progress" json:"progress,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MaintRPC_CollisionResponse) Reset() {
	*x = MaintRPC_CollisionResponse{}
	mi := &file_rpc_api_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MaintRPC_CollisionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintRPC_CollisionResponse) ProtoMessage() {}

func (x *MaintRPC_CollisionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintRPC_CollisionResponse.ProtoReflect.Descriptor instead.
func (*MaintRPC_CollisionResponse) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{16, 12}
}

func (x *MaintRPC_CollisionResponse) GetCollisionID() string {
	if x != nil && x.CollisionID != nil {
		return *x.CollisionID
	}
	return ""
}

func (x *MaintRPC_CollisionResponse) GetRecord() []string {
	if x != nil {
		return x.Record
	}
	return nil
}

func (x *MaintRPC_CollisionResponse) GetProgress() int64 {
	if x != nil && x.Progress != nil {
		return *x.Progress
	}
	return 0
}

type MaintRPC_ExportStatus_SubDBInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SubID         *string                `protobuf:"bytes,1,req,name=subID" json:"subID,omitempty"`
	Size          *int64                 `protobuf:"varint,2,opt,name=size" json:"size,omitempty"`
	NodeAddress   *string                `protobuf:"bytes,3,opt,name=nodeAddress" json:"nodeAddress,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MaintRPC_ExportStatus_SubDBInfo) Reset() {
	*x = MaintRPC_ExportStatus_SubDBInfo{}
	mi := &file_rpc_api_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MaintRPC_ExportStatus_SubDBInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintRPC_ExportStatus_SubDBInfo) ProtoMessage() {}

func (x *MaintRPC_ExportStatus_SubDBInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintRPC_ExportStatus_SubDBInfo.ProtoReflect.Descriptor instead.
func (*MaintRPC_ExportStatus_SubDBInfo) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{16, 10, 0}
}

func (x *MaintRPC_ExportStatus_SubDBInfo) GetSubID() string {
	if x != nil && x.SubID != nil {
		return *x.SubID
	}
	return ""
}

func (x *MaintRPC_ExportStatus_SubDBInfo) GetSize() int64 {
	if x != nil && x.Size != nil {
		return *x.Size
	}
	return 0
}

func (x *MaintRPC_ExportStatus_SubDBInfo) GetNodeAddress() string {
	if x != nil && x.NodeAddress != nil {
		return *x.NodeAddress
	}
	return ""
}

type MaintRPC_CollisionRequest_History struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int64                 `protobuf:"varint,1,req,name=id" json:"id,omitempty"`
	PartNO        []int32                `protobuf:"varint,2,rep,name=partNO" json:"partNO,omitempty"`
	PartID        []int32                `protobuf:"varint,3,rep,name=partID" json:"partID,omitempty"`
	RunNode       []string               `protobuf:"bytes,4,rep,name=runNode" json:"runNode,omitempty"`
	Options       []*Property            `protobuf:"bytes,5,rep,name=options" json:"options,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MaintRPC_CollisionRequest_History) Reset() {
	*x = MaintRPC_CollisionRequest_History{}
	mi := &file_rpc_api_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MaintRPC_CollisionRequest_History) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintRPC_CollisionRequest_History) ProtoMessage() {}

func (x *MaintRPC_CollisionRequest_History) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintRPC_CollisionRequest_History.ProtoReflect.Descriptor instead.
func (*MaintRPC_CollisionRequest_History) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{16, 11, 0}
}

func (x *MaintRPC_CollisionRequest_History) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *MaintRPC_CollisionRequest_History) GetPartNO() []int32 {
	if x != nil {
		return x.PartNO
	}
	return nil
}

func (x *MaintRPC_CollisionRequest_History) GetPartID() []int32 {
	if x != nil {
		return x.PartID
	}
	return nil
}

func (x *MaintRPC_CollisionRequest_History) GetRunNode() []string {
	if x != nil {
		return x.RunNode
	}
	return nil
}

func (x *MaintRPC_CollisionRequest_History) GetOptions() []*Property {
	if x != nil {
		return x.Options
	}
	return nil
}

// search
type SearchRPC_SearchRecordRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Source        *string                `protobuf:"bytes,1,req,name=source" json:"source,omitempty"`
	Query         *string                `protobuf:"bytes,2,req,name=query" json:"query,omitempty"`
	SortMethod    *string                `protobuf:"bytes,3,opt,name=sortMethod" json:"sortMethod,omitempty"`
	Statics       *string                `protobuf:"bytes,4,opt,name=statics" json:"statics,omitempty"`
	DefaultColumn *string                `protobuf:"bytes,5,opt,name=defaultColumn" json:"defaultColumn,omitempty"`
	ReadColumns   *string                `protobuf:"bytes,6,opt,name=readColumns" json:"readColumns,omitempty"`
	ColorColumns  *string                `protobuf:"bytes,7,opt,name=colorColumns" json:"colorColumns,omitempty"`
	Start         *int64                 `protobuf:"varint,8,opt,name=start" json:"start,omitempty"`
	Num           *int64                 `protobuf:"varint,9,opt,name=num" json:"num,omitempty"`
	CutSize       *int64                 `protobuf:"varint,10,opt,name=cutSize" json:"cutSize,omitempty"`
	QuickSearch   *bool                  `protobuf:"varint,11,opt,name=QuickSearch" json:"QuickSearch,omitempty"`
	EstimateUnit  *int64                 `protobuf:"varint,12,opt,name=EstimateUnit" json:"EstimateUnit,omitempty"`
	Opts          []*Property            `protobuf:"bytes,13,rep,name=opts" json:"opts,omitempty"`
	Timeout       *int64                 `protobuf:"varint,14,opt,name=timeout" json:"timeout,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchRPC_SearchRecordRequest) Reset() {
	*x = SearchRPC_SearchRecordRequest{}
	mi := &file_rpc_api_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchRPC_SearchRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRPC_SearchRecordRequest) ProtoMessage() {}

func (x *SearchRPC_SearchRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRPC_SearchRecordRequest.ProtoReflect.Descriptor instead.
func (*SearchRPC_SearchRecordRequest) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{17, 0}
}

func (x *SearchRPC_SearchRecordRequest) GetSource() string {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return ""
}

func (x *SearchRPC_SearchRecordRequest) GetQuery() string {
	if x != nil && x.Query != nil {
		return *x.Query
	}
	return ""
}

func (x *SearchRPC_SearchRecordRequest) GetSortMethod() string {
	if x != nil && x.SortMethod != nil {
		return *x.SortMethod
	}
	return ""
}

func (x *SearchRPC_SearchRecordRequest) GetStatics() string {
	if x != nil && x.Statics != nil {
		return *x.Statics
	}
	return ""
}

func (x *SearchRPC_SearchRecordRequest) GetDefaultColumn() string {
	if x != nil && x.DefaultColumn != nil {
		return *x.DefaultColumn
	}
	return ""
}

func (x *SearchRPC_SearchRecordRequest) GetReadColumns() string {
	if x != nil && x.ReadColumns != nil {
		return *x.ReadColumns
	}
	return ""
}

func (x *SearchRPC_SearchRecordRequest) GetColorColumns() string {
	if x != nil && x.ColorColumns != nil {
		return *x.ColorColumns
	}
	return ""
}

func (x *SearchRPC_SearchRecordRequest) GetStart() int64 {
	if x != nil && x.Start != nil {
		return *x.Start
	}
	return 0
}

func (x *SearchRPC_SearchRecordRequest) GetNum() int64 {
	if x != nil && x.Num != nil {
		return *x.Num
	}
	return 0
}

func (x *SearchRPC_SearchRecordRequest) GetCutSize() int64 {
	if x != nil && x.CutSize != nil {
		return *x.CutSize
	}
	return 0
}

func (x *SearchRPC_SearchRecordRequest) GetQuickSearch() bool {
	if x != nil && x.QuickSearch != nil {
		return *x.QuickSearch
	}
	return false
}

func (x *SearchRPC_SearchRecordRequest) GetEstimateUnit() int64 {
	if x != nil && x.EstimateUnit != nil {
		return *x.EstimateUnit
	}
	return 0
}

func (x *SearchRPC_SearchRecordRequest) GetOpts() []*Property {
	if x != nil {
		return x.Opts
	}
	return nil
}

func (x *SearchRPC_SearchRecordRequest) GetTimeout() int64 {
	if x != nil && x.Timeout != nil {
		return *x.Timeout
	}
	return 0
}

// search response
type SearchRPC_SearchRecordResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Searchid       *string                `protobuf:"bytes,1,opt,name=searchid" json:"searchid,omitempty"` // SearchID.toString()
	StatusCode     *int32                 `protobuf:"varint,2,req,name=statusCode" json:"statusCode,omitempty"`
	SearchTime     *int32                 `protobuf:"varint,3,req,name=searchTime" json:"searchTime,omitempty"`
	NumFound       *uint64                `protobuf:"varint,4,req,name=numFound" json:"numFound,omitempty"`
	Start          *uint64                `protobuf:"varint,5,req,name=start" json:"start,omitempty"`
	Record         []*ResultRecord        `protobuf:"bytes,6,rep,name=record" json:"record,omitempty"`
	SearchProgress []*Property            `protobuf:"bytes,7,rep,name=searchProgress" json:"searchProgress,omitempty"` //异步检索进度
	DbHitsMap      []*Property            `protobuf:"bytes,8,rep,name=dbHitsMap" json:"dbHitsMap,omitempty"`           //多库检索时保存各库的命中记录数
	Opts           []*Property            `protobuf:"bytes,9,rep,name=opts" json:"opts,omitempty"`
	SubProperties  *PropertyTable         `protobuf:"bytes,10,opt,name=subProperties" json:"subProperties,omitempty"` //每个子库的信息
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SearchRPC_SearchRecordResponse) Reset() {
	*x = SearchRPC_SearchRecordResponse{}
	mi := &file_rpc_api_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchRPC_SearchRecordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRPC_SearchRecordResponse) ProtoMessage() {}

func (x *SearchRPC_SearchRecordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRPC_SearchRecordResponse.ProtoReflect.Descriptor instead.
func (*SearchRPC_SearchRecordResponse) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{17, 1}
}

func (x *SearchRPC_SearchRecordResponse) GetSearchid() string {
	if x != nil && x.Searchid != nil {
		return *x.Searchid
	}
	return ""
}

func (x *SearchRPC_SearchRecordResponse) GetStatusCode() int32 {
	if x != nil && x.StatusCode != nil {
		return *x.StatusCode
	}
	return 0
}

func (x *SearchRPC_SearchRecordResponse) GetSearchTime() int32 {
	if x != nil && x.SearchTime != nil {
		return *x.SearchTime
	}
	return 0
}

func (x *SearchRPC_SearchRecordResponse) GetNumFound() uint64 {
	if x != nil && x.NumFound != nil {
		return *x.NumFound
	}
	return 0
}

func (x *SearchRPC_SearchRecordResponse) GetStart() uint64 {
	if x != nil && x.Start != nil {
		return *x.Start
	}
	return 0
}

func (x *SearchRPC_SearchRecordResponse) GetRecord() []*ResultRecord {
	if x != nil {
		return x.Record
	}
	return nil
}

func (x *SearchRPC_SearchRecordResponse) GetSearchProgress() []*Property {
	if x != nil {
		return x.SearchProgress
	}
	return nil
}

func (x *SearchRPC_SearchRecordResponse) GetDbHitsMap() []*Property {
	if x != nil {
		return x.DbHitsMap
	}
	return nil
}

func (x *SearchRPC_SearchRecordResponse) GetOpts() []*Property {
	if x != nil {
		return x.Opts
	}
	return nil
}

func (x *SearchRPC_SearchRecordResponse) GetSubProperties() *PropertyTable {
	if x != nil {
		return x.SubProperties
	}
	return nil
}

// Category
type SearchRPC_CategoryQueryRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Source         *string                `protobuf:"bytes,1,req,name=source" json:"source,omitempty"`
	Query          *string                `protobuf:"bytes,2,req,name=query" json:"query,omitempty"`
	CategoryColumn *string                `protobuf:"bytes,3,req,name=categoryColumn" json:"categoryColumn,omitempty"`
	DefaultColumn  *string                `protobuf:"bytes,4,opt,name=defaultColumn" json:"defaultColumn,omitempty"`
	Num            *int64                 `protobuf:"varint,5,opt,name=num" json:"num,omitempty"`
	Opts           []*Property            `protobuf:"bytes,6,rep,name=opts" json:"opts,omitempty"`
	Timeout        *int64                 `protobuf:"varint,7,opt,name=timeout" json:"timeout,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SearchRPC_CategoryQueryRequest) Reset() {
	*x = SearchRPC_CategoryQueryRequest{}
	mi := &file_rpc_api_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchRPC_CategoryQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRPC_CategoryQueryRequest) ProtoMessage() {}

func (x *SearchRPC_CategoryQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRPC_CategoryQueryRequest.ProtoReflect.Descriptor instead.
func (*SearchRPC_CategoryQueryRequest) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{17, 2}
}

func (x *SearchRPC_CategoryQueryRequest) GetSource() string {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return ""
}

func (x *SearchRPC_CategoryQueryRequest) GetQuery() string {
	if x != nil && x.Query != nil {
		return *x.Query
	}
	return ""
}

func (x *SearchRPC_CategoryQueryRequest) GetCategoryColumn() string {
	if x != nil && x.CategoryColumn != nil {
		return *x.CategoryColumn
	}
	return ""
}

func (x *SearchRPC_CategoryQueryRequest) GetDefaultColumn() string {
	if x != nil && x.DefaultColumn != nil {
		return *x.DefaultColumn
	}
	return ""
}

func (x *SearchRPC_CategoryQueryRequest) GetNum() int64 {
	if x != nil && x.Num != nil {
		return *x.Num
	}
	return 0
}

func (x *SearchRPC_CategoryQueryRequest) GetOpts() []*Property {
	if x != nil {
		return x.Opts
	}
	return nil
}

func (x *SearchRPC_CategoryQueryRequest) GetTimeout() int64 {
	if x != nil && x.Timeout != nil {
		return *x.Timeout
	}
	return 0
}

// Category response
type SearchRPC_CategoryQueryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Searchid      *string                `protobuf:"bytes,1,opt,name=searchid" json:"searchid,omitempty"` // SearchID.toString()
	StatusCode    *int32                 `protobuf:"varint,2,req,name=statusCode" json:"statusCode,omitempty"`
	SearchTime    *int32                 `protobuf:"varint,3,req,name=searchTime" json:"searchTime,omitempty"`
	Result        []*NumProperty         `protobuf:"bytes,4,rep,name=result" json:"result,omitempty"`
	Num           *int64                 `protobuf:"varint,5,opt,name=num" json:"num,omitempty"`
	Opts          []*Property            `protobuf:"bytes,6,rep,name=opts" json:"opts,omitempty"`
	FunctionMap   []*Property            `protobuf:"bytes,7,rep,name=functionMap" json:"functionMap,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchRPC_CategoryQueryResponse) Reset() {
	*x = SearchRPC_CategoryQueryResponse{}
	mi := &file_rpc_api_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchRPC_CategoryQueryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRPC_CategoryQueryResponse) ProtoMessage() {}

func (x *SearchRPC_CategoryQueryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRPC_CategoryQueryResponse.ProtoReflect.Descriptor instead.
func (*SearchRPC_CategoryQueryResponse) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{17, 3}
}

func (x *SearchRPC_CategoryQueryResponse) GetSearchid() string {
	if x != nil && x.Searchid != nil {
		return *x.Searchid
	}
	return ""
}

func (x *SearchRPC_CategoryQueryResponse) GetStatusCode() int32 {
	if x != nil && x.StatusCode != nil {
		return *x.StatusCode
	}
	return 0
}

func (x *SearchRPC_CategoryQueryResponse) GetSearchTime() int32 {
	if x != nil && x.SearchTime != nil {
		return *x.SearchTime
	}
	return 0
}

func (x *SearchRPC_CategoryQueryResponse) GetResult() []*NumProperty {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *SearchRPC_CategoryQueryResponse) GetNum() int64 {
	if x != nil && x.Num != nil {
		return *x.Num
	}
	return 0
}

func (x *SearchRPC_CategoryQueryResponse) GetOpts() []*Property {
	if x != nil {
		return x.Opts
	}
	return nil
}

func (x *SearchRPC_CategoryQueryResponse) GetFunctionMap() []*Property {
	if x != nil {
		return x.FunctionMap
	}
	return nil
}

// Expression
type SearchRPC_ExpressionQueryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Source        *string                `protobuf:"bytes,1,req,name=source" json:"source,omitempty"`
	Query         *string                `protobuf:"bytes,2,req,name=query" json:"query,omitempty"`
	Expression    *string                `protobuf:"bytes,3,req,name=expression" json:"expression,omitempty"`
	Opts          []*Property            `protobuf:"bytes,4,rep,name=opts" json:"opts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchRPC_ExpressionQueryRequest) Reset() {
	*x = SearchRPC_ExpressionQueryRequest{}
	mi := &file_rpc_api_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchRPC_ExpressionQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRPC_ExpressionQueryRequest) ProtoMessage() {}

func (x *SearchRPC_ExpressionQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRPC_ExpressionQueryRequest.ProtoReflect.Descriptor instead.
func (*SearchRPC_ExpressionQueryRequest) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{17, 4}
}

func (x *SearchRPC_ExpressionQueryRequest) GetSource() string {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return ""
}

func (x *SearchRPC_ExpressionQueryRequest) GetQuery() string {
	if x != nil && x.Query != nil {
		return *x.Query
	}
	return ""
}

func (x *SearchRPC_ExpressionQueryRequest) GetExpression() string {
	if x != nil && x.Expression != nil {
		return *x.Expression
	}
	return ""
}

func (x *SearchRPC_ExpressionQueryRequest) GetOpts() []*Property {
	if x != nil {
		return x.Opts
	}
	return nil
}

// Expression response
type SearchRPC_ExpressionQueryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Searchid      *string                `protobuf:"bytes,1,opt,name=searchid" json:"searchid,omitempty"` // SearchID.toString()
	StatusCode    *int32                 `protobuf:"varint,2,req,name=statusCode" json:"statusCode,omitempty"`
	SearchTime    *int32                 `protobuf:"varint,3,req,name=searchTime" json:"searchTime,omitempty"`
	Result        []*ResultField         `protobuf:"bytes,4,rep,name=result" json:"result,omitempty"`
	Num           *int64                 `protobuf:"varint,5,opt,name=num" json:"num,omitempty"`
	Opts          []*Property            `protobuf:"bytes,6,rep,name=opts" json:"opts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchRPC_ExpressionQueryResponse) Reset() {
	*x = SearchRPC_ExpressionQueryResponse{}
	mi := &file_rpc_api_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchRPC_ExpressionQueryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRPC_ExpressionQueryResponse) ProtoMessage() {}

func (x *SearchRPC_ExpressionQueryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRPC_ExpressionQueryResponse.ProtoReflect.Descriptor instead.
func (*SearchRPC_ExpressionQueryResponse) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{17, 5}
}

func (x *SearchRPC_ExpressionQueryResponse) GetSearchid() string {
	if x != nil && x.Searchid != nil {
		return *x.Searchid
	}
	return ""
}

func (x *SearchRPC_ExpressionQueryResponse) GetStatusCode() int32 {
	if x != nil && x.StatusCode != nil {
		return *x.StatusCode
	}
	return 0
}

func (x *SearchRPC_ExpressionQueryResponse) GetSearchTime() int32 {
	if x != nil && x.SearchTime != nil {
		return *x.SearchTime
	}
	return 0
}

func (x *SearchRPC_ExpressionQueryResponse) GetResult() []*ResultField {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *SearchRPC_ExpressionQueryResponse) GetNum() int64 {
	if x != nil && x.Num != nil {
		return *x.Num
	}
	return 0
}

func (x *SearchRPC_ExpressionQueryResponse) GetOpts() []*Property {
	if x != nil {
		return x.Opts
	}
	return nil
}

type SearchRPCGetRecordsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UuidList      *StringList            `protobuf:"bytes,1,opt,name=uuidList" json:"uuidList,omitempty"` //RecordID列表
	CutSize       *int64                 `protobuf:"varint,2,opt,name=cutSize" json:"cutSize,omitempty"`
	ReadColumns   *string                `protobuf:"bytes,3,opt,name=readColumns" json:"readColumns,omitempty"`
	ColorColumns  *string                `protobuf:"bytes,4,opt,name=colorColumns" json:"colorColumns,omitempty"`
	ColorWords    *string                `protobuf:"bytes,5,opt,name=colorWords" json:"colorWords,omitempty"`
	SearchOpts    []*Property            `protobuf:"bytes,6,rep,name=searchOpts" json:"searchOpts,omitempty"` //检索选项
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchRPCGetRecordsRequest) Reset() {
	*x = SearchRPCGetRecordsRequest{}
	mi := &file_rpc_api_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchRPCGetRecordsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRPCGetRecordsRequest) ProtoMessage() {}

func (x *SearchRPCGetRecordsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRPCGetRecordsRequest.ProtoReflect.Descriptor instead.
func (*SearchRPCGetRecordsRequest) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{17, 6}
}

func (x *SearchRPCGetRecordsRequest) GetUuidList() *StringList {
	if x != nil {
		return x.UuidList
	}
	return nil
}

func (x *SearchRPCGetRecordsRequest) GetCutSize() int64 {
	if x != nil && x.CutSize != nil {
		return *x.CutSize
	}
	return 0
}

func (x *SearchRPCGetRecordsRequest) GetReadColumns() string {
	if x != nil && x.ReadColumns != nil {
		return *x.ReadColumns
	}
	return ""
}

func (x *SearchRPCGetRecordsRequest) GetColorColumns() string {
	if x != nil && x.ColorColumns != nil {
		return *x.ColorColumns
	}
	return ""
}

func (x *SearchRPCGetRecordsRequest) GetColorWords() string {
	if x != nil && x.ColorWords != nil {
		return *x.ColorWords
	}
	return ""
}

func (x *SearchRPCGetRecordsRequest) GetSearchOpts() []*Property {
	if x != nil {
		return x.SearchOpts
	}
	return nil
}

type SearchRPC_FacetQueryRequest struct {
	state         protoimpl.MessageState                          `protogen:"open.v1"`
	Source        *string                                         `protobuf:"bytes,1,req,name=source" json:"source,omitempty"`
	Query         *string                                         `protobuf:"bytes,2,req,name=query" json:"query,omitempty"`
	FacetColumn   []*SearchRPC_FacetQueryRequest_InputFacetColumn `protobuf:"bytes,3,rep,name=facetColumn" json:"facetColumn,omitempty"`
	DefaultColumn *string                                         `protobuf:"bytes,4,opt,name=defaultColumn" json:"defaultColumn,omitempty"`
	Num           *int64                                          `protobuf:"varint,5,opt,name=num" json:"num,omitempty"`
	Opts          []*Property                                     `protobuf:"bytes,6,rep,name=opts" json:"opts,omitempty"`
	Timeout       *int64                                          `protobuf:"varint,7,opt,name=timeout" json:"timeout,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchRPC_FacetQueryRequest) Reset() {
	*x = SearchRPC_FacetQueryRequest{}
	mi := &file_rpc_api_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchRPC_FacetQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRPC_FacetQueryRequest) ProtoMessage() {}

func (x *SearchRPC_FacetQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRPC_FacetQueryRequest.ProtoReflect.Descriptor instead.
func (*SearchRPC_FacetQueryRequest) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{17, 7}
}

func (x *SearchRPC_FacetQueryRequest) GetSource() string {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return ""
}

func (x *SearchRPC_FacetQueryRequest) GetQuery() string {
	if x != nil && x.Query != nil {
		return *x.Query
	}
	return ""
}

func (x *SearchRPC_FacetQueryRequest) GetFacetColumn() []*SearchRPC_FacetQueryRequest_InputFacetColumn {
	if x != nil {
		return x.FacetColumn
	}
	return nil
}

func (x *SearchRPC_FacetQueryRequest) GetDefaultColumn() string {
	if x != nil && x.DefaultColumn != nil {
		return *x.DefaultColumn
	}
	return ""
}

func (x *SearchRPC_FacetQueryRequest) GetNum() int64 {
	if x != nil && x.Num != nil {
		return *x.Num
	}
	return 0
}

func (x *SearchRPC_FacetQueryRequest) GetOpts() []*Property {
	if x != nil {
		return x.Opts
	}
	return nil
}

func (x *SearchRPC_FacetQueryRequest) GetTimeout() int64 {
	if x != nil && x.Timeout != nil {
		return *x.Timeout
	}
	return 0
}

// mixed search
type SearchRPC_MixedSearchRecordRequest struct {
	state         protoimpl.MessageState           `protogen:"open.v1"`
	Source        *string                          `protobuf:"bytes,1,req,name=source" json:"source,omitempty"`
	Query         []*SearchRPC_SearchRecordRequest `protobuf:"bytes,2,rep,name=query" json:"query,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchRPC_MixedSearchRecordRequest) Reset() {
	*x = SearchRPC_MixedSearchRecordRequest{}
	mi := &file_rpc_api_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchRPC_MixedSearchRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRPC_MixedSearchRecordRequest) ProtoMessage() {}

func (x *SearchRPC_MixedSearchRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRPC_MixedSearchRecordRequest.ProtoReflect.Descriptor instead.
func (*SearchRPC_MixedSearchRecordRequest) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{17, 8}
}

func (x *SearchRPC_MixedSearchRecordRequest) GetSource() string {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return ""
}

func (x *SearchRPC_MixedSearchRecordRequest) GetQuery() []*SearchRPC_SearchRecordRequest {
	if x != nil {
		return x.Query
	}
	return nil
}

type SearchRPC_FacetQueryRequest_InputFacetColumn struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Name             *string                `protobuf:"bytes,1,req,name=name" json:"name,omitempty"`
	StatColumn       *string                `protobuf:"bytes,2,req,name=statColumn" json:"statColumn,omitempty"`
	Function         *string                `protobuf:"bytes,3,req,name=function" json:"function,omitempty"`
	MaxResultsetSize *int32                 `protobuf:"varint,4,opt,name=maxResultsetSize" json:"maxResultsetSize,omitempty"`
	Facetlabels      *string                `protobuf:"bytes,5,opt,name=facetlabels" json:"facetlabels,omitempty"`
	Opts             []*Property            `protobuf:"bytes,6,rep,name=opts" json:"opts,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *SearchRPC_FacetQueryRequest_InputFacetColumn) Reset() {
	*x = SearchRPC_FacetQueryRequest_InputFacetColumn{}
	mi := &file_rpc_api_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchRPC_FacetQueryRequest_InputFacetColumn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRPC_FacetQueryRequest_InputFacetColumn) ProtoMessage() {}

func (x *SearchRPC_FacetQueryRequest_InputFacetColumn) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRPC_FacetQueryRequest_InputFacetColumn.ProtoReflect.Descriptor instead.
func (*SearchRPC_FacetQueryRequest_InputFacetColumn) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{17, 7, 0}
}

func (x *SearchRPC_FacetQueryRequest_InputFacetColumn) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *SearchRPC_FacetQueryRequest_InputFacetColumn) GetStatColumn() string {
	if x != nil && x.StatColumn != nil {
		return *x.StatColumn
	}
	return ""
}

func (x *SearchRPC_FacetQueryRequest_InputFacetColumn) GetFunction() string {
	if x != nil && x.Function != nil {
		return *x.Function
	}
	return ""
}

func (x *SearchRPC_FacetQueryRequest_InputFacetColumn) GetMaxResultsetSize() int32 {
	if x != nil && x.MaxResultsetSize != nil {
		return *x.MaxResultsetSize
	}
	return 0
}

func (x *SearchRPC_FacetQueryRequest_InputFacetColumn) GetFacetlabels() string {
	if x != nil && x.Facetlabels != nil {
		return *x.Facetlabels
	}
	return ""
}

func (x *SearchRPC_FacetQueryRequest_InputFacetColumn) GetOpts() []*Property {
	if x != nil {
		return x.Opts
	}
	return nil
}

type SchemaRPC_ColumnInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          *string                `protobuf:"bytes,1,req,name=name" json:"name,omitempty"`
	Sname         *string                `protobuf:"bytes,2,opt,name=sname" json:"sname,omitempty"`
	Type          *int32                 `protobuf:"varint,3,req,name=type" json:"type,omitempty"`
	Property      []*Property            `protobuf:"bytes,4,rep,name=property" json:"property,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SchemaRPC_ColumnInfo) Reset() {
	*x = SchemaRPC_ColumnInfo{}
	mi := &file_rpc_api_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SchemaRPC_ColumnInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SchemaRPC_ColumnInfo) ProtoMessage() {}

func (x *SchemaRPC_ColumnInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SchemaRPC_ColumnInfo.ProtoReflect.Descriptor instead.
func (*SchemaRPC_ColumnInfo) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{18, 0}
}

func (x *SchemaRPC_ColumnInfo) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *SchemaRPC_ColumnInfo) GetSname() string {
	if x != nil && x.Sname != nil {
		return *x.Sname
	}
	return ""
}

func (x *SchemaRPC_ColumnInfo) GetType() int32 {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return 0
}

func (x *SchemaRPC_ColumnInfo) GetProperty() []*Property {
	if x != nil {
		return x.Property
	}
	return nil
}

type SchemaRPC_DatabaseInfo struct {
	state           protoimpl.MessageState  `protogen:"open.v1"`
	Name            *string                 `protobuf:"bytes,1,req,name=name" json:"name,omitempty"`
	Mode            *int32                  `protobuf:"varint,2,req,name=mode" json:"mode,omitempty"`
	Type            *int32                  `protobuf:"varint,3,req,name=type" json:"type,omitempty"`
	Policy          *int32                  `protobuf:"varint,4,opt,name=policy" json:"policy,omitempty"`
	Columns         []*SchemaRPC_ColumnInfo `protobuf:"bytes,5,rep,name=columns" json:"columns,omitempty"`
	DefSearchColumn *string                 `protobuf:"bytes,6,opt,name=defSearchColumn" json:"defSearchColumn,omitempty"`
	UniqueColumn    *string                 `protobuf:"bytes,7,opt,name=uniqueColumn" json:"uniqueColumn,omitempty"`
	Property        []*Property             `protobuf:"bytes,8,rep,name=property" json:"property,omitempty"` //key=runtime.xxx.yyy, 表示随时可修改,不影响已有的数据.
	SplitColumn     *string                 `protobuf:"bytes,9,opt,name=splitColumn" json:"splitColumn,omitempty"`
	Splitter        *string                 `protobuf:"bytes,10,opt,name=splitter" json:"splitter,omitempty"`
	SplitParams     []*Property             `protobuf:"bytes,11,rep,name=splitParams" json:"splitParams,omitempty"`
	EngineType      *string                 `protobuf:"bytes,12,opt,name=engineType" json:"engineType,omitempty"`
	ParserName      *string                 `protobuf:"bytes,13,opt,name=parserName" json:"parserName,omitempty"`
	RepSubNum       *int32                  `protobuf:"varint,14,opt,name=repSubNum" json:"repSubNum,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *SchemaRPC_DatabaseInfo) Reset() {
	*x = SchemaRPC_DatabaseInfo{}
	mi := &file_rpc_api_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SchemaRPC_DatabaseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SchemaRPC_DatabaseInfo) ProtoMessage() {}

func (x *SchemaRPC_DatabaseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SchemaRPC_DatabaseInfo.ProtoReflect.Descriptor instead.
func (*SchemaRPC_DatabaseInfo) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{18, 1}
}

func (x *SchemaRPC_DatabaseInfo) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *SchemaRPC_DatabaseInfo) GetMode() int32 {
	if x != nil && x.Mode != nil {
		return *x.Mode
	}
	return 0
}

func (x *SchemaRPC_DatabaseInfo) GetType() int32 {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return 0
}

func (x *SchemaRPC_DatabaseInfo) GetPolicy() int32 {
	if x != nil && x.Policy != nil {
		return *x.Policy
	}
	return 0
}

func (x *SchemaRPC_DatabaseInfo) GetColumns() []*SchemaRPC_ColumnInfo {
	if x != nil {
		return x.Columns
	}
	return nil
}

func (x *SchemaRPC_DatabaseInfo) GetDefSearchColumn() string {
	if x != nil && x.DefSearchColumn != nil {
		return *x.DefSearchColumn
	}
	return ""
}

func (x *SchemaRPC_DatabaseInfo) GetUniqueColumn() string {
	if x != nil && x.UniqueColumn != nil {
		return *x.UniqueColumn
	}
	return ""
}

func (x *SchemaRPC_DatabaseInfo) GetProperty() []*Property {
	if x != nil {
		return x.Property
	}
	return nil
}

func (x *SchemaRPC_DatabaseInfo) GetSplitColumn() string {
	if x != nil && x.SplitColumn != nil {
		return *x.SplitColumn
	}
	return ""
}

func (x *SchemaRPC_DatabaseInfo) GetSplitter() string {
	if x != nil && x.Splitter != nil {
		return *x.Splitter
	}
	return ""
}

func (x *SchemaRPC_DatabaseInfo) GetSplitParams() []*Property {
	if x != nil {
		return x.SplitParams
	}
	return nil
}

func (x *SchemaRPC_DatabaseInfo) GetEngineType() string {
	if x != nil && x.EngineType != nil {
		return *x.EngineType
	}
	return ""
}

func (x *SchemaRPC_DatabaseInfo) GetParserName() string {
	if x != nil && x.ParserName != nil {
		return *x.ParserName
	}
	return ""
}

func (x *SchemaRPC_DatabaseInfo) GetRepSubNum() int32 {
	if x != nil && x.RepSubNum != nil {
		return *x.RepSubNum
	}
	return 0
}

type SchemaRPC_CreateDBRequest struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Db            *SchemaRPC_DatabaseInfo `protobuf:"bytes,1,req,name=db" json:"db,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SchemaRPC_CreateDBRequest) Reset() {
	*x = SchemaRPC_CreateDBRequest{}
	mi := &file_rpc_api_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SchemaRPC_CreateDBRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SchemaRPC_CreateDBRequest) ProtoMessage() {}

func (x *SchemaRPC_CreateDBRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SchemaRPC_CreateDBRequest.ProtoReflect.Descriptor instead.
func (*SchemaRPC_CreateDBRequest) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{18, 2}
}

func (x *SchemaRPC_CreateDBRequest) GetDb() *SchemaRPC_DatabaseInfo {
	if x != nil {
		return x.Db
	}
	return nil
}

type SchemaRPC_CopyDBRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          *string                `protobuf:"bytes,1,req,name=name" json:"name,omitempty"`
	SourceColumns *string                `protobuf:"bytes,2,req,name=sourceColumns" json:"sourceColumns,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SchemaRPC_CopyDBRequest) Reset() {
	*x = SchemaRPC_CopyDBRequest{}
	mi := &file_rpc_api_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SchemaRPC_CopyDBRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SchemaRPC_CopyDBRequest) ProtoMessage() {}

func (x *SchemaRPC_CopyDBRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SchemaRPC_CopyDBRequest.ProtoReflect.Descriptor instead.
func (*SchemaRPC_CopyDBRequest) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{18, 3}
}

func (x *SchemaRPC_CopyDBRequest) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *SchemaRPC_CopyDBRequest) GetSourceColumns() string {
	if x != nil && x.SourceColumns != nil {
		return *x.SourceColumns
	}
	return ""
}

type SchemaRPC_RenameColumnRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DbName        *string                `protobuf:"bytes,1,req,name=dbName" json:"dbName,omitempty"`
	OldName       *string                `protobuf:"bytes,2,req,name=oldName" json:"oldName,omitempty"`
	NewName       *string                `protobuf:"bytes,3,req,name=newName" json:"newName,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SchemaRPC_RenameColumnRequest) Reset() {
	*x = SchemaRPC_RenameColumnRequest{}
	mi := &file_rpc_api_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SchemaRPC_RenameColumnRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SchemaRPC_RenameColumnRequest) ProtoMessage() {}

func (x *SchemaRPC_RenameColumnRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SchemaRPC_RenameColumnRequest.ProtoReflect.Descriptor instead.
func (*SchemaRPC_RenameColumnRequest) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{18, 4}
}

func (x *SchemaRPC_RenameColumnRequest) GetDbName() string {
	if x != nil && x.DbName != nil {
		return *x.DbName
	}
	return ""
}

func (x *SchemaRPC_RenameColumnRequest) GetOldName() string {
	if x != nil && x.OldName != nil {
		return *x.OldName
	}
	return ""
}

func (x *SchemaRPC_RenameColumnRequest) GetNewName() string {
	if x != nil && x.NewName != nil {
		return *x.NewName
	}
	return ""
}

type SchemaRPC_CommitRequest struct {
	state         protoimpl.MessageState                 `protogen:"open.v1"`
	DbName        *string                                `protobuf:"bytes,1,req,name=dbName" json:"dbName,omitempty"`
	Db            *SchemaRPC_DatabaseInfo                `protobuf:"bytes,2,opt,name=db" json:"db,omitempty"`
	Adds          []*SchemaRPC_ColumnInfo                `protobuf:"bytes,3,rep,name=adds" json:"adds,omitempty"`
	Deletes       []string                               `protobuf:"bytes,4,rep,name=deletes" json:"deletes,omitempty"`
	Alters        []*SchemaRPC_CommitRequest_AlterColumn `protobuf:"bytes,5,rep,name=alters" json:"alters,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SchemaRPC_CommitRequest) Reset() {
	*x = SchemaRPC_CommitRequest{}
	mi := &file_rpc_api_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SchemaRPC_CommitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SchemaRPC_CommitRequest) ProtoMessage() {}

func (x *SchemaRPC_CommitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SchemaRPC_CommitRequest.ProtoReflect.Descriptor instead.
func (*SchemaRPC_CommitRequest) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{18, 5}
}

func (x *SchemaRPC_CommitRequest) GetDbName() string {
	if x != nil && x.DbName != nil {
		return *x.DbName
	}
	return ""
}

func (x *SchemaRPC_CommitRequest) GetDb() *SchemaRPC_DatabaseInfo {
	if x != nil {
		return x.Db
	}
	return nil
}

func (x *SchemaRPC_CommitRequest) GetAdds() []*SchemaRPC_ColumnInfo {
	if x != nil {
		return x.Adds
	}
	return nil
}

func (x *SchemaRPC_CommitRequest) GetDeletes() []string {
	if x != nil {
		return x.Deletes
	}
	return nil
}

func (x *SchemaRPC_CommitRequest) GetAlters() []*SchemaRPC_CommitRequest_AlterColumn {
	if x != nil {
		return x.Alters
	}
	return nil
}

type SchemaRPC_ListDBRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DbName        *string                `protobuf:"bytes,1,req,name=dbName" json:"dbName,omitempty"`
	Property      []*Property            `protobuf:"bytes,2,rep,name=property" json:"property,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SchemaRPC_ListDBRequest) Reset() {
	*x = SchemaRPC_ListDBRequest{}
	mi := &file_rpc_api_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SchemaRPC_ListDBRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SchemaRPC_ListDBRequest) ProtoMessage() {}

func (x *SchemaRPC_ListDBRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SchemaRPC_ListDBRequest.ProtoReflect.Descriptor instead.
func (*SchemaRPC_ListDBRequest) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{18, 6}
}

func (x *SchemaRPC_ListDBRequest) GetDbName() string {
	if x != nil && x.DbName != nil {
		return *x.DbName
	}
	return ""
}

func (x *SchemaRPC_ListDBRequest) GetProperty() []*Property {
	if x != nil {
		return x.Property
	}
	return nil
}

type SchemaRPC_ListDBResponse struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Db            []*SchemaRPC_DatabaseInfo `protobuf:"bytes,1,rep,name=db" json:"db,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SchemaRPC_ListDBResponse) Reset() {
	*x = SchemaRPC_ListDBResponse{}
	mi := &file_rpc_api_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SchemaRPC_ListDBResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SchemaRPC_ListDBResponse) ProtoMessage() {}

func (x *SchemaRPC_ListDBResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SchemaRPC_ListDBResponse.ProtoReflect.Descriptor instead.
func (*SchemaRPC_ListDBResponse) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{18, 7}
}

func (x *SchemaRPC_ListDBResponse) GetDb() []*SchemaRPC_DatabaseInfo {
	if x != nil {
		return x.Db
	}
	return nil
}

type SchemaRPC_CommitRequest_AlterColumn struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          *string                `protobuf:"bytes,1,req,name=name" json:"name,omitempty"`
	NewValue      *SchemaRPC_ColumnInfo  `protobuf:"bytes,2,req,name=newValue" json:"newValue,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SchemaRPC_CommitRequest_AlterColumn) Reset() {
	*x = SchemaRPC_CommitRequest_AlterColumn{}
	mi := &file_rpc_api_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SchemaRPC_CommitRequest_AlterColumn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SchemaRPC_CommitRequest_AlterColumn) ProtoMessage() {}

func (x *SchemaRPC_CommitRequest_AlterColumn) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SchemaRPC_CommitRequest_AlterColumn.ProtoReflect.Descriptor instead.
func (*SchemaRPC_CommitRequest_AlterColumn) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{18, 5, 0}
}

func (x *SchemaRPC_CommitRequest_AlterColumn) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *SchemaRPC_CommitRequest_AlterColumn) GetNewValue() *SchemaRPC_ColumnInfo {
	if x != nil {
		return x.NewValue
	}
	return nil
}

type Report_RecordItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RecordIndex   *int64                 `protobuf:"varint,1,req,name=recordIndex" json:"recordIndex,omitempty"`
	Item          []string               `protobuf:"bytes,2,rep,name=item" json:"item,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Report_RecordItem) Reset() {
	*x = Report_RecordItem{}
	mi := &file_rpc_api_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Report_RecordItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Report_RecordItem) ProtoMessage() {}

func (x *Report_RecordItem) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Report_RecordItem.ProtoReflect.Descriptor instead.
func (*Report_RecordItem) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{19, 0}
}

func (x *Report_RecordItem) GetRecordIndex() int64 {
	if x != nil && x.RecordIndex != nil {
		return *x.RecordIndex
	}
	return 0
}

func (x *Report_RecordItem) GetItem() []string {
	if x != nil {
		return x.Item
	}
	return nil
}

type Report_RecordPro struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RecordIndex   *int64                 `protobuf:"varint,1,req,name=recordIndex" json:"recordIndex,omitempty"`
	Type          *int32                 `protobuf:"varint,2,req,name=type" json:"type,omitempty"`
	Message       *string                `protobuf:"bytes,3,req,name=message" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Report_RecordPro) Reset() {
	*x = Report_RecordPro{}
	mi := &file_rpc_api_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Report_RecordPro) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Report_RecordPro) ProtoMessage() {}

func (x *Report_RecordPro) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Report_RecordPro.ProtoReflect.Descriptor instead.
func (*Report_RecordPro) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{19, 1}
}

func (x *Report_RecordPro) GetRecordIndex() int64 {
	if x != nil && x.RecordIndex != nil {
		return *x.RecordIndex
	}
	return 0
}

func (x *Report_RecordPro) GetType() int32 {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return 0
}

func (x *Report_RecordPro) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

type Report_DelErrorPro struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Uuid          *string                `protobuf:"bytes,1,req,name=uuid" json:"uuid,omitempty"`
	Type          *int32                 `protobuf:"varint,2,req,name=type" json:"type,omitempty"`
	Message       *string                `protobuf:"bytes,3,req,name=message" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Report_DelErrorPro) Reset() {
	*x = Report_DelErrorPro{}
	mi := &file_rpc_api_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Report_DelErrorPro) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Report_DelErrorPro) ProtoMessage() {}

func (x *Report_DelErrorPro) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Report_DelErrorPro.ProtoReflect.Descriptor instead.
func (*Report_DelErrorPro) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{19, 2}
}

func (x *Report_DelErrorPro) GetUuid() string {
	if x != nil && x.Uuid != nil {
		return *x.Uuid
	}
	return ""
}

func (x *Report_DelErrorPro) GetType() int32 {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return 0
}

func (x *Report_DelErrorPro) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

type SuggestRPC_SuggestRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Query         *string                `protobuf:"bytes,1,req,name=query" json:"query,omitempty"`
	Num           *int32                 `protobuf:"varint,2,req,name=num" json:"num,omitempty"`
	Type          *string                `protobuf:"bytes,3,opt,name=type" json:"type,omitempty"`
	Opts          []*Property            `protobuf:"bytes,4,rep,name=opts" json:"opts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SuggestRPC_SuggestRequest) Reset() {
	*x = SuggestRPC_SuggestRequest{}
	mi := &file_rpc_api_proto_msgTypes[66]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SuggestRPC_SuggestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuggestRPC_SuggestRequest) ProtoMessage() {}

func (x *SuggestRPC_SuggestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[66]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuggestRPC_SuggestRequest.ProtoReflect.Descriptor instead.
func (*SuggestRPC_SuggestRequest) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{22, 0}
}

func (x *SuggestRPC_SuggestRequest) GetQuery() string {
	if x != nil && x.Query != nil {
		return *x.Query
	}
	return ""
}

func (x *SuggestRPC_SuggestRequest) GetNum() int32 {
	if x != nil && x.Num != nil {
		return *x.Num
	}
	return 0
}

func (x *SuggestRPC_SuggestRequest) GetType() string {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return ""
}

func (x *SuggestRPC_SuggestRequest) GetOpts() []*Property {
	if x != nil {
		return x.Opts
	}
	return nil
}

type SuggestRPC_SuggestResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Suggest       []string               `protobuf:"bytes,1,rep,name=suggest" json:"suggest,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SuggestRPC_SuggestResponse) Reset() {
	*x = SuggestRPC_SuggestResponse{}
	mi := &file_rpc_api_proto_msgTypes[67]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SuggestRPC_SuggestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuggestRPC_SuggestResponse) ProtoMessage() {}

func (x *SuggestRPC_SuggestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[67]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuggestRPC_SuggestResponse.ProtoReflect.Descriptor instead.
func (*SuggestRPC_SuggestResponse) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{22, 1}
}

func (x *SuggestRPC_SuggestResponse) GetSuggest() []string {
	if x != nil {
		return x.Suggest
	}
	return nil
}

type PermissionRPC_TRSUpdateLicense struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SuccessNodes  *string                `protobuf:"bytes,1,opt,name=successNodes" json:"successNodes,omitempty"`
	FailedNodes   *string                `protobuf:"bytes,2,opt,name=failedNodes" json:"failedNodes,omitempty"`
	ErrorNodes    *string                `protobuf:"bytes,3,opt,name=errorNodes" json:"errorNodes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PermissionRPC_TRSUpdateLicense) Reset() {
	*x = PermissionRPC_TRSUpdateLicense{}
	mi := &file_rpc_api_proto_msgTypes[68]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PermissionRPC_TRSUpdateLicense) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PermissionRPC_TRSUpdateLicense) ProtoMessage() {}

func (x *PermissionRPC_TRSUpdateLicense) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[68]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PermissionRPC_TRSUpdateLicense.ProtoReflect.Descriptor instead.
func (*PermissionRPC_TRSUpdateLicense) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{26, 0}
}

func (x *PermissionRPC_TRSUpdateLicense) GetSuccessNodes() string {
	if x != nil && x.SuccessNodes != nil {
		return *x.SuccessNodes
	}
	return ""
}

func (x *PermissionRPC_TRSUpdateLicense) GetFailedNodes() string {
	if x != nil && x.FailedNodes != nil {
		return *x.FailedNodes
	}
	return ""
}

func (x *PermissionRPC_TRSUpdateLicense) GetErrorNodes() string {
	if x != nil && x.ErrorNodes != nil {
		return *x.ErrorNodes
	}
	return ""
}

type PermissionRPC_TRSUserRPC struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          *string                `protobuf:"bytes,1,req,name=name" json:"name,omitempty"`
	Enable        *bool                  `protobuf:"varint,2,req,name=enable,def=0" json:"enable,omitempty"`
	Selschema     *string                `protobuf:"bytes,3,opt,name=selschema" json:"selschema,omitempty"`
	Ownertype     *int32                 `protobuf:"varint,4,opt,name=ownertype,def=1" json:"ownertype,omitempty"`
	CONSOLE       *int32                 `protobuf:"varint,5,opt,name=CONSOLE,def=0" json:"CONSOLE,omitempty"`
	API           *int32                 `protobuf:"varint,6,opt,name=API,def=0" json:"API,omitempty"`
	Ipaddress     *string                `protobuf:"bytes,7,opt,name=ipaddress" json:"ipaddress,omitempty"`
	Createdate    *int64                 `protobuf:"varint,8,opt,name=createdate" json:"createdate,omitempty"`
	Updatedate    *int64                 `protobuf:"varint,9,opt,name=updatedate" json:"updatedate,omitempty"`
	Comment       *string                `protobuf:"bytes,10,opt,name=comment" json:"comment,omitempty"`
	WhiteIps      []string               `protobuf:"bytes,11,rep,name=whiteIps" json:"whiteIps,omitempty"`
	BlackIps      []string               `protobuf:"bytes,12,rep,name=blackIps" json:"blackIps,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

// Default values for PermissionRPC_TRSUserRPC fields.
const (
	Default_PermissionRPC_TRSUserRPC_Enable    = bool(false)
	Default_PermissionRPC_TRSUserRPC_Ownertype = int32(1)
	Default_PermissionRPC_TRSUserRPC_CONSOLE   = int32(0)
	Default_PermissionRPC_TRSUserRPC_API       = int32(0)
)

func (x *PermissionRPC_TRSUserRPC) Reset() {
	*x = PermissionRPC_TRSUserRPC{}
	mi := &file_rpc_api_proto_msgTypes[69]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PermissionRPC_TRSUserRPC) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PermissionRPC_TRSUserRPC) ProtoMessage() {}

func (x *PermissionRPC_TRSUserRPC) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[69]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PermissionRPC_TRSUserRPC.ProtoReflect.Descriptor instead.
func (*PermissionRPC_TRSUserRPC) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{26, 1}
}

func (x *PermissionRPC_TRSUserRPC) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *PermissionRPC_TRSUserRPC) GetEnable() bool {
	if x != nil && x.Enable != nil {
		return *x.Enable
	}
	return Default_PermissionRPC_TRSUserRPC_Enable
}

func (x *PermissionRPC_TRSUserRPC) GetSelschema() string {
	if x != nil && x.Selschema != nil {
		return *x.Selschema
	}
	return ""
}

func (x *PermissionRPC_TRSUserRPC) GetOwnertype() int32 {
	if x != nil && x.Ownertype != nil {
		return *x.Ownertype
	}
	return Default_PermissionRPC_TRSUserRPC_Ownertype
}

func (x *PermissionRPC_TRSUserRPC) GetCONSOLE() int32 {
	if x != nil && x.CONSOLE != nil {
		return *x.CONSOLE
	}
	return Default_PermissionRPC_TRSUserRPC_CONSOLE
}

func (x *PermissionRPC_TRSUserRPC) GetAPI() int32 {
	if x != nil && x.API != nil {
		return *x.API
	}
	return Default_PermissionRPC_TRSUserRPC_API
}

func (x *PermissionRPC_TRSUserRPC) GetIpaddress() string {
	if x != nil && x.Ipaddress != nil {
		return *x.Ipaddress
	}
	return ""
}

func (x *PermissionRPC_TRSUserRPC) GetCreatedate() int64 {
	if x != nil && x.Createdate != nil {
		return *x.Createdate
	}
	return 0
}

func (x *PermissionRPC_TRSUserRPC) GetUpdatedate() int64 {
	if x != nil && x.Updatedate != nil {
		return *x.Updatedate
	}
	return 0
}

func (x *PermissionRPC_TRSUserRPC) GetComment() string {
	if x != nil && x.Comment != nil {
		return *x.Comment
	}
	return ""
}

func (x *PermissionRPC_TRSUserRPC) GetWhiteIps() []string {
	if x != nil {
		return x.WhiteIps
	}
	return nil
}

func (x *PermissionRPC_TRSUserRPC) GetBlackIps() []string {
	if x != nil {
		return x.BlackIps
	}
	return nil
}

type PermissionRPC_UpdateTRSUserRequest struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Oldname       *string                   `protobuf:"bytes,1,req,name=oldname" json:"oldname,omitempty"`
	NewUser       *PermissionRPC_TRSUserRPC `protobuf:"bytes,2,req,name=newUser" json:"newUser,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PermissionRPC_UpdateTRSUserRequest) Reset() {
	*x = PermissionRPC_UpdateTRSUserRequest{}
	mi := &file_rpc_api_proto_msgTypes[70]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PermissionRPC_UpdateTRSUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PermissionRPC_UpdateTRSUserRequest) ProtoMessage() {}

func (x *PermissionRPC_UpdateTRSUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[70]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PermissionRPC_UpdateTRSUserRequest.ProtoReflect.Descriptor instead.
func (*PermissionRPC_UpdateTRSUserRequest) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{26, 2}
}

func (x *PermissionRPC_UpdateTRSUserRequest) GetOldname() string {
	if x != nil && x.Oldname != nil {
		return *x.Oldname
	}
	return ""
}

func (x *PermissionRPC_UpdateTRSUserRequest) GetNewUser() *PermissionRPC_TRSUserRPC {
	if x != nil {
		return x.NewUser
	}
	return nil
}

type PermissionRPC_TRSSchemaRPC struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          *string                `protobuf:"bytes,1,req,name=name" json:"name,omitempty"`
	Partnum       *int32                 `protobuf:"varint,2,req,name=partnum" json:"partnum,omitempty"`
	Readwrite     *bool                  `protobuf:"varint,3,req,name=readwrite,def=0" json:"readwrite,omitempty"`
	Nodelist      []string               `protobuf:"bytes,4,rep,name=nodelist" json:"nodelist,omitempty"`
	Partmain      []string               `protobuf:"bytes,5,rep,name=partmain" json:"partmain,omitempty"`
	Partcopy      []string               `protobuf:"bytes,6,rep,name=partcopy" json:"partcopy,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

// Default values for PermissionRPC_TRSSchemaRPC fields.
const (
	Default_PermissionRPC_TRSSchemaRPC_Readwrite = bool(false)
)

func (x *PermissionRPC_TRSSchemaRPC) Reset() {
	*x = PermissionRPC_TRSSchemaRPC{}
	mi := &file_rpc_api_proto_msgTypes[71]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PermissionRPC_TRSSchemaRPC) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PermissionRPC_TRSSchemaRPC) ProtoMessage() {}

func (x *PermissionRPC_TRSSchemaRPC) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[71]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PermissionRPC_TRSSchemaRPC.ProtoReflect.Descriptor instead.
func (*PermissionRPC_TRSSchemaRPC) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{26, 3}
}

func (x *PermissionRPC_TRSSchemaRPC) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *PermissionRPC_TRSSchemaRPC) GetPartnum() int32 {
	if x != nil && x.Partnum != nil {
		return *x.Partnum
	}
	return 0
}

func (x *PermissionRPC_TRSSchemaRPC) GetReadwrite() bool {
	if x != nil && x.Readwrite != nil {
		return *x.Readwrite
	}
	return Default_PermissionRPC_TRSSchemaRPC_Readwrite
}

func (x *PermissionRPC_TRSSchemaRPC) GetNodelist() []string {
	if x != nil {
		return x.Nodelist
	}
	return nil
}

func (x *PermissionRPC_TRSSchemaRPC) GetPartmain() []string {
	if x != nil {
		return x.Partmain
	}
	return nil
}

func (x *PermissionRPC_TRSSchemaRPC) GetPartcopy() []string {
	if x != nil {
		return x.Partcopy
	}
	return nil
}

type PermissionRPC_UpdateTRSSchemaRequest struct {
	state             protoimpl.MessageState      `protogen:"open.v1"`
	Redistributecheck *string                     `protobuf:"bytes,1,req,name=redistributecheck,def=on" json:"redistributecheck,omitempty"`
	NewSchema         *PermissionRPC_TRSSchemaRPC `protobuf:"bytes,2,req,name=newSchema" json:"newSchema,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

// Default values for PermissionRPC_UpdateTRSSchemaRequest fields.
const (
	Default_PermissionRPC_UpdateTRSSchemaRequest_Redistributecheck = string("on")
)

func (x *PermissionRPC_UpdateTRSSchemaRequest) Reset() {
	*x = PermissionRPC_UpdateTRSSchemaRequest{}
	mi := &file_rpc_api_proto_msgTypes[72]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PermissionRPC_UpdateTRSSchemaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PermissionRPC_UpdateTRSSchemaRequest) ProtoMessage() {}

func (x *PermissionRPC_UpdateTRSSchemaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[72]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PermissionRPC_UpdateTRSSchemaRequest.ProtoReflect.Descriptor instead.
func (*PermissionRPC_UpdateTRSSchemaRequest) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{26, 4}
}

func (x *PermissionRPC_UpdateTRSSchemaRequest) GetRedistributecheck() string {
	if x != nil && x.Redistributecheck != nil {
		return *x.Redistributecheck
	}
	return Default_PermissionRPC_UpdateTRSSchemaRequest_Redistributecheck
}

func (x *PermissionRPC_UpdateTRSSchemaRequest) GetNewSchema() *PermissionRPC_TRSSchemaRPC {
	if x != nil {
		return x.NewSchema
	}
	return nil
}

type PermissionRPC_GetTRSSchemaResponse struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Schemas       []*PermissionRPC_TRSSchemaRPC `protobuf:"bytes,1,rep,name=schemas" json:"schemas,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PermissionRPC_GetTRSSchemaResponse) Reset() {
	*x = PermissionRPC_GetTRSSchemaResponse{}
	mi := &file_rpc_api_proto_msgTypes[73]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PermissionRPC_GetTRSSchemaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PermissionRPC_GetTRSSchemaResponse) ProtoMessage() {}

func (x *PermissionRPC_GetTRSSchemaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_api_proto_msgTypes[73]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PermissionRPC_GetTRSSchemaResponse.ProtoReflect.Descriptor instead.
func (*PermissionRPC_GetTRSSchemaResponse) Descriptor() ([]byte, []int) {
	return file_rpc_api_proto_rawDescGZIP(), []int{26, 5}
}

func (x *PermissionRPC_GetTRSSchemaResponse) GetSchemas() []*PermissionRPC_TRSSchemaRPC {
	if x != nil {
		return x.Schemas
	}
	return nil
}

var File_rpc_api_proto protoreflect.FileDescriptor

var file_rpc_api_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x72, 0x70, 0x63, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x96, 0x01, 0x0a, 0x08, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x64, 0x35, 0x68, 0x61, 0x73, 0x68, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x64, 0x35, 0x68, 0x61, 0x73, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x73, 0x75, 0x62, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x75, 0x62, 0x45, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x73, 0x75, 0x62, 0x45, 0x6e, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x48, 0x6f, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x48, 0x6f, 0x73, 0x74, 0x22, 0x7a, 0x0a, 0x08, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x6f, 0x63, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x64, 0x6f, 0x63, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x02, 0x28, 0x03, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x75, 0x62, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73,
	0x75, 0x62, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x64, 0x62, 0x48, 0x6f, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x62,
	0x48, 0x6f, 0x73, 0x74, 0x22, 0x99, 0x01, 0x0a, 0x09, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x46, 0x69,
	0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x73, 0x44, 0x69, 0x72, 0x18,
	0x02, 0x20, 0x02, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x44, 0x69, 0x72, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x75, 0x66, 0x66, 0x69, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x75,
	0x66, 0x66, 0x69, 0x78, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1e,
	0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x72, 0x69, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x69,
	0x22, 0x47, 0x0a, 0x0f, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x66, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x66, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x22, 0x2c, 0x0a, 0x08, 0x46, 0x69, 0x6c,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x46, 0x69, 0x6c, 0x65,
	0x52, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x22, 0xec, 0x03, 0x0a, 0x0b, 0x49, 0x6e, 0x70, 0x75,
	0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x05, 0x62, 0x6f, 0x6f,
	0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x3a, 0x01, 0x31, 0x52, 0x05, 0x62, 0x6f, 0x6f,
	0x73, 0x74, 0x12, 0x32, 0x0a, 0x07, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x52, 0x07, 0x63,
	0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x03, 0x74, 0x61, 0x67, 0x1a, 0xeb, 0x02, 0x0a, 0x0b, 0x49, 0x6e, 0x70,
	0x75, 0x74, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x12, 0x17, 0x0a, 0x05, 0x62, 0x6f, 0x6f, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x3a, 0x01, 0x31, 0x52, 0x05, 0x62, 0x6f, 0x6f, 0x73,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x0a, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x62, 0x79, 0x74, 0x65, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x0a, 0x62, 0x79, 0x74, 0x65, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1f, 0x0a,
	0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x46,
	0x69, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x75, 0x6c, 0x6c,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x6e, 0x75, 0x6c,
	0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x02, 0x52, 0x0b, 0x76, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x3c, 0x0a, 0x0c, 0x72, 0x65, 0x64, 0x75,
	0x6e, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x49, 0x6e, 0x70,
	0x75, 0x74, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x52, 0x0c, 0x72, 0x65, 0x64, 0x75, 0x6e, 0x43,
	0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73, 0x22, 0xf0, 0x03, 0x0a, 0x0c, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x02, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x6c,
	0x65, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x72, 0x65,
	0x6c, 0x65, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x63, 0x6f, 0x6c, 0x75, 0x6d,
	0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x43, 0x6f,
	0x6c, 0x75, 0x6d, 0x6e, 0x52, 0x07, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73, 0x12, 0x16, 0x0a,
	0x06, 0x64, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64,
	0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x12, 0x28, 0x0a, 0x0f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x1a, 0x99, 0x02,
	0x0a, 0x0c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x73, 0x74, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x73, 0x74, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x66, 0x6c,
	0x6f, 0x61, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a,
	0x66, 0x6c, 0x6f, 0x61, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x62, 0x79,
	0x74, 0x65, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a,
	0x62, 0x79, 0x74, 0x65, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1f, 0x0a, 0x05, 0x66, 0x69,
	0x6c, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x46, 0x69, 0x6c, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x70,
	0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x70, 0x61,
	0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x56, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x02, 0x52, 0x0c, 0x66, 0x6c, 0x6f,
	0x61, 0x74, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x62, 0x79, 0x74,
	0x65, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x62,
	0x79, 0x74, 0x65, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x22, 0x32, 0x0a, 0x08, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x02,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x02, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x35, 0x0a,
	0x0c, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x25, 0x0a,
	0x08, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x09, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x79, 0x22, 0x3a, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x54, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x29, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x4c, 0x69, 0x73, 0x74,
	0x22, 0x1e, 0x0a, 0x0a, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x73, 0x74, 0x72, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x73, 0x74, 0x72,
	0x22, 0xcf, 0x01, 0x0a, 0x0b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x02,
	0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x62, 0x79, 0x74, 0x65, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x62, 0x79, 0x74, 0x65, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x22, 0x73, 0x0a, 0x0b, 0x4e, 0x75, 0x6d, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x02,
	0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x64, 0x6f, 0x75, 0x62,
	0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x37, 0x0a, 0x0d, 0x46, 0x6c, 0x6f, 0x61, 0x74,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x22, 0x37, 0x0a, 0x0d, 0x42, 0x79, 0x74, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x02,
	0x28, 0x0c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x3f, 0x0a, 0x11, 0x46, 0x6c, 0x6f,
	0x61, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2a,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x0e, 0x2e, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x52, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x22, 0x91, 0x13, 0x0a, 0x08, 0x4d,
	0x61, 0x69, 0x6e, 0x74, 0x52, 0x50, 0x43, 0x1a, 0xc9, 0x01, 0x0a, 0x13, 0x49, 0x6e, 0x73, 0x65,
	0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x09, 0x6f, 0x76, 0x65, 0x72, 0x77,
	0x72, 0x69, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x04, 0x74, 0x72, 0x75, 0x65,
	0x52, 0x09, 0x6f, 0x76, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x06, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x06, 0x72, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x12, 0x23, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x09, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x07, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2b, 0x0a, 0x0d, 0x73, 0x6b, 0x69, 0x70, 0x46, 0x69,
	0x6c, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66,
	0x61, 0x6c, 0x73, 0x65, 0x52, 0x0d, 0x73, 0x6b, 0x69, 0x70, 0x46, 0x69, 0x6c, 0x65, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x1a, 0xc7, 0x01, 0x0a, 0x11, 0x4c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x22, 0x0a, 0x09, 0x6f, 0x76, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x3a, 0x04, 0x74, 0x72, 0x75, 0x65, 0x52, 0x09, 0x6f, 0x76, 0x65, 0x72,
	0x77, 0x72, 0x69, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x06, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x06, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x23, 0x0a, 0x07, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x2b, 0x0a, 0x0d, 0x73, 0x6b, 0x69, 0x70, 0x46, 0x69, 0x6c, 0x65, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x0d,
	0x73, 0x6b, 0x69, 0x70, 0x46, 0x69, 0x6c, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x1a, 0x78, 0x0a,
	0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01,
	0x20, 0x02, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65,
	0x72, 0x79, 0x12, 0x23, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x07,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0xc9, 0x01, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x09, 0x6f, 0x76, 0x65, 0x72, 0x77,
	0x72, 0x69, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x04, 0x74, 0x72, 0x75, 0x65,
	0x52, 0x09, 0x6f, 0x76, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x06, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x06, 0x72, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x12, 0x23, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x09, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x07, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2b, 0x0a, 0x0d, 0x73, 0x6b, 0x69, 0x70, 0x46, 0x69,
	0x6c, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66,
	0x61, 0x6c, 0x73, 0x65, 0x52, 0x0d, 0x73, 0x6b, 0x69, 0x70, 0x46, 0x69, 0x6c, 0x65, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x1a, 0x2f, 0x0a, 0x0e, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x04,
	0x69, 0x6e, 0x66, 0x6f, 0x1a, 0x7a, 0x0a, 0x0c, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01,
	0x20, 0x02, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2d, 0x0a, 0x06,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x4d,
	0x61, 0x69, 0x6e, 0x74, 0x52, 0x50, 0x43, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x07, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x1a, 0x7c, 0x0a, 0x14, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x65, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x12, 0x22, 0x0a, 0x09, 0x77, 0x61, 0x69, 0x74, 0x46, 0x6c, 0x75, 0x73, 0x68, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x3a, 0x04, 0x74, 0x72, 0x75, 0x65, 0x52, 0x09, 0x77, 0x61, 0x69, 0x74, 0x46,
	0x6c, 0x75, 0x73, 0x68, 0x12, 0x28, 0x0a, 0x0c, 0x77, 0x61, 0x69, 0x74, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x04, 0x74, 0x72, 0x75, 0x65,
	0x52, 0x0c, 0x77, 0x61, 0x69, 0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x65, 0x72, 0x1a, 0x8c,
	0x02, 0x0a, 0x13, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x44, 0x0a, 0x0d, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x50, 0x43, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0d, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x07,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x44, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x44, 0x1a, 0xab, 0x01,
	0x0a, 0x14, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x25,
	0x0a, 0x06, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x06, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x49,
	0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x49,
	0x44, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x78, 0x4e, 0x75, 0x6d,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x78,
	0x4e, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x1a, 0x48, 0x0a, 0x10, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28,
	0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x66,
	0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x66, 0x4c,
	0x65, 0x6e, 0x67, 0x74, 0x68, 0x1a, 0xaa, 0x02, 0x0a, 0x0c, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x44, 0x0a, 0x0d, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x50, 0x43, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0d, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x36, 0x0a, 0x05,
	0x73, 0x75, 0x62, 0x44, 0x42, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x4d, 0x61,
	0x69, 0x6e, 0x74, 0x52, 0x50, 0x43, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x2e, 0x53, 0x75, 0x62, 0x44, 0x42, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x73,
	0x75, 0x62, 0x44, 0x42, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x09,
	0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x1a, 0x57, 0x0a, 0x09, 0x53, 0x75, 0x62,
	0x44, 0x42, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x75, 0x62, 0x49, 0x44, 0x18,
	0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x05, 0x73, 0x75, 0x62, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x6e, 0x6f, 0x64, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x6f, 0x64, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x1a, 0x9c, 0x03, 0x0a, 0x10, 0x43, 0x6f, 0x6c, 0x6c, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0d, 0x68, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x50, 0x43, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x52, 0x0d, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x44, 0x0a, 0x0d, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x52, 0x50, 0x43, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0d, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x20, 0x0a, 0x0b,
	0x63, 0x6f, 0x6c, 0x6c, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x63, 0x6f, 0x6c, 0x6c, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x1a, 0x88, 0x01, 0x0a, 0x07, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x74, 0x4e, 0x4f, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x05, 0x52, 0x06, 0x70, 0x61, 0x72, 0x74, 0x4e, 0x4f, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61,
	0x72, 0x74, 0x49, 0x44, 0x18, 0x03, 0x20, 0x03, 0x28, 0x05, 0x52, 0x06, 0x70, 0x61, 0x72, 0x74,
	0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x75, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x07, 0x72, 0x75, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x07,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x1a, 0x69, 0x0a, 0x11, 0x43, 0x6f, 0x6c, 0x6c, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x6c, 0x6c, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6c,
	0x6c, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x02,
	0x28, 0x03, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x22, 0x40, 0x0a, 0x0b,
	0x41, 0x64, 0x6d, 0x69, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0a, 0x0a, 0x06, 0x45,
	0x4e, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x49, 0x53, 0x41, 0x42,
	0x4c, 0x45, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x4f, 0x4d, 0x4d, 0x49, 0x54, 0x10, 0x03,
	0x12, 0x0c, 0x0a, 0x08, 0x4f, 0x50, 0x54, 0x49, 0x4d, 0x49, 0x5a, 0x45, 0x10, 0x04, 0x22, 0x94,
	0x13, 0x0a, 0x09, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x50, 0x43, 0x1a, 0xaa, 0x03, 0x0a,
	0x13, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01,
	0x20, 0x02, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x02, 0x20, 0x02, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65,
	0x72, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x74, 0x61, 0x74, 0x69, 0x63, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x61, 0x74, 0x69, 0x63, 0x73, 0x12, 0x24, 0x0a, 0x0d,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x6c, 0x75,
	0x6d, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c,
	0x75, 0x6d, 0x6e, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x6c,
	0x75, 0x6d, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6e, 0x75, 0x6d,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x75, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x63, 0x75, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x51, 0x75,
	0x69, 0x63, 0x6b, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0b, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x22, 0x0a, 0x0c,
	0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0c, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x55, 0x6e, 0x69, 0x74,
	0x12, 0x1d, 0x0a, 0x04, 0x6f, 0x70, 0x74, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09,
	0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x04, 0x6f, 0x70, 0x74, 0x73, 0x12,
	0x18, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x1a, 0xfc, 0x02, 0x0a, 0x14, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x69, 0x64, 0x12, 0x1e,
	0x0a, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x02,
	0x28, 0x05, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x02,
	0x28, 0x05, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x6e, 0x75, 0x6d, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x02, 0x28, 0x04,
	0x52, 0x08, 0x6e, 0x75, 0x6d, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x18, 0x05, 0x20, 0x02, 0x28, 0x04, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x12, 0x25, 0x0a, 0x06, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x0d, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x06, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x31, 0x0a, 0x0e, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x09, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x0e, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x27, 0x0a, 0x09, 0x64, 0x62,
	0x48, 0x69, 0x74, 0x73, 0x4d, 0x61, 0x70, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x09, 0x64, 0x62, 0x48, 0x69, 0x74, 0x73,
	0x4d, 0x61, 0x70, 0x12, 0x1d, 0x0a, 0x04, 0x6f, 0x70, 0x74, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x09, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x04, 0x6f, 0x70,
	0x74, 0x73, 0x12, 0x34, 0x0a, 0x0d, 0x73, 0x75, 0x62, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x79, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x0d, 0x73, 0x75, 0x62, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x1a, 0xdd, 0x01, 0x0a, 0x14, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65,
	0x72, 0x79, 0x18, 0x02, 0x20, 0x02, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12,
	0x26, 0x0a, 0x0e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x75, 0x6d,
	0x6e, 0x18, 0x03, 0x20, 0x02, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x12, 0x10, 0x0a,
	0x03, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12,
	0x1d, 0x0a, 0x04, 0x6f, 0x70, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x04, 0x6f, 0x70, 0x74, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x1a, 0xf7, 0x01, 0x0a, 0x15, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x69, 0x64, 0x12, 0x1e,
	0x0a, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x02,
	0x28, 0x05, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x02,
	0x28, 0x05, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x24,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c,
	0x2e, 0x4e, 0x75, 0x6d, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x1d, 0x0a, 0x04, 0x6f, 0x70, 0x74, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52,
	0x04, 0x6f, 0x70, 0x74, 0x73, 0x12, 0x2b, 0x0a, 0x0b, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x61, 0x70, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x0b, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x61, 0x70, 0x1a, 0x85, 0x01, 0x0a, 0x16, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x02,
	0x20, 0x02, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x65,
	0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x02, 0x28, 0x09, 0x52,
	0x0a, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x04, 0x6f,
	0x70, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x79, 0x52, 0x04, 0x6f, 0x70, 0x74, 0x73, 0x1a, 0xcc, 0x01, 0x0a, 0x17, 0x45,
	0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65,
	0x18, 0x02, 0x20, 0x02, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x02, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x24, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x1d, 0x0a, 0x04, 0x6f, 0x70,
	0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x52, 0x04, 0x6f, 0x70, 0x74, 0x73, 0x1a, 0xe7, 0x01, 0x0a, 0x11, 0x67, 0x65,
	0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x27, 0x0a, 0x08, 0x75, 0x75, 0x69, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x08,
	0x75, 0x75, 0x69, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x75, 0x74, 0x53,
	0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x75, 0x74, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c,
	0x75, 0x6d, 0x6e, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x6c,
	0x75, 0x6d, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x29, 0x0a, 0x0a, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x4f, 0x70, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f,
	0x70, 0x74, 0x73, 0x1a, 0xd5, 0x03, 0x0a, 0x11, 0x46, 0x61, 0x63, 0x65, 0x74, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x02, 0x20, 0x02, 0x28, 0x09,
	0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x4f, 0x0a, 0x0b, 0x66, 0x61, 0x63, 0x65, 0x74,
	0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x50, 0x43, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x74, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x46, 0x61, 0x63, 0x65, 0x74, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x52, 0x0b, 0x66, 0x61, 0x63,
	0x65, 0x74, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x12, 0x10,
	0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6e, 0x75, 0x6d,
	0x12, 0x1d, 0x0a, 0x04, 0x6f, 0x70, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09,
	0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x04, 0x6f, 0x70, 0x74, 0x73, 0x12,
	0x18, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x1a, 0xcf, 0x01, 0x0a, 0x10, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x46, 0x61, 0x63, 0x65, 0x74, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e,
	0x18, 0x02, 0x20, 0x02, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x43, 0x6f, 0x6c, 0x75,
	0x6d, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x02, 0x28, 0x09, 0x52, 0x08, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a,
	0x0a, 0x10, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x65, 0x74, 0x53, 0x69,
	0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x73, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x61,
	0x63, 0x65, 0x74, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x66, 0x61, 0x63, 0x65, 0x74, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x1d, 0x0a, 0x04,
	0x6f, 0x70, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x04, 0x6f, 0x70, 0x74, 0x73, 0x1a, 0x68, 0x0a, 0x18, 0x4d,
	0x69, 0x78, 0x65, 0x64, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12,
	0x34, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x50, 0x43, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x05,
	0x71, 0x75, 0x65, 0x72, 0x79, 0x22, 0xf3, 0x09, 0x0a, 0x09, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61,
	0x52, 0x50, 0x43, 0x1a, 0x71, 0x0a, 0x0a, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x02, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x25, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x09, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x08, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x1a, 0xd1, 0x03, 0x0a, 0x0c, 0x44, 0x61, 0x74, 0x61, 0x62,
	0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6d,
	0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x05, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x02, 0x28, 0x05, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x2f, 0x0a, 0x07, 0x63,
	0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x53,
	0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x50, 0x43, 0x2e, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x07, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73, 0x12, 0x28, 0x0a, 0x0f,
	0x64, 0x65, 0x66, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x64, 0x65, 0x66, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65,
	0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x75, 0x6e,
	0x69, 0x71, 0x75, 0x65, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x12, 0x25, 0x0a, 0x08, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x43, 0x6f, 0x6c,
	0x75, 0x6d, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x74, 0x65, 0x72, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x74, 0x65, 0x72, 0x12,
	0x2b, 0x0a, 0x0b, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x0b,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52,
	0x0b, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1e, 0x0a, 0x0a,
	0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x70, 0x61, 0x72, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x70, 0x61, 0x72, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x72, 0x65, 0x70, 0x53, 0x75, 0x62, 0x4e, 0x75, 0x6d, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x72, 0x65, 0x70, 0x53, 0x75, 0x62, 0x4e, 0x75, 0x6d, 0x1a, 0x3a, 0x0a, 0x0f, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x44, 0x42, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a,
	0x02, 0x64, 0x62, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x53, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x52, 0x50, 0x43, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x02, 0x64, 0x62, 0x1a, 0x49, 0x0a, 0x0d, 0x43, 0x6f, 0x70, 0x79, 0x44, 0x42,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x02,
	0x28, 0x09, 0x52, 0x0d, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e,
	0x73, 0x1a, 0x61, 0x0a, 0x13, 0x52, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x6c, 0x75, 0x6d,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x62, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x06, 0x64, 0x62, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x6f, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28,
	0x09, 0x52, 0x07, 0x6f, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65,
	0x77, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x02, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x77,
	0x4e, 0x61, 0x6d, 0x65, 0x1a, 0xa9, 0x02, 0x0a, 0x0d, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x62, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x06, 0x64, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27,
	0x0a, 0x02, 0x64, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x53, 0x63, 0x68,
	0x65, 0x6d, 0x61, 0x52, 0x50, 0x43, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x02, 0x64, 0x62, 0x12, 0x29, 0x0a, 0x04, 0x61, 0x64, 0x64, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x50,
	0x43, 0x2e, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x61, 0x64,
	0x64, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x73, 0x12, 0x3c, 0x0a, 0x06,
	0x61, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x53,
	0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x50, 0x43, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x6c, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x75,
	0x6d, 0x6e, 0x52, 0x06, 0x61, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x1a, 0x54, 0x0a, 0x0b, 0x41, 0x6c,
	0x74, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x31, 0x0a,
	0x08, 0x6e, 0x65, 0x77, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x50, 0x43, 0x2e, 0x43, 0x6f, 0x6c, 0x75,
	0x6d, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x6e, 0x65, 0x77, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x1a, 0x4e, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x42, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28,
	0x09, 0x52, 0x06, 0x64, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x08, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x1a, 0x39, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x42, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x27, 0x0a, 0x02, 0x64, 0x62, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x50, 0x43, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x62,
	0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x02, 0x64, 0x62, 0x22, 0xf2, 0x06, 0x0a, 0x06,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x02, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x23,
	0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x65, 0x64, 0x4e,
	0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x69, 0x6e, 0x73, 0x65, 0x72, 0x74,
	0x65, 0x64, 0x4e, 0x75, 0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x4e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x4e, 0x75, 0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x4e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x4e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x65,
	0x64, 0x52, 0x65, 0x63, 0x18, 0x07, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x69, 0x6e, 0x73, 0x65,
	0x72, 0x74, 0x65, 0x64, 0x52, 0x65, 0x63, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x52, 0x65, 0x63, 0x18, 0x08, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x63, 0x12, 0x29, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x52, 0x65, 0x63, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x52,
	0x65, 0x63, 0x12, 0x23, 0x0a, 0x07, 0x67, 0x65, 0x6e, 0x55, 0x55, 0x49, 0x44, 0x18, 0x0a, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x07,
	0x67, 0x65, 0x6e, 0x55, 0x55, 0x49, 0x44, 0x12, 0x29, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x55, 0x55, 0x49, 0x44, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x55,
	0x49, 0x44, 0x12, 0x2c, 0x0a, 0x07, 0x64, 0x65, 0x6c, 0x55, 0x55, 0x49, 0x44, 0x18, 0x0c, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x55, 0x55, 0x49, 0x44,
	0x12, 0x25, 0x0a, 0x04, 0x73, 0x6b, 0x69, 0x70, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x50, 0x72,
	0x6f, 0x52, 0x04, 0x73, 0x6b, 0x69, 0x70, 0x12, 0x27, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x12, 0x2f, 0x0a, 0x08, 0x64, 0x65, 0x6c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x0f, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x44, 0x65, 0x6c, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x52, 0x08, 0x64, 0x65, 0x6c, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x12, 0x2c, 0x0a, 0x07, 0x6d, 0x61, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x10, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x07, 0x6d, 0x61, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x12,
	0x2d, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x11, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x53, 0x75, 0x62, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x52, 0x08, 0x73, 0x75, 0x62, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x1a, 0x42,
	0x0a, 0x0a, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x20, 0x0a, 0x0b,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x02, 0x28,
	0x03, 0x52, 0x0b, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x12,
	0x0a, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x69, 0x74,
	0x65, 0x6d, 0x1a, 0x5b, 0x0a, 0x09, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x12,
	0x20, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01,
	0x20, 0x02, 0x28, 0x03, 0x52, 0x0b, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x05, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x03, 0x20, 0x02, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a,
	0x4f, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x12, 0x12,
	0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x05,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x03, 0x20, 0x02, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x22, 0x6e, 0x0a, 0x10, 0x53, 0x75, 0x62, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x75, 0x62, 0x44, 0x61, 0x74, 0x61, 0x62,
	0x61, 0x73, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x75, 0x62,
	0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x0d, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x52, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x3d, 0x0a, 0x0d, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22,
	0xa6, 0x01, 0x0a, 0x0a, 0x53, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x52, 0x50, 0x43, 0x1a, 0x6b,
	0x0a, 0x0e, 0x53, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52,
	0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20,
	0x02, 0x28, 0x05, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x04,
	0x6f, 0x70, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x04, 0x6f, 0x70, 0x74, 0x73, 0x1a, 0x2b, 0x0a, 0x0f, 0x53,
	0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x07, 0x73, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x22, 0x57, 0x0a, 0x0b, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x62, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x06, 0x64, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x73, 0x70, 0x6c, 0x69, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x53, 0x70, 0x6c, 0x69,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x75, 0x62, 0x53, 0x70, 0x6c, 0x69,
	0x74, 0x22, 0x4b, 0x0a, 0x0b, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52,
	0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x26, 0x0a, 0x07, 0x64, 0x62, 0x53, 0x70, 0x6c, 0x69,
	0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x53, 0x70, 0x6c, 0x69, 0x74, 0x52, 0x07, 0x64, 0x62, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x22, 0x5e,
	0x0a, 0x0e, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x31,
	0x12, 0x2e, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x51, 0x75, 0x65, 0x72, 0x79, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x52, 0x0b, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x75, 0x73, 0x74, 0x51, 0x75, 0x65, 0x72, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x75, 0x73, 0x74, 0x51, 0x75, 0x65, 0x72, 0x79, 0x22, 0xe2,
	0x07, 0x0a, 0x0d, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x50, 0x43,
	0x1a, 0x78, 0x0a, 0x10, 0x54, 0x52, 0x53, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x63,
	0x65, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4e,
	0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x61, 0x69, 0x6c,
	0x65, 0x64, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66,
	0x61, 0x69, 0x6c, 0x65, 0x64, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x1a, 0xe0, 0x02, 0x0a, 0x0a, 0x54,
	0x52, 0x53, 0x55, 0x73, 0x65, 0x72, 0x52, 0x50, 0x43, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a,
	0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x08, 0x3a, 0x05, 0x66,
	0x61, 0x6c, 0x73, 0x65, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x73, 0x65, 0x6c, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x73, 0x65, 0x6c, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x1f, 0x0a, 0x09, 0x6f, 0x77,
	0x6e, 0x65, 0x72, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x01, 0x31,
	0x52, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x07, 0x43,
	0x4f, 0x4e, 0x53, 0x4f, 0x4c, 0x45, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x01, 0x30, 0x52,
	0x07, 0x43, 0x4f, 0x4e, 0x53, 0x4f, 0x4c, 0x45, 0x12, 0x13, 0x0a, 0x03, 0x41, 0x50, 0x49, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x01, 0x30, 0x52, 0x03, 0x41, 0x50, 0x49, 0x12, 0x1c, 0x0a,
	0x09, 0x69, 0x70, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x69, 0x70, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x77, 0x68, 0x69, 0x74, 0x65, 0x49, 0x70,
	0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x77, 0x68, 0x69, 0x74, 0x65, 0x49, 0x70,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x49, 0x70, 0x73, 0x18, 0x0c, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x49, 0x70, 0x73, 0x1a, 0x65, 0x0a,
	0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x52, 0x53, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x6c, 0x64, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x6c, 0x64, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x33, 0x0a, 0x07, 0x6e, 0x65, 0x77, 0x55, 0x73, 0x65, 0x72, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x50, 0x43,
	0x2e, 0x54, 0x52, 0x53, 0x55, 0x73, 0x65, 0x72, 0x52, 0x50, 0x43, 0x52, 0x07, 0x6e, 0x65, 0x77,
	0x55, 0x73, 0x65, 0x72, 0x1a, 0xb5, 0x01, 0x0a, 0x0c, 0x54, 0x52, 0x53, 0x53, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x52, 0x50, 0x43, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x02, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x72,
	0x74, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x02, 0x28, 0x05, 0x52, 0x07, 0x70, 0x61, 0x72, 0x74,
	0x6e, 0x75, 0x6d, 0x12, 0x23, 0x0a, 0x09, 0x72, 0x65, 0x61, 0x64, 0x77, 0x72, 0x69, 0x74, 0x65,
	0x18, 0x03, 0x20, 0x02, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x09, 0x72,
	0x65, 0x61, 0x64, 0x77, 0x72, 0x69, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x6f, 0x64, 0x65,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f, 0x64, 0x65,
	0x6c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x61, 0x69, 0x6e,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x61, 0x69, 0x6e,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x72, 0x74, 0x63, 0x6f, 0x70, 0x79, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x72, 0x74, 0x63, 0x6f, 0x70, 0x79, 0x1a, 0x85, 0x01, 0x0a,
	0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x52, 0x53, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x11, 0x72, 0x65, 0x64, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x02,
	0x28, 0x09, 0x3a, 0x02, 0x6f, 0x6e, 0x52, 0x11, 0x72, 0x65, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x39, 0x0a, 0x09, 0x6e, 0x65, 0x77,
	0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x50,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x50, 0x43, 0x2e, 0x54, 0x52, 0x53,
	0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x50, 0x43, 0x52, 0x09, 0x6e, 0x65, 0x77, 0x53, 0x63,
	0x68, 0x65, 0x6d, 0x61, 0x1a, 0x4d, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x54, 0x52, 0x53, 0x53, 0x63,
	0x68, 0x65, 0x6d, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x35, 0x0a, 0x07,
	0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x50, 0x43, 0x2e, 0x54, 0x52,
	0x53, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x50, 0x43, 0x52, 0x07, 0x73, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x73, 0x42, 0x37, 0x0a, 0x13, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x72, 0x73, 0x2e, 0x68,
	0x79, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x42, 0x09, 0x48, 0x79, 0x62, 0x61,
	0x73, 0x65, 0x52, 0x50, 0x43, 0x5a, 0x15, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x72, 0x73, 0x2f, 0x68,
	0x79, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
}

var (
	file_rpc_api_proto_rawDescOnce sync.Once
	file_rpc_api_proto_rawDescData = file_rpc_api_proto_rawDesc
)

func file_rpc_api_proto_rawDescGZIP() []byte {
	file_rpc_api_proto_rawDescOnce.Do(func() {
		file_rpc_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_rpc_api_proto_rawDescData)
	})
	return file_rpc_api_proto_rawDescData
}

var file_rpc_api_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_rpc_api_proto_msgTypes = make([]protoimpl.MessageInfo, 74)
var file_rpc_api_proto_goTypes = []any{
	(MaintRPC_AdminAction)(0),                            // 0: MaintRPC.AdminAction
	(*SearchID)(nil),                                     // 1: SearchID
	(*RecordID)(nil),                                     // 2: RecordID
	(*InputFile)(nil),                                    // 3: InputFile
	(*InputFileHeader)(nil),                              // 4: InputFileHeader
	(*FileList)(nil),                                     // 5: FileList
	(*InputRecord)(nil),                                  // 6: InputRecord
	(*ResultRecord)(nil),                                 // 7: ResultRecord
	(*Property)(nil),                                     // 8: Property
	(*PropertyList)(nil),                                 // 9: PropertyList
	(*PropertyTable)(nil),                                // 10: PropertyTable
	(*StringList)(nil),                                   // 11: StringList
	(*ResultField)(nil),                                  // 12: ResultField
	(*NumProperty)(nil),                                  // 13: NumProperty
	(*FloatProperty)(nil),                                // 14: FloatProperty
	(*BytesProperty)(nil),                                // 15: BytesProperty
	(*FloatPropertyList)(nil),                            // 16: FloatPropertyList
	(*MaintRPC)(nil),                                     // 17: MaintRPC
	(*SearchRPC)(nil),                                    // 18: SearchRPC
	(*SchemaRPC)(nil),                                    // 19: SchemaRPC
	(*Report)(nil),                                       // 20: Report
	(*SubDatabaseError)(nil),                             // 21: SubDatabaseError
	(*ErrorResponse)(nil),                                // 22: ErrorResponse
	(*SuggestRPC)(nil),                                   // 23: SuggestRPC
	(*FilterSplit)(nil),                                  // 24: FilterSplit
	(*FilterQuery)(nil),                                  // 25: FilterQuery
	(*OptimizeQuery1)(nil),                               // 26: OptimizeQuery1
	(*PermissionRPC)(nil),                                // 27: PermissionRPC
	(*InputRecord_InputColumn)(nil),                      // 28: InputRecord.InputColumn
	(*ResultRecord_ResultColumn)(nil),                    // 29: ResultRecord.ResultColumn
	(*MaintRPC_InsertRecordRequest)(nil),                 // 30: MaintRPC.InsertRecordRequest
	(*MaintRPC_LoadRecordRequest)(nil),                   // 31: MaintRPC.LoadRecordRequest
	(*MaintRPC_DeleteRecordRequest)(nil),                 // 32: MaintRPC.DeleteRecordRequest
	(*MaintRPC_UpdateRecordRequest)(nil),                 // 33: MaintRPC.UpdateRecordRequest
	(*MaintRPC_CommitResponse)(nil),                      // 34: MaintRPC.CommitResponse
	(*MaintRPC_AdminRequest)(nil),                        // 35: MaintRPC.AdminRequest
	(*MaintRPC_OptimizeIndexRequest)(nil),                // 36: MaintRPC.OptimizeIndexRequest
	(*MaintRPC_ExportRecordRequest)(nil),                 // 37: MaintRPC.ExportRecordRequest
	(*MaintRPC_ExportRecordResponse)(nil),                // 38: MaintRPC.ExportRecordResponse
	(*MaintRPC_ExportFileHeader)(nil),                    // 39: MaintRPC.ExportFileHeader
	(*MaintRPC_ExportStatus)(nil),                        // 40: MaintRPC.ExportStatus
	(*MaintRPC_CollisionRequest)(nil),                    // 41: MaintRPC.CollisionRequest
	(*MaintRPC_CollisionResponse)(nil),                   // 42: MaintRPC.CollisionResponse
	(*MaintRPC_ExportStatus_SubDBInfo)(nil),              // 43: MaintRPC.ExportStatus.SubDBInfo
	(*MaintRPC_CollisionRequest_History)(nil),            // 44: MaintRPC.CollisionRequest.History
	(*SearchRPC_SearchRecordRequest)(nil),                // 45: SearchRPC.SearchRecordRequest
	(*SearchRPC_SearchRecordResponse)(nil),               // 46: SearchRPC.SearchRecordResponse
	(*SearchRPC_CategoryQueryRequest)(nil),               // 47: SearchRPC.CategoryQueryRequest
	(*SearchRPC_CategoryQueryResponse)(nil),              // 48: SearchRPC.CategoryQueryResponse
	(*SearchRPC_ExpressionQueryRequest)(nil),             // 49: SearchRPC.ExpressionQueryRequest
	(*SearchRPC_ExpressionQueryResponse)(nil),            // 50: SearchRPC.ExpressionQueryResponse
	(*SearchRPCGetRecordsRequest)(nil),                   // 51: SearchRPC.getRecordsRequest
	(*SearchRPC_FacetQueryRequest)(nil),                  // 52: SearchRPC.FacetQueryRequest
	(*SearchRPC_MixedSearchRecordRequest)(nil),           // 53: SearchRPC.MixedSearchRecordRequest
	(*SearchRPC_FacetQueryRequest_InputFacetColumn)(nil), // 54: SearchRPC.FacetQueryRequest.InputFacetColumn
	(*SchemaRPC_ColumnInfo)(nil),                         // 55: SchemaRPC.ColumnInfo
	(*SchemaRPC_DatabaseInfo)(nil),                       // 56: SchemaRPC.DatabaseInfo
	(*SchemaRPC_CreateDBRequest)(nil),                    // 57: SchemaRPC.CreateDBRequest
	(*SchemaRPC_CopyDBRequest)(nil),                      // 58: SchemaRPC.CopyDBRequest
	(*SchemaRPC_RenameColumnRequest)(nil),                // 59: SchemaRPC.RenameColumnRequest
	(*SchemaRPC_CommitRequest)(nil),                      // 60: SchemaRPC.CommitRequest
	(*SchemaRPC_ListDBRequest)(nil),                      // 61: SchemaRPC.ListDBRequest
	(*SchemaRPC_ListDBResponse)(nil),                     // 62: SchemaRPC.ListDBResponse
	(*SchemaRPC_CommitRequest_AlterColumn)(nil),          // 63: SchemaRPC.CommitRequest.AlterColumn
	(*Report_RecordItem)(nil),                            // 64: Report.RecordItem
	(*Report_RecordPro)(nil),                             // 65: Report.RecordPro
	(*Report_DelErrorPro)(nil),                           // 66: Report.DelErrorPro
	(*SuggestRPC_SuggestRequest)(nil),                    // 67: SuggestRPC.SuggestRequest
	(*SuggestRPC_SuggestResponse)(nil),                   // 68: SuggestRPC.SuggestResponse
	(*PermissionRPC_TRSUpdateLicense)(nil),               // 69: PermissionRPC.TRSUpdateLicense
	(*PermissionRPC_TRSUserRPC)(nil),                     // 70: PermissionRPC.TRSUserRPC
	(*PermissionRPC_UpdateTRSUserRequest)(nil),           // 71: PermissionRPC.UpdateTRSUserRequest
	(*PermissionRPC_TRSSchemaRPC)(nil),                   // 72: PermissionRPC.TRSSchemaRPC
	(*PermissionRPC_UpdateTRSSchemaRequest)(nil),         // 73: PermissionRPC.UpdateTRSSchemaRequest
	(*PermissionRPC_GetTRSSchemaResponse)(nil),           // 74: PermissionRPC.GetTRSSchemaResponse
}
var file_rpc_api_proto_depIdxs = []int32{
	3,  // 0: FileList.files:type_name -> InputFile
	28, // 1: InputRecord.columns:type_name -> InputRecord.InputColumn
	29, // 2: ResultRecord.columns:type_name -> ResultRecord.ResultColumn
	8,  // 3: PropertyList.property:type_name -> Property
	9,  // 4: PropertyTable.propList:type_name -> PropertyList
	14, // 5: FloatPropertyList.property:type_name -> FloatProperty
	9,  // 6: Report.items:type_name -> PropertyList
	8,  // 7: Report.deletedRec:type_name -> Property
	8,  // 8: Report.genUUID:type_name -> Property
	8,  // 9: Report.updateUUID:type_name -> Property
	64, // 10: Report.delUUID:type_name -> Report.RecordItem
	65, // 11: Report.skip:type_name -> Report.RecordPro
	65, // 12: Report.error:type_name -> Report.RecordPro
	66, // 13: Report.delError:type_name -> Report.DelErrorPro
	64, // 14: Report.marking:type_name -> Report.RecordItem
	21, // 15: Report.subError:type_name -> SubDatabaseError
	22, // 16: SubDatabaseError.errorResponse:type_name -> ErrorResponse
	24, // 17: FilterQuery.dbSplit:type_name -> FilterSplit
	25, // 18: OptimizeQuery1.filterQuery:type_name -> FilterQuery
	5,  // 19: InputRecord.InputColumn.files:type_name -> FileList
	28, // 20: InputRecord.InputColumn.redunColumns:type_name -> InputRecord.InputColumn
	5,  // 21: ResultRecord.ResultColumn.files:type_name -> FileList
	6,  // 22: MaintRPC.InsertRecordRequest.record:type_name -> InputRecord
	8,  // 23: MaintRPC.InsertRecordRequest.options:type_name -> Property
	6,  // 24: MaintRPC.LoadRecordRequest.record:type_name -> InputRecord
	8,  // 25: MaintRPC.LoadRecordRequest.options:type_name -> Property
	8,  // 26: MaintRPC.DeleteRecordRequest.options:type_name -> Property
	6,  // 27: MaintRPC.UpdateRecordRequest.record:type_name -> InputRecord
	8,  // 28: MaintRPC.UpdateRecordRequest.options:type_name -> Property
	8,  // 29: MaintRPC.CommitResponse.info:type_name -> Property
	0,  // 30: MaintRPC.AdminRequest.action:type_name -> MaintRPC.AdminAction
	8,  // 31: MaintRPC.AdminRequest.options:type_name -> Property
	45, // 32: MaintRPC.ExportRecordRequest.searchRequest:type_name -> SearchRPC.SearchRecordRequest
	8,  // 33: MaintRPC.ExportRecordRequest.options:type_name -> Property
	7,  // 34: MaintRPC.ExportRecordResponse.record:type_name -> ResultRecord
	45, // 35: MaintRPC.ExportStatus.searchRequest:type_name -> SearchRPC.SearchRecordRequest
	43, // 36: MaintRPC.ExportStatus.subDB:type_name -> MaintRPC.ExportStatus.SubDBInfo
	44, // 37: MaintRPC.CollisionRequest.historyResult:type_name -> MaintRPC.CollisionRequest.History
	45, // 38: MaintRPC.CollisionRequest.searchRequest:type_name -> SearchRPC.SearchRecordRequest
	8,  // 39: MaintRPC.CollisionRequest.options:type_name -> Property
	8,  // 40: MaintRPC.CollisionRequest.History.options:type_name -> Property
	8,  // 41: SearchRPC.SearchRecordRequest.opts:type_name -> Property
	7,  // 42: SearchRPC.SearchRecordResponse.record:type_name -> ResultRecord
	8,  // 43: SearchRPC.SearchRecordResponse.searchProgress:type_name -> Property
	8,  // 44: SearchRPC.SearchRecordResponse.dbHitsMap:type_name -> Property
	8,  // 45: SearchRPC.SearchRecordResponse.opts:type_name -> Property
	10, // 46: SearchRPC.SearchRecordResponse.subProperties:type_name -> PropertyTable
	8,  // 47: SearchRPC.CategoryQueryRequest.opts:type_name -> Property
	13, // 48: SearchRPC.CategoryQueryResponse.result:type_name -> NumProperty
	8,  // 49: SearchRPC.CategoryQueryResponse.opts:type_name -> Property
	8,  // 50: SearchRPC.CategoryQueryResponse.functionMap:type_name -> Property
	8,  // 51: SearchRPC.ExpressionQueryRequest.opts:type_name -> Property
	12, // 52: SearchRPC.ExpressionQueryResponse.result:type_name -> ResultField
	8,  // 53: SearchRPC.ExpressionQueryResponse.opts:type_name -> Property
	11, // 54: SearchRPC.getRecordsRequest.uuidList:type_name -> StringList
	8,  // 55: SearchRPC.getRecordsRequest.searchOpts:type_name -> Property
	54, // 56: SearchRPC.FacetQueryRequest.facetColumn:type_name -> SearchRPC.FacetQueryRequest.InputFacetColumn
	8,  // 57: SearchRPC.FacetQueryRequest.opts:type_name -> Property
	45, // 58: SearchRPC.MixedSearchRecordRequest.query:type_name -> SearchRPC.SearchRecordRequest
	8,  // 59: SearchRPC.FacetQueryRequest.InputFacetColumn.opts:type_name -> Property
	8,  // 60: SchemaRPC.ColumnInfo.property:type_name -> Property
	55, // 61: SchemaRPC.DatabaseInfo.columns:type_name -> SchemaRPC.ColumnInfo
	8,  // 62: SchemaRPC.DatabaseInfo.property:type_name -> Property
	8,  // 63: SchemaRPC.DatabaseInfo.splitParams:type_name -> Property
	56, // 64: SchemaRPC.CreateDBRequest.db:type_name -> SchemaRPC.DatabaseInfo
	56, // 65: SchemaRPC.CommitRequest.db:type_name -> SchemaRPC.DatabaseInfo
	55, // 66: SchemaRPC.CommitRequest.adds:type_name -> SchemaRPC.ColumnInfo
	63, // 67: SchemaRPC.CommitRequest.alters:type_name -> SchemaRPC.CommitRequest.AlterColumn
	8,  // 68: SchemaRPC.ListDBRequest.property:type_name -> Property
	56, // 69: SchemaRPC.ListDBResponse.db:type_name -> SchemaRPC.DatabaseInfo
	55, // 70: SchemaRPC.CommitRequest.AlterColumn.newValue:type_name -> SchemaRPC.ColumnInfo
	8,  // 71: SuggestRPC.SuggestRequest.opts:type_name -> Property
	70, // 72: PermissionRPC.UpdateTRSUserRequest.newUser:type_name -> PermissionRPC.TRSUserRPC
	72, // 73: PermissionRPC.UpdateTRSSchemaRequest.newSchema:type_name -> PermissionRPC.TRSSchemaRPC
	72, // 74: PermissionRPC.GetTRSSchemaResponse.schemas:type_name -> PermissionRPC.TRSSchemaRPC
	75, // [75:75] is the sub-list for method output_type
	75, // [75:75] is the sub-list for method input_type
	75, // [75:75] is the sub-list for extension type_name
	75, // [75:75] is the sub-list for extension extendee
	0,  // [0:75] is the sub-list for field type_name
}

func init() { file_rpc_api_proto_init() }
func file_rpc_api_proto_init() {
	if File_rpc_api_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_rpc_api_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   74,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rpc_api_proto_goTypes,
		DependencyIndexes: file_rpc_api_proto_depIdxs,
		EnumInfos:         file_rpc_api_proto_enumTypes,
		MessageInfos:      file_rpc_api_proto_msgTypes,
	}.Build()
	File_rpc_api_proto = out.File
	file_rpc_api_proto_rawDesc = nil
	file_rpc_api_proto_goTypes = nil
	file_rpc_api_proto_depIdxs = nil
}
