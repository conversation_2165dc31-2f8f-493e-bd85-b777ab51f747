package client

type TRSFacetColumn struct {
	name         string
	statColumn   string
	statFunction string
	facetLabels  string
	maxSize      int
	options      map[string]string
}

type TRSStatFunction struct {
	MIN   string
	MAX   string
	COUNT string
	SUM   string
}

var statFunctions = TRSStatFunction{
	MIN:   "MIN",
	MAX:   "MAX",
	COUNT: "COUNT",
	SUM:   "SUM",
}

func NewTRSFacetColumn(name, statColumn, function, facetLabels string, maxSize int, opts map[string]string) *TRSFacetColumn {
	column := &TRSFacetColumn{
		name:         name,
		statColumn:   statColumn,
		statFunction: function,
		facetLabels:  facetLabels,
		maxSize:      maxSize,
		options:      make(map[string]string),
	}
	if opts != nil {
		column.options = opts
	}
	return column
}

func (c *TRSFacetColumn) SetName(name string) {
	c.name = name
}

func (c *TRSFacetColumn) SetStatColumn(statColumn string) {
	c.statColumn = statColumn
}

func (c *TRSFacetColumn) SetStatFunction(function string) {
	c.statFunction = function
}

func (c *TRSFacetColumn) SetFacetLabels(facetLabels string) {
	c.facetLabels = facetLabels
}

func (c *TRSFacetColumn) SetMaxSize(maxSize int) {
	c.maxSize = maxSize
}

func (c *TRSFacetColumn) SetOption(key, value string) *TRSFacetColumn {
	c.options[key] = value
	return c
}

func (c *TRSFacetColumn) GetOption(key string) string {
	return c.options[key]
}

func (c *TRSFacetColumn) GetAllOptions() map[string]string {
	return c.options
}

func (c *TRSFacetColumn) GetName() string {
	return c.name
}

func (c *TRSFacetColumn) GetStatColumn() string {
	return c.statColumn
}

func (c *TRSFacetColumn) GetStatFunction() string {
	return c.statFunction
}

func (c *TRSFacetColumn) GetFacetLabels() string {
	return c.facetLabels
}

func (c *TRSFacetColumn) GetMaxSize() int {
	return c.maxSize
}

func (c *TRSFacetColumn) HasFacetLabels() bool {
	return c.facetLabels != ""
}

func (c *TRSFacetColumn) HasMaxSize() bool {
	return c.maxSize > 0
}
