package client

import (
	"fmt"
)

// TRSException represents various exceptions in the system.
type TRSException struct {
	ErrorNo int
	Message string
}

// Error implements the error interface for TRSException.
func (e *TRSException) Error() string {
	return fmt.Sprintf("Error %d: %s", e.<PERSON><PERSON><PERSON><PERSON><PERSON>, e.Message)
}

// Error codes
const (
	ERR_NONE              = 500000
	ERR_DATAFORMAT        = 500001
	ERR_CONNECT_HANDLE    = 500002
	ERR_LAST_FAILED       = 500003
	ERR_JNI_DLLNOFOUND    = 500004
	ERR_JNI_DLLNOLINKED   = 500005
	ERR_JNI_VERMISMATCH   = 500006
	ERR_JNI_DLLUNKNOWN    = 500007
	ERR_LANGUAGE_TYPE     = 500008
	ERR_PROPERTY_SPLIT    = 500009
	ERR_PROPERTY_NAME     = 500010
	ERR_PROPERTY_VALUE    = 500011
	ERR_PROPERTY_NOFOUND  = 500012
	ERR_KNOWASS_TYPE      = 500013
	ERR_OBJECT_NAME       = 500014
	ERR_OBJECT_TYPE       = 500015
	ERR_INVALID_PARAMETER = 500016
	ERR_EMPTY_VALUES      = 500017
	ERR_RESULT_HANDLE     = 500018
	ERR_RECORD_POS        = 500019
	ERR_COLUMN_NAME       = 500020
	ERR_COLUMN_NOFOUND    = 500021
	ERR_COLUMN_TYPE       = 500022
	ERR_COLUMN_VALUE      = 500023
	ERR_COLUMN_LIST       = 500024
	ERR_COLUMN_NUM        = 500025
	ERR_DELETE_WHERE      = 500026
	ERR_UPDATE_VALUE      = 500027
	ERR_CONNECT_FAILED    = 500028
	ERR_UNDEFINED         = 9999999
)

// AlreadyClosedException represents an exception thrown when an operation is attempted on a closed resource.
type AlreadyClosedException struct {
	TRSException
}

// NewTRSException creates a new TRSException with a specific error code and message.
func NewTRSException(errorNo int, message string) *TRSException {
	return &TRSException{
		ErrorNo: errorNo,
		Message: message,
	}
}

// NewTRSExceptionWithError creates a new TRSException from an existing error.
func NewTRSExceptionWithError(err error) *TRSException {
	var errorNo int
	switch err.(type) {
	case *TRSException:
		errorNo = err.(*TRSException).ErrorNo
	default:
		errorNo = -1
	}
	return &TRSException{
		ErrorNo: errorNo,
		Message: err.Error(),
	}
}
