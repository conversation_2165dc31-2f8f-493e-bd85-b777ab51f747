package client

import (
	"com/trs/hybase/client/common"
)

type TRSDatabaseColumn struct {
	name      string
	sname     string
	colType   int
	TRSObject *common.TRSObject
}

const (
	TYPE_DATE     = 0
	TYPE_NUMBER   = 1
	TYPE_CHAR     = 2
	TYPE_PHRASE   = 3
	TYPE_DOCUMENT = 4
	TYPE_BIT      = 5
	TYPE_OBJECT   = 6
	TYPE_VECTOR   = 7
	TYPE_BOOL     = 8
	TYPE_GEO      = 9
)

func NewTRSDatabaseColumn(name string, colType int) *TRSDatabaseColumn {
	return &TRSDatabaseColumn{
		name:      name,
		colType:   colType,
		TRSObject: common.NewTRSObject(),
	}
}

func (c *TRSDatabaseColumn) GetName() string {
	return c.name
}

func (c *TRSDatabaseColumn) GetColType() int {
	return c.colType
}

func (c *TRSDatabaseColumn) GetSName() string {
	return c.sname
}

func (c *TRSDatabaseColumn) SetSName(sname string) {
	c.sname = sname
}

func (c *TRSDatabaseColumn) GetProperty(key string) string {
	return c.TRSObject.GetProperty(key)
}

func (c *TRSDatabaseColumn) SetProperty(key, value string) {
	c.TRSObject.SetProperty(key, value)
}

func (c *TRSDatabaseColumn) GetAllOptions() map[string]string {
	return c.TRSObject.Properties
}

func (c *TRSDatabaseColumn) SetMultiValue(multiValue bool) {
	c.SetProperty("multivalue", boolToString(multiValue))
}

func (c *TRSDatabaseColumn) IsMultiValue() bool {
	return stringToBool(c.GetProperty("multivalue"))
}

func (c *TRSDatabaseColumn) SetValueMust(valueMust bool) {
	c.SetProperty("valuemust", boolToString(valueMust))
}

func (c *TRSDatabaseColumn) IsValueMust() bool {
	return stringToBool(c.GetProperty("valuemust"))
}

func (c *TRSDatabaseColumn) SetNotIndex(notIndex bool) {
	c.SetProperty("notindex", boolToString(notIndex))
}

func (c *TRSDatabaseColumn) IsNotIndex() bool {
	return stringToBool(c.GetProperty("notindex"))
}

func (c *TRSDatabaseColumn) SetNotStore(notStore bool) {
	c.SetProperty("notstore", boolToString(notStore))
}

func (c *TRSDatabaseColumn) IsNotStore() bool {
	return stringToBool(c.GetProperty("notstore"))
}

func (c *TRSDatabaseColumn) SetStoreFile(storeFile bool) {
	c.SetProperty("storefile", boolToString(storeFile))
}

func (c *TRSDatabaseColumn) IsStoreFile() bool {
	return stringToBool(c.GetProperty("storefile"))
}

func (c *TRSDatabaseColumn) SetCentStore(centStore bool) {
	c.SetProperty("centstore", boolToString(centStore))
}

func (c *TRSDatabaseColumn) IsCentStore() bool {
	return stringToBool(c.GetProperty("centstore"))
}

func (c *TRSDatabaseColumn) SetCategoryColumn(isCategory bool) {
	c.SetProperty("iscategory", boolToString(isCategory))
}

func (c *TRSDatabaseColumn) IsCategoryColumn() bool {
	return stringToBool(c.GetProperty("iscategory"))
}

func (c *TRSDatabaseColumn) SetIsFloat(isFloat bool) {
	c.SetProperty("isfloat", boolToString(isFloat))
}

func (c *TRSDatabaseColumn) IsFloat() bool {
	return stringToBool(c.GetProperty("isfloat"))
}

func (c *TRSDatabaseColumn) SetDefaultValue(defaultValue string) {
	c.SetProperty("defaultvalue", defaultValue)
}

func (c *TRSDatabaseColumn) GetDefaultValue() string {
	return c.GetProperty("defaultvalue")
}

func (c *TRSDatabaseColumn) SetAlias(alias string) {
	c.SetProperty("alias", alias)
}

func (c *TRSDatabaseColumn) GetAlias() string {
	return c.GetProperty("alias")
}

func (c *TRSDatabaseColumn) SetComment(comment string) {
	c.SetProperty("comment", comment)
}

func (c *TRSDatabaseColumn) GetComment() string {
	return c.GetProperty("comment")
}

func (c *TRSDatabaseColumn) SetValuesource(valueSource string) {
	c.SetProperty("valuesource", valueSource)
}

func (c *TRSDatabaseColumn) GetValuesource() string {
	return c.GetProperty("valuesource")
}

func boolToString(b bool) string {
	if b {
		return "true"
	}
	return "false"
}

func stringToBool(s string) bool {
	return s == "true"
}
