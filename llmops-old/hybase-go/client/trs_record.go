package client

import (
	"com/trs/hybase/client/common"
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"
)

type TRSRecord struct {
	closed          bool
	recUUID         string
	recTag          string
	relevance       float64
	dbName          string
	recColumnsData  map[string]ResultRecord_ResultColumn
	recColumnFiles  map[string][]os.File
	schema          *TRSDatabase
	size            int64
	timestamp       int64
	createTimestamp int64
	conn            *TRSConnection
}

const (
	UUID = "#UUID"
)

var DATE_FORMATS = []string{
	"2006/01/02 15:04:05",
	"2006.01.02 15:04:05",
	"2006-01-02 15:04:05",
	"20060102150405",
	"2006/01/02",
	"2006.01.02",
	"2006-01-02",
	"20060102",
	"01-02-2006",
	"Jan-02-2006",
	"January-02-2006",
}

func NewTRSRecord(conn *TRSConnection) *TRSRecord {
	return &TRSRecord{
		recColumnsData: make(map[string]ResultRecord_ResultColumn),
		recColumnFiles: make(map[string][]os.File),
		recTag:         "",
		relevance:      1.0,
		conn:           conn,
	}
}

func (tr *TRSRecord) Close() {
	if tr.closed {
		return
	}
	tr.closed = true
	tr.reset()
}

func (tr *TRSRecord) reset() {
	tr.recColumnFiles = make(map[string][]os.File)
	tr.recColumnsData = make(map[string]ResultRecord_ResultColumn)
}

func (tr *TRSRecord) SetUid(uid string) {
	tr.recUUID = uid
}

func (tr *TRSRecord) GetUid() string {
	return tr.recUUID
}

func (tr *TRSRecord) GetRelevance() float64 {
	return tr.relevance
}

func (tr *TRSRecord) SetRelevance(rel float64) {
	tr.relevance = rel
}

func (tr *TRSRecord) AddValue(colName string, val string) {
	cBuilder := ResultRecord_ResultColumn{
		Name:     &colName,
		StrValue: &val,
	}
	tr.recColumnsData[colName] = cBuilder
}

func (tr *TRSRecord) GetColumn(colName string) (ResultRecord_ResultColumn, bool) {
	val, exists := tr.recColumnsData[colName]
	return val, exists
}

func (tr *TRSRecord) GetString(colName string) (string, *TRSException) {
	if strings.EqualFold(colName, "UUID") {
		return tr.GetUid(), nil
	}
	col, exists := tr.GetColumn(colName)
	if !exists {
		return "", NewTRSException(-1, fmt.Sprintf("column %s not found", colName))
	}
	if col.StrValue != nil {
		return *col.StrValue, nil
	} else if col.NumValue != nil {
		return strconv.FormatInt(*col.NumValue, 10), nil
	} else if col.FloatValue != nil {
		return strconv.FormatFloat(float64(*col.FloatValue), 'f', -1, 64), nil
	} else if len(col.FloatVectors) > 0 {
		var vectorString strings.Builder
		for i, v := range col.FloatVectors {
			vectorString.WriteString(strconv.FormatFloat(float64(v), 'f', -1, 32))
			if i < len(col.FloatVectors)-1 {
				vectorString.WriteString(",")
			}
		}
		return vectorString.String(), nil
	} else if col.ByteVector != nil {
		return string(col.ByteVector), nil
	}
	return "", NewTRSException(-1, fmt.Sprintf("column %s not found", colName))
}

func (tr *TRSRecord) GetInt(colName string) (int, error) {
	longValue, err := tr.GetLong(colName)
	if err != nil {
		return 0, err
	}
	return int(longValue), nil
}

func (tr *TRSRecord) GetValues(colName string) []string {
	value, err := tr.GetString(colName)
	if err != nil {
		return nil
	}
	if value == "" {
		return nil
	}

	return strings.Split(value, common.DefaultColumnSepChar)
}

func (tr *TRSRecord) GetColumnNames() []string {
	names := make([]string, 0, len(tr.recColumnsData))
	for name := range tr.recColumnsData {
		names = append(names, name)
	}
	return names
}

func (tr *TRSRecord) SetTimestamp(ts int64) {
	tr.timestamp = ts
}

func (tr *TRSRecord) GetTimestamp() int64 {
	return tr.timestamp
}

func (tr *TRSRecord) SetCreateTimestamp(ts int64) {
	tr.createTimestamp = ts
}

func (tr *TRSRecord) GetCreateTimestamp() int64 {
	return tr.createTimestamp
}

func (tr *TRSRecord) ToString(format string) string {
	if format == "json" {
		return tr.toJsonData()
	}
	return tr.toTrsData()
}

func (tr *TRSRecord) toJsonData() string {
	// Implement JSON conversion logic
	return ""
}

func (tr *TRSRecord) toTrsData() string {
	// Implement TRS data conversion logic
	return ""
}

func (tr *TRSRecord) GetSize() int64 {
	return tr.size
}

func (tr *TRSRecord) SetSize(size int64) {
	tr.size = size
}

func (tr *TRSRecord) GetLong(colName string) (int64, error) {
	values := tr.GetValues(colName)
	if values == nil || len(values) == 0 {
		return 0, nil
	}
	value := values[0]
	longValue, err := strconv.ParseInt(value, 10, 64)
	if err != nil {
		return 0, fmt.Errorf("data format error: %v", err)
	}
	return longValue, nil
}

func (tr *TRSRecord) GetFloat(colName string) (float32, error) {
	values := tr.GetValues(colName)
	if values == nil || len(values) == 0 {
		return 0, nil
	}
	floatValue, err := strconv.ParseFloat(values[0], 32)
	if err != nil {
		return 0, fmt.Errorf("data format error: %v", err)
	}
	return float32(floatValue), nil
}

func (tr *TRSRecord) GetDouble(colName string) (float64, error) {
	values := tr.GetValues(colName)
	if values == nil || len(values) == 0 {
		return 0, nil
	}
	doubleValue, err := strconv.ParseFloat(values[0], 64)
	if err != nil {
		return 0, fmt.Errorf("data format error: %v", err)
	}
	return doubleValue, nil
}

func (tr *TRSRecord) GetDate(colName string) (*time.Time, error) {
	values := tr.GetValues(colName)
	if values == nil || len(values) == 0 {
		return nil, nil
	}

	for _, layout := range DATE_FORMATS {
		parsedTime, err := time.Parse(layout, values[0])
		if err == nil {
			return &parsedTime, nil
		}
	}

	return nil, nil
}

func (tr *TRSRecord) SetDbName(dbName string) {
	tr.dbName = dbName
}

func (tr *TRSRecord) GetDbName() string {
	return tr.dbName
}
