package client

import (
	"fmt"
)

type DBRequestBuilder struct{}

func int32Ptr(i int32) *int32 {
	return &i
}

func (b *DBRequestBuilder) BuildCreateDbReq(db *TRSDatabase) (*SchemaRPC_CreateDBRequest, error) {
	database, err := b.buildDatabase(db)
	if err != nil {
		return nil, err
	}

	request := &SchemaRPC_CreateDBRequest{
		Db: database,
	}
	return request, nil
}

func (b *DBRequestBuilder) buildDatabase(db *TRSDatabase) (*SchemaRPC_DatabaseInfo, error) {
	if db.GetName() == "" {
		return nil, fmt.Errorf("DBName can not be null!")
	}

	dbBuilder := &SchemaRPC_DatabaseInfo{
		Name:       &db.name,
		Mode:       int32Ptr(int32(db.mode)),
		Type:       int32Ptr(int32(db.dbType)),
		EngineType: &db.engineType,
		Policy:     int32Ptr(int32(db.policy)),
	}

	for _, column := range db.addColumns {
		colInfo, err := b.buildColumn(column)
		if err != nil {
			return nil, err
		}
		dbBuilder.Columns = append(dbBuilder.Columns, colInfo)
	}

	if db.defSearchColumn != "" {
		dbBuilder.DefSearchColumn = &db.defSearchColumn
	}

	if db.uniqueColumn != "" {
		dbBuilder.UniqueColumn = &db.uniqueColumn
	}

	if db.parser != "" {
		dbBuilder.ParserName = &db.parser
	}

	if db.replications != 0 {
		dbBuilder.RepSubNum = int32Ptr(int32(db.replications))
	}

	if db.splitter != "" {
		dbBuilder.Splitter = &db.splitter
	}

	if db.splitColumn != "" {
		dbBuilder.SplitColumn = &db.splitColumn
	}

	for key, value := range db.splitParams {
		dbBuilder.SplitParams = append(dbBuilder.SplitParams, &Property{
			Key:   &key,
			Value: &value,
		})
	}

	for key, value := range db.TRSObject.Properties {
		dbBuilder.Property = append(dbBuilder.Property, &Property{
			Key:   &key,
			Value: &value,
		})
	}

	return dbBuilder, nil
}

func (b *DBRequestBuilder) buildColumn(col *TRSDatabaseColumn) (*SchemaRPC_ColumnInfo, error) {
	if col.GetName() == "" {
		return nil, fmt.Errorf("Column name can not be null!")
	}

	colInfo := &SchemaRPC_ColumnInfo{
		Name: &col.name,
		Type: int32Ptr(int32(col.colType)),
	}

	for key, value := range col.TRSObject.Properties {
		var k, v = key, value
		colInfo.Property = append(colInfo.Property, &Property{
			Key:   &k,
			Value: &v,
		})
	}

	return colInfo, nil
}

func (b *DBRequestBuilder) BuildRenameColumnRequest(name, oldName, newName string) *SchemaRPC_RenameColumnRequest {
	return &SchemaRPC_RenameColumnRequest{
		DbName:  &name,
		OldName: &oldName,
		NewName: &newName,
	}
}

func (b *DBRequestBuilder) BuildUpdateDBRequest(db *TRSDatabase) (*SchemaRPC_CommitRequest, error) {
	builder := &SchemaRPC_CommitRequest{
		DbName: &db.name,
	}

	database, err := b.buildDatabase(db)
	if err != nil {
		return nil, err
	}
	builder.Db = database

	for _, col := range db.addColumns {
		colInfo, err := b.buildColumn(col)
		if err != nil {
			return nil, err
		}
		builder.Adds = append(builder.Adds, colInfo)
	}

	for _, del := range db.delColumns {
		builder.Deletes = append(builder.Deletes, del)
	}

	for key, col := range db.altColumns {
		colInfo, err := b.buildColumn(col)
		if err != nil {
			return nil, err
		}
		builder.Alters = append(builder.Alters, &SchemaRPC_CommitRequest_AlterColumn{
			Name:     &key,
			NewValue: colInfo,
		})
	}

	return builder, nil
}

func (b *DBRequestBuilder) BuildCopyDBRequest(dbName, sourceCols string) *SchemaRPC_CopyDBRequest {
	return &SchemaRPC_CopyDBRequest{
		Name:          &dbName,
		SourceColumns: &sourceCols,
	}
}

func (b *DBRequestBuilder) BuildListDBRequest(dbName string, options map[string]string) *SchemaRPC_ListDBRequest {
	builder := &SchemaRPC_ListDBRequest{
		DbName: &dbName,
	}

	for key, value := range options {
		builder.Property = append(builder.Property, &Property{
			Key:   &key,
			Value: &value,
		})
	}

	return builder
}
