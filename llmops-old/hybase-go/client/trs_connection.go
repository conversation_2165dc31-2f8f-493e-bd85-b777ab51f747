package client

import (
	"bytes"
	"com/trs/hybase/client/common"
	"com/trs/hybase/client/params"
	"encoding/base64"
	"fmt"
	"hash/fnv"
	"io"
	"math/rand"
	"net"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"google.golang.org/protobuf/proto"
)

var (
	http4ShortClient *http.Client
	http4LongClient  *http.Client
)

// TRSConnection represents a connection to the TRS server
type TRSConnection struct {
	closed                 atomic.Bool
	loadingExtractorFailed atomic.Bool
	loadingThreads         atomic.Int64
	userID                 string
	password               string
	serverURL              string
	failedHosts            sync.Map
	randomServerList       []string
	randomServerLast       string
	requestEncrypt         bool
	connectParams          *params.ConnectParams
	publicKeyString        string
}

// Constants
const (
	INSERT_SKIP_ERROR             = "insert.skip.error"
	SKIP_FILE_ERROR               = "skip.file.error"
	HYBASE_HEADER_LENGTH          = "X-Hybase-HeaderLength"
	HYBASE_HEADER_USER_ID         = "X-Hybase-UserID"
	HYBASE_HEADER_USER_PASSWORD   = "X-Hybase-Password"
	HYBASE_HEADER_CIPHER          = "Hybase-Api-Cipher"
	HYBASE_REQUEST_ENTITY_ENCRYPT = "Hybase-Request-Entity-Encrypt"
	SIZE_1M                       = 1024 * 1024
	SIZE_1G                       = 1024 * SIZE_1M
	MAX_RESPONSE_SIZE             = 64 * SIZE_1M // 限制响应数据的最大长度为64M
	DefaultSoTimeOut              = 5 * time.Minute
)

func init() {
	// Create a new HTTP client with the custom Transport
	http4ShortClient = &http.Client{
		Transport: &http.Transport{
			MaxIdleConns:        1000,
			MaxIdleConnsPerHost: 500,
			IdleConnTimeout:     90 * time.Second,
		},
		Timeout: DefaultSoTimeOut, // Overall timeout for requests
	}

	http4LongClient = &http.Client{
		Transport: &http.Transport{
			MaxIdleConns:        1000,
			MaxIdleConnsPerHost: 500,
			IdleConnTimeout:     90 * time.Second,
		},
		Timeout: DefaultSoTimeOut, // Overall timeout for requests
	}
}

// NewTRSConnection creates a new TRSConnection instance
func NewTRSConnection(serverList, userID, password string, connectParams *params.ConnectParams) *TRSConnection {
	return &TRSConnection{
		serverURL:        serverList,
		randomServerList: strings.Split(serverList, ";"),
		userID:           userID,
		password:         password,
		connectParams:    connectParams,
		requestEncrypt:   connectParams.TRSObject.GetBoolProperty(params.RequestEncryptEnable, false),
	}
}

// Method signatures
func (c *TRSConnection) GetHttp4ShortClient() *http.Client {
	return http4ShortClient
}

func (c *TRSConnection) GetHttp4LongClient() *http.Client {
	return http4LongClient
}

func (c *TRSConnection) EncodeURLData(p string) string {
	encoded := url.QueryEscape(p)
	return encoded
}

func (c *TRSConnection) GetURL() string {
	return c.serverURL
}

func (c *TRSConnection) GetLastConnectServer() string {
	return c.randomServerLast
}

func (c *TRSConnection) GetUserID() string {
	return c.userID
}

func (c *TRSConnection) GetPassword() string {
	return c.password
}

func (c *TRSConnection) GetConnectParams() *params.ConnectParams {
	return c.connectParams
}

func (c *TRSConnection) GetLastConnectURL() string {
	return c.randomServerLast
}

func (c *TRSConnection) NewRequest1(serverList string, path string) (*http.Request, error) {
	if len(c.randomServerList) > 1 {
		serverList = c.SelectServer(false, -1)
		if serverList == "" {
			return nil, &TRSException{ErrorNo: ERR_CONNECT_FAILED, Message: "no server is available!"}
		}

	}
	// Construct the POST request
	post, err := http.NewRequest("POST", serverList+path, nil) // Replace nil with your request body if needed
	if err != nil {
		return nil, &TRSException{ErrorNo: ERR_CONNECT_FAILED, Message: "Connection failed: " + err.Error()}
	}
	return post, nil
}

func (c *TRSConnection) NewRequest(path string) (*http.Request, error) {
	return c.NewRequest1(c.serverURL, path)
}

func (c *TRSConnection) CheckFailedHosts() {
	// Create a slice to hold hosts that need to be recycled
	var recycledHosts []string

	// Iterate over the failed hosts
	c.failedHosts.Range(func(key, value interface{}) bool {
		host := key.(string)
		timestamp := value.(int64)

		// Check if the host has been idle for more than 30 seconds
		if time.Now().UnixNano() > timestamp+30*time.Second.Nanoseconds() {
			recycledHosts = append(recycledHosts, host)
		}
		return true // continue iteration
	})

	// Remove recycled hosts from the failedHosts map
	for _, host := range recycledHosts {
		c.failedHosts.Delete(host)
	}
}

func (c *TRSConnection) SelectServer(isRandom bool, priorIndex int) string {
	// Recycle hosts that have been idle for more than 30 seconds
	c.CheckFailedHosts()

	var idx int
	if priorIndex != -1 {
		// If a prior index is set, select that IP first
		idx = priorIndex
	} else {
		// If isRandom is false, start from the last successful connection
		// If isRandom is true or it's the first request, select randomly
		lastIndex := -1
		for i, host := range c.randomServerList {
			if host == c.randomServerLast {
				lastIndex = i
				break
			}
		}
		if lastIndex < 0 || lastIndex >= len(c.randomServerList) || isRandom {
			idx = rand.Intn(len(c.randomServerList))
		} else {
			idx = lastIndex
		}
	}

	// Check the list of failed hosts
	var selectedAddr string
	for i := 0; i < len(c.randomServerList); i++ {
		selectedAddr = c.randomServerList[(i+idx)%len(c.randomServerList)]
		if _, ok := c.failedHosts.Load(selectedAddr); !ok {
			c.randomServerLast = selectedAddr
			return selectedAddr
		}
	}

	return "" // Return an empty string if no available server is found
}

func (c *TRSConnection) SendHttpRequest(request *http.Request, urlPath string, isLongRequest, isRandom bool, priorIndex int, soTimeOut time.Duration) (*http.Response, error) {
	var failure error
	var response *http.Response
	var curServer string
	retryCount := 0

	for retryCount < len(c.randomServerList) {
		curServer = c.SelectServer(isRandom, priorIndex)
		if curServer == "" {
			break
		}
		retryCount++
		var err error
		// Set user ID and password headers
		request.Header.Set(HYBASE_HEADER_USER_ID, c.userID)
		request.Header.Set(HYBASE_HEADER_USER_PASSWORD, c.password)
		request.Header.Set(HYBASE_HEADER_CIPHER, "false")

		// Set timeout for the request
		client := c.GetHttp4ShortClient()
		if isLongRequest {
			client = c.GetHttp4LongClient()
		} else {
			if soTimeOut >= DefaultSoTimeOut {
				client.Timeout = soTimeOut
			}
			client = c.GetHttp4ShortClient()
		}

		// Send the request
		response, err = client.Do(request)
		if err != nil {
			if isNetworkError(err) {
				// Connection failed, add to failed hosts and retry
				c.failedHosts.Store(curServer, time.Now().UnixNano())
				failure = NewTRSException(ERR_CONNECT_FAILED, err.Error())
				priorIndex = -1
				c.randomServerLast = ""
				continue
			}
			return nil, NewTRSException(ERR_CONNECT_FAILED, err.Error())
		}

		// Check for response errors
		if err := c.CheckResponseError(response); err != nil {
			return nil, err
		}
		break
	}

	// If all IPs failed to connect, return an error
	if response == nil {
		if failure != nil {
			return nil, failure.(*TRSException)
		}

		return nil, NewTRSException(ERR_CONNECT_FAILED, "no server is available")
	}

	return response, nil
}

func (c *TRSConnection) CheckResponseError(response *http.Response) *TRSException {
	if response == nil || response.StatusCode == http.StatusOK {
		return nil // No error
	}

	// Close the response body to prevent resource leaks
	defer response.Body.Close()

	// Read the reason message
	reasonMsg := response.Status

	// Extract error number and message
	var errMsg string
	var errNo int
	sepPos := findSeparator(reasonMsg)

	if sepPos > 0 {
		// Extract error message from the reason
		errMsg = reasonMsg[sepPos+1:]
		errNoStr := reasonMsg[:sepPos]

		// Decode the error message if it's base64 encoded
		decodedMsg, decodeErr := base64.StdEncoding.DecodeString(errMsg)
		if decodeErr == nil {
			errMsg = string(decodedMsg)
		}

		// Parse the error number
		errNo = parseErrorNumber(errNoStr)
	} else {
		errMsg = reasonMsg
		errNo = response.StatusCode
	}

	if errNo == 0 {
		errMsg = reasonMsg
		errNo = ERR_UNDEFINED
	}

	return NewTRSException(errNo, errMsg)
}

// findSeparator finds the position of the first comma in the reason message.
func findSeparator(reasonMsg string) int {
	return strings.Index(reasonMsg, ",")
}

// parseErrorNumber parses the error number from a string.
func parseErrorNumber(errNoStr string) int {
	errNo, err := strconv.Atoi(errNoStr)
	if err != nil {
		return -1 // Return -1 if parsing fails
	}
	return errNo
}

func isNetworkError(err error) bool {
	if err == nil {
		return false
	}

	// Check if the error is of type net.Error
	if netErr, ok := err.(net.Error); ok {
		return netErr.Timeout() || netErr.Temporary()
	}

	// Check for specific error types
	switch err.Error() {
	case "connection refused":
		return true
	case "no route to host":
		return true
	case "network is unreachable":
		return true
	}

	// You can add more specific error checks as needed

	return false
}

func (c *TRSConnection) DeleteDatabase(dbName string, recordOnly bool) (bool, *TRSException) {
	if dbName == "" {
		return false, NewTRSException(ERR_INVALID_PARAMETER, "database name can't be null")
	}

	// Construct the URL path for the delete request
	path := fmt.Sprintf("%s?action=delete_db&dbname=%s&hybase.drop.records=%t",
		common.APIURLSchema,
		c.EncodeURLData(dbName),
		recordOnly)

	// Create a new HTTP request
	req, err := c.NewRequest(path)
	if err != nil {
		return false, NewTRSException(ERR_CONNECT_FAILED, err.Error())
	}

	// Send the HTTP request
	response, err := c.SendHttpRequest(req, path, false, false, -1, DefaultSoTimeOut)
	if err != nil {
		return false, NewTRSException(ERR_CONNECT_FAILED, err.Error())
	}
	defer response.Body.Close() // Ensure the response body is closed

	return true, nil
}

func (c *TRSConnection) GetDatabases(DBNames string, options map[string]string) ([]*TRSDatabase, *TRSException) {
	var databaseList []*TRSDatabase
	var respStream io.ReadCloser
	var listDBResponse *SchemaRPC_ListDBResponse

	// 构建请求路径
	path := fmt.Sprintf("%s?action=GET_DB&dbname=%s", common.APIURLSchema, url.QueryEscape(DBNames))

	// 创建 HTTP 请求
	req, err := c.NewRequest(path)
	if err != nil {
		return nil, NewTRSException(ERR_CONNECT_FAILED, err.Error())
	}
	// 设置请求体
	request := (&DBRequestBuilder{}).BuildListDBRequest(DBNames, options)
	byteArray, err := proto.Marshal(request)
	if err != nil {
		return nil, NewTRSException(-1, fmt.Sprintf("failed to marshal request: %v", err))
	}

	req.Header.Set(HYBASE_HEADER_LENGTH, fmt.Sprintf("%d", proto.Size(request)))
	req.Body = io.NopCloser(bytes.NewReader(byteArray))

	response, err := c.SendHttpRequest(req, path, false, false, -1, DefaultSoTimeOut)
	if err != nil {
		return nil, NewTRSException(ERR_CONNECT_FAILED, err.Error())
	}
	defer response.Body.Close()

	// Limit the size of the response stream
	limitedReader := io.LimitReader(response.Body, int64(1.5*MAX_RESPONSE_SIZE))
	respStream = &ReadCloserWrapper{Reader: limitedReader} // Wrap it in ReadCloserWrapper

	// Read all bytes from respStream
	data, err := io.ReadAll(respStream)
	if err != nil {
		return nil, NewTRSException(-1, fmt.Sprintf("failed to read response: %v", err))
	}

	listDBResponse = &SchemaRPC_ListDBResponse{}
	if err := proto.Unmarshal(data, listDBResponse); err != nil {
		return nil, NewTRSException(-1, fmt.Sprintf("failed to unmarshal response: %v", err))
	}

	// Check if the GetDb() slice is empty
	if len(listDBResponse.GetDb()) == 0 {
		return []*TRSDatabase{}, nil
	}

	// 处理数据库信息
	for _, dbInfo := range listDBResponse.GetDb() {
		database := &TRSDatabase{}
		database.ReadFromRPC(dbInfo)
		databaseList = append(databaseList, database)
	}

	return databaseList, nil
}

func (c *TRSConnection) ExecuteSelect(strSources, strWhere string, start, recordNum int64, params *params.SearchParams) (*TRSResultSet, error) {
	if params == nil {
		return nil, NewTRSException(ERR_INVALID_PARAMETER, "params can't be null!")
	}
	if strSources == "" {
		return nil, NewTRSException(ERR_INVALID_PARAMETER, "strSources can't be null!")
	}
	if start < 0 || recordNum < 0 {
		return nil, NewTRSException(ERR_INVALID_PARAMETER, "start or recordNum parameter can't be negative!")
	}
	if strWhere == "" {
		strWhere = "*:*"
	}

	var resultSet *TRSResultSet
	var req *http.Request
	var respStream io.ReadCloser
	var searchResponse *SearchRPC_SearchRecordResponse
	// Construct the request path
	path := "/api/search.do"

	// Set parameters for the request
	params.SetBoolProperty("read.batch.partial", true) // Enable partial batch reading
	request, err := NewRequestBuilder().BuildSearchReq(strSources, strWhere, start, recordNum, params)
	if err != nil {
		return nil, NewTRSException(-1, fmt.Sprintf("failed to build search request: %v", err))
	}

	graph := params.GetProperty("graph.search")
	byteArray, err := proto.Marshal(request)
	if err != nil {
		return nil, NewTRSException(-1, fmt.Sprintf("failed to marshal request: %v", err))
	}

	req, err = c.NewRequest(path)
	if err != nil {
		return nil, NewTRSException(ERR_CONNECT_FAILED, err.Error())
	}
	if graph != "" {
		// requestStream := c.GetSelectHttpEntity(byteArray, graph)
		// requestEntity = io.NopCloser(requestStream)
	}

	// Set the content length header
	req.Header.Set(HYBASE_HEADER_LENGTH, fmt.Sprintf("%d", proto.Size(request)))
	req.Body = io.NopCloser(bytes.NewReader(byteArray))

	// Send the HTTP request
	priorIP := c.GetPriorIP(strWhere)

	// Call SendHttpRequest instead of using http.Client directly
	response, err := c.SendHttpRequest(req, path, false, false, priorIP, time.Duration(params.GetTimeOut())*time.Second)
	if err != nil {
		return nil, NewTRSException(ERR_CONNECT_FAILED, err.Error())
	}
	defer response.Body.Close()

	// Limit the size of the response stream
	limitedReader := io.LimitReader(response.Body, int64(1.5*MAX_RESPONSE_SIZE))
	respStream = &ReadCloserWrapper{Reader: limitedReader} // Wrap it in ReadCloserWrapper

	// Read all bytes from respStream
	data, err := io.ReadAll(respStream)
	if err != nil {
		return nil, NewTRSException(-1, fmt.Sprintf("failed to read response: %v", err))
	}

	// Parse the response
	// protoStream := CodedInputStream.New(respStream)
	// protoStream.SetSizeLimit(int(2 * MAX_RESPONSE_SIZE))
	searchResponse = &SearchRPC_SearchRecordResponse{}
	if err := proto.Unmarshal(data, searchResponse); err != nil {
		return nil, NewTRSException(-1, fmt.Sprintf("failed to unmarshal response: %v", err))
	}

	// Process the search response
	parser := &ResultSetParser{}
	resultSet, err = parser.ProcessSearchResponse(searchResponse, c)
	if err != nil {
		return nil, NewTRSException(-1, fmt.Sprintf("failed to process search response: %v", err))
	}
	resultSet.SetStart(start)
	resultSet.SetSearchRequest(request)
	resultSet.SetSearchParams(params)

	return resultSet, nil
}

// Internal classes or types can be defined here if needed
// For example, if there are any specific types or structs used within TRSConnection

// ReadCloserWrapper wraps an io.Reader and implements io.ReadCloser
type ReadCloserWrapper struct {
	io.Reader
}

// Close does nothing, as we are not managing any resources
func (rcw *ReadCloserWrapper) Close() error {
	return nil
}

// getPriorIP calculates the index of the server based on the provided strWhere string.
func (c *TRSConnection) GetPriorIP(strWhere string) int {
	if len(c.randomServerList) > 1 && strWhere != "" {
		// Hash the strWhere string
		h := fnv.New32a()
		h.Write([]byte(strWhere))
		hashValue := h.Sum32()
		return int(hashValue % uint32(len(c.randomServerList)))
	}
	return -1
}

// GetRecords retrieves records based on the provided request.
func (c *TRSConnection) GetRecords(request *SearchRPCGetRecordsRequest) (*SearchRPC_SearchRecordResponse, error) {
	var req *http.Request
	var respStream io.Reader
	var searchResponse *SearchRPC_SearchRecordResponse

	// Create the request
	path := "/api/getrecords.do"
	body, err := proto.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %v", err)
	}

	req, err = http.NewRequest("POST", c.serverURL+path, bytes.NewReader(body))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// Set the appropriate headers
	req.Header.Set("Content-Type", "application/octet-stream")

	// Send the request
	response, err := c.SendHttpRequest(req, path, false, false, -1, DefaultSoTimeOut)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %v", err)
	}
	defer response.Body.Close()

	// Limit the size of the response stream
	respStream = io.LimitReader(response.Body, MAX_RESPONSE_SIZE)

	data, err := io.ReadAll(respStream)
	if err != nil {
		return nil, NewTRSException(-1, fmt.Sprintf("failed to read response: %v", err))
	}

	// Parse the response
	if err := proto.Unmarshal(data, searchResponse); err != nil {
		return nil, fmt.Errorf("failed to parse response: %v", err)
	}

	return searchResponse, nil
}

func (c *TRSConnection) CategoryQuery(strSources, query, defaultColumn, categoryColumn string, topNum int64, params *params.SearchParams) (*TRSResultSet, *TRSException) {
	if strSources == "" {
		return nil, NewTRSException(ERR_INVALID_PARAMETER, "strSources can't be null!")
	}
	if categoryColumn == "" {
		return nil, NewTRSException(ERR_INVALID_PARAMETER, "categoryColumn can't be null!")
	}
	if topNum <= 0 {
		return nil, NewTRSException(ERR_INVALID_PARAMETER, "topNum parameter must be positive!")
	}

	if query == "" {
		query = "*:*"
	}

	var req *http.Request
	var respStream io.Reader
	var resultList *TRSResultSet

	// Create the request
	path := "/api/category.do"
	request, err := NewRequestBuilder().BuildCategoryQueryReq(strSources, query, defaultColumn, categoryColumn, topNum, params)
	if err != nil {
		return nil, NewTRSException(-1, fmt.Sprintf("failed to build category query request: %v", err))
	}
	body, err := proto.Marshal(request)
	if err != nil {
		return nil, NewTRSException(-1, fmt.Sprintf("failed to marshal request: %v", err))
	}

	req, err = c.NewRequest(path)
	req.Body = io.NopCloser(bytes.NewReader(body))
	if err != nil {
		return nil, NewTRSException(-1, fmt.Sprintf("failed to create request: %v", err))
	}

	// req, err = http.NewRequest("POST", c.serverURL+path, bytes.NewReader(body))

	// Set the appropriate headers
	req.Header.Set("Content-Type", "application/octet-stream")

	// Send the request
	response, err := c.SendHttpRequest(req, path, false, false, -1, DefaultSoTimeOut)
	if err != nil {
		return nil, NewTRSException(-1, fmt.Sprintf("failed to send request: %v", err))
	}
	defer response.Body.Close()

	// Read the response stream
	respStream = io.LimitReader(response.Body, MAX_RESPONSE_SIZE)

	// Parse the response
	categoryResponse := &SearchRPC_CategoryQueryResponse{}
	data, err := io.ReadAll(respStream)
	if err != nil {
		return nil, NewTRSException(-1, fmt.Sprintf("failed to read response: %v", err))
	}
	if err := proto.Unmarshal(data, categoryResponse); err != nil {
		return nil, NewTRSException(-1, fmt.Sprintf("failed to parse response: %v", err))
	}

	// Process the category response into a TRSResultSet
	parser := &ResultSetParser{}
	resultList, err = parser.ProcessCategoryResponse(categoryResponse)
	if err != nil {
		return nil, NewTRSException(-1, fmt.Sprintf("failed to process category response: %v", err))
	}
	return resultList, nil
}

func (c *TRSConnection) CreateDatabase(db *TRSDatabase) (bool, error) {
	if db.dbType == TYPE_ALIAS {
		if db.GetProperty("runtime.alias.db") == "" {
			return false, NewTRSException(ERR_INVALID_PARAMETER, "aliasDb is Empty!")
		}
	}

	if db.dbType != TYPE_ALIAS && db.dbType != TYPE_MIRROR && db.dbType != TYPE_BALANCE {
		if len(db.addColumns) == 0 && db.columns != nil {
			for key, column := range db.columns {
				db.addColumns[key] = column
			}
		}
		if len(db.addColumns) == 0 {
			return false, NewTRSException(ERR_COLUMN_LIST, "no field definition found in database")
		}
	}

	for _, column := range db.addColumns {
		if column.GetColType() == TYPE_PHRASE || column.GetColType() == TYPE_DOCUMENT {
			if column.GetProperty("index.cjkwidth.convert") == "" {
				column.SetProperty("index.cjkwidth.convert", "true")
			}
		}

		if column.GetColType() == TYPE_VECTOR {
			if column.GetProperty("index.vector.dims") == "" {
				column.SetProperty("index.vector.dims", "4096")
			}
			if column.GetProperty("index.vector.similarity") == "" {
				column.SetProperty("index.vector.similarity", "EUCLIDEAN")
			}
			if column.GetProperty("index.vector.arithmetic") == "" {
				column.SetProperty("index.vector.arithmetic", "hnsw")
			}
			if column.GetProperty("index.hnsw.m") == "" {
				column.SetProperty("index.hnsw.m", "16")
			}
			if column.GetProperty("index.hnsw.ef") == "" {
				column.SetProperty("index.hnsw.ef", "100")
			}
			if column.GetProperty("index.vector.encoding.type") == "" {
				column.SetProperty("index.vector.encoding.type", "FLOAT")
			}
		}
	}

	// Build the request body using the database object
	request, err := (&DBRequestBuilder{}).BuildCreateDbReq(db)
	if err != nil {
		return false, err
	}

	byteArray, err := proto.Marshal(request)
	if err != nil {
		return false, NewTRSException(-1, fmt.Sprintf("failed to marshal request: %v", err))
	}

	path := common.APIURLSchema + "?action=create"
	req, err := c.NewRequest(path)
	req.Body = io.NopCloser(bytes.NewReader(byteArray))

	if err != nil {
		return false, err
	}
	// Send the HTTP request
	response, err := c.SendHttpRequest(req, path, false, false, -1, DefaultSoTimeOut)
	if err != nil {
		return false, err
	}
	defer response.Body.Close()

	if response.StatusCode != http.StatusOK {
		return false, fmt.Errorf("failed to create database, status code: %d", response.StatusCode)
	}

	return true, nil
}

func (c *TRSConnection) getRelatedFiles(recordList []*InputRecord) []*InputFile {
	if recordList == nil {
		return nil
	}
	var files []*InputFile
	for _, inputRecord := range recordList {
		columnsList := inputRecord.GetColumns() // Assuming this method exists
		for _, inputColumn := range columnsList {
			if inputColumn.GetFiles() != nil { // Assuming this method exists
				files = append(files, inputColumn.GetFiles().GetFiles()...) // Assuming this method exists
			}
		}
	}
	return files
}

func (c *TRSConnection) flushLoadRequest(builder *MaintRPC_InsertRecordRequest, skipFileError bool, report *TRSReport) (int64, error) {
	if len(builder.GetRecord()) == 0 {
		return 0, nil
	}

	var requestStream io.Reader
	var requestEntity io.Reader

	path := "/api/insertrecords.do"
	req, err := c.NewRequest(path)
	if err != nil {
		return 0, err
	}

	byteArray, err := proto.Marshal(builder)
	if err != nil {
		return 0, err
	}
	req.Header.Set(HYBASE_HEADER_LENGTH, fmt.Sprintf("%d", proto.Size(builder)))

	relatedFiles := c.getRelatedFiles(builder.GetRecord())
	if len(relatedFiles) == 0 {
		requestEntity = bytes.NewReader(byteArray)
	} else {
		requestStream = NewCountInputStream(NewRequestMultiFileStream(byteArray, relatedFiles, skipFileError))
		requestEntity = requestStream
	}

	req.Body = io.NopCloser(requestEntity)
	req.Header.Set("Transfer-Encoding", "chunked")

	response, err := c.SendHttpRequest(req, path, false, false, -1, DefaultSoTimeOut)
	if err != nil {
		return 0, err
	}
	defer response.Body.Close()

	respStream := io.LimitReader(response.Body, MAX_RESPONSE_SIZE)

	data, err := io.ReadAll(respStream) // Read all data from the reader
	if err != nil {
		return 0, err
	}

	r := &Report{}
	if err := proto.Unmarshal(data, r); err != nil {
		return 0, err
	}

	report.OnReport(r)
	return max(0, r.GetInsertedNum()), nil
}

// ExecuteInsert inserts records into the specified database.
func (c *TRSConnection) ExecuteInsert(DBName string, records []TRSInputRecord, params *params.OperationParams, report *TRSReport, listener IReportListener) error {
	if DBName == "" {
		return NewTRSException(ERR_INVALID_PARAMETER, "DBName cannot be null")
	}
	if len(records) == 0 {
		return nil // No records to insert
	}

	maxBufferSize := int64(16 * SIZE_1M)
	addReq := &MaintRPC_InsertRecordRequest{
		Source: &DBName,
	}

	track := false
	if report != nil {
		if listener != nil {
			report.listener = listener
			report.AddSkipListener()
			report.AddErrorListener() // Compatibility with old versions
		}
		if report.GetListenerItems() > 0 {
			isSkip := params.GetBoolProperty(INSERT_SKIP_ERROR, false)
			track = isSkip
			key := "hybase.listen.records"
			value := fmt.Sprintf("%d", report.GetListenerItems())
			addReq.Options = append(addReq.Options, &Property{
				Key:   &key,
				Value: &value,
			})
		}
	}

	if params != nil {
		for key, value := range params.Properties {
			addReq.Options = append(addReq.Options, &Property{
				Key:   &key,
				Value: &value,
			})
		}
	}

	totalReadSize := atomic.Int64{}
	batchStart := 0
	batchEnd := 0

	for i := 0; i < len(records); {
		totalReadSize.Store(0)
		batchStart = i
		for totalReadSize.Load() < maxBufferSize && i < len(records) {
			recordBuilder, err := NewRequestBuilder().BuildRecord(&records[i], true, &totalReadSize, params)
			if err != nil {
				if !track {
					return NewTRSException(ERR_INVALID_PARAMETER, err.Error())
				}
				report.OnError(NewTRSException(ERR_INVALID_PARAMETER, err.Error()), int64(i), int64(i+1))
				i++
				continue
			}
			if recordBuilder != nil {
				recordBuilder.Tag = proto.Int64(int64(i))
				addReq.Record = append(addReq.Record, recordBuilder)
			} else if track {
				report.OnSkip(int64(i), int(SKIP_APPENDIX), "client build record (error) skip!")
			}
			batchEnd = i + 1
			i++
		}
		if len(addReq.GetRecord()) > 0 {
			if _, err := c.flushLoadRequest(addReq, params.GetBoolProperty(SKIP_FILE_ERROR, false), report); err != nil {
				if track {
					trsErr := NewTRSException(ERR_UNDEFINED, err.Error())
					report.OnError(trsErr, int64(batchStart), int64(batchEnd)) // Report the error
					report.OnSkip(int64(batchEnd), int(SKIP_UNHANDLE), "client batch aborted!")
					return nil
				} else {
					return NewTRSException(ERR_INVALID_PARAMETER, err.Error())
				}
			}
		}
	}

	if report != nil {
		report.OnInsertFinish()
	}

	return nil
}

func (c *TRSConnection) ExecuteDeleteQuery(DBName string, query string, params *params.SearchParams, report *TRSReport) (int64, *TRSException) {
	if query == "" {
		return 0, NewTRSException(ERR_DELETE_WHERE, "Query can't be null!")
	}

	qList := []string{query}
	if report != nil && report.GetListenerItems() > 0 {
		params.Properties["hybase.listen.records"] = fmt.Sprintf("%d", report.GetListenerItems())
	}

	request, err := (&RequestBuilder{}).BuildDeleteReqByQuery(DBName, qList, params)
	if err != nil {
		return 0, NewTRSException(ERR_INVALID_PARAMETER, err.Error())
	}

	path := "/api/deletequery.do"
	req, err := c.NewRequest(path)
	if err != nil {
		return 0, NewTRSException(ERR_CONNECT_FAILED, err.Error())
	}
	byteArray, err := proto.Marshal(request)
	if err != nil {
		return 0, NewTRSException(ERR_CONNECT_FAILED, err.Error())
	}
	req.Body = io.NopCloser(bytes.NewReader(byteArray))
	req.Header.Set(HYBASE_HEADER_LENGTH, fmt.Sprintf("%d", proto.Size(request)))

	response, err := c.SendHttpRequest(req, path, false, false, -1, DefaultSoTimeOut)
	if err != nil {
		return 0, NewTRSException(ERR_CONNECT_FAILED, err.Error())
	}
	defer response.Body.Close()

	respStream := io.LimitReader(response.Body, MAX_RESPONSE_SIZE)
	data, err := io.ReadAll(respStream)
	if err != nil {
		return 0, NewTRSException(ERR_CONNECT_FAILED, err.Error())
	}
	r := &Report{}
	if err := proto.Unmarshal(data, r); err != nil {
		return 0, NewTRSException(ERR_CONNECT_FAILED, err.Error())
	}

	report.OnReport(r)
	report.OnDeleteFinish()

	return r.GetDeletedNum(), nil
}

func (c *TRSConnection) ExecuteDelete(DBName string, uids []string, report *TRSReport) *TRSException {
	path := "/api/deleterecords.do"
	options := make(map[string]string)
	if report != nil && report.GetListenerItems() > 0 {
		options["hybase.listen.records"] = fmt.Sprintf("%d", report.GetListenerItems())
	}
	request, err := (&RequestBuilder{}).BuildDeleteReqByIDWithOptions(DBName, uids, options)
	if err != nil {
		return NewTRSException(ERR_INVALID_PARAMETER, err.Error())
	}
	byteArray, err := proto.Marshal(request)
	if err != nil {
		return NewTRSException(ERR_INVALID_PARAMETER, err.Error())
	}

	req, err := c.NewRequest(path)
	if err != nil {
		return NewTRSException(ERR_INVALID_PARAMETER, err.Error())
	}
	req.Body = io.NopCloser(bytes.NewReader(byteArray))
	req.Header.Set(HYBASE_HEADER_LENGTH, fmt.Sprintf("%d", len(byteArray)))

	response, err := c.SendHttpRequest(req, path, false, false, -1, DefaultSoTimeOut)
	if err != nil {
		return NewTRSException(ERR_INVALID_PARAMETER, err.Error())
	}

	defer response.Body.Close()

	respStream := io.LimitReader(response.Body, MAX_RESPONSE_SIZE)
	data, err := io.ReadAll(respStream)
	if err != nil {
		return NewTRSException(ERR_CONNECT_FAILED, err.Error())
	}
	r := &Report{}
	if err := proto.Unmarshal(data, r); err != nil {
		return NewTRSException(ERR_CONNECT_FAILED, err.Error())
	}

	report.OnReport(r)
	report.OnDeleteFinish()

	return nil
}

func (c *TRSConnection) ExecuteUpdate(DBName string, records []TRSInputRecord, params *params.OperationParams, report *TRSReport) *TRSException {
	if len(records) == 0 {
		return nil
	}

	if DBName == "" {
		return NewTRSException(ERR_INVALID_PARAMETER, "DBName can not be null!")
	}

	track := false
	if report != nil && report.GetListenerItems() > 0 {
		track = true
		if params != nil {
			params.SetBoolProperty(INSERT_SKIP_ERROR, true)
		}
	}

	// textExtractFilter := make(map[string]map[string]struct{})
	// if params != nil && params.Properties["server.doc.extract"] == "true" {
	// 	if extractFilter, ok := params.Properties["doc.extract.suffix.wl"]; ok {
	// 		wl := make(map[string]struct{})
	// 		for _, item := range strings.Split(strings.ToLower(extractFilter), ",") {
	// 			wl[item] = struct{}{}
	// 		}
	// 		textExtractFilter["wl"] = wl
	// 	}
	// 	if extractFilter, ok := params.Properties["doc.extract.suffix.bl"]; ok {
	// 		bl := make(map[string]struct{})
	// 		for _, item := range strings.Split(strings.ToLower(extractFilter), ",") {
	// 			bl[item] = struct{}{}
	// 		}
	// 		textExtractFilter["bl"] = bl
	// 	}
	// }

	builder := MaintRPC_UpdateRecordRequest{}
	builder.Source = &DBName
	builder.SkipFileError = proto.Bool(params.GetBoolProperty(SKIP_FILE_ERROR, false))

	if params != nil {
		for key, value := range params.Properties {
			builder.Options = append(builder.Options, buildProperty(key, value))
		}
	}
	if track {
		builder.Options = append(builder.Options, buildProperty("hybase.listen.records", fmt.Sprintf("%d", report.GetListenerItems())))
	}

	const MAX_INSERT_SIZE int64 = 16 * SIZE_1M
	totalReadSize := atomic.Int64{}

	batchStart := 0
	batchEnd := 0
	for i := 0; i < len(records); {
		batchStart = i
		totalReadSize.Store(0)
		builder.Record = nil

		for totalReadSize.Load() < MAX_INSERT_SIZE && i < len(records) {
			record, err := (&RequestBuilder{}).BuildRecord(&records[i], false, &totalReadSize, params)
			if err != nil {
				if !track {
					return NewTRSException(ERR_INVALID_PARAMETER, err.Error())
				}
				report.OnError(NewTRSException(int(i), err.Error()), int64(i), int64(i+1))
				i++
				continue
			}

			if record != nil {
				record.Tag = proto.Int64(int64(i))
				builder.Record = append(builder.Record, record)
			} else if track {
				report.OnSkip(int64(i), int(SKIP_APPENDIX), "client build record (error) skip!")
			}
			batchEnd = i + 1
			i++
		}

		if len(builder.GetRecord()) > 0 {
			if _, err := c.executeUpdate(&builder, params, report); err != nil {
				trsErr := NewTRSException(ERR_UNDEFINED, err.Error())
				report.OnError(trsErr, int64(batchStart), int64(batchEnd)) // Report the error
				report.OnSkip(int64(batchEnd), int(SKIP_UNHANDLE), "client batch aborted!")
				return trsErr
			}
		}
	}

	if report != nil {
		report.OnUpdateFinish()
		report.OnInsertFinish()
	}

	return nil
}

func (c *TRSConnection) executeUpdate(updateReq *MaintRPC_UpdateRecordRequest, params *params.OperationParams, report *TRSReport) (int, *TRSException) {
	if updateReq == nil {
		return 0, nil
	}

	var req *http.Request
	var resp *http.Response
	var err error
	defer func() {
		if resp != nil {
			resp.Body.Close()
		}
	}()

	path := "/api/updaterecords.do"
	req, err = c.NewRequest(path)
	if err != nil {
		return 0, NewTRSException(ERR_CONNECT_FAILED, err.Error())
	}
	byteArray, err := proto.Marshal(updateReq)
	if err != nil {
		return 0, NewTRSException(ERR_CONNECT_FAILED, fmt.Sprintf("failed to marshal request: %v", err))
	}

	req.Header.Set(HYBASE_HEADER_LENGTH, fmt.Sprintf("%d", len(byteArray)))

	relatedFiles := c.getRelatedFiles(updateReq.GetRecord())
	entity := NewCountInputStream(NewRequestMultiFileStream(byteArray, relatedFiles, params.GetBoolProperty(SKIP_FILE_ERROR, false)))
	req.Body = io.NopCloser(entity)
	req.Header.Set("Transfer-Encoding", "chunked")

	resp, err = c.SendHttpRequest(req, path, false, false, -1, DefaultSoTimeOut)
	if err != nil {
		return 0, NewTRSException(ERR_CONNECT_FAILED, err.Error())
	}

	defer resp.Body.Close()

	respStream := io.LimitReader(resp.Body, MAX_RESPONSE_SIZE)
	data, err := io.ReadAll(respStream)
	if err != nil {
		return 0, NewTRSException(ERR_CONNECT_FAILED, err.Error())
	}
	r := &Report{}
	if err := proto.Unmarshal(data, r); err != nil {
		return 0, NewTRSException(ERR_CONNECT_FAILED, err.Error())
	}

	report.OnReport(r)

	return int(r.GetUpdatedNum()) + int(r.GetInsertedNum()), nil
}
