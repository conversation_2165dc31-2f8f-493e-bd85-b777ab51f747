package client

import (
	"fmt"
	"strconv"
	"strings"
	"sync"
)

type SKIPTYPE int

const (
	SKIP_UNKNOWN SKIPTYPE = iota
	SKIP_DUPLICATE
	SKIP_COLUMNVALUE
	SKIP_APPENDIX
	SKIP_UNHANDLE
)

const (
	INSERTED_REC = 1 << iota
	UPDATED_REC
	DEL_UNIQUE
	GEN_UUID
	UPDATE_UUID
	DEL_UUID
	SKIP
	ERROR
)

func (s SKIPTYPE) String() string {
	switch s {
	case SKIP_UNKNOWN:
		return "SKIP_UNKNOWN"
	case SKIP_DUPLICATE:
		return "SKIP_DUPLICATE"
	case SKIP_COLUMNVALUE:
		return "SKIP_COLUMNVALUE"
	case SKIP_APPENDIX:
		return "SKIP_APPENDIX"
	case SKIP_UNHANDLE:
		return "SKIP_UNHANDLE"
	default:
		return fmt.Sprintf("Unknown SKIPTYPE: %d", s)
	}
}

type IReportListener interface {
	Report(tag string, report []string)
	OnInsertedRec(recordIndex int64)
	OnUpdatedRec(recordIndex int64)
	OnDelRec(uuid string, unique string)
	OnGenUUID(recordIndex int64, UUID string)
	OnUpdateUUID(recordIndex int64, UUID string)
	OnDelUUID(recordIndex int64, UUIDs []string)
	OnSkip(recordIndex int64, skipID int, reason string)
	OnError(recordIndex int64, errorID int, errorMessage string)
	OnDelError(uuid string, errorID int, errorMessage string)
	OnInsertFinish(totalNum int64)
	OnUpdateFinish(totalNum int64)
	OnDeleteFinish(totalNum int64)
	Clear()
}

type TRSReport struct {
	listenerItems int
	listener      IReportListener
	insertedNum   int64
	updatedNum    int64
	deletedNum    int64
	insertedRecs  map[int64]string
	updatedRecs   map[int64]string
	deletedRecs   map[string]string
	genUUIDs      map[int64]string
	updateUUIDs   map[int64]string
	delUUIDs      map[int64]string
	skipRecs      map[int64]string
	errorRecs     map[int64]string
	delError      map[string]string
	errorNo       int
	errorMsg      string
	keyValues     map[string]string
	mu            sync.Mutex
}

func NewTRSReport(listener IReportListener) *TRSReport {
	if listener == nil {
		listener = &defaultListener{}
	}
	return &TRSReport{
		listener:     listener,
		insertedRecs: make(map[int64]string),
		updatedRecs:  make(map[int64]string),
		deletedRecs:  make(map[string]string),
		genUUIDs:     make(map[int64]string),
		updateUUIDs:  make(map[int64]string),
		delUUIDs:     make(map[int64]string),
		skipRecs:     make(map[int64]string),
		errorRecs:    make(map[int64]string),
		delError:     make(map[string]string),
		keyValues:    make(map[string]string),
	}
}

func (r *TRSReport) GetLong(item string) int64 {
	value, exists := r.keyValues[item]
	if !exists || value == "" {
		return 0
	}
	valueInt, err := strconv.ParseInt(value, 10, 64)
	if err != nil {
		return 0
	}
	return valueInt
}

func (r *TRSReport) Get(item string) string {
	return r.keyValues[item]
}

func (r *TRSReport) GetReportMap() map[string]string {
	return r.keyValues
}

func (r *TRSReport) ToString() string {
	var sb strings.Builder
	for key, value := range r.keyValues {
		sb.WriteString(key + "=" + value)
		if sb.Len() > 0 {
			sb.WriteString(",")
		}
	}
	return sb.String()
}

func (r *TRSReport) AddItem(key, value string) {
	r.mu.Lock()
	defer r.mu.Unlock()
	r.keyValues[key] = value
}

func (r *TRSReport) GetInsertedNum() int64 {
	return r.insertedNum
}

func (r *TRSReport) GetDeletedNum() int64 {
	return r.deletedNum
}

func (r *TRSReport) GetUpdatedNum() int64 {
	return r.updatedNum
}

func (r *TRSReport) GetErrorNo() int {
	return r.errorNo
}

func (r *TRSReport) GetErrorMessage() string {
	return r.errorMsg
}

func (r *TRSReport) GetListenerItems() int {
	return r.listenerItems
}

func (r *TRSReport) SetListenerItems(listenerItems int) {
	r.listenerItems = listenerItems
}

func (r *TRSReport) AddAllListenerItems() {
	r.listenerItems = INSERTED_REC | UPDATED_REC | DEL_UNIQUE | GEN_UUID | UPDATE_UUID | DEL_UUID | SKIP | ERROR
}

func (r *TRSReport) AddInsertedRecListener() {
	r.listenerItems |= INSERTED_REC
}

func (r *TRSReport) AddUpdatedRecListener() {
	r.listenerItems |= UPDATED_REC
}

func (r *TRSReport) AddDelUniqueListener() {
	r.listenerItems |= DEL_UNIQUE
}

func (r *TRSReport) AddGenUUIDListener() {
	r.listenerItems |= GEN_UUID
}

func (r *TRSReport) AddUpdateUUIDListener() {
	r.listenerItems |= UPDATE_UUID
}

func (r *TRSReport) AddDelUUIDListener() {
	r.listenerItems |= DEL_UUID
}

func (r *TRSReport) AddSkipListener() {
	r.listenerItems |= SKIP
}

func (r *TRSReport) AddErrorListener() {
	r.listenerItems |= ERROR
}

func (r *TRSReport) OnError(e *TRSException, recordIndexFrom, recordIndexTo int64) {
	if (r.listenerItems & ERROR) != ERROR {
		return
	}
	r.SetError(e.ErrorNo, e.Message)
	for i := recordIndexFrom; i < recordIndexTo; i++ {
		r.errorRecs[i] = strconv.Itoa(e.ErrorNo) + ":" + e.Message
		r.listener.OnError(i, e.ErrorNo, e.Message)
	}
}

func (r *TRSReport) OnInsertFinish() {
	r.listener.OnInsertFinish(r.insertedNum)
	r.AddItem("insertedNum", strconv.Itoa(int(r.insertedNum)))
}

func (r *TRSReport) OnDeleteFinish() {
	if r.listener != nil {
		r.listener.OnDeleteFinish(r.deletedNum) // Notify the listener
	}
	r.AddItem("deletedNum", strconv.FormatInt(r.deletedNum, 10)) // Update the report
}

func (r *TRSReport) OnSkip(recordIndex int64, skipID int, reason string) {
	if (r.listenerItems & SKIP) != SKIP {
		return
	}
	r.skipRecs[recordIndex] = SKIPTYPE(skipID).String() + ":" + reason
	r.listener.OnSkip(recordIndex, skipID, reason)
}

func (r *TRSReport) OnReport(report *Report) {
	for _, success := range report.GetInsertedRec() {
		r.insertedRecs[success] = ""
		r.listener.OnInsertedRec(success)
	}
	for _, success := range report.GetUpdatedRec() {
		r.updatedRecs[success] = ""
		r.listener.OnUpdatedRec(success)
	}
	for _, success := range report.GetDeletedRec() {
		r.deletedRecs[success.GetKey()] = success.GetValue()
		r.listener.OnDelRec(success.GetKey(), success.GetValue())
	}
	for _, uuid := range report.GetGenUUID() {
		key, err := strconv.ParseInt(uuid.GetKey(), 10, 64)
		if err != nil {
			continue
		}
		r.genUUIDs[key] = uuid.GetValue()
		r.listener.OnGenUUID(key, uuid.GetValue())
	}
	for _, uuid := range report.GetUpdateUUID() {
		key, err := strconv.ParseInt(uuid.GetKey(), 10, 64)
		if err != nil {
			continue
		}
		r.updateUUIDs[key] = uuid.GetValue()
		r.listener.OnUpdateUUID(key, uuid.GetValue())
	}
	for _, uuid := range report.GetDelUUID() {
		r.delUUIDs[int64(uuid.GetRecordIndex())] = strings.Join(uuid.GetItem(), ",")
		r.listener.OnDelUUID(int64(uuid.GetRecordIndex()), uuid.GetItem())
	}
	for _, skipRecord := range report.GetSkip() {
		r.skipRecs[skipRecord.GetRecordIndex()] = SKIPTYPE(skipRecord.GetType()).String() + ":" + skipRecord.GetMessage()
		r.listener.OnSkip(skipRecord.GetRecordIndex(), int(skipRecord.GetType()), skipRecord.GetMessage())
	}
	if len(report.GetError()) > 0 {
		e := report.GetError()[0]
		r.SetError(int(e.GetType()), e.GetMessage())
	} else if len(report.GetDelError()) > 0 {
		e := report.GetDelError()[0]
		r.SetError(int(e.GetType()), e.GetMessage())
	}
	for _, errorRecord := range report.GetError() {
		r.errorRecs[errorRecord.GetRecordIndex()] = strconv.Itoa(int(errorRecord.GetType())) + ":" + errorRecord.GetMessage()
		r.listener.OnError(errorRecord.GetRecordIndex(), int(errorRecord.GetType()), errorRecord.GetMessage())
	}
	for _, errorRecord := range report.GetDelError() {
		r.delError[errorRecord.GetUuid()] = strconv.Itoa(int(errorRecord.GetType())) + ":" + errorRecord.GetMessage()
		r.listener.OnDelError(errorRecord.GetUuid(), int(errorRecord.GetType()), errorRecord.GetMessage())
	}
	r.insertedNum += report.GetInsertedNum()
	r.updatedNum += report.GetUpdatedNum()
	r.deletedNum += report.GetDeletedNum()
}

func (r *TRSReport) SetError(errorNo int, errorMsg string) {
	if r.errorNo != -1 {
		return
	}
	r.errorNo = errorNo
	r.errorMsg = errorMsg
}

func (r *TRSReport) Clear() {
	r.insertedNum = 0
	r.updatedNum = 0
	r.deletedNum = 0
	r.insertedRecs = make(map[int64]string)
	r.updatedRecs = make(map[int64]string)
	r.deletedRecs = make(map[string]string)
	r.genUUIDs = make(map[int64]string)
	r.updateUUIDs = make(map[int64]string)
	r.delUUIDs = make(map[int64]string)
	r.skipRecs = make(map[int64]string)
	r.errorRecs = make(map[int64]string)
	r.delError = make(map[string]string)
	r.keyValues = make(map[string]string)
	r.listener.Clear()
}

func (r *TRSReport) OnUpdateFinish() {
	if r.listener != nil {
		r.listener.OnUpdateFinish(r.updatedNum)
	}
	r.AddItem("updatedNum", fmt.Sprintf("%d", r.updatedNum))
}
