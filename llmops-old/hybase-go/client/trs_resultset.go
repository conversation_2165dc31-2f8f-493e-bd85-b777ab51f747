package client

import (
	"com/trs/hybase/client/params"
	"fmt"
	"strings"
	"sync"
)

// TRSResultSet represents a search result set containing records and statistics
type TRSResultSet struct {
	mu            sync.Mutex
	closed        bool
	numFound      int64
	start         int64
	curRecordID   int64
	records       []TRSRecord
	categoryMap   map[string]map[string]int64
	facetMap      map[string]map[string]float64
	expressionMap map[string]*ResultField
	searchID      string
	progress      string
	dbHitsNum     map[string]int64
	conn          *TRSConnection
	searchRequest *SearchRPC_SearchRecordRequest
	searchParams  *params.SearchParams
}

// NewTRSResultSet creates a new TRSResultSet instance
func NewTRSResultSet(conn *TRSConnection) *TRSResultSet {
	return &TRSResultSet{
		records:     make([]TRSRecord, 0),
		categoryMap: make(map[string]map[string]int64),
		facetMap:    make(map[string]map[string]float64),
		dbHitsNum:   make(map[string]int64),
		progress:    "100%",
		conn:        conn,
	}
}

// Close closes the result set and releases resources
func (trs *TRSResultSet) Close() {
	trs.mu.Lock()
	defer trs.mu.Unlock()
	if trs.closed {
		return
	}
	trs.closed = true
	trs.reset()
}

// reset clears all data in the result set
func (trs *TRSResultSet) reset() {
	trs.records = nil
	trs.curRecordID = -1
	trs.categoryMap = nil
	trs.expressionMap = nil
	trs.dbHitsNum = nil
}

// Size returns the number of records in the result set
func (trs *TRSResultSet) Size() int {
	return len(trs.records)
}

// GetNumFound returns the total number of records found
func (trs *TRSResultSet) GetNumFound() int64 {
	return trs.numFound
}

// GetStart returns the starting position of the result set
func (trs *TRSResultSet) GetStart() int64 {
	return trs.start
}

// GetCurrentRecordID returns the current record position
func (trs *TRSResultSet) GetCurrentRecordID() int64 {
	return trs.curRecordID
}

// SetNumFound sets the total number of records found
func (trs *TRSResultSet) SetNumFound(numFound int64) {
	trs.numFound = numFound
}

// SetStart sets the starting position
func (trs *TRSResultSet) SetStart(start int64) {
	trs.start = start
}

// Add adds a record to the result set
func (trs *TRSResultSet) Add(record TRSRecord) {
	trs.records = append(trs.records, record)
}

// GetRecord returns the record at the specified position
func (trs *TRSResultSet) GetRecord(recordID int64) (TRSRecord, bool) {
	if recordID < 0 || recordID >= int64(len(trs.records)) {
		return TRSRecord{}, false
	}
	return trs.records[recordID], true
}

// MoveNext moves to the next record
func (trs *TRSResultSet) MoveNext() bool {
	if trs.curRecordID >= int64(len(trs.records))-1 {
		return false
	}
	trs.curRecordID++
	return true
}

// MovePrevious moves to the previous record
func (trs *TRSResultSet) MovePrevious() bool {
	if trs.curRecordID <= 0 {
		return false
	}
	trs.curRecordID--
	return true
}

// GetCategoryCount returns the number of categories for a given column
func (trs *TRSResultSet) GetCategoryCount(categoryColumn string) int {
	if categoryInfo, exists := trs.categoryMap[categoryColumn]; exists {
		return len(categoryInfo)
	}
	return 0
}

// GetCategory returns the count of a specific category
func (trs *TRSResultSet) GetCategory(categoryColumn, categoryName string) int64 {
	if categoryInfo, exists := trs.categoryMap[categoryColumn]; exists {
		if value, ok := categoryInfo[categoryName]; ok {
			return value
		}
	}
	return 0
}

// GetCategoryMap returns the category map for a given column
func (trs *TRSResultSet) GetCategoryMap(categoryColumn string) map[string]int64 {
	if categoryInfo, exists := trs.categoryMap[categoryColumn]; exists {
		return categoryInfo
	}
	return nil
}

// GetProgress returns the progress of the search
func (trs *TRSResultSet) GetProgress() string {
	return trs.progress
}

// GetDBHitsNum returns the database hits number
func (trs *TRSResultSet) GetDBHitsNum(includeSplitDB bool) map[string]int64 {
	if !includeSplitDB {
		result := make(map[string]int64)
		for k, v := range trs.dbHitsNum {
			if !containsRune(k, '#') {
				result[k] = v
			}
		}
		return result
	}
	return trs.dbHitsNum
}

// GetSearchID returns the search ID
func (trs *TRSResultSet) GetSearchID() string {
	return trs.searchID
}

// containsRune checks if a string contains a specific rune
func containsRune(s string, r rune) bool {
	for _, c := range s {
		if c == r {
			return true
		}
	}
	return false
}

func NewTRSResultSetWithCategories(categoryMap map[string]map[string]int64, expressionMap map[string]*ResultField, facetMap map[string]map[string]float64, numFound int64) *TRSResultSet {
	return &TRSResultSet{
		categoryMap:   categoryMap,
		expressionMap: expressionMap,
		facetMap:      facetMap,
		numFound:      numFound,
		records:       make([]TRSRecord, 0),
	}
}

func NewTRSResultSetWithFacets(categoryMap map[string]map[string]int64, expressionMap map[string]*ResultField, facetMap map[string]map[string]float64, numFound int64) *TRSResultSet {
	return &TRSResultSet{
		categoryMap:   categoryMap,
		expressionMap: expressionMap,
		facetMap:      facetMap,
		numFound:      numFound,
		records:       make([]TRSRecord, 0),
	}
}

func NewTRSResultSetWithExpressions(categoryMap map[string]map[string]int64, expressionMap map[string]*ResultField, facetMap map[string]map[string]float64, numFound int64) *TRSResultSet {
	return &TRSResultSet{
		categoryMap:   categoryMap,
		expressionMap: expressionMap,
		facetMap:      facetMap,
		numFound:      numFound,
		records:       make([]TRSRecord, 0),
	}
}

// SetSearchRequest sets the search request for the result set
func (trs *TRSResultSet) SetSearchRequest(request *SearchRPC_SearchRecordRequest) {
	trs.searchRequest = request
}

// SetSearchParams sets the search parameters for the result set
func (trs *TRSResultSet) SetSearchParams(params *params.SearchParams) {
	trs.searchParams = params
}

// MoveFirst moves the current record position to the first record.
// Returns true if the move was successful, false if there are no records.
func (trs *TRSResultSet) MoveFirst() bool {
	if trs.GetCount() == 0 {
		trs.curRecordID = -1
		return false
	}

	trs.curRecordID = 0
	return true
}

// MoveLast moves the current record position to the last record.
// Returns true if the move was successful, false if there are no records.
func (trs *TRSResultSet) MoveLast() bool {
	if trs.GetCount() == 0 {
		trs.curRecordID = -1
		return false
	}

	trs.curRecordID = trs.GetCount() - 1
	return true
}

// MoveTo moves the current record position to the specified recordID.
// Returns true if the move was successful, false if the recordID is out of bounds.
func (trs *TRSResultSet) MoveTo(recordID int64) bool {
	if recordID < 0 || recordID >= trs.GetCount() {
		return false
	}

	trs.curRecordID = recordID
	return true
}

// GetCount returns the number of records in the result set.
func (trs *TRSResultSet) GetCount() int64 {
	return int64(len(trs.records))
}

// Get retrieves the current TRSRecord. If the current record is not loaded, it fetches additional records if needed.
func (trs *TRSResultSet) Get() (*TRSRecord, *TRSException) {
	if trs.curRecordID == -1 {
		return nil, nil // Return nil if there is no current record
	}

	rec := trs.records[trs.curRecordID]
	// rec.SetSearchParams(trs.searchParams)

	if trs.searchRequest != nil && !strings.EqualFold(trs.searchRequest.GetReadColumns(), UUID) && len(rec.recColumnsData) == 0 {
		// Fetch up to 100 records
		idsBuilder := &StringList{}
		for i := trs.curRecordID; i < min(trs.curRecordID+100, trs.GetCount()); i++ {
			r := trs.records[i]
			if len(r.recColumnsData) > 0 {
				break
			}
			idsBuilder.Str = append(idsBuilder.Str, r.GetUid())
		}

		response, err := trs.GetRecords(idsBuilder)
		if err != nil {
			return nil, NewTRSException(-1, fmt.Sprintf("failed to get records: %v", err))
		}

		// Clear previous records
		toGet := len(idsBuilder.Str)
		trs.ClearRecords(0, int(trs.curRecordID))
		trs.ClearRecords(int(trs.curRecordID)+toGet, int(trs.GetCount()))

		// Fill new records
		resultList := response.GetRecord()
		fillStart := trs.curRecordID
		for _, re := range resultList {
			record := trs.records[fillStart]
			if re.GetTimestamp() != 0 {
				record.SetTimestamp(re.GetTimestamp())
			}
			if re.GetCreatetimestamp() != 0 {
				record.SetCreateTimestamp(re.GetCreatetimestamp())
			}
			record.reset()
			trs.SetRecord(&record, re)
			fillStart++
		}
	}
	return &rec, nil
}

func min(a, b int64) int64 {
	if a < b {
		return a
	}
	return b
}

// ClearRecords resets the records in the specified range from fromIndex to toIndex.
func (trs *TRSResultSet) ClearRecords(fromIndex, toIndex int) error {
	if fromIndex < 0 || toIndex > len(trs.records) || fromIndex >= toIndex {
		return fmt.Errorf("invalid index range: fromIndex=%d, toIndex=%d", fromIndex, toIndex)
	}

	for i := fromIndex; i < toIndex; i++ {
		rec := trs.records[i]
		rec.reset() // Assuming Reset is a method on TRSRecord that resets its state
	}
	return nil
}

// GetRecords retrieves records based on the provided UUIDs and the current search request parameters.
func (trs *TRSResultSet) GetRecords(idsBuilder *StringList) (*SearchRPC_SearchRecordResponse, error) {
	// Create a new request builder for getting records
	// Create temporary variables to hold the string values
	readColumns := trs.searchRequest.GetReadColumns()
	colorColumns := trs.searchRequest.GetColorColumns()
	colorWords := trs.searchRequest.GetQuery()
	cutSize := trs.searchRequest.GetCutSize()

	builder := &SearchRPCGetRecordsRequest{
		ReadColumns:  &readColumns,
		ColorColumns: &colorColumns,
		ColorWords:   &colorWords,
		CutSize:      &cutSize, // Take the address of the temporary variable
		SearchOpts:   trs.searchRequest.GetOpts(),
		UuidList:     idsBuilder,
	}

	// Call the connection's getRecords method
	response, err := trs.conn.GetRecords(builder)
	if err != nil {
		return nil, err
	}

	return response, nil
}

// SetRecord sets the database name and values for the given TRSRecord from the provided TRSRPC.ResultRecord.
func (trs *TRSResultSet) SetRecord(record *TRSRecord, resultRec *ResultRecord) {
	// Set the database name
	record.SetDbName(resultRec.GetDbName())

	// Iterate over the columns in the result record and add them to the TRSRecord
	for _, col := range resultRec.GetColumns() {
		record.AddValue(col.GetName(), col.GetStrValue()) // Assuming AddValue is a method on TRSRecord
	}
}
