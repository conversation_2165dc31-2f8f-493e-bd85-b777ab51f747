package client

import (
	"io"
)

type CountInputStream struct {
	reader  io.Reader
	counter int64
}

func NewCountInputStream(r io.Reader) *CountInputStream {
	return &CountInputStream{
		reader: r,
	}
}

func (c *CountInputStream) Read(b []byte) (int, error) {
	n, err := c.reader.Read(b)
	if n > 0 {
		c.counter += int64(n)
	}
	return n, err
}

func (c *CountInputStream) Skip(n int64) (int64, error) {
	m, err := io.CopyN(io.Discard, c.reader, n)
	if err == nil {
		c.counter += m
	}
	return m, err
}

func (c *CountInputStream) GetReadBytes() int64 {
	return c.counter
}

func (c *CountInputStream) GetReader() io.Reader {
	return c.reader
}
