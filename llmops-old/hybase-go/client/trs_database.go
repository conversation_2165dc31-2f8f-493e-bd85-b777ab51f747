package client

import (
	"com/trs/hybase/client/common"
	"fmt"
	"sync"
)

type TRSDatabase struct {
	name            string
	mode            int
	dbType          int
	engineType      string
	policy          DBPOLICY
	defSearchColumn string
	uniqueColumn    string
	parser          string
	replications    int
	splitColumn     string
	splitter        string
	splitParams     map[string]string
	conn            *TRSConnection
	columns         map[string]*TRSDatabaseColumn
	addColumns      map[string]*TRSDatabaseColumn
	altColumns      map[string]*TRSDatabaseColumn
	delColumns      []string
	mu              sync.Mutex
	TRSObject       *common.TRSObject
}

// Define a custom type for the enum
type DBPOLICY int

// Define constants for each enum value
const (
	NORMAL DBPOLICY = iota
	FASTEST
	SAFEST
)

const (
	MODE_SINGLE      = 0
	MODE_DISTRIBUTED = 1
	MODE_MEMORY      = 2

	TYPE_DATABASE = 0
	TYPE_VIEW     = 1
	TYPE_ALIAS    = 2
	TYPE_FIFO     = 3
	TYPE_MIRROR   = 4
	TYPE_BALANCE  = 5

	ANALYZER_TRSSTD  = "analyzer001"
	ANALYZER_TRSCJK  = "analyzer002"
	SEG_PINYIN_ALL   = "pinyin_all"
	SEG_PINYIN_FIRST = "pinyin_first"

	CREATEDATE = "createdate"
	UPDATEDATE = "updatedate"
	COLUMNNUM  = "columnnum"
	RECORDNUM  = "recordnum"
	MIRRORDB   = "mirrordb"
)

// String method to convert enum to string representation
func (p DBPOLICY) String() string {
	return [...]string{"NORMAL", "FASTEST", "SAFEST"}[p]
}

// ParseDBPolicy converts a string to a DBPOLICY enum
func ParseDBPolicy(s string) (DBPOLICY, error) {
	switch s {
	case "NORMAL":
		return NORMAL, nil
	case "FASTEST":
		return FASTEST, nil
	case "SAFEST":
		return SAFEST, nil
	default:
		return -1, fmt.Errorf("invalid DBPOLICY: %s", s)
	}
}

func NewTRSDatabase(name string, dbType int) *TRSDatabase {
	return &TRSDatabase{
		name:       name,
		dbType:     dbType,
		addColumns: make(map[string]*TRSDatabaseColumn),
		altColumns: make(map[string]*TRSDatabaseColumn),
		delColumns: []string{},
		TRSObject:  common.NewTRSObject(),
	}
}

func NewTRSDatabaseWithConnection(conn *TRSConnection) *TRSDatabase {
	return &TRSDatabase{
		TRSObject:  common.NewTRSObject(),
		conn:       conn,
		addColumns: make(map[string]*TRSDatabaseColumn),
		altColumns: make(map[string]*TRSDatabaseColumn),
		delColumns: []string{},
	}
}

func (db *TRSDatabase) GetName() string {
	return db.name
}

func (db *TRSDatabase) SetName(name string) {
	db.name = name
}

// GetReplications returns the current value of replications
func (db *TRSDatabase) GetReplications() int {
	return db.replications
}

// SetReplications sets the value of replications
func (db *TRSDatabase) SetReplications(value int) {
	db.replications = value
}

func (db *TRSDatabase) GetEngineType() string {
	return db.engineType
}

func (db *TRSDatabase) SetEngineType(engineType string) {
	db.engineType = engineType
}

func (db *TRSDatabase) AddColumn(column *TRSDatabaseColumn) error {
	db.mu.Lock()
	defer db.mu.Unlock()
	if _, exists := db.addColumns[column.GetName()]; exists {
		return fmt.Errorf("column name [%s] exists", column.GetName())
	}
	db.addColumns[column.GetName()] = column
	return nil
}

func (db *TRSDatabase) AddColumns(columns []*TRSDatabaseColumn) error {
	for _, col := range columns {
		if err := db.AddColumn(col); err != nil {
			return err
		}
	}
	return nil
}

func (db *TRSDatabase) Freeze() {
	db.mu.Lock()
	defer db.mu.Unlock()
	if db.columns == nil && len(db.addColumns) > 0 {
		db.columns = make(map[string]*TRSDatabaseColumn)
		for k, v := range db.addColumns {
			db.columns[k] = v
		}
		db.addColumns = make(map[string]*TRSDatabaseColumn)
	}
}

func (db *TRSDatabase) DeleteColumn(colName string) {
	db.mu.Lock()
	defer db.mu.Unlock()
	db.delColumns = append(db.delColumns, colName)
}

func (db *TRSDatabase) AlterColumn(alterColumn *TRSDatabaseColumn) {
	db.mu.Lock()
	defer db.mu.Unlock()
	db.altColumns[alterColumn.GetName()] = alterColumn
}

func (db *TRSDatabase) Clear() {
	db.mu.Lock()
	defer db.mu.Unlock()
	db.addColumns = make(map[string]*TRSDatabaseColumn)
	db.delColumns = []string{}
	db.altColumns = make(map[string]*TRSDatabaseColumn)
}

func (db *TRSDatabase) GetColumn(name string) (*TRSDatabaseColumn, error) {
	db.mu.Lock()
	defer db.mu.Unlock()
	db.Freeze()
	if db.columns == nil {
		return nil, fmt.Errorf("please call TRSConnection.getDatabases method to get database object first")
	}
	return db.columns[name], nil
}

func (db *TRSDatabase) GetAllColumns() ([]*TRSDatabaseColumn, error) {
	db.mu.Lock()
	defer db.mu.Unlock()
	db.Freeze()
	if db.columns == nil {
		return nil, fmt.Errorf("please call TRSConnection.getDatabases method to get database object first")
	}
	cols := make([]*TRSDatabaseColumn, 0, len(db.columns))
	for _, col := range db.columns {
		cols = append(cols, col)
	}
	return cols, nil
}

func (db *TRSDatabase) ListColumns() ([]string, error) {
	db.mu.Lock()
	defer db.mu.Unlock()
	if db.columns == nil {
		return nil, fmt.Errorf("please call TRSConnection.getDatabases method to get database object first")
	}
	names := make([]string, 0, len(db.columns))
	for name := range db.columns {
		names = append(names, name)
	}
	return names, nil
}

func (db *TRSDatabase) GetColumnCount() (int, error) {
	db.mu.Lock()
	defer db.mu.Unlock()
	if db.columns == nil {
		return 0, fmt.Errorf("please call TRSConnection.getDatabases method to get database object first")
	}
	return len(db.columns), nil
}

func (db *TRSDatabase) GetRecordNum() (int, error) {
	//return db.conn.GetDbRecordsNum(db.name)
	return 0, nil
}

func (db *TRSDatabase) GetCreateDate() (string, error) {
	value := db.GetProperty("createdate")
	if value == "" {
		return "", fmt.Errorf("please call TRSConnection.getDatabases method to get database object first")
	}
	return value, nil
}

func (db *TRSDatabase) GetUpdateDate() (string, error) {
	value := db.GetProperty("updatedate")
	if value == "" {
		return "", fmt.Errorf("please call TRSConnection.getDatabases method to get database object first")
	}
	return value, nil
}

func (db *TRSDatabase) GetMode() int {
	return db.mode
}

func (db *TRSDatabase) GetType() int {
	return db.dbType
}

func (db *TRSDatabase) GetPolicy() string {
	if db.policy == 0 {
		return ""
	}
	return db.policy.String()
}

func (db *TRSDatabase) GetProperty(key string) string {
	return db.TRSObject.GetProperty(key)
}

func (db *TRSDatabase) SetProperty(key, value string) *TRSDatabase {
	db.TRSObject.SetProperty(key, value)
	return db
}

func (db *TRSDatabase) SetMirrorDB(mirrorDBName, mirrorCopy string) *TRSDatabase {
	db.SetProperty("mirrordb", mirrorDBName)
	db.SetProperty("mirror.copy", mirrorCopy)
	return db
}

func (db *TRSDatabase) GetMirrorDB() string {
	return db.GetProperty("mirrordb")
}

func (db *TRSDatabase) ReadFromRPC(protoDB *SchemaRPC_DatabaseInfo) error {
	db.name = protoDB.GetName()
	db.mode = int(protoDB.GetMode())
	db.dbType = int(protoDB.GetType())
	db.policy = DBPOLICY(protoDB.GetPolicy())
	db.engineType = protoDB.GetEngineType()
	defSearchColumn := protoDB.GetDefSearchColumn()
	if defSearchColumn != "" {
		db.defSearchColumn = defSearchColumn
	}
	uniqueColumn := protoDB.GetUniqueColumn()
	if uniqueColumn != "" {
		db.uniqueColumn = uniqueColumn
	}
	parserName := protoDB.GetParserName()
	if parserName != "" {
		db.parser = parserName
	}
	db.replications = int(protoDB.GetRepSubNum())
	splitter := protoDB.GetSplitter()
	if splitter != "" {
		db.splitter = splitter
	}
	splitColumn := protoDB.GetSplitColumn()
	if splitColumn != "" {
		db.splitColumn = splitColumn
	}
	splitParams := protoDB.GetSplitParams()
	if splitParams != nil {
		db.splitParams = make(map[string]string, len(splitParams))
		for _, p := range splitParams {
			db.splitParams[p.GetKey()] = p.GetValue()
		}
	}
	db.columns = make(map[string]*TRSDatabaseColumn)
	columns := protoDB.GetColumns()
	if columns != nil {
		for _, c := range columns {
			column := NewTRSDatabaseColumn(c.GetName(), int(c.GetType()))
			db.columns[column.GetName()] = column
		}
	}
	return nil
}
