package common

import (
	"fmt"
	"strconv"
	"strings"
)

// TRSObject represents the base object for TRS objects
type TRSObject struct {
	Properties map[string]string
}

// NewTRSObject creates a new TRSObject instance
func NewTRSObject() *TRSObject {
	return &TRSObject{
		Properties: make(map[string]string),
	}
}

// SetProperty sets a property value
func (t *TRSObject) SetProperty(key, value string) *TRSObject {
	if value == "" {
		panic(fmt.Sprintf("property %s:null", key))
	}
	t.Properties[strings.ToLower(key)] = value
	return t
}

// GetProperty gets a property value
func (t *TRSObject) GetProperty(key string) string {
	rev := t.Properties[key]
	if rev == "" {
		rev = t.Properties[strings.ToLower(key)]
	}
	return rev
}

// SetBoolProperty sets a boolean property value
func (t *TRSObject) SetBoolProperty(key string, bValue bool) {
	t.SetProperty(key, strconv.FormatBool(bValue))
}

// GetBoolProperty gets a boolean property value with default value
func (t *TRSObject) GetBoolProperty(key string, defValue bool) bool {
	value := t.GetProperty(strings.ToLower(key))
	if value == "" {
		return defValue
	}
	boolVal, err := strconv.ParseBool(value)
	if err != nil {
		return defValue
	}
	return boolVal
}

// GetIntProperty gets an integer property value
func (t *TRSObject) GetIntProperty(key string) int {
	return t.GetIntPropertyWithDefault(key, 0)
}

// GetIntPropertyWithDefault gets an integer property value with default value
func (t *TRSObject) GetIntPropertyWithDefault(key string, def int) int {
	value := t.GetProperty(strings.ToLower(key))
	if value == "" {
		return def
	}
	intVal, err := strconv.Atoi(value)
	if err != nil {
		return def
	}
	return intVal
}

// GetInt gets an integer option parameter with range check
func (t *TRSObject) GetInt(name string, defaultValue, minValue, maxValue int) int {
	val := defaultValue
	val = t.GetIntPropertyWithDefault(name, val)

	// Check range [minValue, maxValue]
	if val > maxValue {
		val = maxValue
	} else if val < minValue {
		val = minValue
	}
	return val
}

// SetIntProperty sets an integer property value
func (t *TRSObject) SetIntProperty(key string, iValue int) {
	t.SetProperty(key, strconv.Itoa(iValue))
}

// GetLongProperty gets a long integer property value
func (t *TRSObject) GetLongProperty(key string) int64 {
	value := t.GetProperty(strings.ToLower(key))
	if value == "" {
		return 0
	}
	longVal, err := strconv.ParseInt(value, 10, 64)
	if err != nil {
		return 0
	}
	return longVal
}

// SetLongProperty sets a long integer property value
func (t *TRSObject) SetLongProperty(key string, lValue int64) {
	t.SetProperty(key, strconv.FormatInt(lValue, 10))
}

// Keys gets all property names
func (t *TRSObject) Keys() []string {
	keys := make([]string, 0, len(t.Properties))
	for k := range t.Properties {
		keys = append(keys, k)
	}
	return keys
}

// ListProperties gets all properties as "key=value" strings
func (t *TRSObject) ListProperties() []string {
	if len(t.Properties) == 0 {
		return nil
	}

	props := make([]string, 0, len(t.Properties))
	for key, value := range t.Properties {
		props = append(props, fmt.Sprintf("%s=%s", key, value))
	}
	return props
}

// String returns a string representation of the object
func (t *TRSObject) String() string {
	var sb strings.Builder
	first := true
	for key, value := range t.Properties {
		if !first {
			sb.WriteString(";")
		}
		sb.WriteString(fmt.Sprintf("%s=%s", key, value))
		first = false
	}
	return sb.String()
}
