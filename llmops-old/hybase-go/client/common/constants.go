package common

// Data type constants
const (
	TypeString   = 0 // String type integer representation
	TypeInt      = 1 // Int type integer representation
	TypeLong     = 2 // Long type integer representation
	TypeFloat    = 3 // Float type integer representation
	TypeDouble   = 4 // Double type integer representation
	TypeDate     = 5 // Date type integer representation
	TypeFile     = 6 // File type integer representation
	TypeDocument = 7 // Document type integer representation

	// API URLs
	APIURLSchema = "/api/schema.do"

	// Default separator for multiple field values
	DefaultColumnSepChar = ";"

	// Default connection timeout (1 minute)
	DefaultTimeout = 60000

	// Internal constants
	MergeNum = "$mergeNum$"
	TotalNum = "$totalNum$"

	// Job types
	JobImportHDFSIndex     = 0  // Import offline index job (Deprecated)
	JobSwitchDisk          = 1  // Disk switch
	JobDataRedistribute    = 2  // Data redistribution job (Deprecated)
	JobDataCollision       = 6  // Data collision
	JobDataCopy            = 7  // Data copy
	JobRemoveDuplicates    = 8  // Unique value deduplication job
	JobPreDataRedistribute = 9  // Data pre-redistribution job
	JobUpdateSynonym       = 10 // Update synonym dictionary job
)
