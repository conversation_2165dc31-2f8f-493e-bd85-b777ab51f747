package pipeline

import (
	"time"

	"transwarp.io/applied-ai/applet-engine/models"
)

// CdnTmplNode 为自定义算子的示范模板，不提供实际数据处理能力
// Example:
//
//	stream
//	    |cndTmpl()
//			.param1('string')
//			.param2(123)
//			.param3(5m)
//			.param4(true)
type CdnTmplNode struct {
	chainnode `json:"-"`
	Param1    string        `json:"param_1"`
	Param2    int           `json:"param_2"`
	Param3    time.Duration `json:"param_3"`
	Param4    bool          `json:"param_4"`
	models.NodeMeta
}

func newCDNTmplNode() *CdnTmplNode {
	return &CdnTmplNode{
		chainnode: newBasicChainNode("cndTmpl", StreamEdge, StreamEdge),
	}
}

// validate 判断算子初始化的一些属性是否合法
func (s *CdnTmplNode) validate() error {
	return s.NodeMeta.Validate()
}

func (n *CdnTmplNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

// CdnTmpl 为 TickScript 中 chain func 类型的定义, 接收者为 chainnode , 表明该算子可以作为所有 chainnode 的下游算子
// 亦即：使用 |cdnTmpl() 方式进行初始化的算子
// TickScript 中 使用该 chin func 时，需要在和方法接收的参数在数量与类型上保持一致：
// 例如：
//
//		func (n *chainnode) CdnTmpl(a, b int64) *CdnTmplNode
//		对应的 TickScript 定义应为：
//	 |cdnTmpl(100, 200)
//			.param1()
func (n *chainnode) CdnTmpl(a, b int64) *CdnTmplNode {
	s := newCDNTmplNode()
	n.linkChild(s)
	return s
}
