package pipeline

import (
	"encoding/json"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
	"transwarp.io/applied-ai/applet-engine/models"
)

type FullTextInsert struct {
	chainnode `json:"-"`
	models.NodeMeta
	Params string `json:"params"`
	// 内部变量
	WidgetParams widgets.WidgetParamsFullTextInsert `json:"widget_params"`
}

func newFulltextInsert() *FullTextInsert {
	return &FullTextInsert{chainnode: newBasicChainNode("fulltextInsert", StreamEdge, StreamEdge)}
}

func (n *FullTextInsert) validate() error {
	if err := n.NodeMeta.Validate(); err != nil {
		return err
	}
	if n.Params == "" {
		return stderr.InvalidParam.Error("params field is unspecified")
	}
	if err := json.Unmarshal([]byte(n.Params), &n.WidgetParams); err != nil {
		return stderr.Wrap(err, "failed to parse TextKnowledgeSearchNode params")
	}
	return nil
}
func (n *FullTextInsert) Meta() models.NodeMeta {
	return n.NodeMeta
}
func (n *chainnode) FullTextInsert() *FullTextInsert {
	f := newFulltextInsert()
	n.linkChild(f)
	return f
}
