package pipeline

import (
	"encoding/json"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
	"transwarp.io/applied-ai/applet-engine/models"
)

// QaSearchNode
//
//	stream
//	    |qaSearch()
type QaSearchNode struct {
	chainnode `json:"-"`
	models.NodeMeta
	Params string `json:"params"`

	// 内部变量，解析Params值
	ParsedParams *script.WidgetParamsQaSearch
}

func newQaSearchNode() *QaSearchNode {
	return &QaSearchNode{chainnode: newBasicChainNode("qaSearch", StreamEdge, StreamEdge)}
}

func (n *QaSearchNode) validate() error {
	if err := n.NodeMeta.Validate(); err != nil {
		return err
	}
	if n.Params == "" {
		return stderr.Errorf("params of %s is empty", n.NodeName)
	}
	if err := json.Unmarshal([]byte(n.Params), &n.ParsedParams); err != nil {
		return stderr.Wrap(err, "failed to parse params of %s", n.NodeName)
	}
	if err := n.ParsedParams.Validate(); err != nil {
		return stderr.Wrap(err, "validate params of %s failed", n.NodeName)
	}
	return nil
}

func (n *QaSearchNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *chainnode) QaSearch() *QaSearchNode {
	f := newQaSearchNode()
	n.linkChild(f)
	return f
}
