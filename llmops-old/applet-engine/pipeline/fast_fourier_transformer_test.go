package pipeline

import (
	"encoding/json"
	"reflect"
	"testing"
)

func TestFastFourierTransformerNode_MarshalJSON(t *testing.T) {
	type fields struct {
		Field   string
		Inverse int64
	}

	tests := []struct {
		name    string
		fields  fields
		want    string
		wantErr bool
	}{
		{
			name: "all fields set",
			fields: fields{
				Field:   "field_name",
				Inverse: 1,
			},
			want: `{"typeOf":"fastFourierTransformer","id":"0","field":"field_name","inverse":1}`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			f := newFastFourierTransformerNode(BatchEdge, tt.fields.Field, tt.fields.Inverse)
			MarshalTestHelper(t, f, tt.wantErr, tt.want)
		})
	}
}

func TestFastFourierTransformerNode_UnmarshalJSON(t *testing.T) {
	tests := []struct {
		name    string
		input   string
		want    *FastFourierTransformerNode
		wantErr bool
	}{
		{
			name:  "all fields set",
			input: `{"typeOf":"fastFourierTransformer","id":"0","field":"field_name","inverse":1}`,
			want: &FastFourierTransformerNode{
				Field:   "field_name",
				Inverse: 1,
			},
		},
		{
			name:    "invalid field",
			input:   `{"typeOf":"fastFourierTransformer","id":"0","field":1}`,
			want:    &FastFourierTransformerNode{},
			wantErr: true,
		},
		{
			name:    "invalid inverse",
			input:   `{"typeOf":"fastFourierTransformer","id":"0","inverse":"1"}`,
			want:    &FastFourierTransformerNode{},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			f := &FastFourierTransformerNode{}
			err := json.Unmarshal([]byte(tt.input), f)
			if (err != nil) != tt.wantErr {
				t.Errorf("FastFourierTransformerNode.UnmarshalJSON() error = %v, wantError %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(f, tt.want) {
				t.Errorf("FastFourierTransformerNode.UnmarshalJSON() =\n%#+v\nwant\n%#+v", f, tt.wantErr)
			}
		})
	}
}
