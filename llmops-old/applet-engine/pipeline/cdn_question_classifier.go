package pipeline

import (
	"encoding/json"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
	"transwarp.io/applied-ai/applet-engine/models"
)

// QuestionClassifierNode
//
//	stream
//	    |questionClassifier()
type QuestionClassifierNode struct {
	chainnode `json:"-"`
	models.NodeMeta
	Params string `json:"params"`

	// 内部变量，解析Params值
	ParsedParams script.WidgetParamsQuestionClassifier
}

func newQuestionClassifierNode() *QuestionClassifierNode {
	return &QuestionClassifierNode{chainnode: newBasicChainNode("questionClassifier", StreamEdge, StreamEdge)}
}

func (n *QuestionClassifierNode) validate() error {
	if err := n.NodeMeta.Validate(); err != nil {
		return err
	}
	if n.Params == "" {
		return stderr.InvalidParam.Error("params of %s is empty", n.NodeName)
	}
	if err := json.Unmarshal([]byte(n.Params), &n.ParsedParams); err != nil {
		return stderr.Wrap(err, "failed to parse params of %s", n.NodeName)
	}
	return nil
}

func (n *QuestionClassifierNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *chainnode) QuestionClassifier() *QuestionClassifierNode {
	f := newQuestionClassifierNode()
	n.linkChild(f)
	return f
}
