package pipeline

import (
	"fmt"

	"transwarp.io/applied-ai/applet-engine/models"
)

// An NsqPubNode will take the incoming data stream and publish it to an Mqtt broker.
//
// Example:
//    stream
//        |window()
//            .period(10s)
//            .every(5s)
//        |top('value', 10)
//        //Post the top 10 results over the last 10s updated every 5s.
//        |nsqPub('topic',  'payload')

type NsqPubNode struct {
	chainnode
	Topic   string
	Payload string
	Address string
	models.NodeMeta
}

func newNsqPubNode(wants EdgeType, topic string) *NsqPubNode {
	return &NsqPubNode{
		chainnode: newBasicChainNode("nsq_pub", wants, wants),
		Topic:     topic,
	}
}

func (n *NsqPubNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

// tick:ignore
func (p *NsqPubNode) validate() error {
	if err := p.NodeMeta.Validate(); err != nil {
		return err
	}
	if p.Topic == "" {
		return fmt.Errorf("must provide nsq topic")
	}
	if p.Address == "" {
		return fmt.Errorf("must provide nsq address")
	}
	return nil
}
