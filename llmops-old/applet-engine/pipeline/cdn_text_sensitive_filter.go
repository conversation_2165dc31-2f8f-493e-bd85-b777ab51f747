package pipeline

import (
	"strings"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
	"transwarp.io/applied-ai/applet-engine/models"
)

// TextSensitiveFilterNode
//
//	stream
//	    |textSplit()
type TextSensitiveFilterNode struct {
	chainnode      `json:"-"`
	SensitiveWords string `json:"sensitiveWords"`
	DisplayText    string `json:"displayText"`
	models.NodeMeta
	Words []string `json:"-"`
}

func newTextSensitiveFilterNode() *TextSensitiveFilterNode {
	return &TextSensitiveFilterNode{chainnode: newBasicChainNode("textSensitiveFilter", StreamEdge, StreamEdge)}
}

// validate 判断算子初始化的一些属性是否合法
func (s *TextSensitiveFilterNode) validate() error {
	if err := s.NodeMeta.Validate(); err != nil {
		return err
	}
	if s.SensitiveWords == "" {
		return stderr.InvalidParam.Error("SensitiveWords are necessary")

	}
	rawWords := strings.Split(s.SensitiveWords, "\n")
	if len(rawWords) == 0 {
		return stderr.InvalidParam.Error("SensitiveWords are necessary")
	}
	ws := utils.NewSet()
	for _, word := range rawWords {
		// 去除首尾空格
		word = strings.Trim(word, " ")
		if word == "" {
			// 去除空行
			continue
		}
		// 去重
		ws.Add(word)
	}
	if ws.Count() == 0 {
		return stderr.InvalidParam.Error("No valid SensitiveWords found")
	}
	s.Words = ws.List()

	if s.DisplayText == "" {
		s.DisplayText = "Abort."
	}
	return nil
}

func (n *chainnode) TextSensitiveFilter() *TextSensitiveFilterNode {
	f := newTextSensitiveFilterNode()
	n.linkChild(f)
	return f
}

func (n *TextSensitiveFilterNode) Meta() models.NodeMeta {
	return n.NodeMeta
}
