package pipeline

import (
	"strings"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/tools/uuid"
)

const DEFAULT_LISTEN_HTTP_NODE_NAME = "listen_http"

// A ListenHttpNode selects a subset of the data flowing through a StreamNode.
// The stream node allows you to select which portion of the stream you want to process.
//
// Example:
//
//		stream
//		    |listenHttp()
//		       .port('')
//		       .path('/')
//	       	   .handler('default')
type ListenHttpNode struct {
	chainnode `json:"-"`
	Port      int64         `json:"port"`      // 监听HTTP请求的TCP端口
	Path      string        `json:"path"`      // 接收请求的路径地址
	Handler   string        `json:"handler"`   // 选择的请求处理方式，默认为 defaultListener
	Async     bool          `json:"async"`     // 是否等待收到响应或超时后再返回
	Timeout   time.Duration `json:"timeout"`   // 同步模式下，等待响应的最大时间
	NoPretty  bool          `json:"no_pretty"` // 返回结构体的情况下，是否不对json输出进行美化
	models.NodeMeta
}

func newListenHttpNode() *ListenHttpNode {
	return &ListenHttpNode{chainnode: newBasicChainNode("listenHttp", StreamEdge, StreamEdge)}
}

func (s *ListenHttpNode) ListenHttp() *ListenHttpNode {
	f := newListenHttpNode()
	s.linkChild(f)
	return f
}

func (s *ListenHttpNode) validate() error {
	// 初始化NodeMeta
	if s.Id == "" {
		s.Id = uuid.New().String()
	}
	if s.NodeID == "" {
		s.NodeID = uuid.New().String()
	}
	s.NodeName = DEFAULT_LISTEN_HTTP_NODE_NAME
	s.InnerNode = true

	if s.Port == 0 {
		s.Port = 80
	}
	if s.Path == "" {
		return stderr.InvalidParam.Error("url path of listen http node is empty")
	}
	if !strings.HasPrefix(s.Path, "/") || strings.HasSuffix(s.Path, "/") {
		return stderr.InvalidParam.Error("invalid url path %s of listen http node, it must start with '/' but not end with '/'", s.Path)
	}
	return nil
}

func (n *ListenHttpNode) Meta() models.NodeMeta {
	return n.NodeMeta
}
