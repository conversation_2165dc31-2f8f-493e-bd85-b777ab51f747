package pipeline

import "github.com/influxdata/influxdb/influxql"

//tick:ignore
type ReduceCreater struct {
{{with $types := .}}
{{range $a := $types}}
{{range $e := $types}}
	Create{{$a.Name}}{{if ne $a.Name $e.Name}}{{$e.Name}}{{end}}Reducer func() (influxql.{{$a.Name}}PointAggregator, influxql.{{$e.Name}}PointEmitter)
{{end}}{{end}}{{end}}

	TopBottomCallInfo *TopBottomCallInfo
	IsSimpleSelector  bool
	IsStreamTransformation bool
	IsEmptyOK bool
}

