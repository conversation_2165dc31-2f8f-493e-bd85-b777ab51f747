package pipeline

import (
	text "text/template"
	"text/template/parse"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-engine/models"
)

// PromptTmplNode
//
//	stream
//	    |promptTmpl()
type PromptTmplNode struct {
	chainnode `json:"-"`
	TmplText  string `json:"tmpl_text"` // 提示词模板文本, 格式为 go text/template
	RawInput  bool   `json:"raw_input"` // 是否使用原始输入，为true时直接把输入作为变量渲染模板
	models.NodeMeta
	// 内部变量
	Tmpl *text.Template
}

func newPromptTmplNode() *PromptTmplNode {
	return &PromptTmplNode{chainnode: newBasicChainNode("promptTmpl", StreamEdge, StreamEdge)}
}

// validate 判断算子初始化的一些属性是否合法
func (s *PromptTmplNode) validate() error {
	if err := s.NodeMeta.Validate(); err != nil {
		return err
	}
	tmpl := text.New("prompt-tmpl")
	if _, err := tmpl.Parse(s.TmplText); err != nil {
		return stderr.InvalidParam.Cause(err, "invalid go text template")
	}
	s.Tmpl = tmpl
	for _, n := range tmpl.Root.Nodes {
		if n.Type() != parse.NodeAction {
			continue
		}
		stdlog.Infof("template node: %s", n.String())
	}
	return nil
}

func (n *PromptTmplNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *chainnode) PromptTmpl() *PromptTmplNode {
	f := newPromptTmplNode()
	n.linkChild(f)
	return f
}
