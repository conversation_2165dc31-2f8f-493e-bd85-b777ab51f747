package pipeline

import (
	"encoding/json"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
	"transwarp.io/applied-ai/applet-engine/models"
)

// FileInputNode 读取文件内容
// |fileInput()
//
//	// 必填， 用于多个输入算子的情况下，标识数据来源，过滤不感兴趣字段
//	.nodeID('WidgetKeyFileInput')
//	// 可选， 用于指定提取 listenHttp 请求体中特定部分的数据
//	.inputKey('FileInput')
type FileInputNode struct {
	chainnode `json:"-"`
	inputnode
	models.NodeMeta
	Params string `json:"params"`

	// 内部变量，解析Params值
	ParsedParams script.WidgetParamsFileInput
}

func newFileInputNode() *FileInputNode {
	return &FileInputNode{chainnode: newBasicChainNode("fileInput", StreamEdge, StreamEdge)}
}

// validate 判断算子初始化的一些属性是否合法
func (n *FileInputNode) validate() error {
	if err := n.NodeMeta.Validate(); err != nil {
		return err
	}
	if n.InputKey == "" {
		stdlog.Warnf("using the default value of InputKey of file input node: %s", models.PredefinedFieldFileInput)
		n.InputKey = models.PredefinedFieldFileInput
	}
	if n.Params == "" {
		return stderr.Errorf("params of %s is empty", n.NodeName)
	}
	if err := json.Unmarshal([]byte(n.Params), &n.ParsedParams); err != nil {
		return stderr.Wrap(err, "failed to parse params of %s", n.NodeName)
	}
	if err := n.ParsedParams.Validate(); err != nil {
		return stderr.Wrap(err, "validate params of %s failed", n.NodeName)
	}
	return nil
}

func (n *FileInputNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *chainnode) FileInput() *FileInputNode {
	f := newFileInputNode()
	n.linkChild(f)
	return f
}
