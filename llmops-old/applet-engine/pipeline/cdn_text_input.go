package pipeline

import (
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-engine/models"
)

// TextInputNode
//
//	stream
//	    |textInput()
type TextInputNode struct {
	chainnode `json:"-"`
	inputnode
	models.NodeMeta
}

func newTextInputNode() *TextInputNode {
	return &TextInputNode{chainnode: newBasicChainNode("textInput", StreamEdge, StreamEdge)}
}

// validate 判断算子初始化的一些属性是否合法
func (s *TextInputNode) validate() error {
	if err := s.inputnode.validate(); err != nil {
		return err
	}
	if err := s.NodeMeta.Validate(); err != nil {
		return err
	}
	if s.InputKey == "" {
		stdlog.Warnf("using the default value of InputKey of text input node: %s", models.PredefinedFieldTextInput)
		s.InputKey = models.PredefinedFieldTextInput
	}
	return nil
}

func (n *chainnode) TextInput() *TextInputNode {
	f := newTextInputNode()
	n.linkChild(f)
	return f
}

func (n *TextInputNode) Meta() models.NodeMeta {
	return n.NodeMeta
}
