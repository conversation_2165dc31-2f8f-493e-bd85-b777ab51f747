package pipeline

import (
	"encoding/json"
	"fmt"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-backend/pkg/models/health"
	"transwarp.io/applied-ai/applet-engine/clients"
	"transwarp.io/applied-ai/applet-engine/models"
)

type ToolCallNode struct {
	chainnode `json:"-"`
	models.NodeMeta
	Params string `json:"params"`

	// 内部变量
	ToolDescriber *agent_definition.APIToolDescriber
}

func newToolCallNode() *ToolCallNode {
	return &ToolCallNode{chainnode: newBasicChainNode("tool_call", StreamEdge, StreamEdge)}
}

// validate 判断算子初始化的一些属性是否合法
func (t *ToolCallNode) validate() error {
	if err := t.NodeMeta.Validate(); err != nil {
		return err
	}
	if t.Params == "" {
		return stderr.InvalidParam.Error("params field is unspecified")
	}
	if err := json.Unmarshal([]byte(t.Params), &t.ToolDescriber); err != nil {
		return stderr.Wrap(err, "failed to parse tool call params")
	}
	return nil
}

func (t *ToolCallNode) Meta() models.NodeMeta {
	return t.NodeMeta
}

func (t *ToolCallNode) GetHealth() []health.ServiceHealth {
	// TODO: 服务名称与ID从ToolDescriber获取
	serviceName := "ToolCall"
	serviceID := t.Name() + "-" + serviceName
	errorHealth := health.ServiceHealth{
		ID:      serviceID,
		Name:    serviceName,
		Healthy: false,
		Detail:  "",
	}
	serviceHealth, err := clients.ApiToolExecutor.CheckHealth(t.ToolDescriber)
	if err != nil {
		errorHealth.Detail = fmt.Sprintf("Failed to check api tool health: %v", err)
		return []health.ServiceHealth{errorHealth}
	}
	return []health.ServiceHealth{serviceHealth}
}

func (n *chainnode) ToolCall() *ToolCallNode {
	f := newToolCallNode()
	n.linkChild(f)
	return f
}
