package pipeline

import (
	"encoding/json"
	"fmt"

	"transwarp.io/applied-ai/applet-engine/models"
)

// An KafkaPubNode will take the incoming data stream and publish it to an kafka broker.
//
// Example:
//    stream
//        |window()
//            .period(10s)
//            .every(5s)
//        |top('value', 10)
//        //Post the top 10 results over the last 10s updated every 5s.
//        |kafkaPub('topic',  'payload')

type KafkaPubNode struct {
	chainnode

	// tick:ignore
	Topic string `json:"topic"`

	// tick:ignore
	Payload string `json:"payload"`

	//tick:ignore
	Address string `json:"address"`
	models.NodeMeta
}

func newKafkaPubNode(wants EdgeType, topic string) *KafkaPubNode {
	return &KafkaPubNode{
		chainnode: newBasicChainNode("kafka_pub", wants, wants),
		Topic:     topic,
	}
}

// MarshalJSON converts KafkaPubNode to JSON
// tick:ignore
func (n *KafkaPubNode) MarshalJSON() ([]byte, error) {
	type <PERSON>as <PERSON>f<PERSON>ubN<PERSON>
	var raw = &struct {
		TypeOf
		*Alias
	}{
		TypeOf: TypeOf{
			Type: "kafkaPub",
			ID:   n.ID(),
		},
		Alias: (*Alias)(n),
	}
	return json.Marshal(raw)
}

// UnmarshalJSON converts JSON to an KafkaPubNode
// tick:ignore
func (n *KafkaPubNode) UnmarshalJSON(data []byte) error {
	type Alias KafkaPubNode
	var raw = &struct {
		TypeOf
		*Alias
	}{
		Alias: (*Alias)(n),
	}
	err := json.Unmarshal(data, raw)
	if err != nil {
		return err
	}
	if raw.Type != "kafkaPub" {
		return fmt.Errorf("error unmarshaling node %d of type %s as KafkaPubNode", raw.ID, raw.Type)
	}
	n.setID(raw.ID)
	return nil
}

func (n *KafkaPubNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

// tick:ignore
func (p *KafkaPubNode) validate() error {
	if err := p.NodeMeta.Validate(); err != nil {
		return err
	}
	if p.Topic == "" {
		return fmt.Errorf("must provide kafka topic")
	}
	if p.Address == "" {
		return fmt.Errorf("must provide kafka address")
	}
	return nil
}
