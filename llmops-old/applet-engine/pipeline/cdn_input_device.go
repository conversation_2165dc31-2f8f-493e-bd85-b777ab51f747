package pipeline

import (
	"encoding/json"
	"fmt"

	"transwarp.io/applied-ai/applet-engine/models"
)

const (
	NodeInputDevice  = "inputDevice"
	PropertyProtocol = "protocol"
	PropertyProduct  = "product"
	PropertyDevice   = "device"
)

// A InputDeviceNode the specific MQTT topic from (protocol, product, device).
//
// Example:
//
//	var data = stream
//	    |inputDevice()
//	       .protocol("csv")
//	       .product("SO2")
//	       .device("SO2")
//	// Continue normal processing of the data stream
//	data...
type InputDeviceNode struct {
	chainnode

	Protocol string `json:"protocol"`
	Product  string `json:"product"`
	Device   string `json:"device"`
	models.NodeMeta
}

func newInputDeviceNode() *InputDeviceNode {
	return &InputDeviceNode{
		chainnode: newBasicChainNode(NodeInputDevice, StreamEdge, StreamEdge),
	}
}

// MarshalJSON converts InputDeviceNode to JSON
// tick:ignore
func (n *InputDeviceNode) MarshalJSON() ([]byte, error) {
	type Alias InputDeviceNode
	var raw = &struct {
		TypeOf
		*Alias
	}{
		TypeOf: TypeOf{
			Type: NodeInputDevice,
			ID:   n.ID(),
		},
		Alias: (*Alias)(n),
	}
	return json.Marshal(raw)
}

// UnmarshalJSON converts JSON to an InputDeviceNode
// tick:ignore
func (n *InputDeviceNode) UnmarshalJSON(data []byte) error {
	type Alias InputDeviceNode
	var raw = &struct {
		TypeOf
		*Alias
	}{
		Alias: (*Alias)(n),
	}
	if err := json.Unmarshal(data, raw); err != nil {
		return err
	}
	if raw.Type != NodeInputDevice {
		return fmt.Errorf("error unmarshaling node %d of type %s as InputDeviceNode", raw.ID, raw.Type)
	}

	n.setID(raw.ID)
	return nil
}

func (n *InputDeviceNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

// tick:ignore
func (n *InputDeviceNode) validate() error {
	if err := n.NodeMeta.Validate(); err != nil {
		return err
	}
	if n.Protocol == "" {
		return fmt.Errorf("must specify protocol's ID")
	}

	if n.Product == "" {
		return fmt.Errorf("must specify product's ID")
	}

	if n.Device == "" {
		return fmt.Errorf("must specify device's ID")
	}
	return nil
}
