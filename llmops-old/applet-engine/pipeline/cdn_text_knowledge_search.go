package pipeline

import (
	"encoding/json"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/models/health"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
	"transwarp.io/applied-ai/applet-engine/clients"
	"transwarp.io/applied-ai/applet-engine/models"
)

type TextKnowledgeSearchNode struct {
	chainnode `json:"-"`
	models.NodeMeta
	Params       string `json:"params"`
	WidgetParams widgets.WidgetParamsTextKnowSearch
}

func newTextKnowledgeSearchNode() *TextKnowledgeSearchNode {
	return &TextKnowledgeSearchNode{chainnode: newBasicChainNode("text_knowledge_search", StreamEdge, StreamEdge)}
}

// validate 判断算子初始化的一些属性是否合法
func (t *TextKnowledgeSearchNode) validate() error {
	if err := t.NodeMeta.Validate(); err != nil {
		return err
	}
	if t.Params == "" {
		return stderr.InvalidParam.Error("params field is unspecified")
	}
	if err := json.Unmarshal([]byte(t.Params), &t.WidgetParams); err != nil {
		return stderr.Wrap(err, "failed to parse TextKnowledgeSearchNode params")
	}
	return nil
}

func (t *TextKnowledgeSearchNode) Meta() models.NodeMeta {
	return t.NodeMeta
}

func (n *chainnode) TextKnowledgeSearch() *TextKnowledgeSearchNode {
	f := newTextKnowledgeSearchNode()
	n.linkChild(f)
	return f
}

func (t *TextKnowledgeSearchNode) GetHealth() []health.ServiceHealth {
	respSvcHealth := health.ServiceHealth{
		ID:      t.Id,
		Name:    "TextKnowledgeSearchNode",
		Healthy: true,
		Detail:  "",
	}
	for _, kb := range t.WidgetParams.KnowledgeBaseDesc {
		serviceHealth, err := clients.KnowledgeToolExecutor.CheckHealth(kb)
		if err != nil || serviceHealth.Healthy == false {
			respSvcHealth.Healthy = false
			respSvcHealth.Detail = respSvcHealth.Detail + "," + serviceHealth.Detail
		}
	}
	return []health.ServiceHealth{respSvcHealth}
}
