package pipeline

import (
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/models/health"

	"transwarp.io/applied-ai/applet-engine/conf"
	"transwarp.io/applied-ai/applet-engine/models"
)

// runCodeNode
//
//	stream
//	    |runCode()
//	        .code('def handler():')
type RunCodeNode struct {
	chainnode `json:"-"`
	Runtime   string `json:"runtime"`
	Code      string `json:"code"`
	models.NodeMeta
}

func newRunCodeNode() *RunCodeNode {
	return &RunCodeNode{chainnode: newBasicChainNode("runCode", StreamEdge, StreamEdge)}
}

// validate 判断算子初始化的一些属性是否合法
func (n *RunCodeNode) validate() error {
	if err := n.NodeMeta.Validate(); err != nil {
		return err
	}
	if n.Code == "" {
		return stderr.InvalidParam.Error("code field is unspecified")
	}
	return nil
}

func (n *RunCodeNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *chainnode) RunCode() *RunCodeNode {
	f := newRunCodeNode()
	n.linkChild(f)
	return f
}

func (n *RunCodeNode) GetHealth() []health.ServiceHealth {
	serviceName := "RunCode"
	serviceID := n.Name() + "-" + serviceName
	serviceHealth := checkUrlHealth(serviceID, serviceName, conf.Config.BackendTask.RunCodeUrl)
	return []health.ServiceHealth{serviceHealth}
}
