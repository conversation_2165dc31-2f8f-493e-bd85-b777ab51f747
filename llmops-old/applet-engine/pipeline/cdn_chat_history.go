package pipeline

import (
	text "text/template"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-engine/models"
)

// ChatHistoryNode
//
//	stream
//	    |chatHistory()
type ChatHistoryNode struct {
	chainnode `json:"-"`
	inputnode
	models.NodeMeta
	MaxRounds int64  `json:"max_rounds"`
	Tmpl      string `json:"tmpl"`
	// 内部变量
	GoTmpl *text.Template
}

func newChatHistoryNode() *ChatHistoryNode {
	return &ChatHistoryNode{chainnode: newBasicChainNode("chatHistory", StreamEdge, StreamEdge)}
}

func (c *ChatHistoryNode) Meta() models.NodeMeta {
	return c.NodeMeta
}

// validate 判断算子初始化的一些属性是否合法
func (c *ChatHistoryNode) validate() error {
	if err := c.inputnode.validate(); err != nil {
		return err
	}
	if err := c.NodeMeta.Validate(); err != nil {
		return err
	}
	if c.<PERSON>ounds <= 0 {
		return stderr.InvalidParam.Error("max rounds of %s is %d, must greater than 0", c.Name(), c.MaxRounds)
	}
	if c.InputKey == "" {
		stdlog.Warnf("using the default value of InputKey of chat history node: %s", models.PredefinedFieldChatInput)
		c.InputKey = models.PredefinedFieldChatInput
	}
	if c.Tmpl == "" {
		return stderr.InvalidParam.Error("go text template is empty")
	}
	tmpl := text.New("chat-history-tmpl")
	if _, err := tmpl.Parse(c.Tmpl); err != nil {
		return stderr.InvalidParam.Cause(err, "invalid go text template")
	}
	c.GoTmpl = tmpl
	return nil
}

func (n *chainnode) ChatHistory() *ChatHistoryNode {
	f := newChatHistoryNode()
	n.linkChild(f)
	return f
}
