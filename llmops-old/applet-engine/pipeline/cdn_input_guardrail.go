package pipeline

import (
	"encoding/json"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
	"transwarp.io/applied-ai/applet-engine/models"
)

// inputGuardrailNode
//
//	stream
//	    |inputGuardrail()
//	         . params('''{}''')
type InputGuardrailNode struct {
	chainnode `json:"-"`
	models.NodeMeta
	Params string `json:"params"`

	// 内部变量
	ParsedParams *widgets.WidgetParamsInputGuardrail
}

func newInputGuardrailNode() *InputGuardrailNode {
	return &InputGuardrailNode{chainnode: newBasicChainNode("inputGuardrail", StreamEdge, StreamEdge)}
}

// validate 判断算子初始化的一些属性是否合法
func (n *InputGuardrailNode) validate() error {
	if err := n.NodeMeta.Validate(); err != nil {
		return err
	}
	if n.Params == "" {
		return stderr.InvalidParam.Error("params of %s is empty", n.NodeName)
	}
	if err := json.Unmarshal([]byte(n.Params), &n.ParsedParams); err != nil {
		return stderr.Wrap(err, "failed to parse params of %s", n.NodeName)
	}
	if n.ParsedParams.SafetyConfig.ID == "" {
		return stderr.InvalidParam.Error("safety_config.id of %s is empty", n.NodeName)
	}
	return nil
}

func (n *InputGuardrailNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *chainnode) InputGuardrail() *InputGuardrailNode {
	f := newInputGuardrailNode()
	n.linkChild(f)
	return f
}
