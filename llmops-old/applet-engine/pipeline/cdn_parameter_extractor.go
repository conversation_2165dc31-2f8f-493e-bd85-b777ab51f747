package pipeline

import (
	"encoding/json"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
	"transwarp.io/applied-ai/applet-engine/models"
)

// ParameterExtractorNode
//
//	stream
//	    |parameterExtractor()
type ParameterExtractorNode struct {
	chainnode `json:"-"`
	models.NodeMeta
	Params string `json:"params"`

	// 内部变量，解析Params值
	ParsedParams script.WidgetParamsParameterExtractor
}

func newParameterExtractorNode() *ParameterExtractorNode {
	return &ParameterExtractorNode{chainnode: newBasicChainNode("parameterExtractor", StreamEdge, StreamEdge)}
}

func (n *ParameterExtractorNode) validate() error {
	if err := n.NodeMeta.Validate(); err != nil {
		return err
	}
	if n.Params == "" {
		return stderr.InvalidParam.Error("params of %s is empty", n.NodeName)
	}
	if err := json.Unmarshal([]byte(n.Params), &n.ParsedParams); err != nil {
		return stderr.Wrap(err, "failed to parse params of %s", n.NodeName)
	}
	return nil
}

func (n *ParameterExtractorNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *chainnode) ParameterExtractor() *ParameterExtractorNode {
	f := newParameterExtractorNode()
	n.linkChild(f)
	return f
}
