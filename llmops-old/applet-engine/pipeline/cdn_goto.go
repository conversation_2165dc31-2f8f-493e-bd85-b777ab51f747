package pipeline

import (
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-engine/models"
)

// GotoNode
//
//	stream
//	    |goto()
type GotoNode struct {
	chainnode     `json:"-"`
	TargetNode    string `json:"target_node"`
	MaxLoopRounds int64  `json:"max_loop_rounds"`
	models.NodeMeta
}

func newGotoNode() *GotoNode {
	return &GotoNode{chainnode: newBasicChainNode("goto", StreamEdge, StreamEdge)}
}

func (n *GotoNode) validate() error {
	if err := n.NodeMeta.Validate(); err != nil {
		return err
	}
	if n.TargetNode == "" {
		return stderr.InvalidParam.Error("target of %s node is empty", n.Name())
	}
	if n.MaxLoopRounds <= 0 {
		return stderr.InvalidParam.Error("max loop rounds of %s is %d, must greater than 0", n.<PERSON>(), n.MaxLoopRounds)
	}
	return nil
}

func (n *GotoNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *chainnode) Goto() *GotoNode {
	f := newGotoNode()
	n.linkChild(f)
	return f
}
