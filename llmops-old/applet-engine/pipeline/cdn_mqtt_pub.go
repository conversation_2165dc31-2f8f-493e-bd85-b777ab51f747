package pipeline

import (
	"encoding/json"
	"fmt"

	"transwarp.io/applied-ai/applet-engine/models"
)

// An MQTTPubNode will take the incoming data stream and publish it to an Mqtt broker.
//
// Example:
//    stream
//        |window()
//            .period(10s)
//            .every(5s)
//        |top('value', 10)
//        //Post the top 10 results over the last 10s updated every 5s.
//        |mqttPub('topic')
//        	  .address('localhost:1883')
// 			  .username('root')
// 			  .password('root')
//        	  .payload('/##input##/')

type MQTTPubNode struct {
	chainnode

	Topic string `json:"topic"`

	Payload string `json:"payload"`

	Username string `json:"username"`

	Password string `json:"password"`

	Address string `json:"address"`
	models.NodeMeta
}

func newMQTTPubNode(wants EdgeType, topic string) *MQTTPubNode {
	return &MQTTPubNode{
		chainnode: newBasicChainNode("mqtt_pub", wants, wants),
		Topic:     topic,
	}
}

// MarshalJSON converts MQTTPubNode to JSON
// tick:ignore
func (n *MQTTPubNode) MarshalJSON() ([]byte, error) {
	type Alias MQTTPubNode
	var raw = &struct {
		TypeOf
		*Alias
	}{
		TypeOf: TypeOf{
			Type: "mqttPub",
			ID:   n.ID(),
		},
		Alias: (*Alias)(n),
	}
	return json.Marshal(raw)
}

// UnmarshalJSON converts JSON to an MQTTPubNode
// tick:ignore
func (n *MQTTPubNode) UnmarshalJSON(data []byte) error {
	type Alias MQTTPubNode
	var raw = &struct {
		TypeOf
		*Alias
	}{
		Alias: (*Alias)(n),
	}
	err := json.Unmarshal(data, raw)
	if err != nil {
		return err
	}
	if raw.Type != "mqttPub" {
		return fmt.Errorf("error unmarshaling node %d of type %s as MQTTPubNode", raw.ID, raw.Type)
	}
	n.setID(raw.ID)
	return nil
}

func (n *MQTTPubNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

// tick:ignore
func (p *MQTTPubNode) validate() error {
	if err := p.NodeMeta.Validate(); err != nil {
		return err
	}
	if p.Topic == "" {
		return fmt.Errorf("must provide topic")
	}
	return nil
}
