package pipeline

import (
	"encoding/json"
	"fmt"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-backend/pkg/models/health"
	"transwarp.io/applied-ai/applet-engine/clients"
	"transwarp.io/applied-ai/applet-engine/models"
)

type TextKnowledgeInsertNode struct {
	chainnode `json:"-"`
	models.NodeMeta
	Params      string `json:"params"`
	KbDescriber agent_definition.KnowlHubDescriber
}

func newTextKnowledgeInsertNode() *TextKnowledgeInsertNode {
	return &TextKnowledgeInsertNode{chainnode: newBasicChainNode("text_knowledge_insert", StreamEdge, StreamEdge)}
}

// validate 判断算子初始化的一些属性是否合法
func (t *TextKnowledgeInsertNode) validate() error {
	if err := t.NodeMeta.Validate(); err != nil {
		return err
	}
	if t.Params == "" {
		return stderr.InvalidParam.Error("params field is unspecified")
	}
	if err := json.Unmarshal([]byte(t.Params), &t.KbDescriber); err != nil {
		return stderr.Wrap(err, "failed to parse TextKnowledgeInsertNode params")
	}
	return nil
}

func (t *TextKnowledgeInsertNode) Meta() models.NodeMeta {
	return t.NodeMeta
}

func (n *chainnode) TextKnowledgeInsert() *TextKnowledgeInsertNode {
	f := newTextKnowledgeInsertNode()
	n.linkChild(f)
	return f
}

func (t *TextKnowledgeInsertNode) GetHealth() []health.ServiceHealth {
	// TODO: 服务名称与ID从ToolDescriber获取
	serviceName := "TextKnowledgeInsert"
	serviceID := t.Name() + "-" + serviceName
	errorHealth := health.ServiceHealth{
		ID:      serviceID,
		Name:    serviceName,
		Healthy: false,
		Detail:  "",
	}
	serviceHealth, err := clients.KnowledgeToolExecutor.CheckHealth(&t.KbDescriber)
	if err != nil {
		errorHealth.Detail = fmt.Sprintf("Failed to check knowledge tool health: %v", err)
		return []health.ServiceHealth{errorHealth}
	}
	return []health.ServiceHealth{serviceHealth}
}
