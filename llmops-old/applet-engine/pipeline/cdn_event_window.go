package pipeline

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/influxdata/influxdb/influxql"
	"transwarp.io/applied-ai/applet-engine/models"
)

type EventWindowNode struct {
	chainnode     `json:"-"`
	PreCount      int64         `json:"preCount"`
	PostCount     int64         `json:"postCount"`
	PrePeriod     time.Duration `json:"prePeriod"`
	PostPeriod    time.Duration `json:"postPeriod"`
	Source        string        `json:"source"`
	WithEventFlag bool          `json:"withEvent" tick:"WithEvent"`
	models.NodeMeta
}

func newEventWindowNode() *EventWindowNode {
	return &EventWindowNode{
		chainnode: newBasicChainNode("eventWindow", StreamEdge, BatchEdge),
	}
}

// MarshalJSON converts EventWindowNode to JSON
// tick:ignore
func (n *EventWindowNode) MarshalJSON() ([]byte, error) {
	type Alias EventWindowNode
	var raw = &struct {
		TypeOf
		*Alias
		PreCount   int64  `json:"preCount"`
		PostCount  int64  `json:"postCount"`
		PrePeriod  string `json:"prePeriod"`
		PostPeriod string `json:"postPeriod"`
		Source     string `json:"source"`
	}{
		TypeOf: TypeOf{
			Type: "eventWindow",
			ID:   n.ID(),
		},
		PreCount:   n.PreCount,
		PostCount:  n.PostCount,
		Source:     n.Source,
		Alias:      (*Alias)(n),
		PrePeriod:  influxql.FormatDuration(n.PrePeriod),
		PostPeriod: influxql.FormatDuration(n.PostPeriod),
	}
	return json.Marshal(raw)
}

// UnmarshalJSON converts JSON to an EventWindowNode
// tick:ignore
func (n *EventWindowNode) UnmarshalJSON(data []byte) error {
	type Alias EventWindowNode
	var raw = &struct {
		TypeOf
		*Alias
		PreCount   int64  `json:"preCount"`
		PostCount  int64  `json:"postCount"`
		PrePeriod  string `json:"prePeriod"`
		PostPeriod string `json:"postPeriod"`
		Source     string `json:"source"`
	}{
		Alias: (*Alias)(n),
	}
	err := json.Unmarshal(data, raw)
	if err != nil {
		return err
	}
	if raw.Type != "eventWindow" {
		return fmt.Errorf("error unmarshaling node %d of type %s as EventWindowNode", raw.ID, raw.Type)
	}

	n.PrePeriod, err = influxql.ParseDuration(raw.PrePeriod)
	if err != nil {
		return err
	}

	n.PostPeriod, err = influxql.ParseDuration(raw.PostPeriod)
	if err != nil {
		return err
	}
	n.PreCount = raw.PreCount
	n.PostCount = raw.PostCount
	n.Source = raw.Source

	n.setID(raw.ID)
	return nil
}

func (w *EventWindowNode) WithEvent() *EventWindowNode {
	w.WithEventFlag = true
	return w
}

func (n *EventWindowNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (w *EventWindowNode) validate() error {
	if w.Source == "" {
		return errors.New("source is necessary for event window node")
	}
	if err := w.NodeMeta.Validate(); err != nil {
		return err
	}
	return nil
}
