package pipeline

import (
	"transwarp.io/applied-ai/applet-engine/models"
)

type ChunksOutputNode struct {
	chainnode `json:"-"`
	models.NodeMeta
}

func newChunksOutputNode() *ChunksOutputNode {
	return &ChunksOutputNode{chainnode: newBasicChainNode("ChunksOutput", StreamEdge, StreamEdge)}
}

// validate 判断算子初始化的一些属性是否合法
func (n *ChunksOutputNode) validate() error {
	if err := n.NodeMeta.Validate(); err != nil {
		return err
	}
	return nil
}

func (n *ChunksOutputNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *chainnode) ChunksOutput() *ChunksOutputNode {
	f := newChunksOutputNode()
	n.linkChild(f)
	return f
}
