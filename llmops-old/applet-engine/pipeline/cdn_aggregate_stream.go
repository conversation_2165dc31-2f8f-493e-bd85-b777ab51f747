package pipeline

import "transwarp.io/applied-ai/applet-engine/models"

// AggregateStreamNode
//
//	stream
//	    |aggregateStream()
type AggregateStreamNode struct {
	chainnode `json:"-"`
	models.NodeMeta
}

func newAggregateStreamNode() *AggregateStreamNode {
	return &AggregateStreamNode{chainnode: newBasicChainNode("aggregateStream", StreamEdge, StreamEdge)}
}

func (n *AggregateStreamNode) validate() error {
	return n.NodeMeta.Validate()
}

func (n *AggregateStreamNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *chainnode) AggregateStream() *AggregateStreamNode {
	f := newAggregateStreamNode()
	n.linkChild(f)
	return f
}
