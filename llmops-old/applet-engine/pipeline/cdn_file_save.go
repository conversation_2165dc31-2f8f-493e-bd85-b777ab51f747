package pipeline

import (
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-engine/models"
)

// FileSaveNode
//
//	stream
//	    |fileSave()
type FileSaveNode struct {
	chainnode          `json:"-"`
	SaveDir            string `json:"save_dir"`
	DisallowOverwrite  bool   `json:"disallow_overwrite"`   // DisallowOverwrite 为 true 时不允许覆盖已有文件，默认为 false，允许覆盖
	EnableDownloadMode bool   `json:"enable_download_mode"` // EnableDownloadMode 输出对应文件的下载地址
	models.NodeMeta
}

func newFileSaveNode() *FileSaveNode {
	return &FileSaveNode{chainnode: newBasicChainNode("fileSave", StreamEdge, StreamEdge)}
}

// validate 判断算子初始化的一些属性是否合法
func (f *FileSaveNode) validate() error {
	if err := f.NodeMeta.Validate(); err != nil {
		return err
	}
	if f.SaveDir == "" {
		stdlog.Warnf("the SaveDir field of FileSaveNode is nil, save file to sfs root dir")
	}
	return nil
}

func (n *FileSaveNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *chainnode) FileSave() *FileSaveNode {
	f := newFileSaveNode()
	n.linkChild(f)
	return f
}
