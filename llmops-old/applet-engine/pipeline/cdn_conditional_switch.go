package pipeline

import (
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-engine/models"
)

// ConditionalSwitchNode
//
//	stream
//		|conditionalSwitch()
//			.condition('input == 1')

type ConditionalSwitchNode struct {
	chainnode `json:"-"`
	Condition string `json:"condition"`
	IfNode    string `json:"if_target"`
	ElseNode  string `json:"else_target"`
	models.NodeMeta
}

func newConditionalSwitchNode() *ConditionalSwitchNode {
	return &ConditionalSwitchNode{chainnode: newBasicChainNode("conditionalSwitch", StreamEdge, StreamEdge)}
}

// validate 判断算子初始化的一些属性是否合法
func (n *ConditionalSwitchNode) validate() error {
	if err := n.NodeMeta.Validate(); err != nil {
		return err
	}
	if n.Condition == "" {
		return stderr.InvalidParam.Error("condition of %s node is empty", n.Name())
	}
	if n.IfNode == "" {
		return stderr.InvalidParam.Error("if target of %s node is empty", n.Name())
	}
	if n.ElseNode == "" {
		return stderr.InvalidParam.Error("else target of %s node is empty", n.Name())
	}
	return nil
}

func (n *ConditionalSwitchNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *chainnode) ConditionalSwitch() *ConditionalSwitchNode {
	f := newConditionalSwitchNode()
	n.linkChild(f)
	return f
}
