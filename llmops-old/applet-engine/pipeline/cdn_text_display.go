package pipeline

import (
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-engine/models"
)

// TextDisplayNode
//
//	stream
//		|textDisplay()
//			.prefix('xxx')
//			.suffix('xxx')

type TextDisplayNode struct {
	chainnode `json:"-"`
	Prefix    string `json:"prefix"`
	Suffix    string `json:"suffix"`
	models.NodeMeta
}

func newTextDisplayNode() *TextDisplayNode {
	return &TextDisplayNode{chainnode: newBasicChainNode("textDisplay", StreamEdge, StreamEdge)}
}

// validate 判断算子初始化的一些属性是否合法
func (s *TextDisplayNode) validate() error {
	if err := s.NodeMeta.Validate(); err != nil {
		return err
	}
	if s.Prefix == "" {
		stdlog.Warnf("current prefix is empty string")
	}
	if s.Suffix == "" {
		stdlog.Warnf("current suffix is empty string")
	}
	return nil
}

func (n *TextDisplayNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *chainnode) TextDisplay() *TextDisplayNode {
	f := newTextDisplayNode()
	n.linkChild(f)
	return f
}
