package pipeline

import (
	"encoding/json"
	"fmt"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/models/health"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
	"transwarp.io/applied-ai/applet-engine/models"
)

// A DlieInferNode selects a subset of the data flowing through a StreamNode.
// The stream node allows you to select which portion of the stream you want to process.
//
// Example:
//
//		stream
//		    |llmInfer()
//		       .port('')
//		       .path('/')
//	       	   .handler('default')
type DlieInferNode struct {
	chainnode `json:"-"`
	models.NodeMeta
	IsStream     bool                          `json:"is_stream"`
	System       string                        `json:"system"`        // 系统提示词
	UseBase64    bool                          `json:"use_base64"`    // 调用模型时,是否使用base64传输文件 暂时用于图像及视频理解+图像生成
	Params       string                        `json:"params"`        // 模型相关参数
	WidgetParams widgets.WidgetParamsDlieInfer `json:"widget_params"` // params对应的结构体
}

func newDlieInferNode() *DlieInferNode {
	return &DlieInferNode{
		chainnode: newBasicChainNode("llmInfer", StreamEdge, StreamEdge),
	}
}

func (n *DlieInferNode) DlieInfer() *DlieInferNode {
	f := newDlieInferNode()
	n.linkChild(f)
	return f
}

func (n *DlieInferNode) Meta() models.NodeMeta {
	return n.NodeMeta
}

func (n *DlieInferNode) validate() error {
	if err := n.NodeMeta.Validate(); err != nil {
		return err
	}
	if n.System == "" {
		n.System = script.DlieInferDefaultSystemPrompt
		stdlog.Warnf("current system prompt is empty string, use default system prompt: %s", script.DlieInferDefaultSystemPrompt)
	}
	if n.Params == "" {
		return stderr.InvalidParam.Errorf("params field is unspecified")
	}
	if err := json.Unmarshal([]byte(n.Params), &n.WidgetParams); err != nil {
		return stderr.Wrap(err, "failed to parse widget params")
	}
	_, err := clients.CheckModelConn(&n.WidgetParams.ModelService)
	if err != nil {
		return err
	}
	return nil
}

func (n *DlieInferNode) GetHealth() []health.ServiceHealth {
	model := n.WidgetParams.ModelService
	host, port, _ := clients.GetModelHost(&model)
	serviceName := n.WidgetParams.Name
	serviceID := n.Name() + "-" + serviceName
	url := fmt.Sprintf("%s%s:%s", models.HTTPProtocol, host, port)
	serviceHealth := checkUrlHealth(serviceID, serviceName, url)
	return []health.ServiceHealth{serviceHealth}
}
