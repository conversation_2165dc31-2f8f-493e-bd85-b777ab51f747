// zookeeper_latency_batch

// metric: "avg_latency"
// available_fields: "approximate_data_size","ephemerals_count","max_file_descriptor_count","max_latency","min_latency","num_alive_connections","open_file_descriptor_count","outstanding_requests","packets_received","packets_sent","version","watch_count","znode_count" 

// TELEGRAF CONFIGURATION
// [inputs.zookeeper]
//   servers = [":2181"]

// DEFINE: kapacitor define zookeeper_latency_batch -type batch -tick zookeeper/zookeeper_latency_batch.tick -dbrp telegraf.autogen
// ENABLE: kapacitor enable zookeeper_latency_batch

// Parameters
var info = 8 // ms
var warn = 12 // ms 
var crit = 15 // ms
var infoSig = 2.5
var warnSig = 3
var critSig = 3.5
var period = 10s
var every = 10s

// Dataframe
var data = batch
  |query('''SELECT mean(avg_latency) AS stat FROM "telegraf"."autogen"."zookeeper"''')
    .period(period)
    .every(every)
    .groupBy('host')
        
// Thresholds
var alert = data
  |eval(lambda: sigma("stat"))
    .as('sigma')
    .keep()
  |alert()
    .id('{{ index .Tags "host"}}/avg_latency')
    .message('{{ .ID }}:{{ index .Fields "stat" }}')
    .info(lambda: "stat" > info OR "sigma" > infoSig)
    .warn(lambda: "stat" > warn OR "sigma" > warnSig)
    .crit(lambda: "stat" > crit OR "sigma" > critSig)

// Alert
alert
  .log('/tmp/zookeeper_latency_batch_log.txt')


  