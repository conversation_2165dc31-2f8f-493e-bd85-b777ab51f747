// disk_alert_batch

// metric: used_percent
// available_fields: "free","inodes_free","inodes_total","inodes_used","total","used"

// TELEGRAF CONFIGURATION
// [[inputs.disk]]
//   ignore_fs = ["tmpfs", "devtmpfs"]

// DEFINE: kapacitor define disk_alert_batch -type batch -tick disk/disk_alert_batch.tick -dbrp telegraf.autogen
// ENABLE: kapacitor enable disk_alert_batch

// Parameters
var info = 75

var warn = 85

var crit = 92

var period = 10s

var every = 10s

// Dataframe
var data = batch
    |query('''SELECT mean(used_percent) AS stat FROM "telegraf"."autogen"."disk" WHERE path = '/' ''')
        .period(period)
        .every(every)
        .groupBy('host', 'path')

// Thresholds
var alert = data
    |alert()
        .id('{{ index .Tags "host"}}/disk_used/{{ index .Tags "path" }}')
        .message('{{ .ID }}:{{ index .Fields "stat" }}')
        .info(lambda: "stat" > info)
        .warn(lambda: "stat" > warn)
        .crit(lambda: "stat" > crit)

// Alert
alert
        .log('/tmp/disk_alert_log.txt')
