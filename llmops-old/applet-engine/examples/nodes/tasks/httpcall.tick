dbrp "telegraf"."autogen"

stream
    |from()
        .measurement('system')
    |window()
        .period(1m)
        .every(10s)
    |httpCall('http://127.0.0.1:5000/test')
        .payload('{ "instances": [[ "{{ index .Values "c0" }}", "{{ index .Values "c1" }}", "{{ index .Values "c2" }}", "{{ index .Values "c3" }}", "{{ index .Values "c4" }}", "{{ index .Values "c5" }}", "{{ index .Values "c6" }}", "{{ index .Values "c7" }}", "{{ index .Values "c8" }}", "{{ index .Values "c9" }}", "{{ index .Values "c10" }}", "{{ index .Values "c11" }}", "{{ index .Values "c12" }}", "{{ index .Values "c13" }}", "{{ index .Values "c14" }}", "{{ index .Values "c15" }}", "{{ index .Values "c16" }}", "{{ index .Values "c17" }}", "{{ index .Values "c18" }}", "{{ index .Values "c19" }}", "{{ index .Values "c20" }}" ]] }')