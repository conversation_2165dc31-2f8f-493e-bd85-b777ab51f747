// Code generated by protoc-gen-go. DO NOT EDIT.
// source: udf.proto

/*
Package agent is a generated protocol buffer package.

It is generated from these files:
	udf.proto

It has these top-level messages:
	InfoRequest
	InfoResponse
	OptionInfo
	InitRequest
	Option
	OptionValue
	InitResponse
	SnapshotRequest
	SnapshotResponse
	RestoreRequest
	RestoreResponse
	KeepaliveRequest
	KeepaliveResponse
	ErrorResponse
	BeginBatch
	Point
	EndBatch
	Request
	Response
*/
package agent

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type EdgeType int32

const (
	EdgeType_STREAM EdgeType = 0
	EdgeType_BATCH  EdgeType = 1
)

var EdgeType_name = map[int32]string{
	0: "STREAM",
	1: "BATCH",
}
var EdgeType_value = map[string]int32{
	"STREAM": 0,
	"BATCH":  1,
}

func (x EdgeType) String() string {
	return proto.EnumName(EdgeType_name, int32(x))
}
func (EdgeType) EnumDescriptor() ([]byte, []int) { return fileDescriptor0, []int{0} }

type ValueType int32

const (
	ValueType_BOOL     ValueType = 0
	ValueType_INT      ValueType = 1
	ValueType_DOUBLE   ValueType = 2
	ValueType_STRING   ValueType = 3
	ValueType_DURATION ValueType = 4
)

var ValueType_name = map[int32]string{
	0: "BOOL",
	1: "INT",
	2: "DOUBLE",
	3: "STRING",
	4: "DURATION",
}
var ValueType_value = map[string]int32{
	"BOOL":     0,
	"INT":      1,
	"DOUBLE":   2,
	"STRING":   3,
	"DURATION": 4,
}

func (x ValueType) String() string {
	return proto.EnumName(ValueType_name, int32(x))
}
func (ValueType) EnumDescriptor() ([]byte, []int) { return fileDescriptor0, []int{1} }

// Request that the process return information about available Options.
type InfoRequest struct {
}

func (m *InfoRequest) Reset()                    { *m = InfoRequest{} }
func (m *InfoRequest) String() string            { return proto.CompactTextString(m) }
func (*InfoRequest) ProtoMessage()               {}
func (*InfoRequest) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{0} }

type InfoResponse struct {
	Wants    EdgeType               `protobuf:"varint,1,opt,name=wants,enum=agent.EdgeType" json:"wants,omitempty"`
	Provides EdgeType               `protobuf:"varint,2,opt,name=provides,enum=agent.EdgeType" json:"provides,omitempty"`
	Options  map[string]*OptionInfo `protobuf:"bytes,3,rep,name=options" json:"options,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
}

func (m *InfoResponse) Reset()                    { *m = InfoResponse{} }
func (m *InfoResponse) String() string            { return proto.CompactTextString(m) }
func (*InfoResponse) ProtoMessage()               {}
func (*InfoResponse) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{1} }

func (m *InfoResponse) GetWants() EdgeType {
	if m != nil {
		return m.Wants
	}
	return EdgeType_STREAM
}

func (m *InfoResponse) GetProvides() EdgeType {
	if m != nil {
		return m.Provides
	}
	return EdgeType_STREAM
}

func (m *InfoResponse) GetOptions() map[string]*OptionInfo {
	if m != nil {
		return m.Options
	}
	return nil
}

type OptionInfo struct {
	ValueTypes []ValueType `protobuf:"varint,1,rep,packed,name=valueTypes,enum=agent.ValueType" json:"valueTypes,omitempty"`
}

func (m *OptionInfo) Reset()                    { *m = OptionInfo{} }
func (m *OptionInfo) String() string            { return proto.CompactTextString(m) }
func (*OptionInfo) ProtoMessage()               {}
func (*OptionInfo) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{2} }

func (m *OptionInfo) GetValueTypes() []ValueType {
	if m != nil {
		return m.ValueTypes
	}
	return nil
}

// Request that the process initialize itself with the provided options.
type InitRequest struct {
	Options []*Option `protobuf:"bytes,1,rep,name=options" json:"options,omitempty"`
	TaskID  string    `protobuf:"bytes,2,opt,name=taskID" json:"taskID,omitempty"`
	NodeID  string    `protobuf:"bytes,3,opt,name=nodeID" json:"nodeID,omitempty"`
}

func (m *InitRequest) Reset()                    { *m = InitRequest{} }
func (m *InitRequest) String() string            { return proto.CompactTextString(m) }
func (*InitRequest) ProtoMessage()               {}
func (*InitRequest) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{3} }

func (m *InitRequest) GetOptions() []*Option {
	if m != nil {
		return m.Options
	}
	return nil
}

func (m *InitRequest) GetTaskID() string {
	if m != nil {
		return m.TaskID
	}
	return ""
}

func (m *InitRequest) GetNodeID() string {
	if m != nil {
		return m.NodeID
	}
	return ""
}

type Option struct {
	Name   string         `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"`
	Values []*OptionValue `protobuf:"bytes,2,rep,name=values" json:"values,omitempty"`
}

func (m *Option) Reset()                    { *m = Option{} }
func (m *Option) String() string            { return proto.CompactTextString(m) }
func (*Option) ProtoMessage()               {}
func (*Option) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{4} }

func (m *Option) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Option) GetValues() []*OptionValue {
	if m != nil {
		return m.Values
	}
	return nil
}

type OptionValue struct {
	Type ValueType `protobuf:"varint,1,opt,name=type,enum=agent.ValueType" json:"type,omitempty"`
	// Types that are valid to be assigned to Value:
	//	*OptionValue_BoolValue
	//	*OptionValue_IntValue
	//	*OptionValue_DoubleValue
	//	*OptionValue_StringValue
	//	*OptionValue_DurationValue
	Value isOptionValue_Value `protobuf_oneof:"value"`
}

func (m *OptionValue) Reset()                    { *m = OptionValue{} }
func (m *OptionValue) String() string            { return proto.CompactTextString(m) }
func (*OptionValue) ProtoMessage()               {}
func (*OptionValue) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{5} }

type isOptionValue_Value interface {
	isOptionValue_Value()
}

type OptionValue_BoolValue struct {
	BoolValue bool `protobuf:"varint,2,opt,name=boolValue,oneof"`
}
type OptionValue_IntValue struct {
	IntValue int64 `protobuf:"varint,3,opt,name=intValue,oneof"`
}
type OptionValue_DoubleValue struct {
	DoubleValue float64 `protobuf:"fixed64,4,opt,name=doubleValue,oneof"`
}
type OptionValue_StringValue struct {
	StringValue string `protobuf:"bytes,5,opt,name=stringValue,oneof"`
}
type OptionValue_DurationValue struct {
	DurationValue int64 `protobuf:"varint,6,opt,name=durationValue,oneof"`
}

func (*OptionValue_BoolValue) isOptionValue_Value()     {}
func (*OptionValue_IntValue) isOptionValue_Value()      {}
func (*OptionValue_DoubleValue) isOptionValue_Value()   {}
func (*OptionValue_StringValue) isOptionValue_Value()   {}
func (*OptionValue_DurationValue) isOptionValue_Value() {}

func (m *OptionValue) GetValue() isOptionValue_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (m *OptionValue) GetType() ValueType {
	if m != nil {
		return m.Type
	}
	return ValueType_BOOL
}

func (m *OptionValue) GetBoolValue() bool {
	if x, ok := m.GetValue().(*OptionValue_BoolValue); ok {
		return x.BoolValue
	}
	return false
}

func (m *OptionValue) GetIntValue() int64 {
	if x, ok := m.GetValue().(*OptionValue_IntValue); ok {
		return x.IntValue
	}
	return 0
}

func (m *OptionValue) GetDoubleValue() float64 {
	if x, ok := m.GetValue().(*OptionValue_DoubleValue); ok {
		return x.DoubleValue
	}
	return 0
}

func (m *OptionValue) GetStringValue() string {
	if x, ok := m.GetValue().(*OptionValue_StringValue); ok {
		return x.StringValue
	}
	return ""
}

func (m *OptionValue) GetDurationValue() int64 {
	if x, ok := m.GetValue().(*OptionValue_DurationValue); ok {
		return x.DurationValue
	}
	return 0
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*OptionValue) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _OptionValue_OneofMarshaler, _OptionValue_OneofUnmarshaler, _OptionValue_OneofSizer, []interface{}{
		(*OptionValue_BoolValue)(nil),
		(*OptionValue_IntValue)(nil),
		(*OptionValue_DoubleValue)(nil),
		(*OptionValue_StringValue)(nil),
		(*OptionValue_DurationValue)(nil),
	}
}

func _OptionValue_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*OptionValue)
	// value
	switch x := m.Value.(type) {
	case *OptionValue_BoolValue:
		t := uint64(0)
		if x.BoolValue {
			t = 1
		}
		b.EncodeVarint(2<<3 | proto.WireVarint)
		b.EncodeVarint(t)
	case *OptionValue_IntValue:
		b.EncodeVarint(3<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.IntValue))
	case *OptionValue_DoubleValue:
		b.EncodeVarint(4<<3 | proto.WireFixed64)
		b.EncodeFixed64(math.Float64bits(x.DoubleValue))
	case *OptionValue_StringValue:
		b.EncodeVarint(5<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.StringValue)
	case *OptionValue_DurationValue:
		b.EncodeVarint(6<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.DurationValue))
	case nil:
	default:
		return fmt.Errorf("OptionValue.Value has unexpected type %T", x)
	}
	return nil
}

func _OptionValue_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*OptionValue)
	switch tag {
	case 2: // value.boolValue
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.Value = &OptionValue_BoolValue{x != 0}
		return true, err
	case 3: // value.intValue
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.Value = &OptionValue_IntValue{int64(x)}
		return true, err
	case 4: // value.doubleValue
		if wire != proto.WireFixed64 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed64()
		m.Value = &OptionValue_DoubleValue{math.Float64frombits(x)}
		return true, err
	case 5: // value.stringValue
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.Value = &OptionValue_StringValue{x}
		return true, err
	case 6: // value.durationValue
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.Value = &OptionValue_DurationValue{int64(x)}
		return true, err
	default:
		return false, nil
	}
}

func _OptionValue_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*OptionValue)
	// value
	switch x := m.Value.(type) {
	case *OptionValue_BoolValue:
		n += proto.SizeVarint(2<<3 | proto.WireVarint)
		n += 1
	case *OptionValue_IntValue:
		n += proto.SizeVarint(3<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64(x.IntValue))
	case *OptionValue_DoubleValue:
		n += proto.SizeVarint(4<<3 | proto.WireFixed64)
		n += 8
	case *OptionValue_StringValue:
		n += proto.SizeVarint(5<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(len(x.StringValue)))
		n += len(x.StringValue)
	case *OptionValue_DurationValue:
		n += proto.SizeVarint(6<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64(x.DurationValue))
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

// Respond to Kapacitor whether initialization was successful.
type InitResponse struct {
	Success bool   `protobuf:"varint,1,opt,name=success" json:"success,omitempty"`
	Error   string `protobuf:"bytes,2,opt,name=error" json:"error,omitempty"`
}

func (m *InitResponse) Reset()                    { *m = InitResponse{} }
func (m *InitResponse) String() string            { return proto.CompactTextString(m) }
func (*InitResponse) ProtoMessage()               {}
func (*InitResponse) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{6} }

func (m *InitResponse) GetSuccess() bool {
	if m != nil {
		return m.Success
	}
	return false
}

func (m *InitResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

// Request that the process provide a snapshot of its state.
type SnapshotRequest struct {
}

func (m *SnapshotRequest) Reset()                    { *m = SnapshotRequest{} }
func (m *SnapshotRequest) String() string            { return proto.CompactTextString(m) }
func (*SnapshotRequest) ProtoMessage()               {}
func (*SnapshotRequest) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{7} }

// Respond to Kapacitor with a serialized snapshot of the running state.
type SnapshotResponse struct {
	Snapshot []byte `protobuf:"bytes,1,opt,name=snapshot,proto3" json:"snapshot,omitempty"`
}

func (m *SnapshotResponse) Reset()                    { *m = SnapshotResponse{} }
func (m *SnapshotResponse) String() string            { return proto.CompactTextString(m) }
func (*SnapshotResponse) ProtoMessage()               {}
func (*SnapshotResponse) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{8} }

func (m *SnapshotResponse) GetSnapshot() []byte {
	if m != nil {
		return m.Snapshot
	}
	return nil
}

// Request that the process restore its state from a snapshot.
type RestoreRequest struct {
	Snapshot []byte `protobuf:"bytes,1,opt,name=snapshot,proto3" json:"snapshot,omitempty"`
}

func (m *RestoreRequest) Reset()                    { *m = RestoreRequest{} }
func (m *RestoreRequest) String() string            { return proto.CompactTextString(m) }
func (*RestoreRequest) ProtoMessage()               {}
func (*RestoreRequest) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{9} }

func (m *RestoreRequest) GetSnapshot() []byte {
	if m != nil {
		return m.Snapshot
	}
	return nil
}

// Respond with success or failure to a RestoreRequest
type RestoreResponse struct {
	Success bool   `protobuf:"varint,1,opt,name=success" json:"success,omitempty"`
	Error   string `protobuf:"bytes,2,opt,name=error" json:"error,omitempty"`
}

func (m *RestoreResponse) Reset()                    { *m = RestoreResponse{} }
func (m *RestoreResponse) String() string            { return proto.CompactTextString(m) }
func (*RestoreResponse) ProtoMessage()               {}
func (*RestoreResponse) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{10} }

func (m *RestoreResponse) GetSuccess() bool {
	if m != nil {
		return m.Success
	}
	return false
}

func (m *RestoreResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

// Request that the process respond with a Keepalive to verify it is responding.
type KeepaliveRequest struct {
	// The number of nanoseconds since the epoch.
	// Used only for debugging keepalive requests.
	Time int64 `protobuf:"varint,1,opt,name=time" json:"time,omitempty"`
}

func (m *KeepaliveRequest) Reset()                    { *m = KeepaliveRequest{} }
func (m *KeepaliveRequest) String() string            { return proto.CompactTextString(m) }
func (*KeepaliveRequest) ProtoMessage()               {}
func (*KeepaliveRequest) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{11} }

func (m *KeepaliveRequest) GetTime() int64 {
	if m != nil {
		return m.Time
	}
	return 0
}

// Respond to KeepaliveRequest
type KeepaliveResponse struct {
	// The number of nanoseconds since the epoch.
	// Used only for debugging keepalive requests.
	Time int64 `protobuf:"varint,1,opt,name=time" json:"time,omitempty"`
}

func (m *KeepaliveResponse) Reset()                    { *m = KeepaliveResponse{} }
func (m *KeepaliveResponse) String() string            { return proto.CompactTextString(m) }
func (*KeepaliveResponse) ProtoMessage()               {}
func (*KeepaliveResponse) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{12} }

func (m *KeepaliveResponse) GetTime() int64 {
	if m != nil {
		return m.Time
	}
	return 0
}

// Sent from the process to Kapacitor indicating an error has occurred.
// If an ErrorResponse is received, Kapacitor will terminate the process.
type ErrorResponse struct {
	Error string `protobuf:"bytes,1,opt,name=error" json:"error,omitempty"`
}

func (m *ErrorResponse) Reset()                    { *m = ErrorResponse{} }
func (m *ErrorResponse) String() string            { return proto.CompactTextString(m) }
func (*ErrorResponse) ProtoMessage()               {}
func (*ErrorResponse) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{13} }

func (m *ErrorResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

// Indicates the beginning of a batch.
// All subsequent points should be considered
// part of the batch until EndBatch arrives.
// This includes grouping. Batches of
// differing groups may not be interleaved.
//
// All the meta data but tmax is provided,
// since tmax may not be known at
// the beginning of a batch.
//
// Size is the number of points in the batch.
// If size is 0 then the batch has an undetermined size.
type BeginBatch struct {
	Name   string            `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"`
	Group  string            `protobuf:"bytes,2,opt,name=group" json:"group,omitempty"`
	Tags   map[string]string `protobuf:"bytes,3,rep,name=tags" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Size   int64             `protobuf:"varint,4,opt,name=size" json:"size,omitempty"`
	ByName bool              `protobuf:"varint,5,opt,name=byName" json:"byName,omitempty"`
}

func (m *BeginBatch) Reset()                    { *m = BeginBatch{} }
func (m *BeginBatch) String() string            { return proto.CompactTextString(m) }
func (*BeginBatch) ProtoMessage()               {}
func (*BeginBatch) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{14} }

func (m *BeginBatch) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *BeginBatch) GetGroup() string {
	if m != nil {
		return m.Group
	}
	return ""
}

func (m *BeginBatch) GetTags() map[string]string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *BeginBatch) GetSize() int64 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *BeginBatch) GetByName() bool {
	if m != nil {
		return m.ByName
	}
	return false
}

// Message containing information about a single data point.
// Can be sent on it's own or bookended by BeginBatch and EndBatch messages.
type Point struct {
	Time            int64              `protobuf:"varint,1,opt,name=time" json:"time,omitempty"`
	Name            string             `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	Database        string             `protobuf:"bytes,3,opt,name=database" json:"database,omitempty"`
	RetentionPolicy string             `protobuf:"bytes,4,opt,name=retentionPolicy" json:"retentionPolicy,omitempty"`
	Group           string             `protobuf:"bytes,5,opt,name=group" json:"group,omitempty"`
	Dimensions      []string           `protobuf:"bytes,6,rep,name=dimensions" json:"dimensions,omitempty"`
	Tags            map[string]string  `protobuf:"bytes,7,rep,name=tags" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	FieldsDouble    map[string]float64 `protobuf:"bytes,8,rep,name=fieldsDouble" json:"fieldsDouble,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	FieldsInt       map[string]int64   `protobuf:"bytes,9,rep,name=fieldsInt" json:"fieldsInt,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	FieldsString    map[string]string  `protobuf:"bytes,10,rep,name=fieldsString" json:"fieldsString,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	FieldsBool      map[string]bool    `protobuf:"bytes,12,rep,name=fieldsBool" json:"fieldsBool,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	ByName          bool               `protobuf:"varint,11,opt,name=byName" json:"byName,omitempty"`
}

func (m *Point) Reset()                    { *m = Point{} }
func (m *Point) String() string            { return proto.CompactTextString(m) }
func (*Point) ProtoMessage()               {}
func (*Point) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{15} }

func (m *Point) GetTime() int64 {
	if m != nil {
		return m.Time
	}
	return 0
}

func (m *Point) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Point) GetDatabase() string {
	if m != nil {
		return m.Database
	}
	return ""
}

func (m *Point) GetRetentionPolicy() string {
	if m != nil {
		return m.RetentionPolicy
	}
	return ""
}

func (m *Point) GetGroup() string {
	if m != nil {
		return m.Group
	}
	return ""
}

func (m *Point) GetDimensions() []string {
	if m != nil {
		return m.Dimensions
	}
	return nil
}

func (m *Point) GetTags() map[string]string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *Point) GetFieldsDouble() map[string]float64 {
	if m != nil {
		return m.FieldsDouble
	}
	return nil
}

func (m *Point) GetFieldsInt() map[string]int64 {
	if m != nil {
		return m.FieldsInt
	}
	return nil
}

func (m *Point) GetFieldsString() map[string]string {
	if m != nil {
		return m.FieldsString
	}
	return nil
}

func (m *Point) GetFieldsBool() map[string]bool {
	if m != nil {
		return m.FieldsBool
	}
	return nil
}

func (m *Point) GetByName() bool {
	if m != nil {
		return m.ByName
	}
	return false
}

// Indicates the end of a batch and contains
// all meta data associated with the batch.
// The same meta information is provided for
// ease of use with the addition of tmax since it
// may not be know at BeginBatch.
type EndBatch struct {
	Name   string            `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"`
	Group  string            `protobuf:"bytes,2,opt,name=group" json:"group,omitempty"`
	Tmax   int64             `protobuf:"varint,3,opt,name=tmax" json:"tmax,omitempty"`
	Tags   map[string]string `protobuf:"bytes,4,rep,name=tags" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	ByName bool              `protobuf:"varint,5,opt,name=byName" json:"byName,omitempty"`
}

func (m *EndBatch) Reset()                    { *m = EndBatch{} }
func (m *EndBatch) String() string            { return proto.CompactTextString(m) }
func (*EndBatch) ProtoMessage()               {}
func (*EndBatch) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{16} }

func (m *EndBatch) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *EndBatch) GetGroup() string {
	if m != nil {
		return m.Group
	}
	return ""
}

func (m *EndBatch) GetTmax() int64 {
	if m != nil {
		return m.Tmax
	}
	return 0
}

func (m *EndBatch) GetTags() map[string]string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *EndBatch) GetByName() bool {
	if m != nil {
		return m.ByName
	}
	return false
}

// Request message wrapper -- sent from Kapacitor to process
type Request struct {
	// Types that are valid to be assigned to Message:
	//	*Request_Info
	//	*Request_Init
	//	*Request_Keepalive
	//	*Request_Snapshot
	//	*Request_Restore
	//	*Request_Begin
	//	*Request_Point
	//	*Request_End
	Message isRequest_Message `protobuf_oneof:"message"`
}

func (m *Request) Reset()                    { *m = Request{} }
func (m *Request) String() string            { return proto.CompactTextString(m) }
func (*Request) ProtoMessage()               {}
func (*Request) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{17} }

type isRequest_Message interface {
	isRequest_Message()
}

type Request_Info struct {
	Info *InfoRequest `protobuf:"bytes,1,opt,name=info,oneof"`
}
type Request_Init struct {
	Init *InitRequest `protobuf:"bytes,2,opt,name=init,oneof"`
}
type Request_Keepalive struct {
	Keepalive *KeepaliveRequest `protobuf:"bytes,3,opt,name=keepalive,oneof"`
}
type Request_Snapshot struct {
	Snapshot *SnapshotRequest `protobuf:"bytes,4,opt,name=snapshot,oneof"`
}
type Request_Restore struct {
	Restore *RestoreRequest `protobuf:"bytes,5,opt,name=restore,oneof"`
}
type Request_Begin struct {
	Begin *BeginBatch `protobuf:"bytes,16,opt,name=begin,oneof"`
}
type Request_Point struct {
	Point *Point `protobuf:"bytes,17,opt,name=point,oneof"`
}
type Request_End struct {
	End *EndBatch `protobuf:"bytes,18,opt,name=end,oneof"`
}

func (*Request_Info) isRequest_Message()      {}
func (*Request_Init) isRequest_Message()      {}
func (*Request_Keepalive) isRequest_Message() {}
func (*Request_Snapshot) isRequest_Message()  {}
func (*Request_Restore) isRequest_Message()   {}
func (*Request_Begin) isRequest_Message()     {}
func (*Request_Point) isRequest_Message()     {}
func (*Request_End) isRequest_Message()       {}

func (m *Request) GetMessage() isRequest_Message {
	if m != nil {
		return m.Message
	}
	return nil
}

func (m *Request) GetInfo() *InfoRequest {
	if x, ok := m.GetMessage().(*Request_Info); ok {
		return x.Info
	}
	return nil
}

func (m *Request) GetInit() *InitRequest {
	if x, ok := m.GetMessage().(*Request_Init); ok {
		return x.Init
	}
	return nil
}

func (m *Request) GetKeepalive() *KeepaliveRequest {
	if x, ok := m.GetMessage().(*Request_Keepalive); ok {
		return x.Keepalive
	}
	return nil
}

func (m *Request) GetSnapshot() *SnapshotRequest {
	if x, ok := m.GetMessage().(*Request_Snapshot); ok {
		return x.Snapshot
	}
	return nil
}

func (m *Request) GetRestore() *RestoreRequest {
	if x, ok := m.GetMessage().(*Request_Restore); ok {
		return x.Restore
	}
	return nil
}

func (m *Request) GetBegin() *BeginBatch {
	if x, ok := m.GetMessage().(*Request_Begin); ok {
		return x.Begin
	}
	return nil
}

func (m *Request) GetPoint() *Point {
	if x, ok := m.GetMessage().(*Request_Point); ok {
		return x.Point
	}
	return nil
}

func (m *Request) GetEnd() *EndBatch {
	if x, ok := m.GetMessage().(*Request_End); ok {
		return x.End
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*Request) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _Request_OneofMarshaler, _Request_OneofUnmarshaler, _Request_OneofSizer, []interface{}{
		(*Request_Info)(nil),
		(*Request_Init)(nil),
		(*Request_Keepalive)(nil),
		(*Request_Snapshot)(nil),
		(*Request_Restore)(nil),
		(*Request_Begin)(nil),
		(*Request_Point)(nil),
		(*Request_End)(nil),
	}
}

func _Request_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*Request)
	// message
	switch x := m.Message.(type) {
	case *Request_Info:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Info); err != nil {
			return err
		}
	case *Request_Init:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Init); err != nil {
			return err
		}
	case *Request_Keepalive:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Keepalive); err != nil {
			return err
		}
	case *Request_Snapshot:
		b.EncodeVarint(4<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Snapshot); err != nil {
			return err
		}
	case *Request_Restore:
		b.EncodeVarint(5<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Restore); err != nil {
			return err
		}
	case *Request_Begin:
		b.EncodeVarint(16<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Begin); err != nil {
			return err
		}
	case *Request_Point:
		b.EncodeVarint(17<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Point); err != nil {
			return err
		}
	case *Request_End:
		b.EncodeVarint(18<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.End); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("Request.Message has unexpected type %T", x)
	}
	return nil
}

func _Request_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*Request)
	switch tag {
	case 1: // message.info
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(InfoRequest)
		err := b.DecodeMessage(msg)
		m.Message = &Request_Info{msg}
		return true, err
	case 2: // message.init
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(InitRequest)
		err := b.DecodeMessage(msg)
		m.Message = &Request_Init{msg}
		return true, err
	case 3: // message.keepalive
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(KeepaliveRequest)
		err := b.DecodeMessage(msg)
		m.Message = &Request_Keepalive{msg}
		return true, err
	case 4: // message.snapshot
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SnapshotRequest)
		err := b.DecodeMessage(msg)
		m.Message = &Request_Snapshot{msg}
		return true, err
	case 5: // message.restore
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(RestoreRequest)
		err := b.DecodeMessage(msg)
		m.Message = &Request_Restore{msg}
		return true, err
	case 16: // message.begin
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(BeginBatch)
		err := b.DecodeMessage(msg)
		m.Message = &Request_Begin{msg}
		return true, err
	case 17: // message.point
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(Point)
		err := b.DecodeMessage(msg)
		m.Message = &Request_Point{msg}
		return true, err
	case 18: // message.end
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(EndBatch)
		err := b.DecodeMessage(msg)
		m.Message = &Request_End{msg}
		return true, err
	default:
		return false, nil
	}
}

func _Request_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*Request)
	// message
	switch x := m.Message.(type) {
	case *Request_Info:
		s := proto.Size(x.Info)
		n += proto.SizeVarint(1<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case *Request_Init:
		s := proto.Size(x.Init)
		n += proto.SizeVarint(2<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case *Request_Keepalive:
		s := proto.Size(x.Keepalive)
		n += proto.SizeVarint(3<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case *Request_Snapshot:
		s := proto.Size(x.Snapshot)
		n += proto.SizeVarint(4<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case *Request_Restore:
		s := proto.Size(x.Restore)
		n += proto.SizeVarint(5<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case *Request_Begin:
		s := proto.Size(x.Begin)
		n += proto.SizeVarint(16<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case *Request_Point:
		s := proto.Size(x.Point)
		n += proto.SizeVarint(17<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case *Request_End:
		s := proto.Size(x.End)
		n += proto.SizeVarint(18<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

// Response message wrapper -- sent from process to Kapacitor
type Response struct {
	// Types that are valid to be assigned to Message:
	//	*Response_Info
	//	*Response_Init
	//	*Response_Keepalive
	//	*Response_Snapshot
	//	*Response_Restore
	//	*Response_Error
	//	*Response_Begin
	//	*Response_Point
	//	*Response_End
	Message isResponse_Message `protobuf_oneof:"message"`
}

func (m *Response) Reset()                    { *m = Response{} }
func (m *Response) String() string            { return proto.CompactTextString(m) }
func (*Response) ProtoMessage()               {}
func (*Response) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{18} }

type isResponse_Message interface {
	isResponse_Message()
}

type Response_Info struct {
	Info *InfoResponse `protobuf:"bytes,1,opt,name=info,oneof"`
}
type Response_Init struct {
	Init *InitResponse `protobuf:"bytes,2,opt,name=init,oneof"`
}
type Response_Keepalive struct {
	Keepalive *KeepaliveResponse `protobuf:"bytes,3,opt,name=keepalive,oneof"`
}
type Response_Snapshot struct {
	Snapshot *SnapshotResponse `protobuf:"bytes,4,opt,name=snapshot,oneof"`
}
type Response_Restore struct {
	Restore *RestoreResponse `protobuf:"bytes,5,opt,name=restore,oneof"`
}
type Response_Error struct {
	Error *ErrorResponse `protobuf:"bytes,6,opt,name=error,oneof"`
}
type Response_Begin struct {
	Begin *BeginBatch `protobuf:"bytes,16,opt,name=begin,oneof"`
}
type Response_Point struct {
	Point *Point `protobuf:"bytes,17,opt,name=point,oneof"`
}
type Response_End struct {
	End *EndBatch `protobuf:"bytes,18,opt,name=end,oneof"`
}

func (*Response_Info) isResponse_Message()      {}
func (*Response_Init) isResponse_Message()      {}
func (*Response_Keepalive) isResponse_Message() {}
func (*Response_Snapshot) isResponse_Message()  {}
func (*Response_Restore) isResponse_Message()   {}
func (*Response_Error) isResponse_Message()     {}
func (*Response_Begin) isResponse_Message()     {}
func (*Response_Point) isResponse_Message()     {}
func (*Response_End) isResponse_Message()       {}

func (m *Response) GetMessage() isResponse_Message {
	if m != nil {
		return m.Message
	}
	return nil
}

func (m *Response) GetInfo() *InfoResponse {
	if x, ok := m.GetMessage().(*Response_Info); ok {
		return x.Info
	}
	return nil
}

func (m *Response) GetInit() *InitResponse {
	if x, ok := m.GetMessage().(*Response_Init); ok {
		return x.Init
	}
	return nil
}

func (m *Response) GetKeepalive() *KeepaliveResponse {
	if x, ok := m.GetMessage().(*Response_Keepalive); ok {
		return x.Keepalive
	}
	return nil
}

func (m *Response) GetSnapshot() *SnapshotResponse {
	if x, ok := m.GetMessage().(*Response_Snapshot); ok {
		return x.Snapshot
	}
	return nil
}

func (m *Response) GetRestore() *RestoreResponse {
	if x, ok := m.GetMessage().(*Response_Restore); ok {
		return x.Restore
	}
	return nil
}

func (m *Response) GetError() *ErrorResponse {
	if x, ok := m.GetMessage().(*Response_Error); ok {
		return x.Error
	}
	return nil
}

func (m *Response) GetBegin() *BeginBatch {
	if x, ok := m.GetMessage().(*Response_Begin); ok {
		return x.Begin
	}
	return nil
}

func (m *Response) GetPoint() *Point {
	if x, ok := m.GetMessage().(*Response_Point); ok {
		return x.Point
	}
	return nil
}

func (m *Response) GetEnd() *EndBatch {
	if x, ok := m.GetMessage().(*Response_End); ok {
		return x.End
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*Response) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _Response_OneofMarshaler, _Response_OneofUnmarshaler, _Response_OneofSizer, []interface{}{
		(*Response_Info)(nil),
		(*Response_Init)(nil),
		(*Response_Keepalive)(nil),
		(*Response_Snapshot)(nil),
		(*Response_Restore)(nil),
		(*Response_Error)(nil),
		(*Response_Begin)(nil),
		(*Response_Point)(nil),
		(*Response_End)(nil),
	}
}

func _Response_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*Response)
	// message
	switch x := m.Message.(type) {
	case *Response_Info:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Info); err != nil {
			return err
		}
	case *Response_Init:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Init); err != nil {
			return err
		}
	case *Response_Keepalive:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Keepalive); err != nil {
			return err
		}
	case *Response_Snapshot:
		b.EncodeVarint(4<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Snapshot); err != nil {
			return err
		}
	case *Response_Restore:
		b.EncodeVarint(5<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Restore); err != nil {
			return err
		}
	case *Response_Error:
		b.EncodeVarint(6<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Error); err != nil {
			return err
		}
	case *Response_Begin:
		b.EncodeVarint(16<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Begin); err != nil {
			return err
		}
	case *Response_Point:
		b.EncodeVarint(17<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Point); err != nil {
			return err
		}
	case *Response_End:
		b.EncodeVarint(18<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.End); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("Response.Message has unexpected type %T", x)
	}
	return nil
}

func _Response_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*Response)
	switch tag {
	case 1: // message.info
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(InfoResponse)
		err := b.DecodeMessage(msg)
		m.Message = &Response_Info{msg}
		return true, err
	case 2: // message.init
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(InitResponse)
		err := b.DecodeMessage(msg)
		m.Message = &Response_Init{msg}
		return true, err
	case 3: // message.keepalive
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(KeepaliveResponse)
		err := b.DecodeMessage(msg)
		m.Message = &Response_Keepalive{msg}
		return true, err
	case 4: // message.snapshot
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SnapshotResponse)
		err := b.DecodeMessage(msg)
		m.Message = &Response_Snapshot{msg}
		return true, err
	case 5: // message.restore
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(RestoreResponse)
		err := b.DecodeMessage(msg)
		m.Message = &Response_Restore{msg}
		return true, err
	case 6: // message.error
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ErrorResponse)
		err := b.DecodeMessage(msg)
		m.Message = &Response_Error{msg}
		return true, err
	case 16: // message.begin
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(BeginBatch)
		err := b.DecodeMessage(msg)
		m.Message = &Response_Begin{msg}
		return true, err
	case 17: // message.point
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(Point)
		err := b.DecodeMessage(msg)
		m.Message = &Response_Point{msg}
		return true, err
	case 18: // message.end
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(EndBatch)
		err := b.DecodeMessage(msg)
		m.Message = &Response_End{msg}
		return true, err
	default:
		return false, nil
	}
}

func _Response_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*Response)
	// message
	switch x := m.Message.(type) {
	case *Response_Info:
		s := proto.Size(x.Info)
		n += proto.SizeVarint(1<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case *Response_Init:
		s := proto.Size(x.Init)
		n += proto.SizeVarint(2<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case *Response_Keepalive:
		s := proto.Size(x.Keepalive)
		n += proto.SizeVarint(3<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case *Response_Snapshot:
		s := proto.Size(x.Snapshot)
		n += proto.SizeVarint(4<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case *Response_Restore:
		s := proto.Size(x.Restore)
		n += proto.SizeVarint(5<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case *Response_Error:
		s := proto.Size(x.Error)
		n += proto.SizeVarint(6<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case *Response_Begin:
		s := proto.Size(x.Begin)
		n += proto.SizeVarint(16<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case *Response_Point:
		s := proto.Size(x.Point)
		n += proto.SizeVarint(17<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case *Response_End:
		s := proto.Size(x.End)
		n += proto.SizeVarint(18<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

func init() {
	proto.RegisterType((*InfoRequest)(nil), "agent.InfoRequest")
	proto.RegisterType((*InfoResponse)(nil), "agent.InfoResponse")
	proto.RegisterType((*OptionInfo)(nil), "agent.OptionInfo")
	proto.RegisterType((*InitRequest)(nil), "agent.InitRequest")
	proto.RegisterType((*Option)(nil), "agent.Option")
	proto.RegisterType((*OptionValue)(nil), "agent.OptionValue")
	proto.RegisterType((*InitResponse)(nil), "agent.InitResponse")
	proto.RegisterType((*SnapshotRequest)(nil), "agent.SnapshotRequest")
	proto.RegisterType((*SnapshotResponse)(nil), "agent.SnapshotResponse")
	proto.RegisterType((*RestoreRequest)(nil), "agent.RestoreRequest")
	proto.RegisterType((*RestoreResponse)(nil), "agent.RestoreResponse")
	proto.RegisterType((*KeepaliveRequest)(nil), "agent.KeepaliveRequest")
	proto.RegisterType((*KeepaliveResponse)(nil), "agent.KeepaliveResponse")
	proto.RegisterType((*ErrorResponse)(nil), "agent.ErrorResponse")
	proto.RegisterType((*BeginBatch)(nil), "agent.BeginBatch")
	proto.RegisterType((*Point)(nil), "agent.Point")
	proto.RegisterType((*EndBatch)(nil), "agent.EndBatch")
	proto.RegisterType((*Request)(nil), "agent.Request")
	proto.RegisterType((*Response)(nil), "agent.Response")
	proto.RegisterEnum("agent.EdgeType", EdgeType_name, EdgeType_value)
	proto.RegisterEnum("agent.ValueType", ValueType_name, ValueType_value)
}

func init() { proto.RegisterFile("udf.proto", fileDescriptor0) }

var fileDescriptor0 = []byte{
	// 1159 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x57, 0xdd, 0x72, 0xdb, 0x44,
	0x14, 0xb6, 0x22, 0xcb, 0x96, 0x8e, 0x9d, 0x58, 0xd9, 0x86, 0x56, 0x84, 0x4e, 0x27, 0x88, 0xb6,
	0x71, 0x42, 0x31, 0x60, 0x60, 0x5a, 0x3a, 0xa5, 0x4c, 0x8c, 0x0d, 0xf6, 0xd0, 0x26, 0x9d, 0x8d,
	0xdb, 0x7b, 0x39, 0xde, 0xb8, 0x9a, 0x38, 0x92, 0x91, 0xd6, 0x01, 0x73, 0xc5, 0xe3, 0xf0, 0x00,
	0x3c, 0x04, 0x17, 0x3c, 0x09, 0x33, 0xbc, 0x03, 0xb3, 0x3f, 0x5a, 0xad, 0x6c, 0x43, 0xa6, 0x4c,
	0x67, 0xb8, 0xd3, 0x9e, 0xf3, 0x9d, 0x9f, 0x3d, 0xbf, 0x2b, 0x70, 0xe6, 0xe3, 0xf3, 0xd6, 0x2c,
	0x89, 0x69, 0x8c, 0xac, 0x60, 0x42, 0x22, 0xea, 0x6f, 0x42, 0x6d, 0x10, 0x9d, 0xc7, 0x98, 0xfc,
	0x30, 0x27, 0x29, 0xf5, 0xff, 0x32, 0xa0, 0x2e, 0xce, 0xe9, 0x2c, 0x8e, 0x52, 0x82, 0xee, 0x81,
	0xf5, 0x63, 0x10, 0xd1, 0xd4, 0x33, 0xf6, 0x8c, 0xe6, 0x56, 0xbb, 0xd1, 0xe2, 0x62, 0xad, 0xde,
	0x78, 0x42, 0x86, 0x8b, 0x19, 0xc1, 0x82, 0x8b, 0x3e, 0x04, 0x7b, 0x96, 0xc4, 0x57, 0xe1, 0x98,
	0xa4, 0xde, 0xc6, 0x7a, 0xa4, 0x02, 0xa0, 0xc7, 0x50, 0x8d, 0x67, 0x34, 0x8c, 0xa3, 0xd4, 0x33,
	0xf7, 0xcc, 0x66, 0xad, 0xbd, 0x27, 0xb1, 0xba, 0xe5, 0xd6, 0x89, 0x80, 0xf4, 0x22, 0x9a, 0x2c,
	0x70, 0x26, 0xb0, 0xfb, 0x1c, 0xea, 0x3a, 0x03, 0xb9, 0x60, 0x5e, 0x90, 0x05, 0xf7, 0xce, 0xc1,
	0xec, 0x13, 0xed, 0x83, 0x75, 0x15, 0x4c, 0xe7, 0x84, 0xfb, 0x51, 0x6b, 0x6f, 0x4b, 0xdd, 0x42,
	0x8a, 0x5b, 0x10, 0xfc, 0xc7, 0x1b, 0x8f, 0x0c, 0xff, 0x29, 0x40, 0xce, 0x40, 0x9f, 0x00, 0x70,
	0x16, 0xf3, 0x97, 0xdd, 0xd8, 0x6c, 0x6e, 0xb5, 0x5d, 0x29, 0xff, 0x2a, 0x63, 0x60, 0x0d, 0xe3,
	0x9f, 0xb3, 0xf0, 0x85, 0x54, 0x86, 0x0f, 0xed, 0xe7, 0x37, 0x33, 0xf8, 0xcd, 0x36, 0x0b, 0xd6,
	0xd5, 0x35, 0xd0, 0x4d, 0xa8, 0xd0, 0x20, 0xbd, 0x18, 0x74, 0xb9, 0x97, 0x0e, 0x96, 0x27, 0x46,
	0x8f, 0xe2, 0x31, 0x19, 0x74, 0x3d, 0x53, 0xd0, 0xc5, 0xc9, 0xef, 0x43, 0x45, 0xa8, 0x40, 0x08,
	0xca, 0x51, 0x70, 0x49, 0xe4, 0x8d, 0xf9, 0x37, 0x3a, 0x84, 0x0a, 0xf7, 0x89, 0xc5, 0x9e, 0x59,
	0x45, 0x05, 0xab, 0xdc, 0x73, 0x2c, 0x11, 0xfe, 0x9f, 0x06, 0xd4, 0x34, 0x3a, 0xba, 0x0b, 0x65,
	0xba, 0x98, 0x11, 0x99, 0xdf, 0xd5, 0xdb, 0x72, 0x2e, 0xba, 0x03, 0xce, 0x28, 0x8e, 0xa7, 0xaf,
	0x54, 0x60, 0xed, 0x7e, 0x09, 0xe7, 0x24, 0x74, 0x1b, 0xec, 0x30, 0xa2, 0x82, 0xcd, 0x3c, 0x37,
	0xfb, 0x25, 0xac, 0x28, 0xc8, 0x87, 0xda, 0x38, 0x9e, 0x8f, 0xa6, 0x44, 0x00, 0xca, 0x7b, 0x46,
	0xd3, 0xe8, 0x97, 0xb0, 0x4e, 0x64, 0x98, 0x94, 0x26, 0x61, 0x34, 0x11, 0x18, 0x8b, 0x5d, 0x8f,
	0x61, 0x34, 0x22, 0xba, 0x0f, 0x9b, 0xe3, 0x79, 0x12, 0x28, 0xe7, 0xbd, 0x8a, 0x34, 0x55, 0x24,
	0x77, 0xaa, 0xb2, 0x04, 0xfc, 0xa7, 0xac, 0x9a, 0x59, 0x7a, 0x64, 0x35, 0x7b, 0x50, 0x4d, 0xe7,
	0x67, 0x67, 0x24, 0x15, 0xf5, 0x6c, 0xe3, 0xec, 0x88, 0x76, 0xc0, 0x22, 0x49, 0x12, 0x27, 0x32,
	0x1f, 0xe2, 0xe0, 0x6f, 0x43, 0xe3, 0x34, 0x0a, 0x66, 0xe9, 0xeb, 0x38, 0x4b, 0xb1, 0xdf, 0x02,
	0x37, 0x27, 0x49, 0xb5, 0xbb, 0x60, 0xa7, 0x92, 0xc6, 0xf5, 0xd6, 0xb1, 0x3a, 0xfb, 0x0f, 0x60,
	0x0b, 0x93, 0x94, 0xc6, 0x09, 0xc9, 0x8a, 0xe4, 0xdf, 0xd0, 0x47, 0xd0, 0x50, 0xe8, 0xff, 0xe8,
	0xf3, 0x7d, 0x70, 0xbf, 0x27, 0x64, 0x16, 0x4c, 0xc3, 0x2b, 0x65, 0x12, 0x41, 0x99, 0x86, 0xb2,
	0x68, 0x4c, 0xcc, 0xbf, 0xfd, 0x7d, 0xd8, 0xd6, 0x70, 0xd2, 0xd8, 0x3a, 0xe0, 0x3d, 0xd8, 0xec,
	0x31, 0xcd, 0x0a, 0xa4, 0xec, 0x1a, 0xba, 0xdd, 0x3f, 0x0c, 0x80, 0x0e, 0x99, 0x84, 0x51, 0x27,
	0xa0, 0x67, 0xaf, 0xd7, 0xd6, 0xe9, 0x0e, 0x58, 0x93, 0x24, 0x9e, 0xcf, 0x32, 0x87, 0xf9, 0x01,
	0x7d, 0x0c, 0x65, 0x1a, 0x4c, 0xb2, 0x59, 0xf0, 0x9e, 0xac, 0xc0, 0x5c, 0x55, 0x6b, 0x18, 0x4c,
	0xe4, 0x18, 0xe0, 0x40, 0xa6, 0x3a, 0x0d, 0x7f, 0x16, 0x75, 0x64, 0x62, 0xfe, 0xcd, 0x1a, 0x67,
	0xb4, 0x38, 0x66, 0x06, 0x2d, 0x1e, 0x24, 0x79, 0xda, 0x7d, 0x08, 0x8e, 0x12, 0x5f, 0x33, 0x2c,
	0x76, 0xf4, 0x61, 0xe1, 0xe8, 0x93, 0xe1, 0xd7, 0x0a, 0x58, 0x2f, 0xe2, 0x30, 0x5a, 0x1b, 0x3c,
	0x75, 0xbb, 0x0d, 0xed, 0x76, 0xbb, 0x60, 0x8f, 0x03, 0x1a, 0x8c, 0x82, 0x94, 0xc8, 0xee, 0x55,
	0x67, 0xd4, 0x84, 0x46, 0x42, 0x28, 0x89, 0x58, 0x8d, 0xbe, 0x88, 0xa7, 0xe1, 0xd9, 0x82, 0x7b,
	0xef, 0xe0, 0x65, 0x72, 0x1e, 0x23, 0x4b, 0x8f, 0xd1, 0x1d, 0x80, 0x71, 0x78, 0x49, 0xa2, 0x94,
	0xcf, 0x96, 0xca, 0x9e, 0xd9, 0x74, 0xb0, 0x46, 0x41, 0x87, 0x32, 0x86, 0x55, 0x1e, 0xc3, 0x9b,
	0x32, 0x86, 0xdc, 0xff, 0x95, 0xf0, 0x75, 0xa0, 0x7e, 0x1e, 0x92, 0xe9, 0x38, 0xed, 0xf2, 0xf6,
	0xf3, 0x6c, 0x2e, 0x73, 0xa7, 0x20, 0xf3, 0xad, 0x06, 0x10, 0xb2, 0x05, 0x19, 0xf4, 0x25, 0x38,
	0xe2, 0x3c, 0x88, 0xa8, 0xe7, 0x14, 0x12, 0xa7, 0x2b, 0x18, 0x44, 0x54, 0x48, 0xe7, 0xe8, 0xdc,
	0xfc, 0x29, 0xef, 0x6c, 0x0f, 0xfe, 0xd1, 0xbc, 0x00, 0x14, 0xcc, 0x0b, 0x12, 0x7a, 0x02, 0x20,
	0xce, 0x9d, 0x38, 0x9e, 0x7a, 0x75, 0xae, 0xe1, 0xf6, 0x1a, 0x0d, 0x8c, 0x2d, 0xe4, 0x35, 0xbc,
	0x56, 0x2b, 0xb5, 0xb7, 0x52, 0x2b, 0xbb, 0x5f, 0xc3, 0xf6, 0x4a, 0xc0, 0xae, 0x53, 0x60, 0xe8,
	0x0a, 0x9e, 0xc0, 0x56, 0x31, 0x60, 0xd7, 0x49, 0x9b, 0x6b, 0xcd, 0x6b, 0x01, 0x7b, 0x23, 0xff,
	0xbf, 0x82, 0xc6, 0x52, 0xbc, 0xae, 0x13, 0xb7, 0xf5, 0x56, 0xf9, 0xdd, 0x00, 0xbb, 0x17, 0x8d,
	0xdf, 0xb4, 0xef, 0x59, 0x5f, 0x5d, 0x06, 0x3f, 0x89, 0x7d, 0x81, 0xf9, 0x37, 0xfa, 0x48, 0xd6,
	0x71, 0x99, 0xa7, 0xf4, 0xdd, 0xec, 0x0d, 0x21, 0x95, 0xaf, 0x94, 0xf2, 0x5b, 0xef, 0xfa, 0x5f,
	0x4c, 0xa8, 0x66, 0x43, 0xb3, 0x09, 0xe5, 0x30, 0x3a, 0x8f, 0xb9, 0x60, 0xbe, 0x53, 0xb5, 0xd7,
	0x52, 0xbf, 0x84, 0x39, 0x42, 0x20, 0x43, 0x2a, 0x5f, 0x1c, 0x39, 0x52, 0x3d, 0x0c, 0x04, 0x32,
	0xa4, 0xe8, 0x21, 0x38, 0x17, 0xd9, 0xd0, 0xe5, 0x17, 0xaf, 0xb5, 0x6f, 0x49, 0xf8, 0xf2, 0xd0,
	0x66, 0x0b, 0x56, 0x61, 0xd1, 0xe7, 0xda, 0xd2, 0x28, 0x73, 0xb9, 0xac, 0xc9, 0x97, 0x16, 0x14,
	0x5b, 0xbc, 0x19, 0x12, 0x7d, 0x0a, 0xd5, 0x44, 0xac, 0x13, 0x1e, 0xa0, 0x5a, 0xfb, 0x1d, 0x29,
	0x54, 0x5c, 0x49, 0xfd, 0x12, 0xce, 0x70, 0xe8, 0x00, 0xac, 0x11, 0x1b, 0xbd, 0x9e, 0x5b, 0x78,
	0x3e, 0xe5, 0xe3, 0xb8, 0x5f, 0xc2, 0x02, 0x81, 0xee, 0x82, 0x35, 0x63, 0xcd, 0xe6, 0x6d, 0x73,
	0x68, 0x5d, 0x6f, 0x40, 0x86, 0xe2, 0x4c, 0xf4, 0x01, 0x98, 0x24, 0x1a, 0x7b, 0x88, 0x63, 0x1a,
	0x4b, 0x19, 0xed, 0x97, 0x30, 0xe3, 0x76, 0x1c, 0xa8, 0x5e, 0x92, 0x34, 0x0d, 0x26, 0xc4, 0xff,
	0xcd, 0x04, 0x5b, 0xad, 0x9a, 0x83, 0x42, 0x0e, 0x6e, 0xac, 0x79, 0x27, 0xaa, 0x24, 0x1c, 0x14,
	0x92, 0x70, 0xa3, 0x90, 0x04, 0x1d, 0x1a, 0x52, 0xf4, 0x68, 0x35, 0x0b, 0xde, 0x6a, 0x16, 0x94,
	0x90, 0x96, 0x86, 0x2f, 0x56, 0xd2, 0x70, 0x6b, 0x25, 0x0d, 0x4a, 0x2e, 0xcf, 0x43, 0x7b, 0x39,
	0x0f, 0x37, 0x97, 0xf3, 0xa0, 0x84, 0x54, 0x22, 0x1e, 0x64, 0x5b, 0xb6, 0xc2, 0x25, 0x76, 0xb2,
	0xc8, 0xe9, 0xab, 0x98, 0x45, 0x99, 0x83, 0xfe, 0xf7, 0xb4, 0x1d, 0xbe, 0x0f, 0x76, 0xf6, 0xd4,
	0x47, 0x00, 0x95, 0xd3, 0x21, 0xee, 0x1d, 0x3d, 0x77, 0x4b, 0xc8, 0x01, 0xab, 0x73, 0x34, 0xfc,
	0xa6, 0xef, 0x1a, 0x87, 0x5d, 0x70, 0xd4, 0xbb, 0x12, 0xd9, 0x50, 0xee, 0x9c, 0x9c, 0x3c, 0x73,
	0x4b, 0xa8, 0x0a, 0xe6, 0xe0, 0x78, 0xe8, 0x1a, 0x4c, 0xac, 0x7b, 0xf2, 0xb2, 0xf3, 0xac, 0xe7,
	0x6e, 0x48, 0x15, 0x83, 0xe3, 0xef, 0x5c, 0x13, 0xd5, 0xc1, 0xee, 0xbe, 0xc4, 0x47, 0xc3, 0xc1,
	0xc9, 0xb1, 0x5b, 0x1e, 0x55, 0xf8, 0xff, 0xcb, 0x67, 0x7f, 0x07, 0x00, 0x00, 0xff, 0xff, 0x71,
	0xb7, 0xac, 0x48, 0xcc, 0x0c, 0x00, 0x00,
}
