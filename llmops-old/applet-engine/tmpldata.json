[{"Name": "Float", "name": "float", "Type": "float64", "Kind": "reflect.Float64", "Nil": "0", "Zero": "float64(0)"}, {"Name": "Integer", "name": "integer", "Type": "int64", "Kind": "reflect.Int64", "Nil": "0", "Zero": "int64(0)"}, {"Name": "String", "name": "string", "Type": "string", "Kind": "reflect.String", "Nil": "\"\"", "Zero": "\"\""}, {"Name": "Boolean", "name": "boolean", "Type": "bool", "Kind": "<PERSON>.<PERSON>", "Nil": "false", "Zero": "false"}]