package edge

import (
	"context"
	"fmt"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
	"transwarp.io/applied-ai/applet-engine/pkg/debug"
)

type MsgErrHandler func(p Message, meta models.NodeMeta, err error) error

type cdnConsumer struct {
	edge Edge
	r    Receiver
	eh   MsgErrHandler
}

// NewCdnConsumerWithReceiverAndErrHandler 在 Consumer 的基础上实现了
// 自定义请求产生错误后，将错误返回的逻辑
func NewCdnConsumerWithReceiverAndErrHandler(e Edge, r Receiver, eh MsgErrHandler) Consumer {
	return &cdnConsumer{
		edge: e,
		r:    r,
		eh:   eh,
	}
}

func (ec *cdnConsumer) Consume() error {
	defer ec.r.Done()
	for msg, ok := ec.edge.Emit(); ok; msg, ok = ec.edge.Emit() {
		m := msg // 循环变量转为普通变量，在闭包函数中正确引用
		call, err := getCall(m)
		if err != nil {
			stdlog.Errorf("failed to get call from message: %v", err)
			continue
		}
		node, err := getCurNode(ec.r)
		if err != nil {
			stdlog.Errorf("failed to get current node from receiver: %v", err)
			continue
		}
		name := node.Name()
		if err := call.Lock(name); err != nil {
			stdlog.Errorf("failed to lock node %s of call %+v", name, call)
			continue
		}
		go func() {
			var err error
			// FIXME: 未记录BufferedBatchMessage与BarrierMessage的Trace
			switch m := m.(type) {
			case BeginBatchMessage:
				err = receiveBeginBatchMessage(NewSingleReceiver(ec.r), m)
			case BatchPointMessage:
				err = receiveBatchPointMessage(NewSingleReceiver(ec.r), m)
			case EndBatchMessage:
				err = receiveEndBatchMessage(NewSingleReceiver(ec.r), m)
			case BufferedBatchMessage:
				err = receiveBufferedBatch(ec.r, m)
			case PointMessage:
				err = receivePointMessage(NewSingleReceiver(ec.r), m)
			case BarrierMessage:
				err = ec.r.Barrier(m)
			default:
				stdlog.Errorf("unexpected message of type %T", m)
			}
			if err != nil && ec.eh != nil {
				err = handleReceiverError(m, ec.r, ec.eh, err)
			}
			err = stderr.JoinErrors(err, call.Unlock(name))
			if err != nil {
				stdlog.Error(err)
			}
		}()
	}
	return nil
}

// TODO: setPointMessageTrace改为PointMessage内的方法，在consumer函数设置当前meta到trace，而不是设置上一个meta到trace
func setPointMessageTrace(msg PointMessage) error {
	trace, err := msg.Trace()
	if err != nil {
		return stderr.Wrap(err, "get trace of point message")
	}
	meta, err := msg.Meta()
	if err != nil {
		return stderr.Wrap(err, "get meta of point message")
	}
	trace[meta.NodeID] = &models.TraceItem{
		Index: len(trace),
		Meta:  meta,
	}
	return nil
}

func setBeginBatchMessageTrace(msg BeginBatchMessage) error {
	meta, _ := msg.Meta()
	if meta.NodeID == "" {
		return stderr.Error("node id of meta is empty")
	}
	trace := msg.Trace()
	if trace == nil {
		trace = make(models.Trace)
		msg.SetTrace(trace)
	}
	trace[meta.NodeID] = &models.TraceItem{
		Index: len(trace),
		Meta:  meta,
	}
	return nil
}

func checkFields(msg FieldGetter) error {
	if msg == nil || msg.Fields() == nil {
		return stderr.Internal.Errorf("received message is nil or has no fields")
	}
	return nil
}

func logError(msg ContextGetter, errMsg error) {
	ctx, err := msg.Context()
	if err != nil {
		stdlog.Errorf("failed to get context for log error")
		return
	}
	if errMsg != nil {
		ctx.Errorf(errMsg.Error())
	}
}

func receivePointMessage(cr *commonReceiver, msg PointMessage) (err error) {
	cpMsg, err := msg.DeepCopy()
	if err != nil {
		return stderr.Wrap(err, "deep copy point message")
	}
	if err := setPointMessageTrace(cpMsg); err != nil {
		return stderr.Wrap(err, "set point message trace")
	}
	// 获取发送调试信息需要的参数
	taskID, call, preNodeMeta, err := getContextProperty(cpMsg)
	if err != nil {
		return stderr.Wrap(err, "get context property from point message")
	}
	preOutput, err := getPreOutput(cpMsg)
	if err != nil {
		return stderr.Wrap(err, "get pre output from point message")
	}

	// 设置context中的meta为当前算子meta
	curNodeMeta, err := getCurNodeMetaFromCommonReceiver(cr)
	if err != nil {
		return stderr.Wrap(err, "get cur node meta from receiver")
	}
	// 重新设置pm上下文context
	if err := cpMsg.SetMeta(curNodeMeta); err != nil {
		return stderr.Wrap(err, "set meta of point message")
	}
	reqID := call.ReqID()

	// 发送上一个算子的输出数据
	if err := PubOutput(reqID, call, preNodeMeta, preOutput, debug.DEBUG_SCOPE_NODE, false); err != nil {
		return stderr.Wrap(err, "publish output of pre node %s", preNodeMeta.NodeName)
	}
	// 处理子链，发送上一个子链的输出数据以及运行成功状态
	preSubChainID := preNodeMeta.SubChainID
	curSubChainID := curNodeMeta.SubChainID

	// 刚出子链
	if preSubChainID != curSubChainID && preSubChainID != "" {
		if err := PubOutput(reqID, call, preNodeMeta, preOutput, debug.DEBUG_SCOPE_SUB_CHAIN, false); err != nil {
			return stderr.Wrap(err, "publish running status of pre sub chain %s", preNodeMeta.SubChainName)
		}
		if err := PubSuccessStatus(reqID, call, preNodeMeta, debug.DEBUG_SCOPE_SUB_CHAIN, false); err != nil {
			return stderr.Wrap(err, "publish success status of pre sub chain %s", preNodeMeta.SubChainName)
		}
	}
	callCtx := call.Ctx()
	// 判断用户是否主动停止了请求
	defer func() {
		if err != nil && callCtx.Err() == context.Canceled {
			err = context.Canceled
		}
	}()

	// 判断ctx是否取消，上层函数会直接处理此错误，并把当前节点设置为failed或canceled状态
	if call.Done() {
		errMsg := fmt.Sprintf("request ctx is done before %s node starts", curNodeMeta.NodeName)
		stdlog.Warnf(errMsg)
		return stderr.Error(errMsg)
	}

	curInput := preOutput
	// 处理子链，发送当前子链的输出数据以及正在运行状态
	// 刚进子链
	if preSubChainID != curSubChainID && curSubChainID != "" {
		if err := PubRunningStatus(taskID, call, curNodeMeta, debug.DEBUG_SCOPE_SUB_CHAIN); err != nil {
			return stderr.Wrap(err, "publish running status of cur sub chain %s", curNodeMeta.SubChainName)
		}

		if err := PubInput(reqID, call, curNodeMeta, curInput, debug.DEBUG_SCOPE_SUB_CHAIN, false); err != nil {
			return stderr.Wrap(err, "publish input of cur sub chain %s", curNodeMeta.SubChainName)
		}
	}
	// 发送当前算子的正在运行状态以及输入数据
	if err := PubRunningStatus(taskID, call, curNodeMeta, debug.DEBUG_SCOPE_NODE); err != nil {
		return stderr.Wrap(err, "publish running status of cur node %s", curNodeMeta.NodeName)
	}

	if err := PubInput(reqID, call, curNodeMeta, curInput, debug.DEBUG_SCOPE_NODE, false); err != nil {
		return stderr.Wrap(err, "publish input of cur node %s", curNodeMeta.NodeName)
	}

	if err := cr.Point(cpMsg); err != nil {
		return stderr.Wrap(err, "receive Point")
	}

	// 发送当前算子的运行成功状态
	if err := PubSuccessStatus(reqID, call, curNodeMeta, debug.DEBUG_SCOPE_NODE, false); err != nil {
		return stderr.Wrap(err, "publish success status of cur node %s", curNodeMeta.NodeName)
	}
	return nil
}

func receiveBeginBatchMessage(cr *commonReceiver, msg BeginBatchMessage) (err error) {
	cpMsg := msg.DeepCopy()
	if err := setBeginBatchMessageTrace(cpMsg); err != nil {
		return stderr.Wrap(err, "set beginbatchmessage trace")
	}
	// 获取发送调试信息需要的参数
	taskID, call, _, err := getContextProperty(cpMsg)
	if err != nil {
		return stderr.Wrap(err, "get task id and call from begin batch message")
	}
	// 设置context中的meta为当前算子meta
	curNodeMeta, err := getCurNodeMetaFromCommonReceiver(cr)
	if err != nil {
		return stderr.Wrap(err, "get cur node meta from receiver")
	}
	if err := cpMsg.SetMeta(curNodeMeta); err != nil {
		return stderr.Wrap(err, "set meta of point message")
	}
	callCtx := call.Ctx()
	// 判断用户是否主动停止了请求
	defer func() {
		if err != nil && callCtx.Err() == context.Canceled {
			err = context.Canceled
		}
	}()
	// 判断ctx是否取消，上层函数会直接处理此错误，并把当前节点设置为failed或canceled状态
	if call.Done() {
		errMsg := fmt.Sprintf("request ctx is done before %s node starts begin batch", curNodeMeta.NodeName)
		stdlog.Warnf(errMsg)
		return stderr.Error(errMsg)
	}

	if err := PubRunningStatus(taskID, call, curNodeMeta, debug.DEBUG_SCOPE_NODE); err != nil {
		return stderr.Wrap(err, "publish running status of cur node %s", curNodeMeta.NodeName)
	}

	if err := cr.BeginBatch(cpMsg); err != nil {
		return stderr.Wrap(err, "use receiver BeginBatch function to receive beginbatchmessage")
	}
	return nil
}

func receiveBatchPointMessage(cr *commonReceiver, msg BatchPointMessage) (err error) {
	cpMsg, err := msg.DeepCopy()
	if err != nil {
		return stderr.Wrap(err, "deep copy batchpointmessage")
	}
	// 获取发送调试信息需要的参数
	_, call, preNodeMeta, err := getContextProperty(cpMsg)
	if err != nil {
		return stderr.Wrap(err, "get task id and call from batch message")
	}
	preOutput, err := getPreOutput(cpMsg)
	if err != nil {
		return stderr.Wrap(err, "get pre output from batch message")
	}
	// 设置context中的meta为当前算子meta
	curNodeMeta, err := getCurNodeMetaFromCommonReceiver(cr)
	if err != nil {
		return stderr.Wrap(err, "get cur node meta from receiver")
	}
	if err := cpMsg.SetMeta(curNodeMeta); err != nil {
		return stderr.Wrap(err, "set meta of point message")
	}
	reqID := call.ReqID()
	// 发送上一个算子的输出数据
	if err := PubOutput(reqID, call, preNodeMeta, preOutput, debug.DEBUG_SCOPE_NODE, true); err != nil {
		return stderr.Wrap(err, "publish output of pre node %s", preNodeMeta.NodeName)
	}
	// 处理子链，发送上一个子链的输出数据
	preSubChainID := preNodeMeta.SubChainID
	curSubChainID := curNodeMeta.SubChainID

	if preSubChainID != curSubChainID && preSubChainID != "" {
		if err := PubOutput(reqID, call, preNodeMeta, preOutput, debug.DEBUG_SCOPE_SUB_CHAIN, true); err != nil {
			return stderr.Wrap(err, "publish running status of pre sub chain %s", preNodeMeta.SubChainName)
		}
	}
	callCtx := call.Ctx()
	// 判断用户是否主动停止了请求
	defer func() {
		if err != nil && callCtx.Err() == context.Canceled {
			err = context.Canceled
		}
	}()
	// 判断ctx是否取消，上层函数会直接处理此错误，并把当前节点设置为failed或canceled状态
	if call.Done() {
		errMsg := fmt.Sprintf("request ctx is done before %s node starts batch point", curNodeMeta.NodeName)
		stdlog.Warnf(errMsg)
		return stderr.Error(errMsg)
	}
	curInput := preOutput
	// 支持流式的算子不存在作为子链开始算子的情况，无需判断当前子链开始的情况
	// if preSubChainID != curSubChainID && curSubChainID != "" { PubInput }

	// 发送当前算子的输入数据
	if err := PubInput(reqID, call, curNodeMeta, curInput, debug.DEBUG_SCOPE_NODE, true); err != nil {
		return stderr.Wrap(err, "publish input of cur node %s", curNodeMeta.NodeName)
	}
	if err := cr.BatchPoint(cpMsg); err != nil {
		return stderr.Wrap(err, "use receiver BatchPoint function to receive batchpointmessage")
	}
	return nil
}

func receiveEndBatchMessage(cr *commonReceiver, msg EndBatchMessage) (err error) {
	cpMsg := msg.DeepCopy()
	// 获取发送调试信息需要的参数
	_, call, preNodeMeta, err := getContextProperty(cpMsg)
	if err != nil {
		return stderr.Wrap(err, "get task id and call from batch message")
	}
	// 设置context中的meta为当前算子meta
	curNodeMeta, err := getCurNodeMetaFromCommonReceiver(cr)
	if err != nil {
		return stderr.Wrap(err, "get cur node meta from receiver")
	}
	if err := cpMsg.SetMeta(curNodeMeta); err != nil {
		return stderr.Wrap(err, "set meta of point message")
	}
	reqID := call.ReqID()
	// 处理子链，发送上一个子链的运行成功状态
	preSubChainID := preNodeMeta.SubChainID
	curSubChainID := curNodeMeta.SubChainID
	if preSubChainID != curSubChainID && preSubChainID != "" {
		if err := PubSuccessStatus(reqID, call, preNodeMeta, debug.DEBUG_SCOPE_SUB_CHAIN, true); err != nil {
			return stderr.Wrap(err, "publish success status of pre sub chain %s", preNodeMeta.SubChainName)
		}
	}
	callCtx := call.Ctx()
	// 判断用户是否主动停止了请求
	defer func() {
		if err != nil && callCtx.Err() == context.Canceled {
			err = context.Canceled
		}
	}()
	// 判断ctx是否取消，上层函数会直接处理此错误，并把当前节点设置为failed或canceled状态
	if call.Done() {
		errMsg := fmt.Sprintf("request ctx is done before %s node starts end batch", curNodeMeta.NodeName)
		stdlog.Warnf(errMsg)
		return stderr.Error(errMsg)
	}
	if err := cr.EndBatch(cpMsg); err != nil {
		return stderr.Wrap(err, "use receiver EndBatch function to receive endbatchmessage")
	}

	// 发送当前算子的运行成功状态
	if err := PubSuccessStatus(reqID, call, curNodeMeta, debug.DEBUG_SCOPE_NODE, true); err != nil {
		return stderr.Wrap(err, "publish success status of cur node %s", curNodeMeta.NodeName)
	}
	return nil
}

func handleReceiverError(m Message, r Receiver, eh MsgErrHandler, err error) error {
	meta, metaErr := getCurNodeMeta(r)
	if metaErr != nil {
		return stderr.Wrap(metaErr, "get receiver node meta")
	}
	return eh(m, meta, err)
}

func getContextProperty(msg Message) (string, *debug.Call, models.NodeMeta, error) {
	emptyMeta := models.NodeMeta{}
	contextGetter, ok := msg.(ContextGetter)
	if !ok {
		return "", nil, emptyMeta, stderr.Error("failed to convert %T to %T", msg, contextGetter)
	}
	ctx, err := contextGetter.Context()
	if err != nil {
		return "", nil, emptyMeta, stderr.Wrap(err, "get context of message")
	}
	if ctx.Call == nil {
		return "", nil, emptyMeta, stderr.Error("call of context is nil")
	}
	return ctx.TaskID, ctx.Call, ctx.Meta, nil
}

func getCurNodeMeta(r Receiver) (models.NodeMeta, error) {
	node, ok := r.(pipeline.Node)
	if !ok {
		return models.NodeMeta{}, stderr.Error("receiver is not pipeline node")
	}
	return node.Meta(), nil
}
func getCurNodeMetaFromCommonReceiver(cr *commonReceiver) (models.NodeMeta, error) {
	if cr.isSingleReceiver() {
		return getCurNodeMeta(cr.SingleReceiver)
	}
	return getCurNodeMetaByMultiReceiver(cr.MultiReceiver)
}

func getCurNode(r Receiver) (pipeline.Node, error) {
	node, ok := r.(pipeline.Node)
	if !ok {
		return nil, stderr.Error("receiver %+v is not pipeline node", r)
	}
	return node, nil
}
func getPreOutput(msg Message) (any, error) {
	var fields models.Fields
	switch m := msg.(type) {
	case PointMessage:
		fields = m.Fields()
	case BatchPointMessage:
		fields = m.Fields()
	default:
		return nil, stderr.Error("there is no fields in %T message type", m)
	}
	outputKey := models.PredefinedFieldOutput
	output, exist, err := models.GetValueOfFields[any](fields, outputKey)
	if err != nil {
		return nil, stderr.Wrap(err, "get output of pointmessage")
	}
	if !exist {
		return nil, stderr.Error("output of message is not exist")
	}
	return output, nil
}

func decideSecondaryID(nodeMeta models.NodeMeta, scope debug.DebugScope) (string, error) {
	switch scope {
	case debug.DEBUG_SCOPE_NODE:
		return nodeMeta.Id, nil
	case debug.DEBUG_SCOPE_SUB_CHAIN:
		return nodeMeta.SubChainID, nil
	case debug.DEBUG_SCOPE_CHAIN:
		return "", nil
	default:
		return "", stderr.Error("unknown debug scope: %s", scope)
	}
}

// PubRunningStatus
// 在各节点receivePointMessage[父、子一共2次]或者receiveBeginBatchMessage时会调用,以及ServeHTTP调用整个链路时
// 但是后续继续发送消息时,会复用debugMsg对象
// 只有在success、failed、cancel才会进行更改

func PubRunningStatus(taskID string, call *debug.Call, nodeMeta models.NodeMeta, scope debug.DebugScope) error {
	if call == nil {
		return stderr.Error("call is nil")
	}
	isDebug := call.IsDebug()
	// 内部算子不发送debug消息
	if scope == debug.DEBUG_SCOPE_NODE && nodeMeta.InnerNode {
		return nil
	}
	deadlineTime, ok := call.Deadline()
	if !ok {
		return stderr.Error("deadline time of call is not set")
	}
	timeout := time.Until(deadlineTime) // 设置缓存超时时间
	subChainID := nodeMeta.SubChainID
	reqID := call.ReqID()
	var err error

	secondaryID, err := decideSecondaryID(nodeMeta, scope)
	if err != nil {
		return err
	}
	round := call.GetLoopRound(secondaryID)

	var debugMessage *debug.DebugMessage

	switch scope {
	case debug.DEBUG_SCOPE_NODE:
		// secondaryID = nodeMeta.Id，使用算子的全局唯一id nodeMeta.Id作为辅助id，标识cacheKey
		debugMessage, err = debug.InitDebugMessage(call, taskID, reqID, nodeMeta.Id, nodeMeta.NodeID, subChainID, nodeMeta.NodeName, nodeMeta.WidgetKey, debug.DEBUG_SCOPE_NODE, timeout, isDebug, round)
	case debug.DEBUG_SCOPE_SUB_CHAIN:
		// 如果子链有多个输入算子，虚构一个 debugMessage，作为 “虚构父节点的 debugMessage”
		// 当有多个输入算子时，这里（相同的 reqID + subChainID 作为 cacheKey）返回的 “虚构父节点的 debugMessage” 是同一个

		// secondaryID = subChainID，使用子链的全局唯一id subChainID作为辅助id，标识cacheKey
		// nodeID = subChainID, 子链对应画布中的应用链调用算子，该子链中所有算子的SubChainID就是应用链调用算子ID
		// nodeName = subChainName, 子链名称作为应用链调用算子名称
		debugMessage, err = debug.InitDebugMessage(call, taskID, reqID, subChainID, subChainID, "", nodeMeta.SubChainName, "WidgetKeySubChain", debug.DEBUG_SCOPE_SUB_CHAIN, timeout, isDebug, round)
	case debug.DEBUG_SCOPE_CHAIN:
		// secondaryID = ""，主链secondaryID为空时，使用reqID唯一标识主链debugMessage
		// nodeID = "", 主链无nodeID
		// subChainID = "", 主链无subChainID
		// nodeName = chainName, 使用主链名称
		debugMessage, err = debug.InitDebugMessage(call, taskID, reqID, "", "", "", nodeMeta.ChainName, nodeMeta.WidgetKey, debug.DEBUG_SCOPE_CHAIN, timeout, isDebug, 0)
	default:
		return stderr.Errorf("unknown debug scope")
	}
	if err != nil {
		return stderr.Wrap(err, "init debug message for scope %s", scope)
	}

	////把子链的所有算子的 debugMessage 放入 debugMessageStack 中 “虚构父节点的 debugMessage” 的 children 数组里
	//// subChainDebugMsgStackKey 作为 “虚构父节点的 debugMessage” 在 debugMessageStack 中的唯一标识（加上reqID 防止并发
	subChainDebugMsgStackKey := "sub_" + subChainID + "_" + reqID
	if scope == debug.DEBUG_SCOPE_SUB_CHAIN {
		if !call.HasDebugMsgStaskKey(subChainDebugMsgStackKey) {
			call.SetDebugMessage(subChainDebugMsgStackKey, 0, debugMessage)
		}
	} else {
		// 判断当前算子是子链中的
		if scope == debug.DEBUG_SCOPE_NODE && nodeMeta.SubChainID != "" {
			// 如果在子链中，找到 debugMessageStack 中的 “虚构父节点的 debugMessage”，设置成其Children
			if err := call.SetChildDebugMessageIntoStack(subChainDebugMsgStackKey, round, debugMessage); err != nil {
				return err
			}
		} else {
			call.SetDebugMessage(nodeMeta.Id, round, debugMessage)
		}
	}

	if err := debugMessage.SetRunningStatus(round); err != nil {
		return stderr.Wrap(err, "set running status to debug message of scope %s", scope)
	}
	// FIXME: 当为DEBUG_SCOPE_SUB_CHAIN时，增加的是nodeId的轮数，而不是subChainId。需要增加子链的轮数，且当子链有多个输入时，不能重复增加轮数
	// 此代码问题导致了子链中输入算子日志显示报错

	call.IncrementLoopRound(secondaryID)

	return nil
}

func PubInput(reqID string, call *debug.Call, nodeMeta models.NodeMeta, input any, scope debug.DebugScope, isBatch bool) error {
	if nodeMeta.InnerNode {
		return nil
	}

	round := decideRound(call, scope, &nodeMeta)

	secondaryID, err := decideSecondaryID(nodeMeta, scope)
	if err != nil {
		return stderr.Wrap(err, "decideSecondaryID")
	}

	debugMessage, err := debug.GetDebugMessage(reqID, secondaryID, round)
	if err != nil {
		return stderr.Wrap(err, "get debug message for scope %s", scope)
	}

	switch scope {
	case debug.DEBUG_SCOPE_NODE:
		err = debugMessage.SetInput(input, isBatch)
	case debug.DEBUG_SCOPE_SUB_CHAIN:
		err = debugMessage.MergeInput(input, nodeMeta.NodeName, nodeMeta.NodeID)
	}
	if err != nil {
		return stderr.Wrap(err, "set input for debug message for scope %s", scope)
	}
	return nil
}

func PubOutput(reqID string, call *debug.Call, nodeMeta models.NodeMeta, output any, scope debug.DebugScope, isBatch bool) error {
	if nodeMeta.InnerNode {
		return nil
	}

	round := decideRound(call, scope, &nodeMeta)

	secondaryID, err := decideSecondaryID(nodeMeta, scope)
	if err != nil {
		return stderr.Wrap(err, "decideSecondaryID")
	}

	debugMessage, err := debug.GetDebugMessage(reqID, secondaryID, round)
	if err != nil {
		return stderr.Wrap(err, "get debug message for scope %s", scope)
	}
	if err := debugMessage.SetOutput(output, isBatch); err != nil {
		return stderr.Wrap(err, "set output for debug message for scope %s", scope)
	}

	return nil
}

// PubSuccessStatus
// 链整体 httpListener节点  PointMessage  EndBatchMessage
// 单个节点   PointMessage  父 + 子
//
//	单个节点 EndBatchMessage  父 + 子
//
// OutputGuardrailNode  ?? 为啥,能否注释
func PubSuccessStatus(reqID string, call *debug.Call, nodeMeta models.NodeMeta, scope debug.DebugScope, isBatch bool) error {
	if nodeMeta.InnerNode {
		return nil
	}

	round := decideRound(call, scope, &nodeMeta)

	secondaryID, err := decideSecondaryID(nodeMeta, scope)
	if err != nil {
		return stderr.Wrap(err, "decideSecondaryID")
	}

	debugMessage, err := debug.GetDebugMessage(reqID, secondaryID, round)
	if err != nil {
		return stderr.Wrap(err, "get debug message for scope %s", scope)
	}
	if err := debugMessage.SetSuccessStatus(); err != nil {
		return stderr.Wrap(err, "set success status to debug message of scope %s", scope)
	}

	return nil
}

// PubErrorStatus 设置失败状态或取消状态
func PubErrorStatus(reqID string, call *debug.Call, nodeMeta models.NodeMeta, scope debug.DebugScope, isCanceledStatus bool) error {
	if nodeMeta.InnerNode {
		return nil
	}

	round := decideRound(call, scope, &nodeMeta)

	secondaryID, err := decideSecondaryID(nodeMeta, scope)
	if err != nil {
		return stderr.Wrap(err, "decideSecondaryID")
	}

	if !debug.DebugMessageExist(reqID, secondaryID, round) {
		return stderr.Error("Debug Message doesn't exist; error occurs before the %s starts", scope)
	}
	debugMessage, err := debug.GetDebugMessage(reqID, secondaryID, round)
	if err != nil {
		return stderr.Wrap(err, "get debug message for scope %s", scope)
	}
	if isCanceledStatus {
		return debugMessage.SetCanceledStatus()
	}
	return debugMessage.SetFailedStatus()
}

func getCall(msg Message) (*debug.Call, error) {
	contextGetter, ok := msg.(ContextGetter)
	if !ok {
		return nil, stderr.Error("failed to convert %+v to %v", msg, contextGetter)
	}
	ctx, err := contextGetter.Context()
	if err != nil {
		return nil, stderr.Wrap(err, "get context from message %+v", msg)
	}
	if ctx.Call == nil {
		return nil, stderr.Error("the call of context %+v is nil", ctx)
	}
	return ctx.Call, nil
}

func decideRound(call *debug.Call, scope debug.DebugScope, nodeMeta *models.NodeMeta) int {
	if scope == debug.DEBUG_SCOPE_NODE {
		// PubOutput 时已经是第二轮，需取上一轮的 debugMessage
		curRound := call.GetLoopRound(nodeMeta.Id)
		return curRound - 1
	}

	if scope == debug.DEBUG_SCOPE_SUB_CHAIN {
		return 0
	}

	// 处理未定义的 scope 类型
	return 0
}
