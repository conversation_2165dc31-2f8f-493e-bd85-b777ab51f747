package mqtt

import (
	"fmt"
	"time"

	pahomqtt "github.com/eclipse/paho.mqtt.golang"
	"transwarp.io/applied-ai/applet-engine/tools/tlsconfig"
	"github.com/pkg/errors"
)

// Client describes an immutable MQTT client, designed to accommodate the
// incongruencies between real clients and mock clients.
type Client interface {
	Connect() error
	Disconnect()
	IsConnected() bool
	Publish(topic string, qos QoSLevel, retained bool, message []byte) error
	Subscribe(topic string, qos QoSLevel, handler pahomqtt.MessageHandler) error
}

// newClient produces a disconnected MQTT client
var newClient = func(c Config) (Client, error) {
	opts := pahomqtt.NewClientOptions()
	opts.AddBroker(c.URL)
	if c.ClientID != "" {
		opts.SetClientID(c.ClientID)
	} else {
		opts.SetClientID(fmt.Sprintf("%s-%d", c.Name, time.Now().UnixNano()))
	}
	opts.SetUsername(c.Username)
	opts.SetPassword(c.Password)
	// log info
	opts.OnConnect = func(client pahomqtt.Client) {
		or := client.OptionsReader()
		fmt.Printf("[MQTTOnConnect] Mqtt client %s is connecting to servers: %+v",
			(&or).ClientID(),
			(&or).Servers(),
		)
	}
	opts.OnConnectionLost = func(client pahomqtt.Client, e error) {
		or := client.OptionsReader()
		fmt.Printf("[MQTTOnConnect] Mqtt client %s's connections to servers: %+v lost : %s",
			(&or).ClientID(),
			(&or).Servers(),
			e.Error(),
		)
	}

	tlsConfig, err := tlsconfig.Create(c.SSLCA, c.SSLCert, c.SSLKey, c.InsecureSkipVerify)
	if err != nil {
		return nil, err
	}
	opts.SetTLSConfig(tlsConfig)

	return &PahoClient{
		opts: opts,
	}, nil
}

type PahoClient struct {
	opts   *pahomqtt.ClientOptions
	client pahomqtt.Client
}

// DefaultQuiesceTimeout is the duration the client will wait for outstanding
// messages to be published before forcing a disconnection
const DefaultQuiesceTimeout time.Duration = 250 * time.Millisecond

// DefaultWaitTimeout is the duration the client will wait for token completed before timeout.
const DefaultWaitTimeout = 2 * time.Second

func (p *PahoClient) Connect() error {
	// Using a clean session forces the broker to dispose of client session
	// information after disconnecting. Retention of this is useful for
	// constrained clients.  Since Kapacitor is only publishing, it has no
	// storage requirements and can reduce load on the broker by using a clean
	// session.
	p.opts.SetCleanSession(true)
	if p.client == nil {
		// avoid 2 client with same id
		p.client = pahomqtt.NewClient(p.opts)
	}
	token := p.client.Connect()
	if !token.WaitTimeout(DefaultWaitTimeout) { // timeout occurred
		return errors.Errorf("timeout while connecting mqtt client")
	}
	return token.Error()
}

func (p *PahoClient) Disconnect() {
	if p.client != nil {
		p.client.Disconnect(uint(DefaultQuiesceTimeout / time.Millisecond))
	}
}

func (p *PahoClient) Publish(topic string, qos QoSLevel, retained bool, message []byte) error {
	token := p.client.Publish(topic, byte(qos), retained, message)
	if !token.WaitTimeout(DefaultWaitTimeout) { // timeout occurred
		return errors.Errorf("timeout while publishing mqtt message with topic : %s", topic)
	}
	return token.Error()
}

func (p *PahoClient) Subscribe(topic string, qos QoSLevel, handler pahomqtt.MessageHandler) error {
	token := p.client.Subscribe(topic, byte(qos), handler)
	if !token.WaitTimeout(DefaultWaitTimeout) { // timeout occurred
		return errors.Errorf("timeout while subscribing mqtt message with topic : %s", topic)
	}
	return token.Error()
}

func (p *PahoClient) IsConnected() bool {
	return p.client.IsConnected()
}
