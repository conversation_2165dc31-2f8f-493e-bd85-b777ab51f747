package kafkatest

import (
	"bytes"
	"encoding/binary"
	"fmt"
	"io"
	"net"
	"strconv"
	"strings"
	"sync"
)

type apiKey = int16
type apiVersion = int16

const (
	produce     apiKey = 0
	metadata    apiKey = 3
	apiVersions apiKey = 18

	v2 apiVersion = 2
)

// Provides an incomplete Kafka Server implementation.
// Records messages sent via the ProduceRequest and responds to MetadataRequests as the only broker.
type Server struct {
	Addr net.Addr
	mu   sync.Mutex
	wg   sync.WaitGroup

	closed  bool
	closing chan struct{}

	messages []Message
	errors   []error

	nodeID int32

	partitionMessage []byte
}

func NewServer() (*Server, error) {
	s := &Server{
		closing: make(chan struct{}),
		nodeID:  1,
	}
	l, err := net.Listen("tcp", ":0")
	if err != nil {
		return nil, err
	}
	s.Addr = l.Addr()

	// start server
	s.wg.Add(1)
	go func() {
		defer s.wg.Done()
		s.run(l)
	}()
	return s, nil
}

func (s *Server) prepareBrokerMsg() brokerMetadataV1 {
	host, port, _ := net.SplitHostPort(s.Addr.String())
	portN, _ := strconv.Atoi(port)
	return brokerMetadataV1{
		NodeID: s.nodeID,
		Host:   host,
		Port:   int32(portN),
	}
}

func (s *Server) preparePartitionMsg() partitionMetadataV1 {
	return partitionMetadataV1{
		PartitionErrorCode: 0,
		PartitionID:        1,
		Leader:             s.nodeID,
		Replicas:           []int32{},
		Isr:                []int32{},
	}
}

func (s *Server) prepareTopicMsg(names ...string) []topicMetadataV1 {
	topics := make([]topicMetadataV1, len(names))
	for i, name := range names {
		topics[i] = topicMetadataV1{
			TopicErrorCode: 0,
			TopicName:      name,
			Internal:       false,
			Partitions:     []partitionMetadataV1{s.preparePartitionMsg()},
		}
	}
	return topics

}

func (s *Server) Close() {
	s.mu.Lock()
	defer s.mu.Unlock()
	if s.closed {
		return
	}
	s.closed = true
	close(s.closing)
	s.wg.Wait()
}

func (s *Server) Messages() ([]Message, error) {
	if len(s.errors) > 0 {
		return nil, multiError(s.errors)
	}
	return s.messages, nil
}

func (s *Server) run(l net.Listener) {
	defer l.Close()

	accepts := make(chan net.Conn)

	s.wg.Add(1)
	go func() {
		defer s.wg.Done()
		for {
			c, err := l.Accept()
			if err != nil {
				return
			}
			select {
			case accepts <- c:
			case <-s.closing:
				return
			}
		}
	}()

	for {
		select {
		case c := <-accepts:
			s.wg.Add(1)
			go func() {
				defer s.wg.Done()
				defer c.Close()
				if err := s.handle(c); err != nil {
					println("error while handling accepts:", err.Error())
					s.errors = append(s.errors, err)
				}
			}()
		case <-s.closing:
			return
		}
	}
}

func (s *Server) handle(c net.Conn) (err error) {
	connected := true
	for connected {
		var size int32
		err = binary.Read(c, binary.BigEndian, &size)
		if err == io.EOF {
			println("!!!! connection closed")
			return nil
		}
		if err != nil {
			println("!!!! failed to read size")
			return
		}
		buf := make([]byte, int(size))
		_, err = io.ReadFull(c, buf)
		if err != nil {
			println("!!!! failed to read full")
			return
		}
		println("recv: ", string(buf))
		// ApiKey indicated the type of request
		apiKey := int16(binary.BigEndian.Uint16(buf[:2]))
		_, n := readStr(buf[8:])
		request := buf[8+n:]

		// Prepare response
		response := make([]byte, 8, 1024)
		// Leave first 4 bytes for the size.
		// Copy correlationID.
		copy(response[4:], buf[4:8])

		switch apiKey {
		case produce: // ProduceRequest
			connected = false
			topic, partition, offset := s.readProduceRequest(request)

			// Prepare success response
			response = writeArrayHeader(response, 1)
			response = writeStr(response, topic)
			response = writeArrayHeader(response, 1)
			response = writeInt32(response, partition)
			response = writeInt16(response, 0) // Error Code
			response = writeInt64(response, offset)
			response = writeInt64(response, -1) // Timestamp
			response = writeInt32(response, 0)  // ThrottleTime
		case metadata: // Metadata
			connected = false
			topics, _ := readStrList(request)
			meta := metadataResponseV1{
				Brokers:      []brokerMetadataV1{s.prepareBrokerMsg()},
				ControllerID: 1,
				Topics:       s.prepareTopicMsg(topics...),
			}
			buf := bytes.NewBuffer(make([]byte, 0))
			meta.writeTo(&writeBuffer{
				w: buf,
				b: [16]byte{},
			})
			response = append(response, buf.Bytes()...)
		case apiVersions:// NegotiateVersion
			// error code
			response = writeInt16(response, 0)
			// array size of versions
			response = writeArrayHeader(response, 1)
			// api version
			//  1. api key
			response = writeInt16(response, 0)
			//  2. min version
			response = writeInt16(response, v2)
			//  3. max version
			response = writeInt16(response, v2)

			// there also are packets need to handled
			connected = true
		default:
			return fmt.Errorf("unsupported apiKey %d", apiKey)
		}

		// Set response size
		responseSize := len(response) - 4
		binary.BigEndian.PutUint32(response[:4], uint32(responseSize))
		println(fmt.Sprintf("send: %+X", string(response)))
		_, err = c.Write(response)
		if err != nil {
			println("!!!! failed to write response")
			return
		}
	}

	return
}

// readProduceRequest, assume only a single message exists
func (s *Server) readProduceRequest(request []byte) (topic string, partition int32, offset int64) {
	pos := 2 + 4 + 4 // skip RequiredAcks and Timeout and topic array len

	// Read topic name
	topic, n := readStr(request[pos:])
	pos += n

	pos += 4 // skip array len

	partition = readInt32(request[pos:])
	pos += 4

	pos += 4 // skip set size

	offset = readInt64(request[pos:])
	pos += 8

	pos += 4 + 4 + 1 + 1 + 8 // skip size, crc, magic, attributes, timestamp

	key, n := readByteArray(request[pos:])
	pos += n

	message, n := readByteArray(request[pos:])
	pos += n

	s.saveMessage(Message{
		Topic:     topic,
		Partition: partition,
		Offset:    offset,
		Key:       string(key),
		Message:   string(message),
	})
	return
}

func (s *Server) saveMessage(m Message) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.messages = append(s.messages, m)
}

func readList(buf []byte, f func([]byte) int) int {
	pos := 4
	count := int(int32(binary.BigEndian.Uint32(buf[:pos])))
	for i := 0; i < count; i++ {
		pos += f(buf[pos:])
	}
	return pos
}
func readStrList(buf []byte) ([]string, int) {
	var strs []string
	l := readList(buf, func(data []byte) int {
		s, n := readStr(data)
		strs = append(strs, s)
		return n
	})
	return strs, l
}
func readStr(buf []byte) (string, int) {
	n := int(int16(binary.BigEndian.Uint16(buf[:2])))
	return string(buf[2 : 2+n]), n + 2
}
func readByteArray(buf []byte) ([]byte, int) {
	n := int(int32(binary.BigEndian.Uint32(buf[:4])))
	return buf[4 : 4+n], n + 4
}

func readInt16(buf []byte) int16 {
	return int16(binary.BigEndian.Uint16(buf[:2]))
}
func readInt32(buf []byte) int32 {
	return int32(binary.BigEndian.Uint32(buf[:4]))
}
func readInt64(buf []byte) int64 {
	return int64(binary.BigEndian.Uint64(buf[:8]))
}

func writeStr(dst []byte, s string) []byte {
	dst = writeInt16(dst, int16(len(s)))
	return append(dst, []byte(s)...)
}

func writeInt16(dst []byte, n int16) []byte {
	l := len(dst)
	dst = append(dst, []byte{0, 0}...)
	binary.BigEndian.PutUint16(dst[l:l+2], uint16(n))
	return dst
}

func writeInt32(dst []byte, n int32) []byte {
	l := len(dst)
	dst = append(dst, []byte{0, 0, 0, 0}...)
	binary.BigEndian.PutUint32(dst[l:l+4], uint32(n))
	return dst
}
func writeInt64(dst []byte, n int64) []byte {
	l := len(dst)
	dst = append(dst, []byte{0, 0, 0, 0, 0, 0, 0, 0}...)
	binary.BigEndian.PutUint64(dst[l:l+8], uint64(n))
	return dst
}

func writeArrayHeader(dst []byte, n int32) []byte {
	return writeInt32(dst, n)
}

func writeArray(dst []byte, data [][]byte) []byte {
	dst = writeArrayHeader(dst, int32(len(data)))
	for _, d := range data {
		dst = append(dst, d...)
	}
	return dst
}

type multiError []error

func (e multiError) Error() string {
	errs := make([]string, len(e))
	for i := range e {
		errs[i] = e[i].Error()
	}
	return strings.Join(errs, "\n")
}

type Message struct {
	Topic     string
	Partition int32
	Offset    int64
	Key       string
	Message   string
}
