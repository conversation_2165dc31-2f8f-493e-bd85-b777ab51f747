package stream
//
//import (
//	"encoding/json"
//	"time"
//
//	"github.com/influxdata/influxdb/models"
//	stdmqtt "transwarp.io/aiot/thinger-std/transport/mqtt"
//)
//
//var PlaceHolderFields = map[string]interface{}{"empty_fields_placeholder": ""}
//
//type Handler struct {
//	PointsWriter interface {
//		WritePoints(database, retentionPolicy string, consistencyLevel models.ConsistencyLevel, points []models.Point) error
//	}
//
//	diag Diagnostic
//}
//
//func NewHandler(d Diagnostic) *Handler {
//	return &Handler{
//		diag: d,
//	}
//}
//
//func (h *Handler) MsgHandler(msg *stdmqtt.Msg) {
//	// todo: filter some topic types
//
//	//h.diag.Info(fmt.Sprintf("receive msg, topic: %s", msg.Topic))
//	topic, err := stdmqtt.NewTopic(msg.Topic)
//	if err != nil {
//		h.diag.Error("error while parsing mqtt msg topic", err)
//		return
//	}
//	fields := make(map[string]interface{}, 0)
//	err = json.Unmarshal(msg.Payload, &fields)
//	if err != nil {
//		h.diag.Error("error while unmarshal mqtt payload to a map", err)
//		return
//	}
//
//	tags := make(map[string]string, len(topic.Tags()))
//	for k, v := range topic.Tags() {
//		tags[string(k)] = v
//	}
//	// could not new influx point with empty fields
//	if len(fields) == 0 {
//		fields = PlaceHolderFields
//	}
//
//	// todo: handle time.Now()
//	point, err := models.NewPoint(topic.Measurement(), models.NewTags(tags), fields, time.Now())
//	if err != nil {
//		h.diag.Error("error while unmarshal mqtt payload to a influx point", err)
//	}
//
//	if err := h.PointsWriter.WritePoints(
//		DefaultDatabase,
//		DefaultRetentionPolicy,
//		models.ConsistencyLevelAll,
//		[]models.Point{point}); err != nil {
//		h.diag.Error("error while write mqtt stream point", err)
//	}
//}
