package stream
//
//import (
//	stdconf "transwarp.io/aiot/thinger-std/conf"
//)
//
//const (
//	DefaultDatabase        = "thinger"
//	DefaultRetentionPolicy = "autogen"
//)
//
//type Config stdconf.MqttConfig
//
//func NewConfig() Config {
//	c := &Config{}
//	c.Init()
//	return *c
//}
//
//func (c Config) Validate() error {
//	// todo
//	return nil
//}
//
//func (c *Config) Init() {
//	// todo: use something like getOrElse, or put it in kapacitor.conf
//	c.BrokerAddr = "127.0.0.1:1883"
//	c.ClientId = "kapacitor-mqtt-stream"
//	c.Qos = 0
//	c.ConnTimeOut = 30000
//	c.TimeOut = 1000
//	c.PersistentSession = false
//	c.SubTopic = "#"
//
//	stdconf.LoadEnv(&c<PERSON><PERSON><PERSON><PERSON><PERSON>, "MQTT_BROKER_ADDR")
//	stdconf.LoadEnv(&c.ClientId, "MQTT_CLIENT_ID")
//	stdconf.LoadEnvInt(&c.Qos, "MQTT_QOS")
//	stdconf.LoadEnvInt64(&c.ConnTimeOut, "MQTT_CONNECTION_TIMEOUT_MS")
//	stdconf.LoadEnvInt64(&c.TimeOut, "MQTT_PUBLISH_TIMEOUT_MS")
//	stdconf.LoadEnvBool(&c.PersistentSession, "MQTT_PERSISTENT_SESSION")
//	c.SubTopic = "#"
//}
