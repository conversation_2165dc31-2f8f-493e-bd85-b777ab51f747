package stream

//
//type Diagnostic interface {
//	Error(msg string, err error)
//	Info(msg string)
//}
//
//type Service struct {
//	mu sync.RWMutex
//
//	diag    Diagnostic
//	client  stdmqtt.MQTTClient
//	topic   string
//	Handler *Handler
//}
//
//func NewService(c Config, d Diagnostic) (*Service, error) {
//	client := stdmqtt.NewMQTTClient(stdconf.MqttConfig(c))
//
//	return &Service{
//		diag:    d,
//		client:  client,
//		topic:   c.SubTopic,
//		Handler: NewHandler(d),
//	}, nil
//}
//
//func (s *Service) Open() error {
//	s.mu.Lock()
//	defer s.mu.Unlock()
//
//	if err := s.client.Start(); err != nil {
//		return errors.WithMessage(err, "failed to connect MQTT")
//	}
//	if err := s.client.Sub(s.Handler.MsgHandler, s.topic); err != nil {
//		return errors.WithMessage(err, "failed to sub MQTT")
//	}
//
//	return nil
//}
//
//func (s *Service) Close() error {
//	s.mu.Lock()
//	defer s.mu.Unlock()
//
//	s.client.Stop()
//	return nil
//}
