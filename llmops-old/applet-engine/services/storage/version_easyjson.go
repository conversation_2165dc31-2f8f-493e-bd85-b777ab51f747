// Code generated by easyjson for marshaling/unmarshaling. DO NOT EDIT.

package storage

import (
	json "encoding/json"
	easyjson "github.com/mailru/easyjson"
	jlexer "github.com/mailru/easyjson/jlexer"
	jwriter "github.com/mailru/easyjson/jwriter"
)

// suppress unused package warning
var (
	_ *json.RawMessage
	_ *jlexer.Lexer
	_ *jwriter.Writer
	_ easyjson.Marshaler
)

func easyjson8e52a332DecodeGithubComInfluxdataKapacitorServicesStorage(in *jlexer.Lexer, out *VersionWrapper) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeString()
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "version":
			out.Version = int(in.Int())
		case "value":
			if in.IsNull() {
				in.Skip()
				out.Value = nil
			} else {
				if out.Value == nil {
					out.Value = new(json.RawMessage)
				}
				if data := in.Raw(); in.Ok() {
					in.AddError((*out.Value).UnmarshalJSON(data))
				}
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson8e52a332EncodeGithubComInfluxdataKapacitorServicesStorage(out *jwriter.Writer, in VersionWrapper) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"version\":"
		if first {
			first = false
			out.RawString(prefix[1:])
		} else {
			out.RawString(prefix)
		}
		out.Int(int(in.Version))
	}
	{
		const prefix string = ",\"value\":"
		if first {
			first = false
			out.RawString(prefix[1:])
		} else {
			out.RawString(prefix)
		}
		if in.Value == nil {
			out.RawString("null")
		} else {
			out.Raw((*in.Value).MarshalJSON())
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v VersionWrapper) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson8e52a332EncodeGithubComInfluxdataKapacitorServicesStorage(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v VersionWrapper) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson8e52a332EncodeGithubComInfluxdataKapacitorServicesStorage(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *VersionWrapper) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson8e52a332DecodeGithubComInfluxdataKapacitorServicesStorage(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *VersionWrapper) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson8e52a332DecodeGithubComInfluxdataKapacitorServicesStorage(l, v)
}
