package edm

//
// import (
// 	"fmt"
// 	"github.com/influxdata/kapacitor/services/mqtt"
// 	"github.com/thingio/edge-device-std/config"
// 	"github.com/thingio/edge-device-std/logger"
// 	"github.com/thingio/edge-device-std/msgbus"
// 	"github.com/thingio/edge-device-std/operations"
// 	"net/url"
// 	"strconv"
// 	"sync"
// )
//
// type Diagnostic interface {
// 	Error(msg string, err error)
// 	Info(msg string)
// }
//
// type Service struct {
// 	mu sync.RWMutex
//
// 	ms operations.ManagerService
// }
//
// func NewService(c mqtt.Config, d Diagnostic) (*Service, error) {
// 	lg, err := logger.NewLogger(&config.LogOptions{
// 		Level:   "info",
// 		Console: false,
// 	})
// 	if err != nil {
// 		return nil, err
// 	}
// 	u, err := url.Parse(c.URL)
// 	if err != nil {
// 		return nil, err
// 	}
// 	port, _ := strconv.Atoi(u.Port())
// 	mb, err := msgbus.NewMessageBus(&config.MessageBusOptions{
// 		Type: config.MessageBusTypeMQTT,
// 		MQTT: config.MQTTMessageBusOptions{
// 			Host:                         u.Hostname(),
// 			Port:                         port,
// 			Username:                     c.Username,
// 			Password:                     c.Password,
// 			QoS:                          2,
// 			CleanSession:                 false,
// 			ConnectTimoutMillisecond:     30000,
// 			TokenTimeoutMillisecond:      1000,
// 			MethodCallTimeoutMillisecond: 1000,
// 		},
// 	}, lg)
// 	if err != nil {
// 		return nil, err
// 	}
// 	ms, err := operations.NewManagerService(mb, lg)
// 	if err != nil {
// 		return nil, err
// 	}
// 	return &Service{
// 		ms: ms,
// 	}, nil
// }
//
// func (s *Service) GetInstance() operations.ManagerService {
// 	return s.ms
// }
//
// func (s *Service) Open() error {
// 	return nil
// }
//
// func (s *Service) Close() error {
// 	return nil
// }
//
// func (s *Service) Update(c []interface{}) error {
// 	return nil
// }
//
// type testOptions struct {
// }
//
// func (s *Service) TestOptions() interface{} {
// 	s.mu.RLock()
// 	defer s.mu.RUnlock()
//
// 	return &testOptions{}
// }
//
// func (s *Service) Test(o interface{}) error {
// 	options, ok := o.(*testOptions)
// 	if !ok {
// 		return fmt.Errorf("unexpected options type %T", options)
// 	}
// 	return nil
// }
