root = "/kapacitor/v1.4/nodes"
page-header = '''---
title: {{ .Title }}
note: Auto generated by tickdoc

menu:
  kapacitor_1_5:
    name: {{ .Name }}
    identifier: {{ .Identifier }}
    weight: {{ .Weight }}
    parent: nodes
---
'''


chain-method-desc = '''Chaining methods create a new node in the pipeline as a child of the calling node.
They do not modify the calling node.
Chaining methods are marked using the `|` operator.
'''


property-method-desc = '''Property methods modify state on the calling node.
They do not add another node to the pipeline, and always return a reference to the calling node.
Property methods are marked using the `.` operator.
'''


index-width = 10
[weights]
    BatchNode = 4
    StreamNode = 5


