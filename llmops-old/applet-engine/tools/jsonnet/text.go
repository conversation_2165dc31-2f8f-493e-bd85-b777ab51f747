package jsonnet

import "strings"

//TODO abstract this file to std

var (
	spaceChars  = []string{"\t", "\n", "\v", "\f", "\r"}
	escapedSQ   = "\\'"  // escaped single quote
	escapedDQ   = "\\\"" // escaped double quote
	unescapedSQ = "'"    // unescaped single quote
	unescapedDQ = "\""   //un escaped double quote
)

func UnescapeQuotes(str string) string {
	str = strings.Replace(str, escapedSQ, unescapedSQ, -1)
	str = strings.Replace(str, escapedDQ, unescapedDQ, -1)
	return str
}

func EscapeQuotes(str string) string {
	str = UnescapeQuotes(str)
	str = strings.Replace(str, unescapedDQ, escapedDQ, -1)
	str = strings.Replace(str, escapedSQ, unescapedSQ, -1)
	return str
}

func CleanSpace(str string) string {
	for _, c := range spaceChars {
		str = strings.Replace(str, c, "", -1)
	}
	return str
}
