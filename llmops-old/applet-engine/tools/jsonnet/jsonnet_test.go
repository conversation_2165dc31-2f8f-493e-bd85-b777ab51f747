package jsonnet

import (
	"fmt"
	"strings"
	"testing"

	"github.com/google/go-jsonnet"
	"github.com/smartystreets/goconvey/convey"
)

func TestJsonnet(t *testing.T) {
	vm := jsonnet.MakeVM()
	vm.TLACode("obj", "{a:1}")
	v := fmt.Sprintf("local input=%s;", "{\"input\":1}")
	res, err := vm.EvaluateSnippet("", v+"{a:input.input}")
	if err != nil {
		t.Error(err)
	}
	print(res)
}

func TestFillJsonnet1(t *testing.T) {
	inData := struct {
		Ff1 int
		Ff2 float32
		Ff3 string
	}{
		Ff1: 1,
		Ff2: 1.5,
		Ff3: DEFAULT_LOCAL_VAR,
	}
	convey.Convey("test fill jsonnet", t, func() {
		jsonNetCode := "{field1:input.Ff1,field2:input.Ff2,field3:input.Ff3}"
		res, err := FillJsonnet(inData, jsonNetCode)
		convey.So(err, convey.ShouldBeNil)
		res = strings.Replace(res, "\n", "", -1)
		res = strings.Replace(res, " ", "", -1)
		fmt.Print(res)
		// todo: fixme
		//convey.So(res, convey.ShouldEqual, "{\"field1\":1,\"field2\":1.5,\"field3\":\"input\"}")
	})
}

func TestFillJsonnet(t *testing.T) {
	input := struct {
		Ff1 int
		Ff2 float32
		Ff3 string
	}{
		Ff1: 1,
		Ff2: 1.5,
		Ff3: DEFAULT_LOCAL_VAR,
	}
	prefix := "123"
	suffix := "456"
	expect := prefix + `{\"field1\":1,\"field2\":1.5,\"field3\":\"input\"}` + suffix
	payload := prefix +
		LEFT_DELIMITER + "{field1:input.Ff1,field2:input.Ff2,field3:input.Ff3}" +
		RIGHT_DELIMITER + suffix

	tests := []struct {
		name    string
		payload string
		want    string
		wantErr bool
	}{
		{
			"success-case-1: one slice",
			payload,
			expect,
			false,
		},
		{
			"success-case-2: two slice",
			payload + payload,
			expect + expect,
			false,
		},
		{
			"success-case-3: none slice",
			"123456",
			"123456",
			false,
		},
		{
			"success-case-4: ignore shared delimiter",
			"123/####/456/###/789/##/",
			"123456789",
			false,
		},
		{
			"fail-case-1: order error",
			"abc##/ /##",
			"",
			true,
		},
		{
			"fail-case-2: nested error",
			"/##abc/## 456##/ ##/",
			"",
			true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := FillJsonnet(input, tt.payload)
			got = strings.Replace(got, "\n", "", -1)
			got = strings.Replace(got, " ", "", -1)
			if (err != nil) != tt.wantErr {
				t.Errorf("FillJsonnet() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("FillJsonnet() = %v, want %v", got, tt.want)
			}
		})
	}
}
