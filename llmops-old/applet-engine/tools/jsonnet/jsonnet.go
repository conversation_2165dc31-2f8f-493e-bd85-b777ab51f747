package jsonnet

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/google/go-jsonnet"
	"github.com/pkg/errors"
)

var vm *jsonnet.VM

const (
	DEFAULT_LOCAL_VAR = "input"
	LEFT_DELIMITER    = "/##"
	RIGHT_DELIMITER   = "##/"
	DELIMITER_LEN     = len(LEFT_DELIMITER)
)

func init() {
	vm = jsonnet.MakeVM()
}
func FillJsonnet(input interface{}, payload string) (string, error) {
	// convert input to json
	in, err := json.Marshal(input)
	if err != nil {
		return "", err
	}
	payload = CleanSpace(payload)
	lc := strings.Count(payload, LEFT_DELIMITER)
	rc := strings.Count(payload, RIGHT_DELIMITER)
	// no jsonnet code need to evaluate
	if lc == 0 && rc == 0 {
		return payload, nil
	}
	// jsonnet code invalid
	if lc != rc {
		return "", errors.Errorf("invalid jsonnet : "+
			"found %d left delimiters, but found %d right delimiters", lc, rc)
	}

	// make input json as a local var
	preVar := fmt.Sprintf("local %s = %s;", DEFAULT_LOCAL_VAR, string(in))

	asField := true
	if lc == 1 && rc == 1 &&
		strings.HasPrefix(payload, LEFT_DELIMITER) &&
		strings.HasSuffix(payload, RIGHT_DELIMITER) {
		asField = false
	}
	// evaluate jsonnet code in turn
	s := payload
	res := strings.Builder{}
	for i := 0; i < lc; i++ {
		l, m, r, err := split(s)
		if err != nil {
			return "", err
		}

		// add the left part of this jsonnet slice to res
		res.WriteString(l)

		// evaluate jsonnet code if is not empty
		if m != "" {
			m, err = EvaluateJsonnet(preVar, m, asField)
			if err != nil {
				return "", err
			}
			res.WriteString(m)
		}

		// if all jsonnet slices has been evaluated
		// add the right part of this jsonnet slice to res
		if i == lc-1 {
			res.WriteString(r)
		}
		s = r
	}

	return CleanSpace(res.String()), nil
}

// split will split data with delimiter
// then return 3 slice string respectively l,m,r
//
//	l : the slice in front of LEFT_DELIMITER
//	m : the slice between LEFT_DELIMITER and RIGHT_DELIMITER
//	r : the slice after RIGHT_DELIMITER
func split(data string) (l, m, r string, err error) {
	l, m, r = "", "", ""
	li := strings.Index(data, LEFT_DELIMITER)
	ri := strings.Index(data, RIGHT_DELIMITER)
	if li < 0 || ri < 0 {
		return l, m, r, errors.Errorf("invalid jsonnet : " +
			"cannot found next delimiter pair")
	}
	// handle delimiters order error
	// like : ##/ input.Value /##
	if ri < li {
		return l, m, r, errors.Errorf("invalid jsonnet : " +
			"found right delimiter before left delimiter")
	}

	l = data[:li]
	r = data[ri+DELIMITER_LEN:]
	// handle delimiter overlap
	// like /####/ || /###/ || /##/
	if (ri - li) <= DELIMITER_LEN {
		return l, m, r, nil
	}
	m = data[li+DELIMITER_LEN : ri]
	// handle slice contains nested delimiters
	// like : /## input.Value /## ##/
	if strings.Contains(m, LEFT_DELIMITER) {
		return l, m, r, errors.Errorf("invalid jsonnet : " +
			"found multiple left delimiter before a right delimiter")
	}
	return l, m, r, nil
}

func EvaluateJsonnet(preVar, data string, asField bool) (string, error) {
	// unescape \" \' to " '
	if asField {
		data = UnescapeQuotes(data)
	}
	res, err := vm.EvaluateSnippet("", preVar+data)
	if err != nil {
		return "", err
	}
	if asField {

		return EscapeQuotes(res), nil
	}
	return res, nil
}
