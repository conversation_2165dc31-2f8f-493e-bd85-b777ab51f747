package model_service

import (
	"context"
	"transwarp.io/applied-ai/applet-backend/pkg/clients"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
)

func SyncChat(ctx context.Context, model *pb.ModelService, query string) (string, error) {
	chatResult := ""
	syncChatReq := &triton.LLMChatReq{Query: query}
	syncChatHandler := func(textContent string) error {
		chatResult = textContent
		return nil
	}
	if err := clients.SyncChat(ctx, model, clients.Cvt2OpenaiChatReq(syncChatReq), syncChatHandler); err != nil {
		return "", stderr.Wrap(err, "failed to do sync chat with model %s and query %s", model.ModelName, query)
	}
	return chatResult, nil
}
