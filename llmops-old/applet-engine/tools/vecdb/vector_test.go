package vecdb

import (
	"net/url"
	"strings"
	"testing"

	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
)

func TestParseVectorDBConfigFromURL(t *testing.T) {
	type args struct {
		u *url.URL
	}
	tests := []struct {
		name string
		urlS string
		want VectorDBConfig
	}{
		{
			name: "test milvus",
			urlS: "milvus://***********:19530?table=testcos&dimension=1024",
			want: VectorDBConfig{
				Address:      "***********:19530",
				UserName:     "",
				UserPassword: "",
				Database:     "default",
				Table:        "testcos",
				Partition:    "",
				Dimension:    1024,
				MetricType:   "IP",
				IndexType:    "FLAT",
			},
		},
		{
			name: "test hippo",
			urlS: "hippo://shiva:shivaPWD@***********:7788?table=testcos&dimension=1024",
			want: VectorDBConfig{
				Address:      "***********:7788",
				UserName:     "shiva",
				UserPassword: "shivaPWD",
				Database:     "default",
				Table:        "testcos",
				Partition:    "",
				Dimension:    1024,
				MetricType:   "IP",
				IndexType:    "FLAT",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			u, err := url.Parse(tt.urlS)
			if err != nil {
				t.Fatal(err)
			}
			got := ParseVectorDBConfigFromURL(u)
			if diffs := utils.Diff(got, tt.want); diffs != nil {
				t.Errorf("ParseVectorDBConfigFromURL() !=  want: \n%s", strings.Join(diffs, "\n\t"))
			}
		})
	}
}
