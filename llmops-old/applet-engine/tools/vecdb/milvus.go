package vecdb

import (
	"context"
	"fmt"
	"time"

	"github.com/milvus-io/milvus-proto/go-api/v2/commonpb"
	milvus "github.com/milvus-io/milvus-sdk-go/v2/client"
	"github.com/milvus-io/milvus-sdk-go/v2/entity"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
)

type MilvusDBCli struct {
	ctx    context.Context
	cancel context.CancelFunc
	mc     milvus.Client
	cfg    VectorDBConfig
}

func NewMilvusDBCli(ctx context.Context, cfg VectorDBConfig) (client *MilvusDBCli, err error) {
	cCtx, cCancel := context.WithCancel(ctx)
	defer func() {
		if err != nil {
			cCancel()
		}
	}()
	mCfg := milvus.Config{
		Address:       cfg.Address,
		Username:      cfg.UserName,
		Password:      cfg.UserPassword,
		DBName:        cfg.Database,
		Identifier:    fmt.Sprintf("appengine-%s", time.Now().String()),
		EnableTLSAuth: false,
		APIKey:        "",
		ServerVersion: "",
		RetryRateLimit: &milvus.RetryRateLimitOption{
			MaxRetry:   5,
			MaxBackoff: time.Minute,
		},
		DisableConn: false,
	}
	stdlog.Debugf("connect to vector database with config: %+v", cfg)
	connCtxt, connCancel := context.WithTimeout(ctx, 5*time.Second)
	defer connCancel()
	dbc, err := milvus.NewClient(connCtxt, mCfg)
	if err != nil {
		return nil, stderr.Wrap(err, "connect to database with config: %+v", cfg)
	}
	defer func() {
		if err != nil {
			if cerr := dbc.Close(); cerr != nil {
				stdlog.WithError(cerr).Errorf("closing milvus db client while init it failed")
			}
		}
	}()

	ok, err := dbc.HasCollection(ctx, cfg.Table)
	if err != nil {
		return nil, stderr.Wrap(err, "checking the existence of collection %s", cfg.Table)
	}
	if !ok {
		schema := entity.NewSchema().WithName(cfg.Table).
			WithDescription(fmt.Sprintf("the collection auto created by applet-engine at %s for %s", time.Now().String(), cfg.ClientID)).
			WithField(entity.NewField().WithName(VectorDBFieldID).WithDataType(entity.FieldTypeInt64).WithIsPrimaryKey(true).WithIsAutoID(true)).
			WithField(entity.NewField().WithName(VectorDBFieldText).WithDataType(entity.FieldTypeVarChar).WithMaxLength(2048)).
			WithField(entity.NewField().WithName(VectorDBFieldTitle).WithDataType(entity.FieldTypeVarChar).WithMaxLength(2048)).
			WithField(entity.NewField().WithName(VectorDBFieldVector).WithDataType(entity.FieldTypeFloatVector).WithDim(cfg.Dimension))

		// create collection with consistency level, which serves as the default search/query consistency level
		stdlog.Infof("auto create milvus collection with schema %+v", schema)
		if err = dbc.CreateCollection(ctx, schema, entity.DefaultShardNumber, milvus.WithConsistencyLevel(entity.ClBounded)); err != nil {
			return nil, stderr.Wrap(err, "create collection failed")
		}
	}

	state, err := dbc.GetIndexState(ctx, cfg.Table, VectorDBFieldVector)
	if err != nil {
		return nil, stderr.Wrap(err, "get index state of field '%s' of collection '%s'", VectorDBFieldVector, cfg.Table)
	}
	s := commonpb.IndexState(state)
	stdlog.Infof("the index state of field '%s' of collection '%s' is: %s", VectorDBFieldVector, cfg.Table, s.String())
	switch s {
	case commonpb.IndexState_IndexStateNone, commonpb.IndexState_Unissued, commonpb.IndexState_Failed:
		stdlog.Infof("auto create a new index")
		idx, err := entity.NewIndexIvfFlat(entity.MetricType(cfg.MetricType), 128)
		if err != nil {
			return nil, stderr.Wrap(err, "failed to create ivf flat index")
		}

		if err := dbc.CreateIndex(ctx, cfg.Table, VectorDBFieldVector, idx, false); err != nil {
			return nil, stderr.Wrap(err, "failed to create index")
		}
	default:
		stdlog.Infof("skip creating index")
	}

	idxes, err := dbc.DescribeIndex(ctx, cfg.Table, VectorDBFieldVector)
	if err != nil {
		return nil, stderr.Wrap(err, "describe index of table %s of field %s", cfg.Table, VectorDBFieldVector)
	}
	stdlog.Debugf("found %d indexes", len(idxes))
	if len(idxes) == 0 {
		return nil, stderr.Internal.Error("no index found for field %s of table %s", VectorDBFieldVector, cfg.Table)
	}
	// found := false
	// for _, idx := range idxes {
	// 	params := idx.Params()
	// 	if params == nil {
	// 		continue
	// 	}
	// 	mt, it := params["metric_type"], params["index_type"]
	// 	if mt == cfg.MetricType && it == cfg.IndexType {
	// 		stdlog.Infof("fount a index named '%s' with metric_type '%s' and index_type '%s'", idx.Name(), mt, it)
	// 		found = true
	// 		break
	// 	}
	// }
	// if !found {
	// 	return nil, stderr.Internal.Error("index matched index type %s and metric type %s not found", cfg.IndexType, cfg.MetricType)
	// }

	if err := dbc.LoadCollection(ctx, cfg.Table, false); err != nil {
		return nil, stderr.Wrap(err, "load collection %s", cfg.Table)
	}

	return &MilvusDBCli{
		ctx:    cCtx,
		cancel: cCancel,
		mc:     dbc,
		cfg:    cfg,
	}, nil

}

func (m *MilvusDBCli) Close() {
	if m.mc == nil {
		return
	}
	if cerr := m.mc.Close(); cerr != nil {
		stdlog.WithError(cerr).Errorf("closing milvus db client while init it failed")
	}
	m.cancel()
}

func (m *MilvusDBCli) Insert(ctx context.Context, columns ...FieldColumnData) ([]int64, error) {
	eCols := make([]entity.Column, len(columns))
	for i, column := range columns {
		eCol, err := column.ToMilvusColumn()
		if err != nil {
			return nil, stderr.Wrap(err, "convert to milvus column")
		}
		eCols[i] = eCol
	}
	idCol, err := m.mc.Insert(ctx, m.cfg.Table, m.cfg.Partition, eCols...)
	if err != nil {
		return nil, stderr.Wrap(err, "do milvus insert")
	}
	ids := make([]int64, idCol.Len())
	for i := range ids {
		id, err := idCol.GetAsInt64(i)
		if err != nil {
			return nil, stderr.Internal.Cause(err, "cannot get id as int64")
		}
		ids[i] = id
	}
	return ids, nil
}

func (m *MilvusDBCli) Search(ctx context.Context, params *SearchParams) (*SearchResult, error) {
	if params == nil {
		return nil, stderr.InvalidParam.Error("search params cannot be empty")
	}

	var vecs []entity.Vector
	for _, vec := range params.Vectors {
		vecs = append(vecs, entity.FloatVector(vec))
	}
	sp, _ := entity.NewIndexFlatSearchParam()
	part := make([]string, 0)
	if m.cfg.Partition != "" {
		part = append(part, m.cfg.Partition)
	}

	searchResults, err := m.mc.Search(ctx, m.cfg.Table, part, params.Expr, params.OutputFields, vecs,
		VectorDBFieldVector, entity.MetricType(m.cfg.MetricType), params.TopK, sp)
	if err != nil {
		return nil, stderr.Wrap(err, "vec search")
	}

	ret := &SearchResult{
		Queries: len(searchResults),
		TopK:    params.TopK,
		Results: make([]SearchResultItem, len(searchResults)),
	}
	for i, searchResult := range searchResults {
		retResult := SearchResultItem{
			Query:       i,
			ResultCount: searchResult.ResultCount,
			Scores:      searchResult.Scores,
		}
		for _, field := range searchResult.Fields {
			fcd, err := NewFieldColumnDataFromMilvus(field)
			if err != nil {
				return nil, stderr.Wrap(err, "convert milvus result column")
			}
			retResult.Fields = append(retResult.Fields, fcd)
		}
		idc, err := NewFieldColumnDataFromMilvus(searchResult.IDs)
		if err != nil {
			return nil, stderr.Wrap(err, "convert milvus result id column")
		}
		retResult.IDs = utils.MustCvtAny2TSlice[int64](idc.Data)
		ret.Results[i] = retResult
	}
	return ret, nil
}
