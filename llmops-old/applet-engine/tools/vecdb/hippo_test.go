package vecdb

import (
	"context"
	"testing"
)

var c *HippoClient

func init() {

	mc, err := NewHippoDBCli(context.Background(), VectorDBConfig{
		ClientID:     "appengine-test",
		Address:      "***********:7788",
		UserName:     "shiva",
		UserPassword: "shiva",
		Database:     "default",
		Table:        "test",
		Partition:    "",
		Dimension:    1024,
		MetricType:   "IP",
		IndexType:    "FLAT",
	})
	if err != nil {
		panic(err)
	}
	c = mc
}
func TestHippoClient_getTable(t *testing.T) {
	tests := []struct {
		name     string
		c        *HippoClient
		database string
		table    string
		want     string
	}{
		{
			name:     "",
			c:        c,
			database: "default",
			table:    "default",
			want:     "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client := c
			got, err := client.ListDatabases(context.Background())
			if err != nil {
				t.Fatal(err)
			}

			t.Logf("tables: %+v", got.Databases)
		})
	}
}
