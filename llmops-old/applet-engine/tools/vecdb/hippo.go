package vecdb

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"path"
	"runtime"
	"strings"
	"time"

	"github.com/go-http-utils/headers"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
	"transwarp.io/applied-ai/applet-engine/models"
)

const (
	hippoRestfulHttpSchema     = "http"
	hippoRestfulHttpBasePath   = "hippo/v1/"
	hippoDefaultIndexName      = "ivf_flat_index"
	hippoDefaultIndexOpTimeout = "2m"
	databasePath               = "_database?pretty"
	applicationJson            = "application/json"
)

type DataType string

const (
	Int8        DataType = "int8"
	Int16       DataType = "int16"
	Int32       DataType = "int32"
	Int64       DataType = "int64"
	Float       DataType = "float"
	Double      DataType = "double"
	Bool        DataType = "bool"
	String      DataType = "string"
	Char        DataType = "char"
	Varchar     DataType = "varchar"
	Varchar2    DataType = "varchar2"
	Date        DataType = "date"
	Timestamp   DataType = "timestamp"
	Datetime    DataType = "datatime"
	Binary      DataType = "binary"
	Blob        DataType = "blob"
	Clob        DataType = "clob"
	FloatVector DataType = "float_vector"
)

// HippoIndexType 以 Hippo 中支持的为准
// 索引			类型							适用场景
// FLAT										适用于小数据集、召回率100%
// IVF_FLAT		Quantization-based Index	查询性能较高、召回率高
// IVF_SQ		Quantization-based Index	查询性能较高、内存开销低、召回率较高
// IVF_PQ		Quantization-based Index	查询性能高、内存开销低、召回率低于IVF_SQ
// IVF_PQ_FS	Quantization-based Index	查询性能高、内存开销低、召回率低于IVF_SQ
// HNSW			Graph-based Index			查询性能高、召回率高、内存开销大
type HippoIndexType string

const (
	Flat    HippoIndexType = "FLAT"
	IvfFlat HippoIndexType = "IVF_FLAT"
	IvfSq   HippoIndexType = "IVF_SQ"
	IvfPq   HippoIndexType = "IVF_PQ"
	IvfPqFs HippoIndexType = "IVF_PQ_FS"
	HNSW    HippoIndexType = "HNSW"

	defaultIndexType = IvfFlat
	defaultTimeout   = time.Second * 10
)

type MetricType string

const (
	MetricTypeL2      MetricType = "L2"
	MetricTypeIP      MetricType = "IP"
	defaultMetricType            = MetricTypeIP
)

type HippoClient struct {
	host     string
	username string
	password string
	baseUrl  string

	hc     http.Client
	cfg    VectorDBConfig
	router HippoRouter
}

func (c *HippoClient) defaultEmbeddingIndex() *Index {
	return &Index{
		FieldName:  VectorDBFieldVector,
		IndexName:  hippoDefaultIndexName,
		MetricType: defaultMetricType,
		IndexType:  defaultIndexType,
		Params: map[string]any{
			"nlist": 10,
		},
	}
}

func (c *HippoClient) defaultTableSchema(dim int) TableSchema {
	return TableSchema{
		AutoId: false,
		Fields: []TableField{
			{
				Name:         VectorDBFieldID,
				IsPrimaryKey: true,
				DataType:     Int64,
			}, {
				Name:     VectorDBFieldText,
				DataType: String,
			}, {
				Name:     VectorDBFieldTitle,
				DataType: String,
			}, {
				Name:     VectorDBFieldVector,
				DataType: FloatVector,
				TypeParams: &FieldTypeParams{
					Dimension: dim,
				},
			},
		},
	}
}
func (c *HippoClient) Close() {
	return
}

type BulkOpType string

const (
	BulkOpInsert BulkOpType = "insert"
	BulkOpDelete BulkOpType = "delete"
	BulkOpUpdate BulkOpType = "update"
)

func (c *HippoClient) Insert(ctx context.Context, columns ...FieldColumnData) ([]int64, error) {
	if len(columns) == 0 {
		return nil, stderr.InvalidParam.Error("no columns to insert")
	}
	rows := len(columns[0].Data)
	req := &BulkOperation{
		FieldsData: nil,
		NumRows:    rows,
		OpType:     BulkOpInsert,
	}
	// 目前AutoID不支持 int64 类型
	ids := &HippoFieldData{
		FieldName: VectorDBFieldID,
		Field:     make([]any, rows),
	}
	baseID := time.Now().UnixNano()
	for i := range ids.Field {
		ids.Field[i] = baseID + int64(i)
	}
	req.FieldsData = append(req.FieldsData, ids)

	for _, col := range columns {
		hfd, err := col.ToHippoFieldData()
		if err != nil {
			return nil, stderr.Wrap(err, "covert to hippo field data")
		}
		req.FieldsData = append(req.FieldsData, hfd)
		if rows != req.NumRows {
			return nil, stderr.Wrap(err, "field data has difference length")
		}
	}

	rsp := new(BulkOperationRsp)
	err := c.doRequest(ctx, c.router.Bulk.PUT(c.cfg.Table), req, rsp)
	if err != nil {
		return nil, stderr.Trace(err)
	}
	if len(rsp.SuccIndex) != req.NumRows {
		return nil, stderr.Internal.Error("some rows ins")
	}
	return utils.MustCvtAny2TSlice[int64](ids.Field), nil
}

type HippoQueryReq struct {
	OutputFields []string `json:"output_fields"`
	Expr         string   `json:"expr"`
	Limit        int      `json:"limit"`
}

type HippoSearchReq struct {
	OutputFields []string          `json:"output_fields"` // 必填，搜索结果返回的列
	SearchParams HippoSearchParams `json:"search_params"` // 必填，搜索参数
	Vectors      [][]float32       `json:"vectors"`       // 必填，搜索向量
	DSL          string            `json:"dsl"`           // 选填，标量过滤条件,"word_count >= 11000"
}

type HippoSearchParams struct {
	AnnsField      string         `json:"anns_field"`      // 必填，搜索的向量列名
	Topk           int            `json:"topk"`            // 必填，搜索返回结果数
	Params         map[string]any `json:"params"`          // 选填，向量索引相关搜索参数
	EmbeddingIndex string         `json:"embedding_index"` // 选填，本次搜索使用的向量索引，默认使用第一个已激活且对anns_field创建的向量索引
}

type HippoSearchRsp struct {
	NumQueries int                 `json:"num_queries"`
	TopK       int                 `json:"top_k"`
	Results    []HippoSearchResult `json:"results"`
}

type HippoSearchResult struct {
	Query      int                     `json:"query"`       // 请求ID
	FieldsData []*HippoSearchFieldData `json:"fields_data"` // 各字段返回值
	Scores     []float64               `json:"scores"`
}

func (r *HippoSearchResult) GetFieldDataByName(name string) (*HippoSearchFieldData, bool) {
	if r == nil {
		return nil, false
	}
	for _, field := range r.FieldsData {
		if field.FieldName == name {
			return field, true
		}
	}
	return nil, false
}

type HippoSearchFieldData struct {
	FieldName   string `json:"field_name"`   // 字段名
	FieldValues []any  `json:"field_values"` // 字段各行的值
}

func (c *HippoClient) Search(ctx context.Context, params *SearchParams) (*SearchResult, error) {
	if params == nil {
		return nil, stderr.InvalidParam.Error("cannot search without params")
	}
	req := &HippoSearchReq{
		OutputFields: params.OutputFields,
		SearchParams: HippoSearchParams{
			AnnsField:      VectorDBFieldVector,
			Topk:           params.TopK,
			Params:         params.Params,
			EmbeddingIndex: c.cfg.IndexName,
		},
		Vectors: params.Vectors,
		DSL:     params.Expr,
	}
	rsp := new(HippoSearchRsp)
	err := c.doRequest(ctx, c.router.Search.GET(c.cfg.Database, c.cfg.Table), req, rsp)
	if err != nil {
		return nil, stderr.Trace(err)
	}
	res := &SearchResult{
		Queries: rsp.NumQueries,
		TopK:    rsp.TopK,
		Results: nil,
	}
	for _, hr := range rsp.Results {
		item := SearchResultItem{
			Query:       0,
			ResultCount: len(hr.FieldsData),
			IDs:         nil,
			Fields:      nil,
			Scores:      models.CvtFloatSlice[float64, float32](hr.Scores),
		}
		for _, field := range hr.FieldsData {
			item.Fields = append(item.Fields, &FieldColumnData{
				FieldName: field.FieldName,
				Data:      field.FieldValues,
			})
		}
		_, ok := hr.GetFieldDataByName(VectorDBFieldID)
		if !ok {
			stdlog.Warnf("no ids column found in hippo rsp")
		} else {
			// item.IDs = MustCvtAny2TSlice[int64](idC.FieldValues)
		}

		res.Results = append(res.Results, item)
	}
	return res, nil
}

func (c *HippoClient) GetClientVersion() string {
	return "Transwarp Hippo v1.1.0-final"
}

func NewHippoDBCli(ctx context.Context, cfg VectorDBConfig) (*HippoClient, error) {
	if err := cfg.ValidAndSetDefault(); err != nil {
		return nil, stderr.Wrap(err, "invalid config")
	}
	// hippo index name 不能为空
	if cfg.IndexName == "" {
		cfg.IndexName = hippoDefaultIndexName
	}
	br := baseRouteBuilder{
		Host:     cfg.Address,
		Schema:   hippoRestfulHttpSchema,
		BasePath: hippoRestfulHttpBasePath,
		QueryParams: map[string]string{
			HippoQueryParamPretty: "", // 默认响应时格式化
		},
	}
	router := NewHippoRouter(br)
	hc := &HippoClient{
		host:     cfg.Address,
		username: cfg.UserName,
		password: cfg.UserPassword,
		baseUrl:  "",
		hc: http.Client{
			Timeout: defaultTimeout,
		},
		cfg:    cfg,
		router: router,
	}

	// check table existence
	hasTable, _ := hc.HasTable(ctx, cfg.Database, cfg.Table)
	// if err != nil {
	// 	return nil, stderr.Wrap(err, "checking the existence of table %s of %s", cfg.Table, cfg.Database)
	// }
	if !hasTable {
		stdlog.Infof("table '%s' not exist, auto creating it", cfg.Table)
		err := hc.CreateTable(ctx, &CreateTableReq{
			Settings: TableSettings{
				NumberOfShards:   1,
				NumberOfReplicas: 1,
			},
			Schema:    hc.defaultTableSchema(int(cfg.Dimension)),
			TableName: cfg.Table,
			Database:  cfg.Database,
		})
		if err != nil {
			return nil, stderr.Internal.Cause(err, "create table")
		}
		if _, err := hc.CreateIndex(ctx, cfg.Database, cfg.Table, hc.defaultEmbeddingIndex()); err != nil {
			return nil, stderr.Wrap(err, "create hippo embedding index")
		}
	}
	tb, err := hc.GetTable(ctx, cfg.Database, cfg.Table)
	if err != nil {
		return nil, stderr.Wrap(err, "table not exist")
	}
	fields := []string{VectorDBFieldVector, VectorDBFieldTitle, VectorDBFieldID, VectorDBFieldText}
	expFields := utils.NewSet(fields...)
	for _, field := range tb.Schema.Fields {
		expFields.Remove(field.Name)
	}
	if expFields.Count() != 0 {
		return nil, stderr.InvalidParam.Error("following fields in table are necessary: %+v", fields)
	}

	if _, err = hc.LoadIndex(ctx, cfg.Database, cfg.Table, nil); err != nil {
		return nil, stderr.Wrap(err, "load hippo index")
	}
	if _, err = hc.ActivateIndex(ctx, cfg.Database, cfg.Table, nil); err != nil {
		return nil, stderr.Wrap(err, "activate hippo index")
	}
	return hc, nil
}

type Index struct {
	FieldName  string         `json:"field_name"`
	IndexName  string         `json:"index_name"`
	MetricType MetricType     `json:"metric_type"`
	IndexType  HippoIndexType `json:"index_type"`
	Params     map[string]any `json:"params"`
}

func doRequest[T any](c *HippoClient, ctx context.Context, route Route, req any, rsp T) (T, error) {
	err := c.doRequest(ctx, route, req, rsp)
	return rsp, err
}

func (c *HippoClient) doRequest(ctx context.Context, route Route, req any, rsp any) (err error) {
	// wrap error
	pc, _, _, _ := runtime.Caller(1) // 0 表示当前函数调用栈帧
	caller := runtime.FuncForPC(pc).Name()
	if caller != "" {
		parts := strings.Split(caller, ".")
		caller = parts[len(parts)-1]
	}
	stdlog.Infof("[Hippo:%s]%s %s", caller, route.Method(), route.Url())
	defer func() {
		if err != nil {
			err = stderr.Wrap(err, caller)
		}
	}()

	// parse request to io.Reader
	if route.Method() == "" || route.Url() == "" {
		return stderr.InvalidParam.Error("method and url cannot be empty")
	}
	var bodyR io.Reader
	if req != nil {
		bodyBs, err := json.Marshal(req)
		if err != nil {
			return stderr.Wrap(err, "do hippo request: marshal request: %+v", req)
		}
		bodyR = bytes.NewReader(bodyBs)
	}

	// build request
	httpReq, err := http.NewRequest(route.Method(), route.Url(), bodyR)
	if err != nil {
		return stderr.Internal.Cause(err, "new http request")
	}
	httpReq.WithContext(ctx)
	httpReq.Header.Add(headers.ContentType, applicationJson)
	if c.username != "" || c.password != "" {
		httpReq.SetBasicAuth(c.username, c.password)
	}

	// do request and handle response or error
	httpRsp, err := c.hc.Do(httpReq)
	if err != nil {
		return stderr.Wrap(err, "do hippo request: %s %s", route.Method(), route.Url())
	}
	defer func() { _ = httpRsp.Body.Close() }()
	content, err := io.ReadAll(httpRsp.Body)
	if httpRsp.StatusCode != http.StatusOK {
		return stderr.Internal.Error(httpRsp.Status + string(content))
	}
	if rsp == nil {
		return stderr.InvalidParam.Error("must provided a response receiver pointer")
	}
	if err = json.Unmarshal(content, rsp); err != nil {
		return stderr.Unmarshal.Cause(err, "unmatched between giving rsp receiver and rsp content")
	}
	return nil
}

func (c *HippoClient) requestUrl(ctx context.Context, method, url string, body io.Reader) string {

	// request
	req, err := http.NewRequest(method, url, body)
	req.Header.Add(headers.ContentType, applicationJson)
	if c.username != "" || c.password != "" {
		req.SetBasicAuth(c.username, c.password)
	}

	if err != nil {
		println(err)
	}

	// response
	response, err := http.DefaultClient.Do(req)
	if err != nil {
		println(err)
	}
	defer response.Body.Close()

	// read response
	res, err := io.ReadAll(response.Body)
	if err != nil {
		println(err)
	}
	return string(res)
}

type CreateDBReq struct {
	DatabaseName string `json:"database_name"`
}
type CreateDBRsp struct {
	Acknowledged bool `json:"acknowledged"`
}

func (c *HippoClient) CreateDatabase(ctx context.Context, database string) (*CreateDBRsp, error) {
	req := &CreateDBReq{
		DatabaseName: database,
	}

	rsp := new(CreateDBRsp)
	err := c.doRequest(ctx, c.router.DataBase.Create(), req, rsp)
	if err != nil {
		return nil, stderr.Trace(err)
	}
	return rsp, nil
}

type ListDatabaseRsp struct {
	Databases []struct {
		Name       string `json:"name"`
		CreateTime string `json:"create_time"`
	} `json:"databases"`
}

func (c *HippoClient) ListDatabases(ctx context.Context, ) (*ListDatabaseRsp, error) {
	rsp := new(ListDatabaseRsp)
	err := c.doRequest(ctx, c.router.DataBase.List(), nil, rsp)
	if err != nil {
		return nil, stderr.Trace(err)
	}
	return rsp, nil
}

type CreateTableReq struct {
	Settings  TableSettings `json:"settings"`
	Schema    TableSchema   `json:"schema"`
	TableName string        `json:"table_name"`
	Database  string        `json:"database"`
}

type TableDescription struct {
	Id               string           `json:"id"`
	Database         string           `json:"database"`
	Settings         TableSettings    `json:"settings"`
	Schema           TableSchema      `json:"schema"`
	EmbeddingIndexes []EmbeddingIndex `json:"embedding_indexes"`
}

type TableSettings struct {
	NumberOfShards       int    `json:"number_of_shards,omitempty"`
	CreationDate         string `json:"creation_date,omitempty"`
	NumberOfReplicas     int    `json:"number_of_replicas,omitempty"`
	DataCenter           string `json:"data_center,omitempty"`
	DcAffinity           bool   `json:"dc_affinity,omitempty"`
	DisasterPreparedness bool   `json:"disaster_preparedness,omitempty"`
	ScatterReplica       bool   `json:"scatter_replica,omitempty"`
}

type TableSchema struct {
	// AutoId 是否启用自生成ID，当前Hippo只允许为单Primary Key，
	// 且Primary Key为String类型的Schema启用自生成ID
	AutoId bool         `json:"auto_id,omitempty"`
	Fields []TableField `json:"fields,omitempty"`
}

type TableField struct {
	Id           int              `json:"id,omitempty"`
	Name         string           `json:"name,omitempty"`
	IsPrimaryKey bool             `json:"is_primary_key,omitempty"`
	IsNullable   bool             `json:"is_nullable,omitempty"`
	DataType     DataType         `json:"data_type,omitempty"`
	TypeParams   *FieldTypeParams `json:"type_params,omitempty"`
}

type FieldTypeParams struct {
	Dimension int `json:"dimension,omitempty"` // 向量维度	[1, 65536]
	Length    int `json:"length,omitempty"`    // varchar / varchar2类型，长度	[1, 65536]
}

func (r *CreateTableReq) FromMilvusSchema() {

}

func (c *HippoClient) CreateTable(ctx context.Context, req *CreateTableReq) error {
	rsp := new(AckRsp)
	err := c.doRequest(ctx, c.router.Table.Create(req.Database, req.TableName), req, rsp)
	if err != nil {
		return stderr.Trace(err)
	}
	if !rsp.Acknowledged {
		return stderr.Internal.Error("create table not acknowledged")
	}
	return nil
}

type HasTableReq struct {
	DatabaseName string `json:"database_name"`
	TableName    string `json:"table_name"`
}

// AckRsp 通用的确认响应
type AckRsp struct {
	Acknowledged bool `json:"acknowledged"`
}

func (c *HippoClient) HasTable(ctx context.Context, database, table string) (bool, error) {
	req := &HasTableReq{
		DatabaseName: database,
		TableName:    table,
	}
	rsp := new(AckRsp)
	err := c.doRequest(ctx, c.router.Table.Exist(), req, rsp)
	if err != nil {
		// 不存在时返回 HTTP 404
		return false, err
	}
	// 已存在返回 HTTP 200
	// {
	// 	"acknowledged": true
	// }
	return rsp.Acknowledged, nil
}

type DescribeTableRsp map[string]*TableDescription

type EmbeddingIndex struct {
	Name       string         `json:"name"`
	Id         int            `json:"id"`
	Column     string         `json:"column"`
	IndexType  string         `json:"index_type"`
	MetricType string         `json:"metric_type"`
	Params     map[string]any `json:"params"`
}

func (c *HippoClient) GetTable(ctx context.Context, database, table string) (*TableDescription, error) {
	rsp := new(DescribeTableRsp)
	err := c.doRequest(ctx, c.router.Table.Get(database, table), nil, rsp)
	if err != nil {
		return nil, stderr.Trace(err)
	}
	description, ok := (*rsp)[table]
	if !ok || description == nil {
		return nil, stderr.Internal.Error("the description of table %s not found", table)
	}

	return description, nil
}

func (c *HippoClient) InsertRows(ctx context.Context, database, table string, fields [][]interface{}) error {
	return c.operateData(ctx, database, table, "insert", fields)
}

func (c *HippoClient) InsertRows2(ctx context.Context, database, table string, fields [][]interface{}) error {
	return c.operateData(ctx, database, table, "insert", fields)
}

func (c *HippoClient) DeleteRows(ctx context.Context, database, table string, fields [][]interface{}) error {
	return c.operateData(ctx, database, table, "delete", fields)
}

func (c *HippoClient) UpdateRows(ctx context.Context, database, table string, fields [][]interface{}) error {
	return c.operateData(ctx, database, table, "update", fields)
}

type BulkOperation struct {
	FieldsData []*HippoFieldData `json:"fields_data"`
	NumRows    int               `json:"num_rows"`
	OpType     BulkOpType        `json:"op_type"`
}

type HippoFieldData struct {
	FieldName string `json:"field_name"`
	Field     []any  `json:"field"`
}

type BulkOperationRsp struct {
	SuccIndex []int `json:"succ_index"`
	InsertCnt int   `json:"insert_cnt"`
	DeleteCnt int   `json:"delete_cnt"`
	UpdateCnt int   `json:"update_cnt"`
}
type IndexOperationCfg struct {
	IndexName         string `json:"index_name"`
	WaitForCompletion bool   `json:"wait_for_completion"`
	Timeout           string `json:"timeout"`
}

func (c *HippoClient) operateData(ctx context.Context, database, table, operation string, data [][]interface{}) error {
	// // url
	// if database == "" {
	// 	database = "default"
	// }
	// operationUrl := c.baseUrl + "{" + table + "}/_bulk?database_name=" + database + "&pretty"
	//
	// // get table schema
	// allTableInfo := c.GetTable(ctx, database, table)
	// var allTableMap map[string]interface{}
	// err := json.Unmarshal([]byte(allTableInfo), &allTableMap)
	// if err != nil {
	// 	println(err)
	// }
	// fieldMap := allTableMap[table].(map[string]interface{})["schema"].(map[string]interface{})["fields"].([]interface{})
	// fieldNames := make([]string, 0)
	// for _, f := range fieldMap {
	// 	name := f.(map[string]interface{})["name"].(string)
	// 	fieldNames = append(fieldNames, name)
	// }
	// lenOfFields := len(fieldNames)
	// lenOfData := len(data)
	// if lenOfData != lenOfFields {
	// 	return fmt.Errorf("to insert data columns(%d) does not match cols in schema(%d)", lenOfData, lenOfFields)
	// }
	//
	// // build body
	// fieldData := make([]map[string]interface{}, 0)
	// numOfRows := -1
	// for i, f := range fieldNames {
	// 	field := make(map[string]interface{})
	// 	field["field_name"] = f
	// 	field["field"] = data[i]
	// 	lenOfRows := len(data[i])
	// 	if numOfRows != -1 && numOfRows != lenOfRows {
	// 		return fmt.Errorf("to insert data column rows {%d} does not match previous row number {%d}", lenOfRows, numOfRows)
	// 	}
	// 	numOfRows = lenOfRows
	// 	fieldData = append(fieldData, field)
	// }
	// bodyParams := make(map[string]interface{})
	// bodyParams["fields_data"] = fieldData
	// bodyParams["num_rows"] = numOfRows
	// bodyParams["op_type"] = operation
	// bodyJson, err := json.Marshal(bodyParams)
	// if err != nil {
	// 	println(err)
	// }
	// body := strings.NewReader(string(bodyJson))
	//
	// c.requestUrl(ctx, PUT, operationUrl, body)
	return nil
}

func (c *HippoClient) CreateIndex(ctx context.Context, db, table string, idx *Index) (*AckRsp, error) {
	if idx == nil {
		return nil, stderr.InvalidParam.Error("creat index with nil")
	}
	rsp := new(AckRsp)
	err := c.doRequest(ctx, c.router.Index.Create(db, table), idx, rsp)
	if err != nil {
		return nil, stderr.Trace(err)
	}
	return rsp, nil
}

func defaultIndexOperationCfg() *IndexOperationCfg {
	return &IndexOperationCfg{
		IndexName:         hippoDefaultIndexName,
		WaitForCompletion: true,
		Timeout:           hippoDefaultIndexOpTimeout,
	}
}

type HippoJobOpRsp struct {
	JobId           string            `json:"job_id"`
	JobStatus       string            `json:"job_status"`
	EmbeddingNumber int               `json:"embedding_number"`
	TaskResults     []HippoTaskResult `json:"task_results"`
}
type HippoTaskResult struct {
	Id              string  `json:"id"`
	Status          string  `json:"status"`
	Server          string  `json:"server"`
	EmbeddingNumber int     `json:"embedding_number"`
	ExecuteTime     float64 `json:"execute_time"`
}

func (c *HippoClient) ActivateIndex(ctx context.Context, db, table string, cfg *IndexOperationCfg) (*HippoJobOpRsp, error) {
	return c.operateIndex(ctx, db, table, indexOpActivate, cfg)
}
func (c *HippoClient) ReleaseIndex(ctx context.Context, db, table string, cfg *IndexOperationCfg) (*HippoJobOpRsp, error) {
	return c.operateIndex(ctx, db, table, indexOpRelease, cfg)
}
func (c *HippoClient) LoadIndex(ctx context.Context, db, table string, cfg *IndexOperationCfg) (*HippoJobOpRsp, error) {
	return c.operateIndex(ctx, db, table, indexOpLoad, cfg)
}
func (c *HippoClient) CompactIndex(ctx context.Context, db, table string, cfg *IndexOperationCfg) (*HippoJobOpRsp, error) {
	return c.operateIndex(ctx, db, table, indexOpCompact, cfg)
}
func (c *HippoClient) operateIndex(ctx context.Context, db, table string, op HippoIndexOp, cfg *IndexOperationCfg) (*HippoJobOpRsp, error) {
	if cfg == nil {
		cfg = defaultIndexOperationCfg()
	}
	ir := c.router.Index
	var route Route
	switch op {
	case indexOpRelease:
		route = ir.Release(db, table)
	case indexOpActivate:
		route = ir.Activate(db, table)
	case indexOpLoad:
		route = ir.Load(db, table)
	case indexOpCompact:
		route = ir.Compact(db, table)
	default:
		return nil, stderr.InvalidParam.Error("unsupported index op %s", op)
	}
	rsp := new(HippoJobOpRsp)
	err := c.doRequest(ctx, route, cfg, rsp)
	if err != nil {
		return nil, stderr.Trace(err)
	}
	return rsp, err
}

func (c *HippoClient) Search2(ctx context.Context, database, table, searchField, dsl string,
		outputFields []string, topk int, vectors [][]float64, metricType MetricType, params map[string]int) string {
	// url
	if database == "" {
		database = "default"
	}
	searchUrl := c.baseUrl + table + "/_search?database_name=" + database + "&pretty"

	// params
	searchParams := make(map[string]interface{})
	searchParams["anns_field"] = searchField
	searchParams["topk"] = topk
	searchParams["metric_type"] = metricType
	searchParams["params"] = params
	bodyParams := make(map[string]interface{})
	bodyParams["output_fields"] = outputFields
	bodyParams["vectors"] = vectors
	bodyParams["search_params"] = searchParams
	if dsl != "" {
		bodyParams["dsl"] = dsl
	}
	bodyJson, err := json.Marshal(bodyParams)
	if err != nil {
		println(err)
	}
	body := strings.NewReader(string(bodyJson))

	return c.requestUrl(ctx, http.MethodGet, searchUrl, body)
}

func (c *HippoClient) GetIndex(ctx context.Context, database string, table string) string {
	// url
	if database == "" {
		database = "default"
	}
	getIndexUrl := c.baseUrl + table + "/_get_embedding_index?database_name=" + database + "&pretty"

	// request
	return c.requestUrl(ctx, http.MethodGet, getIndexUrl, nil)
}

type HippoQueryParam = string

const (
	HippoQueryParamDB     HippoQueryParam = "database_name"
	HippoQueryParamPretty HippoQueryParam = "pretty"
)

type Route struct {
	method string
	url    string
}

func (r *Route) Method() string {
	return r.method
}
func (r *Route) Url() string {
	return r.url
}

type baseRouteBuilder struct {
	Schema      string
	Host        string
	BasePath    string
	QueryParams map[string]string
}

func (b baseRouteBuilder) NewRoute(method string, route string, qps ...string) Route {
	kvs := make(map[string][]string, 0)
	for key, param := range b.QueryParams {
		kvs[key] = []string{param}
	}
	if len(qps)&1 == 1 {
		stdlog.Warnf("odd number query parameters and values will be ignored: %+v", qps)
	} else {
		for i := 0; i < len(qps); i += 2 {
			kvs[qps[i]] = append(kvs[qps[i]], qps[i+1])
		}
	}

	query := url.Values(kvs).Encode()
	return Route{
		method: method,
		url:    fmt.Sprintf("%s://%s/%s?%s", b.Schema, b.Host, path.Join(b.BasePath, route), query),
	}
}

// HippoRouter 用于构建以下常用的 Hippo Restful API 的Method与URL
// //
// // 数据库操作
// PUT '/_database?pretty'
// GET '/_database?pretty'
// DELETE '/_database?pretty'
//
//
// 表操作
// 基础操作
// PUT '/{table}?database_name={database_name}&pretty'
// GET '/{table}?database_name={database_name}&pretty'
// DELETE '/{table}?database_name={database_name}'
//
// 表其他操作
// PUT '/_rename_table?pretty'
// GET '/_check_table_existence?pretty'
// 表详情
// GET '/_cat/tables/{table}?v'
// GET '/_cat/tables?v'
// GET '/_cat/shards/{table}?v'
// GET '/_cat/shards?v'
// 表配置
// GET '/_settings?pretty'
// GET '/{table}/_settings?pretty'
// GET '/{table}/_setting?pretty'
// PUT '/{table}/_settings?pretty'
// // 表别名
// POST '/_aliases?database_name={database_name}&pretty'
// GET '/_cat/aliases?v'
// DELETE '/_aliases/{alias}?pretty'
//
// // 写入数据
// PUT '/{table}/_bulk?pretty'
// POST '/_copy_by_query?pretty'
// POST '/_delete_by_query?pretty'
// POST '/{table}/_compact_db?database_name={database_name}&pretty'
// POST '/{table}/_warmup_db?database_name={database_name}&pretty'
// POST 'localhost:7788/_drop_block_cache?pretty'
// POST 'localhost:7788/_drop_block_cache/{node}?pretty&drop_index_block_cache=true'
// POST '/_standalone_load_data?database_name={database_name}&pretty'
// POST '/_standalone_export_data?pretty'
// GET '/_get_standalone_job/{job_id}?pretty&delete_finished_jobs=false'
//
// // 向量索引操作
// PUT '/{table}/_create_embedding_index?database_name={database_name}&pretty'
// POST '/{table}/_activate_embedding_index?database_name={database_name}&pretty'
// POST '/{table}/_release_embedding_index?database_name={database_name}&pretty'
// POST '/{table}/_load_embedding_index?database_name={database_name}&pretty'
// GET '/{table}/_get_embedding_index?database_name={database_name}&pretty'
// DELETE '/{table}/_drop_embedding_index?database_name={database_name}&pretty'
// POST ' /{table}/_embedding_index_auto_compaction?database_name={database_name}&pretty'
// POST '/{table}/_compact_embedding_index?database_name={database_name}&pretty'
//
// // 标量索引操作
// PUT '/{table}/_create_scalar_index?database_name={database_name}&pretty'
// DELETE '/{table}/_drop_scalar_index?database_name={database_name}&pretty'
//
// 查询
// 纯向量
// GET '/{table}/_search?database_name={database_name}&pretty'
//
// 标量混合
// GET '/{table}/_query?database_name={database_name}&pretty'
//
// PUT '/book?database_name={database_name}&pretty'
// PUT '/book/_create_scalar_index?database_name={database_name}&pretty'
// PUT '/book/_bulk?pretty' -H'Content-Type: application/json'
// GET 'localhost:9200/book/_search?pretty' -H'Content-Type: application/json'
// GET '/{table}/_count?database_name={database_name}&pretty'
// GET '/{table}/_count?database_name={database_name}&pretty' -
// GET '/_cat/master?v'
// GET '/_cat/nodes?v'
// GET '/_cat/nodes/{node}?v'
//
// 用户相关
// PUT /_security/user/{user_name}?pretty
// GET /_security/user/{user_name}?pretty
// DELETE /_security/user/{user_name}?pretty
// POST /_security/user/{user_name}/_alter?pretty
// POST "/_security/acl/{user_name}?pretty"
// DELETE "/_security/acl/{user_name}?pretty"
// GET "/_security/user/_privileges/{username}?pretty"
// GET "/_security/tables/{table}?pretty"
// GET "/_jobs?pretty"
// DELETE "/_jobs/{jod_id}?pretty"
// GET '/_cat/trash?database_name=book&v'
// DELETE '/_trash/{table}?database_name=book&pretty'
type HippoRouter struct {
	DataBase DatabaseRouter
	Table    TableRouter
	Bulk     BulkRouter
	Search   SearchRouter
	Index    IndexRouter
}
type DatabaseRouter struct {
	baseRouteBuilder
}
type TableRouter struct{ baseRouteBuilder }
type BulkRouter struct{ baseRouteBuilder }
type SearchRouter struct{ baseRouteBuilder }
type IndexRouter struct{ baseRouteBuilder }

func NewHippoRouter(b baseRouteBuilder) HippoRouter {
	return HippoRouter{
		DataBase: DatabaseRouter{baseRouteBuilder: b},
		Table:    TableRouter{baseRouteBuilder: b},
		Bulk:     BulkRouter{baseRouteBuilder: b},
		Search:   SearchRouter{baseRouteBuilder: b},
		Index:    IndexRouter{baseRouteBuilder: b},
	}
}

func (d *DatabaseRouter) Create() Route {
	return d.NewRoute(http.MethodPut, "_database")
}
func (d *DatabaseRouter) Delete() Route {
	return d.NewRoute(http.MethodDelete, "_database")
}
func (d *DatabaseRouter) List() Route {
	return d.NewRoute(http.MethodGet, "_database")
}

func (d *TableRouter) Exist() Route {
	return d.NewRoute(http.MethodGet, "_check_table_existence")
}
func (d *TableRouter) Create(db, table string) Route {
	return d.NewRoute(http.MethodPut, table, HippoQueryParamDB, db)
}
func (d *TableRouter) Get(db, table string) Route {
	return d.NewRoute(http.MethodGet, table, HippoQueryParamDB, db)
}

func (d *BulkRouter) PUT(table string) Route {
	return d.NewRoute(http.MethodPut, fmt.Sprintf("%s/_bulk", table))
}

func (d *SearchRouter) GET(db, table string) Route {
	return d.NewRoute(http.MethodGet, fmt.Sprintf("%s/_search", table), HippoQueryParamDB, db)
}

type HippoIndexOp string

const (
	indexOpGet    HippoIndexOp = "get"
	indexOpCreate HippoIndexOp = "create"
	indexOpDrop   HippoIndexOp = "drop"

	indexOpRelease  HippoIndexOp = "release"
	indexOpActivate HippoIndexOp = "activate"
	indexOpLoad     HippoIndexOp = "load"
	indexOpCompact  HippoIndexOp = "compact"
)

func (d *IndexRouter) Get(db, table string) Route {
	return d.base(db, table, "get", http.MethodGet)
}
func (d *IndexRouter) Create(db, table string) Route {
	return d.base(db, table, "create", http.MethodPut)
}
func (d *IndexRouter) Drop(db, table string) Route {
	return d.base(db, table, "drop", http.MethodPost)
}

func (d *IndexRouter) Activate(db, table string) Route {
	return d.base(db, table, "activate", http.MethodPost)
}
func (d *IndexRouter) Release(db, table string) Route {
	return d.base(db, table, "release", http.MethodPost)
}
func (d *IndexRouter) Load(db, table string) Route {
	return d.base(db, table, "load", http.MethodPost)
}
func (d *IndexRouter) Compact(db, table string) Route {
	return d.base(db, table, "compact", http.MethodPost)
}

func (d *IndexRouter) base(db, table, action, method string) Route {
	return d.NewRoute(method, fmt.Sprintf("%s/_%s_embedding_index", table, action), HippoQueryParamDB, db)
}
