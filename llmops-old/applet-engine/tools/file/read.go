package file

import (
	"bytes"
	"os"

	"code.sajari.com/docconv"
	"github.com/ledongthuc/pdf"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

const (
	DOCX = ".docx"
	PDF  = ".pdf"
	TXT  = ".txt"
	CSV  = ".csv"
	MD   = ".md"
)

func ReadDOCXFileContent(path string) (string, error) {
	res, err := docconv.ConvertPath(path)
	if err != nil {
		return "", stderr.Wrap(err, "get file %s text", path)
	}
	return res.Body, nil
}

func ReadPDFFileContent(path string) (string, error) {
	pdfFile, pdfReader, err := pdf.Open(path)
	if err != nil {
		return "", stderr.Wrap(err, "open pdf file %s", path)
	}
	defer pdfFile.Close()
	ioReader, err := pdfReader.GetPlainText()
	if err != nil {
		return "", stderr.Wrap(err, "get pdf plain text")
	}
	var buffer bytes.Buffer
	if _, err := buffer.ReadFrom(ioReader); err != nil {
		return "", stderr.Wrap(err, "read pdf text to bytes buffer")
	}
	return buffer.String(), nil
}

func ReadTextualFileContent(path string) (string, error) {
	bs, err := os.ReadFile(path)
	if err != nil {
		return "", stderr.Wrap(err, "read file %s", path)
	}
	return string(bs), nil
}
