package http

import (
	"bytes"
	"context"
	"crypto/tls"
	"io"
	"net/http"
	"time"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

const (
	GET    = "GET"
	POST   = "POST"
	PUT    = "PUT"
	DELETE = "DELETE"
)

const (
	PLAIN_CONTENT_TYPE = "text/plain"
	HTML_CONTENT_TYPE  = "text/html"
	JSON_CONTENT_TYPE  = "application/json"
	SSE_CONTENT_TYPE   = "text/event-stream"
	CONTENT_TYPE       = "Content-Type"
	ACCEPT             = "Accept"
)

const STATUS_CODE_OK = 200
const DEFAULT_TIMEOUT = time.Second * 30

var DefaultHeader = map[string]string{
	CONTENT_TYPE: JSON_CONTENT_TYPE,
}

var InsecureTransport = &http.Transport{
	TLSClientConfig: &tls.Config{
		InsecureSkipVerify: true,
	},
}

var AllowedMethodSet = map[string]bool{
	"GET":    true,
	"POST":   true,
	"PUT":    true,
	"DELETE": true,
}

func SendHttpRequest(method, url string, payload []byte, header map[string]string, ctx context.Context) (io.ReadCloser, http.Header, error) {
	if !AllowedMethodSet[method] {
		return nil, nil, stderr.Internal.Error("invalid http method")
	}
	request, err := http.NewRequestWithContext(ctx, method, url, bytes.NewBuffer(payload))
	if err != nil {
		return nil, nil, stderr.Wrap(err, "create %s request %s", method, url)
	}

	// 将ctx中的 token 写入 request.Header 的 "Authorization"，作为默认值
	tk, err := helper.GetToken(ctx)
	if err != nil {
		return nil, nil, stderr.Wrap(err, "unable to get token")
	}

	if tk != "" {
		request.Header.Add(helper.AuthHeaderKey, tk)
	}

	// 如果上游显式指定了header的"Authorization"，则覆盖掉
	for key, value := range header {
		request.Header.Set(key, value)
	}
	client := &http.Client{
		Transport: InsecureTransport,
	}
	response, err := client.Do(request)
	if err != nil {
		return nil, nil, stderr.Wrap(err, "do %s request %s", method, url)
	}
	if response.StatusCode != STATUS_CODE_OK {
		errMsg, _ := ReadResponseBody(response.Body)
		return nil, nil, stderr.Internal.Error("do %s request %s, response code %d is not ok, responseBody is:\n%s",
			method, url, response.StatusCode, string(errMsg))
	}
	return response.Body, response.Header, nil
}

// ReadResponseBody 读取HTTP响应的正文。
// 参数:
// responseBody io.ReadCloser - 函数返回前会关闭ReadCloser
// 返回值:
// []byte - 从response body中读取到的数据。
// error - 在读取过程中遇到的错误。
func ReadResponseBody(responseBody io.ReadCloser) ([]byte, error) {
	defer responseBody.Close()
	data, err := io.ReadAll(responseBody)
	if err != nil {
		return nil, stderr.Wrap(err, "io read all")
	}
	return data, nil
}
