package kapacitor

import (
	"errors"
	"sync"

	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/pipeline"
	"transwarp.io/applied-ai/applet-engine/server/vars"
	"transwarp.io/applied-ai/applet-engine/tools/expvar"
)

const (
	statCollected = "collected"
	statEmitted   = "emitted"

	defaultEdgeBufferSize = 1000
)

var ErrAborted = errors.New("edged aborted")

type EdgeDiagnostic interface {
	ClosingEdge(collected, emitted int64)
}

type Edge struct {
	edge.StatsEdge

	mu     sync.Mutex
	closed bool

	statsKey string
	statMap  *expvar.Map
	diag     EdgeDiagnostic
}

func newStatsEdge(debug bool, taskName, parentName, childName string, t pipeline.EdgeType, size int) edge.StatsEdge {
	var ed edge.Edge
	if debug {
		ed = edge.NewDebugChannelEdge(taskName, parentName, childName, t, size)
	} else {
		ed = edge.NewChannelEdge(t, size)
	}
	return edge.NewStatsEdge(ed)
}

func newEdge(debug bool, taskName, parentName, childName string, t pipeline.EdgeType, size int, d EdgeDiagnostic) edge.StatsEdge {
	e := newStatsEdge(debug, taskName, parentName, childName, t, size)
	tags := map[string]string{
		"task":   taskName,
		"parent": parentName,
		"child":  childName,
		"type":   t.String(),
	}
	key, sm := vars.NewStatistic("edges", tags)
	sm.Set(statCollected, e.CollectedVar())
	sm.Set(statEmitted, e.EmittedVar())
	return &Edge{
		StatsEdge: e,
		statsKey:  key,
		statMap:   sm,
		diag:      d,
	}
}

func (e *Edge) Close() error {
	e.mu.Lock()
	defer e.mu.Unlock()
	if e.closed {
		return nil
	}
	e.closed = true
	vars.DeleteStatistic(e.statsKey)
	e.diag.ClosingEdge(e.Collected(), e.Emitted())
	return e.StatsEdge.Close()
}
