package kapacitor

import (
	"fmt"
	"time"

	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
	"transwarp.io/applied-ai/applet-engine/services/mqtt"
	"transwarp.io/applied-ai/applet-engine/tools/jsonnet"
	"transwarp.io/applied-ai/applet-engine/tools/keyvalue"
)

type MQTTPubNode struct {
	node
	c          *pipeline.MQTTPubNode
	mc         mqtt.Client
	topic      string
	payload    string
	defaultCli bool // 是否使用了默認的mqtt client
}

// Create a new  MQTTPubNode which submits received items via POST to an HTTP endpoint
func newMQTTPubNode(et *ExecutingTask, n *pipeline.MQTTPubNode, d NodeDiagnostic) (*MQTTPubNode, error) {

	ctx := []keyvalue.T{
		keyvalue.KV("task", et.Task.ID),
	}

	var cli mqtt.Client
	defaultCli := false
	if n.Address == "" {
		c, err := et.tm.MQTTService.DefaultClient(ctx...)
		if err != nil {
			return nil, err
		}
		cli = c
		defaultCli = true
	} else {
		cfg := mqtt.Config{
			Enabled:            true,
			URL:                n.Address,
			InsecureSkipVerify: false,
			ClientID:           fmt.Sprintf("kapacitor-%d", time.Now().UnixNano()),
			Name:               et.Task.ID,
			Username:           n.Username,
			Password:           n.Password,
		}
		c, err := cfg.NewClient()
		if err != nil {
			return nil, err
		}
		if err = c.Connect(); err != nil {
			return nil, err
		}
		cli = c
	}

	mn := &MQTTPubNode{
		node:       node{Node: n, et: et, diag: d},
		c:          n,
		mc:         cli,
		topic:      n.Topic,
		payload:    n.Payload,
		defaultCli: defaultCli,
	}
	mn.node.runF = mn.runPub
	mn.node.stopF = mn.stopPub
	return mn, nil
}

func (n *MQTTPubNode) runPub([]byte) error {
	if !n.mc.IsConnected() {
		err := n.mc.Connect()
		if err != nil {
			return err
		}
	}
	consumer := edge.NewGroupedConsumer(
		n.ins[0],
		n,
	)
	n.statMap.Set(statCardinalityGauge, consumer.CardinalityVar())

	return consumer.Consume()
}
func (n *MQTTPubNode) stopPub() {
	if n.defaultCli {
		// 若使用的为默认MQTT Client则直接返回
		return
	}
	// 否则断开链接
	n.mc.Disconnect()
}

func (n *MQTTPubNode) NewGroup(group edge.GroupInfo, first edge.PointMeta) (edge.Receiver, error) {
	g := &mqttPubGroup{
		n:      n,
		buffer: new(edge.BatchBuffer),
	}
	return edge.NewReceiverFromForwardReceiverWithStats(
		n.outs,
		edge.NewTimedForwardReceiver(n.timer, g),
	), nil
}

type mqttPubGroup struct {
	n      *MQTTPubNode
	buffer *edge.BatchBuffer
}

func (g *mqttPubGroup) BeginBatch(begin edge.BeginBatchMessage) (edge.Message, error) {
	return nil, g.buffer.BeginBatch(begin)
}

func (g *mqttPubGroup) BatchPoint(bp edge.BatchPointMessage) (edge.Message, error) {
	return nil, g.buffer.BatchPoint(bp)
}

func (g *mqttPubGroup) EndBatch(end edge.EndBatchMessage) (edge.Message, error) {
	return g.BufferedBatch(g.buffer.BufferedBatchMessage(end))
}

func (g *mqttPubGroup) BufferedBatch(batch edge.BufferedBatchMessage) (edge.Message, error) {
	row := batch.ToRow()
	g.n.doMqttPub(row)
	return batch, nil
}

func (g *mqttPubGroup) Point(p edge.PointMessage) (edge.Message, error) {
	row := p.ToRow()
	g.n.doMqttPub(row)
	return p, nil
}

func (g *mqttPubGroup) Barrier(b edge.BarrierMessage) (edge.Message, error) {
	return b, nil
}
func (g *mqttPubGroup) DeleteGroup(d edge.DeleteGroupMessage) (edge.Message, error) {
	return d, nil
}
func (g *mqttPubGroup) Done() {
}

func (n *MQTTPubNode) doMqttPub(row *models.Row) {
	message, err := jsonnet.FillJsonnet(row.ToMappedRow(), n.payload)
	if err != nil {
		n.diag.Error("fail to render message", err)
		return
	}
	err = n.mc.Publish(n.topic, mqtt.QoSLevel(0), false, []byte(message))
	if err != nil {
		n.diag.Error("fail to publish message", err)
	}
}
