package kapacitor

import (
	"log"
	"math"
	"sort"
	"time"

	"github.com/pkg/errors"

	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type CountWindowNode struct {
	node
	w     *pipeline.CountWindowNode
	buf   []edge.PointMessage
	count int64 // 当前点计数, 用于控制发送间隔
}

// Create a new  WindowNode, which windows data for a period of time and emits the window.
func newCountWindowNode(et *ExecutingTask, n *pipeline.CountWindowNode, d NodeDiagnostic) (*CountWindowNode, error) {
	if n.Every <= 0 || n.Size <= 0 {
		return nil, errors.New("invalid param of count window node")
	}
	ewn := &CountWindowNode{
		buf:  make([]edge.PointMessage, n.Size),
		w:    n,
		node: node{Node: n, et: et, diag: d},
	}
	//ewn.w.Event = ewn.w.EventTable
	ewn.node.runF = ewn.runCountWindow
	return ewn, nil
}

func (n *CountWindowNode) runCountWindow([]byte) (err error) {
	consumer := edge.NewConsumerWithReceiver(n.ins[0], n)
	return consumer.Consume()
}

func (w *CountWindowNode) BeginBatch(edge.BeginBatchMessage) error {
	return errors.New("event window does not support batch data")
}
func (w *CountWindowNode) BatchPoint(edge.BatchPointMessage) error {
	return errors.New("event window does not support batch data")
}
func (w *CountWindowNode) EndBatch(edge.EndBatchMessage) error {
	return errors.New("event window does not support batch data")
}
func (w *CountWindowNode) Barrier(b edge.BarrierMessage) error {
	return nil
}
func (w *CountWindowNode) DeleteGroup(d edge.DeleteGroupMessage) error {
	return nil
}
func (w *CountWindowNode) Done() {}

func (n *CountWindowNode) Point(p edge.PointMessage) error {
	n.count = (n.count + 1) % math.MaxInt64
	n.buf[(n.count-1)%n.w.Size] = p

	// 等待点数积累首次到达指定Size后开始向后转发
	if n.count < n.w.Size {
		return nil
	}

	// 每间隔 Every 向后转发
	if (n.count-1)%n.w.Every == 0 {
		return n.emit()
	}
	return nil
}

// emit will forward a BufferedBatchMessage to its each child
// 等待点数积累首次到达指定Size后开始向后转发

func (n *CountWindowNode) emit() error {
	points := n.collect()
	if len(points) == 0 {
		log.Printf("points buffer is empty")
		return nil
	}

	msg := edge.NewBufferedBatchMessage(
		edge.NewBeginBatchMessage(
			n.w.Name(),
			models.NodeMeta{},
			models.Trace{},
			models.Context{},
			points[0].Tags(),
			false,
			time.Now(),
			len(points),
		),
		points,
		edge.NewEndBatchMessage(),
	)
	// sort points by time
	sort.Slice(points, func(i, j int) bool {
		return points[i].Time().Before(points[j].Time())
	})
	if err := edge.Forward(n.outs, msg); err != nil {
		log.Printf("send count buffered batch message failed :%v", err)
		return err
	}
	return nil
}

func (n *CountWindowNode) collect() []edge.BatchPointMessage {
	points := make([]edge.BatchPointMessage, n.w.Size)
	for idx, point := range n.buf {
		points[idx] = edge.BatchPointFromPoint(point)
	}
	return points
}
