package kapacitor

import (
	"github.com/pkg/errors"
	"scientificgo.org/fft"
	"strconv"
	"time"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type FastFourierTransformerNode struct {
	node
	f *pipeline.FastFourierTransformerNode
}

// Create a new FastFourierTransformerNode which submits received items via POST to an HTTP endpoint
func newFastFourierTransformerNode(et *ExecutingTask, n *pipeline.FastFourierTransformerNode, d NodeDiagnostic) (*FastFourierTransformerNode, error) {
	fftn := &FastFourierTransformerNode{
		node: node{Node: n, et: et, diag: d},
		f:    n,
	}
	fftn.node.runF = fftn.runTransform
	return fftn, nil
}

func (n *FastFourierTransformerNode) runTransform(snapshot []byte) error {
	// n.ins denotes in-edge set, and it only contains one edge
	consumer := edge.NewGroupedConsumer(n.ins[0], n)
	n.statMap.Set(statCardinalityGauge, consumer.CardinalityVar())
	return consumer.Consume()
}

type fastFourierTransformerGroup struct {
	fftn   *FastFourierTransformerNode
	buffer *edge.BatchBuffer
}

func (n *FastFourierTransformerNode) NewGroup(group edge.GroupInfo, first edge.PointMeta) (edge.Receiver, error) {
	g := &fastFourierTransformerGroup{
		fftn:   n,
		buffer: new(edge.BatchBuffer),
	}
	return edge.NewReceiverFromForwardReceiverWithStats(
		n.outs,
		edge.NewTimedForwardReceiver(n.timer, g),
	), nil
}

func (n *FastFourierTransformerNode) newGroup() *fastFourierTransformerGroup {
	return &fastFourierTransformerGroup{
		fftn: n,
	}
}

// batch logic: Init batch
func (g *fastFourierTransformerGroup) BeginBatch(begin edge.BeginBatchMessage) (edge.Message, error) {
	return nil, g.buffer.BeginBatch(begin)
}

// batch logic: Handle one
func (g *fastFourierTransformerGroup) BatchPoint(bp edge.BatchPointMessage) (edge.Message, error) {
	// Put one point into buffer
	return nil, g.buffer.BatchPoint(bp)
}

// batch logic: End batch
func (g *fastFourierTransformerGroup) EndBatch(end edge.EndBatchMessage) (edge.Message, error) {
	return g.BufferedBatch(g.buffer.BufferedBatchMessage(end))
}

// batch login: Handle eventually batch data
func (g *fastFourierTransformerGroup) BufferedBatch(batch edge.BufferedBatchMessage) (edge.Message, error) {
	row := batch.ToRow()
	transformRes, err := g.doTransform(row)
	batch.SetPoints([]edge.BatchPointMessage{transformRes})
	// transmit fft result to downstream node
	return batch, err
}
func (g *fastFourierTransformerGroup) Point(p edge.PointMessage) (edge.Message, error) {
	row := p.ToRow()
	return g.doTransform(row)
}

// Invoke Fft method
func (g *fastFourierTransformerGroup) doTransform(row *models.Row) (edge.BatchPointMessage, error) {
	// locate the specific field
	var fieldIndex = -1
	for i, c := range row.Columns {
		if c == g.fftn.f.Field {
			fieldIndex = i
			break
		}
	}
	if fieldIndex < 0 {
		return nil, errors.Errorf("the specific field not exists")
	}

	lenOfRecords := len(row.Values)
	inset := make([]complex128, lenOfRecords)
	for i, row := range row.Values {
		value := row[fieldIndex]
		switch value.(type) {
		case string:
			r, err := strconv.ParseFloat(value.(string), 64)
			if err != nil {
				return nil, errors.Errorf("fast Fourier transform node not support non number type")
			}
			inset[i] = complex(r, 0)
		case bool: // quantify bool type value, true is 1, false is 0
			if r := value.(bool); r == true {
				inset[i] = complex(1, 0)
			} else {
				inset[i] = complex(0, 0)
			}
		default:
			inset[i] = complex(value.(float64), 64)
		}
	}
	fftRes := fft.Fft(inset, g.fftn.f.Inverse == 1)
	fields := make(models.Fields)
	fields[g.fftn.f.Field+"_fft"] = fftRes
	result := edge.NewBatchPointMessage(fields, row.Tags, time.Now())
	return result, nil
}

func (g *fastFourierTransformerGroup) Barrier(b edge.BarrierMessage) (edge.Message, error) {
	return b, nil
}

func (g *fastFourierTransformerGroup) DeleteGroup(d edge.DeleteGroupMessage) (edge.Message, error) {
	return d, nil
}

func (g *fastFourierTransformerGroup) Done() {}
