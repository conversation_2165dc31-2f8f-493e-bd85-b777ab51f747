package kapacitor

import (
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type GotoNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.GotoNode // 算子的参数定义
}

// newGotoNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newGotoNode(et *ExecutingTask, n *pipeline.GotoNode, d NodeDiagnostic) (*GotoNode, error) {
	hn := &GotoNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *GotoNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

// 占位符算子，只记录本节点meta，透明转发其他数据
func (n *GotoNode) Point(p edge.PointMessage) (err error) {
	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	call := ctx.Call
	if call == nil {
		return stderr.Internal.Error("call of message context is nil")
	}
	if call.GetLoopRound(n.pn.Id) >= int(n.pn.MaxLoopRounds) {
		return stderr.Error("max loop rounds exceeded: max %d rounds", n.pn.MaxLoopRounds)
	}
	return n.forward2TargetNode(n.pn.TargetNode, p)
}
