package kapacitor

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
	"transwarp.io/applied-ai/applet-backend/pkg/models/health"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
	http_ "transwarp.io/applied-ai/applet-engine/tools/http"
)

var httpListeners sync.Map

func getOrInitListenerByPort(port int) (*httpListener, error) {
	var hl *httpListener
	v, ok := httpListeners.Load(port)
	if !ok {
		hl = newHttpListener(port)
		httpListeners.Store(port, hl)
	} else {
		hl = v.(*httpListener)
	}
	return hl, nil
}

// register 将 *ListenHttpNode 注册到一个 httpListener
// 如果存在相同 port 的 listener 则进行复用（通过Path进行区分）；否则新建一个 listener ；
// 如果 port + path 均已被使用，则返回 error
// 若 注册的节点为 listener 中首个节点，则启动该服务
func register(n *ListenHttpNode) (*httpListener, error) {
	if n == nil {
		return nil, stderr.Error("registering nil ListenHttpNode")
	}

	port := int(n.pn.Port)
	l, err := getOrInitListenerByPort(port)
	if err != nil {
		return nil, stderr.Wrap(err, "get the http listener listening %d", port)
	}
	if len(l.ns) == 0 {
		if !utils.IsPortFree("tcp", port) {
			return nil, stderr.InvalidParam.Error("tcp port %d already in use", port)
		}
		go func() {
			e := l.serve()
			stdlog.WithError(e).Errorf("http listener serve exited")
		}()
	}
	if err = l.register(n); err != nil {
		return nil, stderr.Trace(err)
	}
	return l, nil
}

func unregister(n *ListenHttpNode) error {
	if n == nil {
		return stderr.Error("unregistering nil ListenHttpNode")
	}

	port := int(n.pn.Port)
	l, err := getOrInitListenerByPort(port)
	if err != nil {
		return stderr.Wrap(err, "get listener by port %d", port)
	}
	if l == nil {
		return stderr.Error("listener of port %d is nil", port)
	}

	l.unregister(n)
	if len(l.ns) == 0 {
		// now close the server gracefully ("shutdown")
		// timeout could be given with a proper context
		stdlog.Infof("no http listen node remained, stop serve to release the port %d", port)
		if err = l.stopServe(); err != nil {
			return stderr.Trace(err)
		}
	}
	return nil
}

const CheckHealthPath = "/api/v1/health"

func checkHealthHandler(w http.ResponseWriter, ns map[string]*ListenHttpNode) {
	var targetNode *pipeline.ListenHttpNode
	for _, v := range ns {
		targetNode = v.pn
		break
	}
	if targetNode == nil {
		writeFailedHealthResponse(w, "failed to find the chain service")
		return
	}
	chainNodes, err := getChainNodes(targetNode)
	if err != nil {
		writeFailedHealthResponse(w, fmt.Sprintf("%+v failed to get chain nodes from %+v", err, targetNode))
		return
	}
	if len(chainNodes) == 0 {
		writeFailedHealthResponse(w, fmt.Sprintf("0 length of chain nodes that get from %+v", targetNode))
		return
	}

	var chainName string
	var wg sync.WaitGroup
	var once sync.Once

	serviceHealthChannel := make(chan health.ServiceHealth)
	for _, node := range chainNodes {
		wg.Add(1)
		go func(n pipeline.Node) {
			defer wg.Done()
			// 保存应用链名称作为服务名称
			if chainName == "" && n.Meta().ChainName != "" {
				once.Do(func() {
					chainName = n.Meta().ChainName
				})
			}
			for _, serviceHealth := range n.GetHealth() {
				serviceHealthChannel <- serviceHealth
			}
		}(node)
	}

	go func() {
		wg.Wait()
		close(serviceHealthChannel)
	}()

	dependencies := make([]health.ServiceHealth, 0)
	for serviceHealth := range serviceHealthChannel {
		dependencies = append(dependencies, serviceHealth)
	}
	dependencies = removeDuplicateServices(dependencies)
	isHealthy, detail := checkServicesHealth(dependencies)
	chainHealth := health.ChainHealth{
		ServiceHealth: health.ServiceHealth{
			Name:    chainName,
			Healthy: isHealthy,
			Detail:  detail,
		},
		Timestamp:    time.Now().UnixMilli(),
		Dependencies: dependencies,
	}
	writeSuccessHealthResponse(w, &chainHealth)
}

func writeSuccessHealthResponse(w http.ResponseWriter, data *health.ChainHealth) {
	rsp := health.ChainHealthResponse{
		Success: true,
		Error:   "",
		Data:    data,
	}
	if err := writeJsonResponse(w, rsp); err != nil {
		stdlog.Errorf("failed to write response %+v", err)
	}
}

func writeFailedHealthResponse(w http.ResponseWriter, errMsg string) {
	stdlog.Error(errMsg)
	rsp := health.ChainHealthResponse{
		Success: false,
		Error:   errMsg,
		Data:    &health.ChainHealth{},
	}
	if err := writeJsonResponse(w, rsp); err != nil {
		stdlog.Errorf("failed to write response %+v", err)
	}
}

func writeJsonResponse(w http.ResponseWriter, data any) error {
	bs, err := json.Marshal(data)
	if err != nil {
		return stderr.Wrap(err, "failed to marshal %+v", data)
	}
	w.Header().Set(http_.CONTENT_TYPE, http_.JSON_CONTENT_TYPE)
	if _, err = w.Write(bs); err != nil {
		return stderr.Wrap(err, "failed to write http response %s", string(bs))
	}
	return nil
}

func removeDuplicateServices(services []health.ServiceHealth) []health.ServiceHealth {
	reservedServices := make(map[string]bool)
	var uniqueServices []health.ServiceHealth
	for _, service := range services {
		if !reservedServices[service.ID] {
			uniqueServices = append(uniqueServices, service)
			reservedServices[service.ID] = true
		}
	}
	return uniqueServices
}

func checkServicesHealth(services []health.ServiceHealth) (bool, string) {
	unhealthyServiceNames := []string{}
	for _, service := range services {
		if !service.Healthy {
			unhealthyServiceNames = append(unhealthyServiceNames, service.Name)
		}
	}
	if len(unhealthyServiceNames) > 0 {
		names := strings.Join(unhealthyServiceNames, ", ")
		return false, fmt.Sprintf("%s are unhealthy", names)
	}
	return true, ""
}

// handleRequestFormat 处理请求格式,支持多种格式
// 目前支持:
// 1. 原始格式
// 2. /std 结尾的标准格式
// 3. /openai 结尾的OpenAI格式(待实现)
func handleRequestFormat(urlPath string, requestBody []byte) (path string, kvs map[string]any, err error) {
	// 检查URL格式
	switch {
	case strings.HasSuffix(urlPath, "/std"):
		return handleStdFormat(urlPath, requestBody)
	case strings.HasSuffix(urlPath, "/openai"):
		return handleOpenAIFormat(urlPath, requestBody)
	default:
		// 原始格式,直接返回
		kvs = make(map[string]any, 0)
		if err = json.Unmarshal(requestBody, &kvs); err != nil {
			return "", nil, stderr.Wrap(err, "unmarshal request body to json")
		}
		return urlPath, kvs, nil
	}
}

// handleStdFormat 处理标准格式的请求
// 将新的请求格式转换为旧格式:
// TextInput <- query
// ChatInput <- history
// FileInput <- files
// JsonInput <- params
func handleStdFormat(urlPath string, requestBody []byte) (path string, kvs map[string]any, err error) {
	// 移除"/std"后缀
	path = strings.TrimSuffix(urlPath, "/std")

	// 解析请求体
	stdKvs := make(map[string]any)
	if err = json.Unmarshal(requestBody, &stdKvs); err != nil {
		return "", nil, stderr.Wrap(err, "unmarshal request body to json")
	}

	// 转换为旧格式
	kvs = make(map[string]any)
	kvs[models.PredefinedFieldTextInput] = stdKvs[models.PredefinedFieldStdQuery]
	kvs[models.PredefinedFieldChatInput] = stdKvs[models.PredefinedFieldStdHistory]
	kvs[models.PredefinedFieldFileInput] = stdKvs[models.PredefinedFieldStdFiles]
	// 处理 params 字段
	if params, ok := stdKvs[models.PredefinedFieldStdParams]; ok {
		paramsBytes, err := json.Marshal(params)
		if err != nil {
			return "", nil, stderr.Wrap(err, "marshal params to json")
		}
		kvs[models.PredefinedFieldJsonInput] = string(paramsBytes)
	}

	return path, kvs, nil
}

// handleOpenAIFormat 处理OpenAI格式的请求
// TODO: 实现OpenAI格式的转换逻辑
func handleOpenAIFormat(urlPath string, requestBody []byte) (path string, kvs map[string]any, err error) {
	// 移除"/openai"后缀
	path = strings.TrimSuffix(urlPath, "/openai")

	// TODO: 实现OpenAI格式的转换逻辑
	return path, nil, stderr.Errorf("openai format not implemented yet")
}
