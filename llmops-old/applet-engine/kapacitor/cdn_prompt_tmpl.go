package kapacitor

import (
	"bytes"
	"strings"
	text "text/template"
	"text/template/parse"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

const (
	TMPL_VALUE_PREFIX = "{{."
	TMPL_VALUE_SUFFIX = "}}"
)

type PromptTmplNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.PromptTmplNode // 算子的参数定义
}

// newPromptTmplNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newPromptTmplNode(et *ExecutingTask, n *pipeline.PromptTmplNode, d NodeDiagnostic) (*PromptTmplNode, error) {
	hn := &PromptTmplNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *PromptTmplNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *PromptTmplNode) Point(p edge.PointMessage) (err error) {
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	tmplValue, err := extracOutputFromFields[any](p.Fields())
	if err != nil {
		return stderr.Wrap(err, "failed to extract output from fields")
	}
	tmplValue, err = handleTmplValue(tmplValue, n.pn.Tmpl, n.pn.RawInput)
	if err != nil {
		return stderr.Wrap(err, "failed to handle tmpl value")
	}
	res := make([]byte, 0)
	buf := bytes.NewBuffer(res)
	if err := n.pn.Tmpl.Execute(buf, tmplValue); err != nil {
		return stderr.Wrap(err, "execute tmpl with text %s and data %+v", n.pn.TmplText, tmplValue)
	}
	if err := setOutputIntoFields(p.Fields(), buf.String()); err != nil {
		return stderr.Wrap(err, "set output %+v into fields", buf.String())
	}
	return n.forwardMsg(p, true)
}

func handleTmplValue(tmplValue any, textTmpl *text.Template, rawInput bool) (any, error) {
	// rawInput 为 true 时，不做处理
	if rawInput {
		return tmplValue, nil
	}
	tmplValueSet := map[string]any{}
	for _, n := range textTmpl.Tree.Root.Nodes {
		if n.Type() == parse.NodeAction {
			valueName := n.String()
			valueName = strings.TrimPrefix(valueName, TMPL_VALUE_PREFIX)
			valueName = strings.TrimSuffix(valueName, TMPL_VALUE_SUFFIX)
			if valueName == "" {
				return nil, stderr.Error("tmpl value name is empty")
			}
			tmplValueSet[valueName] = nil
		}
	}
	// 模板中变量不为1时，不做处理
	if len(tmplValueSet) != 1 {
		return tmplValue, nil
	}
	// 模板中只有一个变量时，模板变量名作为key，上游算子传入的数据作为value
	var valueName string
	for k := range tmplValueSet {
		valueName = k
	}
	return map[string]any{
		valueName: tmplValue,
	}, nil
}
