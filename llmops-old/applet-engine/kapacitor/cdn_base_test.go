package kapacitor

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"

	"transwarp.io/aip/llmops-common/pb"
)

var _ = os.Setenv("GOLANG_PROTOBUF_REGISTRATION_CONFLICT", "warn")

func Test_tryCvt2Chunks(t *testing.T) {

	tests := []struct {
		name       string
		args       any
		wantChunks []*pb.Chunk
		wantErr    bool
	}{
		{
			args:       map[string]string{},
			wantChunks: nil,
			wantErr:    true,
		}, {
			args:       "aaa",
			wantChunks: []*pb.Chunk{{Content: "aaa"}},
			wantErr:    false,
		}, {
			args:       []string{"aaa", "bbb"},
			wantChunks: []*pb.Chunk{{Content: "aaa"}, {Content: "bbb"}},
			wantErr:    false,
		}, {
			args:       []*pb.Chunk{{Content: "aaa"}, {Content: "bbb"}},
			wantChunks: []*pb.Chunk{{Content: "aaa"}, {Content: "bbb"}},
			wantErr:    false,
		}, {
			args:       &pb.Chunk{Content: "aaa"},
			wantChunks: []*pb.Chunk{{Content: "aaa"}},
			wantErr:    false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotChunks, err := tryCvt2Chunks(tt.args)
			if tt.wantErr != (err != nil) {
				t.Fatalf("unexpected error : err is %v, wantErr is %v", err, tt.wantErr)
				return
			}
			assert.Equalf(t, tt.wantChunks, gotChunks, "tryCvt2Chunks(%v)", tt.args)
		})
	}
}
