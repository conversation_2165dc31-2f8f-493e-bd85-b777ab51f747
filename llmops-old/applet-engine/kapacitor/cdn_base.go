package kapacitor

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"io/fs"
	"net/url"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"sort"
	"strings"
	"time"
	"transwarp.io/applied-ai/applet-engine/clients"

	"github.com/sa<PERSON><PERSON>nov/go-openai"
	"transwarp.io/applied-ai/applet-backend/pkg/agent_executor"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/engine"
	"transwarp.io/applied-ai/applet-engine/pkg/debug"
	"transwarp.io/applied-ai/applet-engine/tools/uuid"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdfs"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

// unsupportedBatchNode 当所实现算子无需支持 Batch 模式数据点位时，
// 可内置该算子，提供部分关于Batch处理函数的默认实现（返回错误信息）
type unsupportedBatchNode struct{}

// BeginBatch 处理一批数据点开始的信号
func (n *unsupportedBatchNode) BeginBatch(begin edge.BeginBatchMessage) error {
	return stderr.Internal.Error("unsupported begin batch message")
}

// BatchPoint 为 BeginBatch EndBatch 两个起止信号之间的单个数据点位
func (n *unsupportedBatchNode) BatchPoint(bp edge.BatchPointMessage) error {
	return stderr.Internal.Error("unsupported batch point message")
}

// EndBatch 处理一批数据点结束的信号
func (n *unsupportedBatchNode) EndBatch(end edge.EndBatchMessage) error {
	return stderr.Internal.Error("unsupported end batch message")
}

// BufferedBatch 一次性处理接收到的一批数据点
func (n *unsupportedBatchNode) BufferedBatch(batch edge.BufferedBatchMessage) (edge.Message, error) {
	return nil, stderr.Internal.Error("unsupported buffered batch message")
}

func (n *unsupportedBatchNode) Barrier(b edge.BarrierMessage) error {
	return nil
}
func (n *unsupportedBatchNode) DeleteGroup(d edge.DeleteGroupMessage) error {
	return nil
}
func (n *unsupportedBatchNode) Done() {}

const (
	ToolThoughtTemplate = "我需要使用 %s 工具获取数据"
)

func sendSSE(call *debug.Call, event models.Event, displayFlag bool) error {
	if !displayFlag {
		return nil
	}
	if call == nil {
		return stderr.Internal.Error("call is nil")
	}
	if call.Done() {
		return stderr.Errorf("handle msg with a completed call")
	}
	response := stdsrv.Response{
		Response: event.GetData(),
	}
	return stdsrv.SSESendDataWithEventName(call.W(), call.ReqID(), event.GetType(), response)
}

func sendThoughtSSE(call *debug.Call, thought string) error {
	thoughtEvent := models.ThoughtEvent{
		Event: stdsrv.SSEEvtThought,
		Data:  thought,
	}
	return sendSSE(call, thoughtEvent, call.IsThought())
}

func replaceAndCancel(call *debug.Call, replace string) error {
	if err := stdsrv.SSESendReplace(call.W(), call.ReqID(), replace); err != nil {
		return err
	}
	call.Cancel()
	return nil
}

func sendTraceSSE(call *debug.Call, trace models.Trace) error {
	if !call.IsTrace() {
		return nil
	}
	traceStr, err := marshalWithPretty(trace, call.IsPretty())
	if err != nil {
		return stderr.Wrap(err, "marshal %v with pretty %t", trace, call.IsPretty())
	}
	return stdsrv.SSESendDataWithEventName(call.W(), call.ReqID(), stdsrv.SSEEvtTrace, traceStr)
}

func sendActionSSE(call *debug.Call, toolDefinition agent_definition.Tool, toolInput any) error {
	actionEvent := models.ActionEvent{
		Event: stdsrv.SSEEvtAction,
		Data: models.ActionEventData{
			ToolInput: toolInput,
			Tool:      toolDefinition,
		},
	}
	return sendSSE(call, actionEvent, call.IsThought())
}

func sendObservationSSE(call *debug.Call, toolDefinition agent_definition.Tool, observation string) error {
	observationEvent := models.ObservationEvent{
		Event: stdsrv.SSEEvtObservation,
		Data: models.ObservationEventData{
			Tool:        toolDefinition,
			Observation: observation,
		},
	}
	return sendSSE(call, observationEvent, call.IsThought())
}

func sendDebugSSE(call *debug.Call, status debug.NodeStatus) error {
	debugMessageStack := call.GetDebugMessageStack()
	overallDebugMessage := call.GetOverallDebugMessage()
	// debug message stack 变量结构
	// {
	// 	"每个算子的唯一id": {
	// 		"轮数": {}
	// 	},
	// 	"id-xxx": {
	// 		"0": {
	// 			"meta": {
	// 				"node_id": "code",
	// 				"sub_chain_id": "",
	// 				"node_name": "1"
	// 			},
	// 			"scope": "node",
	// 			"round": 0,
	// 			"status": "success",
	// 			"input": "1",
	// 			"output": {
	// 				"value": "1"
	// 			},
	// 			"log": "time=2024-04-25T15:28:13+08:00 level=info subchain= node=1 log=\"\ncode output:\n\"\n",
	// 			"start_time": 1714030093323,
	// 			"end_time": 1714030093397
	// 		}
	// 	}
	// }

	// 将 DebugMessageStack 转换成平铺的 []*debug.DebugMessage，包括Children
	allDebugMessages := make([]*debug.DebugMessage, 0)
	for _, nodeDebugMessages := range debugMessageStack {
		if len(nodeDebugMessages) == 0 {
			continue
		}

		// 遍历每一轮的 DebugMsg
		for _, debugMessage := range nodeDebugMessages {
			// 如果debugMessage为空，则跳过
			if debugMessage == nil || debugMessage.Scope == debug.DEBUG_SCOPE_CHAIN {
				continue
			}

			if debugMessage.ChildrenStack != nil {
				debugMessage.OrderChildDebugMsg()
			}

			allDebugMessages = append(allDebugMessages, debugMessage)
		}

	}
	// 对 allDebugMessages 按StartTime字段升序排序
	sort.Slice(allDebugMessages, func(i, j int) bool {
		return allDebugMessages[i].StartTime < allDebugMessages[j].StartTime
	})

	overallDebugMessage.DebugMessages = allDebugMessages
	aggregateTokenLogOverallDebugMessage(overallDebugMessage)
	// 更新overallDebugMessage的状态
	overallDebugMessage.Status = status
	overallDebugMessage.EndTime = time.Now().UnixMilli()
	debugEvent := models.DebugEvent{
		Event: stdsrv.SSEEvtDebug,
		Data:  overallDebugMessage,
	}

	if call.Done() {
		if errors.Is(call.Ctx().Err(), context.Canceled) {
			newCtx, cancel := context.WithTimeout(context.Background(), time.Second*10)
			defer cancel()

			// 连接断开，无法返回 event，只能将 overralDebugMessage 写入 chain_debug_histories 表中，
			taskID, err := call.ExtractTaskIDFromPath()
			if err != nil {
				return err
			}

			evtData := debugEvent.GetData()
			debugMsg, ok := evtData.(*debug.OverallDebugMessage)
			if !ok {
				return stderr.Errorf("强转*debug.OverallDebugMessage类型失败")
			}
			debugMsg.Status = debug.NODE_STATUS_CANCELED

			debugMsgBytes, err := json.Marshal(debugMsg)
			if err != nil {
				return stderr.Wrap(err, "序列化debugMsg出错")
			}

			err = clients.AppletBackendCli.CancelChat(newCtx, taskID, debugMsgBytes)

			if err != nil {
				return err
			}

			return stderr.Errorf("handle debug msg when call is canceled")
		}
		return stderr.Errorf("handle msg with a completed call")
	}

	return sendSSE(call, debugEvent, call.IsDebug())
}

func sendCitationSSE(call *debug.Call, citations []*agent_executor.Citation) error {
	citationEvent := models.CitationEvent{
		Event: stdsrv.SSEEvtCitation,
		Data:  citations,
	}
	return sendSSE(call, citationEvent, true)
}

// SendDebugLogSSE 原先各节点的日志推送转为流式事件
func SendDebugLogSSE(call *debug.Call, debugLog *debug.DebugMessage) error {
	if !call.IsDebug() {
		return nil
	}
	citationEvent := models.CitationEvent{
		Event: stdsrv.SSEEvtLog,
		Data:  debugLog,
	}
	return sendSSE(call, citationEvent, true)
}

func extracOutputFromFields[T any](fields models.Fields) (res T, err error) {
	outputKey := models.PredefinedFieldOutput
	res, exist, err := models.GetValueOfFields[T](fields, outputKey)
	if err != nil {
		return
	}
	if !exist {
		err = stderr.Errorf("there is no %s field in fields of message", outputKey)
		return
	}
	return
}

func extracNodeInPortFromFields[NodeInPortType any](fields models.Fields) (*NodeInPortType, error) {
	outPut, err := extracOutputFromFields[any](fields)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to extract output from fields")
	}
	return tryCvt2NodeInPort[NodeInPortType](outPut)
}

func setOutputIntoFields(fields models.Fields, output any) error {
	outputKey := models.PredefinedFieldOutput
	return models.SetKeyValueOfFields(fields, outputKey, output)
}

func extracValueFromMapOutput[T any](output map[string]any, key string) (res T, err error) {
	if output == nil {
		err = stderr.Errorf("output is nil")
		return
	}
	v, ok := output[key]
	if !ok {
		err = stderr.Errorf("there is no %s field in output of message", key)
		return
	}
	res, ok = v.(T)
	if !ok {
		err = stderr.Errorf("try convert %T to %T in output", v, res)
		return
	}
	return
}

// tryCvt2Chunks 尝试从上游算子输出中解析出 []*pb.Chunk
// 可以适配的结构/数据类型及其转换规则如下：
// string		->	 	长度为1的段落数组
// []string		->		等长的段落数组
// []any 		->		json 序列化后再尝试反序列化为 []*pb.Chunk
// []*pb.Chunk	-> 		直接返回
// *pb.Chunk	-> 		长度为1的段落数组
// 其他			-> 		暂不支持
func tryCvt2Chunks(v any) (chunks []*pb.Chunk, err error) {
	if v == nil {
		return make([]*pb.Chunk, 0), nil
	}
	switch vv := v.(type) {
	case string:
		chunks = append(chunks, &pb.Chunk{Content: vv})
	case []string:
		for _, chunkV := range vv {
			chunks = append(chunks, &pb.Chunk{Content: chunkV})
		}
	case []*pb.Chunk:
		chunks = append(chunks, vv...)
	case *pb.Chunk:
		chunks = append(chunks, vv)
	case []any:
		if err = stdsrv.UnmarshalMixWithProto(vv, &chunks); err != nil {
			return nil, stderr.Wrap(err, "UnmarshalMixWithProto []any to []*pb.Chunk")
		}
	default:
		return nil, stderr.InvalidParam.Error("unexpected output type %T, expecting (string, []string, Chunk, []Chunk, []any) ", v)
	}
	return
}

func tryCvt2NodeInPort[T any](inPort any) (*T, error) {
	if casted, ok := inPort.(T); ok {
		return &casted, nil
	}
	if casted, ok := inPort.(*T); ok {
		return casted, nil
	}
	res := new(T)
	if err := json.Unmarshal(stdsrv.AnyToBytes(inPort), res); err != nil {
		return nil, stderr.Wrap(err, "failed to unmarshal with value %+v", inPort)
	}
	return res, nil
}

// []*pb.DocElement	 -> 	直接返回
// *pb.DocElement    ->  	长度为1的段落数组
// []any 		     ->		json序列化后再尝试反序列化为[]*pb.Chunk
// 其他			     -> 	暂不支持
// TODO 有没有一种更好的写法
func tryCvt2Elements(v any) (elements []*pb.DocElement, err error) {
	if v == nil {
		return make([]*pb.DocElement, 0), nil
	}
	switch vv := v.(type) {
	case []*pb.DocElement:
		elements = append(elements, vv...)
	case *pb.DocElement:
		elements = append(elements, vv)
	case []any:
		if err = stdsrv.UnmarshalMixWithProto(vv, &elements); err != nil {
			return nil, stderr.Wrap(err, "UnmarshalMixWithProto []any to []*pb.DocElement")
		}
	default:
		return nil, stderr.InvalidParam.Error("unexpected output type %T, expecting ([]*pb.DocElement, *pb.DocElement,[]any) ", v)
	}
	return
}

// tryCvt2SFSFile
func tryCvt2SFSFile(v any) (file *engine.SFSFile, err error) {
	if v == nil {
		return nil, stderr.Errorf("v is nil")
	}
	switch t := v.(type) {
	case *engine.SFSFile:
		if t == nil || t.Url == "" {
			return nil, stderr.Errorf("file is nil or url of file is empty")
		}
		file = t
	case string:
		if t == "" {
			return nil, stderr.Errorf("url of file is empty")
		}
		localFilePath, err := stdfs.GetSFSLocalPath(t)
		if err != nil {
			return nil, stderr.Wrap(err, "get sfs local path of %s", t)
		}
		fileBytes, err := os.ReadFile(localFilePath)
		if err != nil {
			return nil, stderr.Wrap(err, "read file %s", t)
		}
		file = &engine.SFSFile{
			Name:    path.Base(t),
			Url:     t,
			Content: fileBytes,
		}
	default:
		return nil, stderr.InvalidParam.Error("unexpected output type %T, expecting (string, *SFSFile) ", v)
	}
	return file, nil
}

func extracChunksFromOutputField(fields models.Fields) (chunks []*pb.Chunk, err error) {
	out, err := extracOutputFromFields[any](fields)
	if err != nil {
		err = stderr.Trace(err)
		return
	}
	chunks, err = tryCvt2Chunks(out)
	if err != nil {
		err = stderr.Trace(err)
		return
	}
	return
}

func logError(ctx models.Context, err error) {
	if err != nil {
		ctx.Errorf(err.Error())
	}
}

// getChainNodes 传入链中的任意节点，获取链中的所有节点
func getChainNodes(n pipeline.Node) ([]pipeline.Node, error) {
	if n == nil {
		return nil, stderr.Errorf("input node is nil")
	}
	var rootNode pipeline.Node = n
	// 当向上遍历node，当node无parents时，断定该node为rootNode
	for len(rootNode.Parents()) > 0 {
		rootNode = rootNode.Parents()[0]
	}
	chainNodes := []pipeline.Node{rootNode}
	curNodes := []pipeline.Node{rootNode}
	for len(curNodes) > 0 {
		nextNodes := []pipeline.Node{}
		for _, n := range curNodes {
			nextNodes = append(nextNodes, n.Children()...)
		}
		chainNodes = append(chainNodes, nextNodes...)
		curNodes = nextNodes
	}
	// 链中第一个node固定为streamNode, 只返回之后的nodes
	return chainNodes[1:], nil
}

func aggregateTokenLog(debugMessage *debug.DebugMessage) {
	if debugMessage.Children == nil {
		// 叶子节点，直接返回
		return
	}
	var totalUsage debug.TokenUsage
	var combinedLog string

	debugMessageOrdered := debugMessage.Children // debugMessageStack 作为 Children，记录子节点

	for _, debugMessage := range debugMessageOrdered { // 遍历所有子节点
		// 递归调用子节点，更新子节点
		aggregateTokenLog(debugMessage)

		// 累加 token 使用情况
		totalUsage.PromptTokens += debugMessage.Usage.PromptTokens
		totalUsage.CompletionTokens += debugMessage.Usage.CompletionTokens
		totalUsage.TotalTokens += debugMessage.Usage.TotalTokens

		// 聚合子节点日志
		if childLog, ok := debugMessage.Log.(string); ok {
			combinedLog += childLog
		}

		debugMessage.Log, debugMessage.Output, debugMessage.Input = nil, nil, nil
	}

	// 更新当前节点的 Usage
	debugMessage.Usage = totalUsage

	// 更新当前节点的 Log
	debugMessage.SetLog(combinedLog)
}

func aggregateTokenLogOverallDebugMessage(overall *debug.OverallDebugMessage) {
	var totalUsage debug.TokenUsage
	var combinedLog string

	for _, debugMsg := range overall.DebugMessages {
		// 对每个顶层 DebugMessage 调用 aggregateTokenAndLog
		aggregateTokenLog(debugMsg)

		// 累加总的 token 使用情况
		totalUsage.PromptTokens += debugMsg.Usage.PromptTokens
		totalUsage.CompletionTokens += debugMsg.Usage.CompletionTokens
		totalUsage.TotalTokens += debugMsg.Usage.TotalTokens

		// 聚合日志
		if log, ok := debugMsg.Log.(string); ok {
			combinedLog += log
		}

		// Backend 已将完整的 DebugMessage 写入 chain_debug_logs 表中
		// 清空 DebugMessage 的 Log, Output, Input 字段防止 OverallDebugMessage 过大
		debugMsg.Log, debugMsg.Output, debugMsg.Input = nil, nil, nil
	}

	// 更新 OverallDebugMessage 的 Usage 和 Log
	overall.Usage = totalUsage
	overall.Log = combinedLog
}

const (
	ToolDir    = "tool"
	TempDir    = "temp"
	AppletDir  = "applet"
	ProjsDir   = "projs"
	TenantsDir = "tenants"
)

var (
	sfsTenantRootRe = regexp.MustCompile(`/tenants/[^/]+`)
)

// saveContentToFile 保存文件到指定的租户和项目目录下，并返回文件的 sfs 地址
func saveContentToFile(tenantID, projectID, name string, content []byte, isPublished bool) (string, error) {
	if len(content) == 0 || name == "" {
		return "", stderr.InvalidParam.Errorf("content or name is empty. content length: %d, name: %s", len(content), name)
	}

	// 生成UUID作为临时目录名
	uuidDir := uuid.New().String()

	// 获取项目目录路径，包含temp和uuid子目录
	tempPath, err := stdfs.NewProjectLocation(tenantID, projectID, true, TempDir, uuidDir)
	if err != nil {
		return "", stderr.Wrap(err, "get project location")
	}

	// 构建完整的文件路径
	filePath := stdfs.RelativeFilePath(filepath.Join(string(tempPath), name))
	absPath := filePath.ToAbsFilePath()
	if isPublished {
		// /sfs/tenants/llmops-assets/projs/assets/xxx  ->  /sfs/projs/assets/xxx
		absPath = sfsTenantRootRe.ReplaceAllString(absPath, "")
	}

	// 确保父目录存在
	parentDir := filepath.Dir(absPath)
	if err := os.MkdirAll(parentDir, os.ModePerm); err != nil {
		return "", stderr.Wrap(err, "create parent directory %s", parentDir)
	}

	// 写入文件内容
	if err := os.WriteFile(absPath, content, fs.ModePerm); err != nil {
		return "", stderr.Wrap(err, "write file %s", absPath)
	}

	// 返回 sfs 地址
	return filePath.ToSFSFilePath(), nil
}

type SFSFiles []*engine.SFSFile

// FromAnyByJSON 通过 JSON 序列化和反序列化的方式将任意类型转换为 SFSFiles
// 参数:
//   - a: 任意类型的输入值
//
// 返回:
//   - error: 转换过程中的错误，如果成功则返回 nil
func (s *SFSFiles) FromAnyByJSON(a any) error {
	if s == nil {
		return stderr.Errorf("SFSFiles not initialized")
	}
	stdlog.Debugf("Parse SFSFiles FromAnyByJSON case any, use Marshal/Unmarshal")
	bs, err := json.Marshal(a)
	if err != nil {
		return stderr.Wrap(err, "marshal file input %+v", a)
	}

	if err = json.Unmarshal(bs, s); err != nil {
		return stderr.Wrap(err, "unmarshal to sfs files")
	}
	return nil
}

// FromAnySlice 将 []any 类型转换为 SFSFiles，支持多种转换路径
// 参数:
//   - v: []any 类型的输入切片
//
// 返回:
//   - error: 转换过程中的错误，如果成功则返回 nil
//
// 转换路径:
//  1. 尝试直接转换为 []*engine.SFSFile
//  2. 尝试转换为 []map[string]any 后通过 mapToSFSFile 转换
//  3. 最后尝试通过 JSON 序列化反序列化转换
func (s *SFSFiles) FromAnySlice(v []any) error {
	// 优先尝试转换为 []*engine.SFSFile
	if files, err := utils.CvtAny2TSlice[*engine.SFSFile](v); err == nil {
		stdlog.Debugf("Parse SFSFiles FromAnySlice case []*engine.SFSFile")
		*s = files
		return nil
	}
	// 尝试转换为 []map[string]any
	if maps, err := utils.CvtAny2TSlice[map[string]any](v); err == nil {
		stdlog.Debugf("Parse SFSFiles FromAnySlice case []map[string]any")
		files := make([]*engine.SFSFile, 0, len(v))
		for _, m := range maps {
			files = append(files, mapToSFSFile(m))
		}
		*s = files
		return nil
	}
	// 作为后备方案使用 FromAnyByJSON
	return s.FromAnyByJSON(v)
}

// FromAnyForFileSave 将任意类型转换为用于文件保存的 SFSFiles
// 参数:
//   - a: 任意类型的输入值
//
// 返回:
//   - error: 转换过程中的错误，如果成功则返回 nil
//
// 支持的输入类型:
//   - []any: 通过 FromAnySlice 处理
//   - *engine.SFSFile: 直接转换为单个文件
//   - []*engine.SFSFile: 直接使用
//   - map[string]any: 通过 mapToSFSFile 转换为单个文件
//   - []map[string]any: 通过 mapToSFSFile 转换为多个文件
//   - 其他类型: 通过 JSON 序列化反序列化转换
func (s *SFSFiles) FromAnyForFileSave(a any) error {
	if s == nil {
		return stderr.Errorf("SFSFiles not initialized")
	}
	switch v := a.(type) {
	case []any:
		stdlog.Debugf("Parse SFSFiles FromAnyForFileSave case []any")
		if err := s.FromAnySlice(v); err != nil {
			return err
		}
	case *engine.SFSFile:
		stdlog.Debugf("Parse SFSFiles FromAnyForFileSave case *engine.SFSFile")
		*s = SFSFiles{v}
	case []*engine.SFSFile:
		stdlog.Debugf("Parse SFSFiles FromAnyForFileSave case []*engine.SFSFile")
		*s = v
	case map[string]any:
		stdlog.Debugf("Parse SFSFiles FromAnyForFileSave case map[string]any")
		*s = SFSFiles{mapToSFSFile(v)}
	case []map[string]any:
		stdlog.Debugf("Parse SFSFiles FromAnyForFileSave case []map[string]any")
		files := make([]*engine.SFSFile, 0, len(v))
		for _, m := range v {
			files = append(files, mapToSFSFile(m))
		}
		*s = files
	default:
		// Fallback to json Marshal/Unmarshal for other types
		stdlog.Debugf("Parse SFSFiles FromAnyForFileSave case other types")
		s.FromAnyByJSON(a)
	}
	return nil
}

// Len 返回 SFSFiles 中的文件数量
// 返回:
//   - int: 文件数量，如果 SFSFiles 为 nil 则返回 0
func (s *SFSFiles) Len() int {
	if s == nil {
		return 0
	}
	return len(*s)
}

// ValidateNameAndContent 验证 SFSFiles 中的文件名和内容是否有效
// 返回:
//   - error: 验证过程中的错误，如果成功则返回 nil
func (s *SFSFiles) ValidateNameAndContent() error {
	if s == nil {
		return stderr.Errorf("SFSFiles not initialized")
	}

	for _, file := range *s {
		if file == nil {
			return stderr.Errorf("file is nil")
		}
		if file.Name == "" {
			return stderr.Errorf("file name is empty")
		}
		if len(file.Content) == 0 {
			return stderr.Errorf("content of file %s is empty", file.Name)
		}
	}
	return nil
}

// mapToSFSFile 将 map[string]any 转换为 *engine.SFSFile
// 参数:
//   - m: 包含文件信息的 map
//
// 返回:
//   - *engine.SFSFile: 转换后的文件对象
//
// 支持的字段:
//   - name: 文件名
//   - uid: 文件唯一标识
//   - url: 文件 URL
//   - http_url: 文件 HTTP URL
//   - content: base64 编码的文件内容
func mapToSFSFile(m map[string]any) *engine.SFSFile {
	file := &engine.SFSFile{}
	if v, ok := m[models.SFSFieldName]; ok && v != nil {
		if name, ok := v.(string); ok {
			stdlog.Debugf("mapToSFSFile found name: %s", name)
			file.Name = name
		}
	}
	if v, ok := m[models.SFSFieldUID]; ok && v != nil {
		if uid, ok := v.(string); ok {
			stdlog.Debugf("mapToSFSFile found uid: %s", uid)
			file.Uid = uid
		}
	}
	if v, ok := m[models.SFSFieldURL]; ok && v != nil {
		if url, ok := v.(string); ok {
			stdlog.Debugf("mapToSFSFile found url: %s", url)
			file.Url = url
		}
	}
	if v, ok := m[models.SFSFieldHTTPURL]; ok && v != nil {
		if httpUrl, ok := v.(string); ok {
			stdlog.Debugf("mapToSFSFile found httpUrl: %s", httpUrl)
			file.HttpUrl = httpUrl
		}
	}
	if v, ok := m[models.SFSFieldContent]; ok && v != nil {
		if base64Content, ok := v.(string); ok {
			stdlog.Debugf("mapToSFSFile found base64Content, length is %d", len(base64Content))
			bs, err := base64.StdEncoding.DecodeString(base64Content)
			if err != nil {
				stdlog.Errorf("decode base64 content error: %s", err)
			} else {
				file.Content = bs
			}
		}
	}
	return file
}

// 将 agent_definition.Tool 数组转换为 openai.Tool 数组
func convertToOpenAITools(tools []agent_definition.Tool) ([]openai.Tool, error) {
	openaiTools := make([]openai.Tool, 0, len(tools))

	for _, tool := range tools {
		// 创建 openai.Tool 结构
		openaiTool := openai.Tool{
			Type: openai.ToolTypeFunction,
			Function: &openai.FunctionDefinition{
				Name:        tool.NameForModel,
				Description: tool.Description,
			},
		}

		// 将 tool.Parameters 转换为 json.RawMessage
		paramsBytes, err := json.Marshal(tool.Parameters)
		if err != nil {
			return nil, stderr.Wrap(err, "marshal tool parameters")
		}

		openaiTool.Function.Parameters = json.RawMessage(paramsBytes)
		openaiTools = append(openaiTools, openaiTool)
	}

	return openaiTools, nil
}

func getOpenaiBaseUrl(urlStr string) (string, error) {
	if urlStr == "" {
		return "", stderr.InvalidParam.Errorf("url is empty")
	}
	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return "", stderr.Wrap(err, "failed to parse url")
	}
	// 移除查询参数
	parsedURL.RawQuery = ""
	// 移除末尾的 /chat/completions
	const openaiChatSuffix = "/chat/completions"
	parsedURL.Path = strings.TrimSuffix(parsedURL.Path, openaiChatSuffix)

	return parsedURL.String(), nil
}

func getOpenaiApiKey(ctx context.Context) (string, error) {
	token, err := helper.GetToken(ctx)
	if err != nil {
		return "", stderr.Wrap(err, "failed to get token")
	}
	return strings.TrimPrefix(token, "Bearer "), nil
}
