package kapacitor

import (
	"os"
	"path/filepath"
	"strings"
	"transwarp.io/applied-ai/applet-engine/kapacitor/util"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdfs"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-engine/conf"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type FileSaveNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.FileSaveNode // 算子的参数定义
}

// newFileSaveNodee 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newFileSaveNode(et *ExecutingTask, n *pipeline.FileSaveNode, d NodeDiagnostic) (*FileSaveNode, error) {
	hn := &FileSaveNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *FileSaveNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *FileSaveNode) Point(p edge.PointMessage) (err error) {
	if p == nil || p.Fields() == nil {
		return stderr.InvalidParam.Error("recved message is nil or has no fields")
	}
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	if ctx.Call == nil {
		return stderr.Errorf("call from message context is nil")
	}
	callCtx := ctx.Call.Ctx()
	ctx.Infof("tenantID from request: %s, projectID from request: %s", helper.GetTenantID(callCtx), helper.GetProjectID(callCtx))
	output, exist, err := models.GetValueOfFields[any](p.Fields(), models.PredefinedFieldOutput)
	if err != nil || !exist {
		return stderr.Wrap(err, "failed to extract output from fields")
	}
	sfsFiles := make(SFSFiles, 0)
	if err := sfsFiles.FromAnyForFileSave(output); err != nil {
		return stderr.Wrap(err, "parse Files from file any slice")
	}
	if sfsFiles.Len() == 0 {
		return stderr.Wrap(err, "the length of Files is 0")
	}
	if err := sfsFiles.ValidateNameAndContent(); err != nil {
		return stderr.Wrap(err, "validate Files name and content")
	}

	tenantId := conf.Config.Tenant.TenantId
	projectId := conf.Config.Tenant.ProjectId
	isPublished := tenantId != "" || projectId != "" // 已经发布的服务，会通过环境变量配置tenantID 与 projectID
	if isPublished && (tenantId == "" || projectId == "") {
		return stderr.Errorf("service is published but tenantId: %s or projectId: %s is empty", tenantId, projectId)
	}
	if !isPublished {
		tenantId = helper.GetTenantID(callCtx)
		projectId = helper.GetProjectID(callCtx)
	}
	for _, sfsFile := range sfsFiles {
		localFilePath, sfsPath, err := n.getFilePaths(tenantId, projectId, sfsFile.Name, isPublished)
		if err != nil {
			return stderr.Wrap(err, "get local file path for %s", sfsFile.Name)
		}
		if n.pn.DisallowOverwrite && fileExists(localFilePath) {
			return stderr.Errorf("file %s already exists", localFilePath)
		}
		// 确保父目录存在
		parentDir := filepath.Dir(localFilePath)
		if err := os.MkdirAll(parentDir, os.ModePerm); err != nil {
			return stderr.Wrap(err, "create parent directory %s", parentDir)
		}
		if err := os.WriteFile(localFilePath, sfsFile.Content, os.ModePerm); err != nil {
			return stderr.Wrap(err, "write file %s with %d length bytes", localFilePath, len(sfsFile.Content))
		}
		sfsFile.Url = sfsPath
		sfsFile.HttpUrl = stdfs.SFSFilePath(sfsPath).ToHttpUrlV2(util.GetSystemNamespace())
	}

	var result any
	// 即使只有一个,也统一为数组格式  []sfs
	if !n.pn.EnableDownloadMode {
		result = sfsFiles
	} else {
		strs := make([]string, 0)
		for _, sfs := range sfsFiles {
			strs = append(strs, sfs.GetSfsDownloadUrl())
		}
		result = strings.Join(strs, ",")
	}
	if err := setOutputIntoFields(p.Fields(), result); err != nil {
		return stderr.Wrap(err, "set output %+v into fields", result)
	}
	return n.forwardMsg(p, true)
}

// getFilePaths 获取文件在系统中的本地存储路径与SFS路径
// 输入参数:
//   - tenantId: 租户ID
//   - projectId: 项目ID
//   - fileName: 要保存的文件名
//   - isPublished: 文件是否处于已发布状态
//
// 返回值:
//   - string: 文件将在本地存储的绝对路径
//   - string: SFS路径，格式为 "sfs:///tenants/{tenantId}/projs/{projectId}/applet/{saveDir}/{fileName}"
//   - error: 路径生成过程中发生的任何错误
//
// 对于未发布的服务:
//
//	本地路径格式: /{sfsRoot}/tenants/{tenantId}/projs/{projectId}/applet/{saveDir}/{fileName}
//	本地路径示例: /sfs/tenants/tenant1/projs/assets/applet/tempDir/a.txt
//	SFS路径示例: sfs:///tenants/tenant1/projs/assets/applet/tempDir/a.txt
//
// 对于已发布的服务:
//
//	本地路径格式: /{sfsRoot}/projs/{projectId}/applet/{saveDir}/{fileName}
//	本地路径示例: /sfs/projs/assets/applet/tempDir/a.txt
//	SFS路径示例: sfs:///tenants/tenant1/projs/assets/applet/tempDir/a.txt
func (n *FileSaveNode) getFilePaths(tenantId, projectId, fileName string, isPublished bool) (string, string, error) {
	projectRelPath, err := stdfs.NewProjectLocation(tenantId, projectId, true, AppletDir, n.pn.SaveDir)
	if err != nil {
		return "", "", stderr.Wrap(err, "create project location")
	}

	// 将文件名添加到项目相对路径中，形成完整的相对路径
	relativeFilePath := stdfs.RelativeFilePath(filepath.Join(string(projectRelPath), fileName))

	// 获取文件的绝对路径，用于本地文件操作
	absoluteFilePath := relativeFilePath.ToAbsFilePath()
	// 获取SFS协议路径，用于外部访问
	sfsProtocolPath := relativeFilePath.ToSFSFilePath()

	// 如果是已发布状态，移除本地路径中的租户部分
	if isPublished {
		absoluteFilePath = sfsTenantRootRe.ReplaceAllString(absoluteFilePath, "")
	}

	return absoluteFilePath, sfsProtocolPath, nil
}

func fileExists(path string) bool {
	// 使用 os.Stat 获取文件信息
	_, err := os.Stat(path)
	// 如果返回的错误为 nil，说明文件存在
	if err == nil {
		return true
	}
	// 使用 os.IsNotExist 判断错误是否为"不存在"
	if os.IsNotExist(err) {
		return false
	}
	// 其他错误情况（如权限不足等）也返回 false
	return false
}
