package kapacitor

import (
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type AggregateInputNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.AggregateInputNode // 算子的参数定义
}

// newAggregateInputNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newAggregateInputNode(et *ExecutingTask, n *pipeline.AggregateInputNode, d NodeDiagnostic) (*AggregateInputNode, error) {
	hn := &AggregateInputNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *AggregateInputNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

// 占位符算子，只记录本节点meta，透明转发其他数据
func (n *AggregateInputNode) Point(p edge.PointMessage) (err error) {
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	return n.forwardMsg(p, true)
}
