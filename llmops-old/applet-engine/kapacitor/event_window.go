package kapacitor

import (
	"fmt"
	"github.com/pkg/errors"
	"log"
	"sort"
	"time"
	"transwarp.io/applied-ai/applet-engine/models"

	"github.com/patrickmn/go-cache"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type EventWindowNode struct {
	node
	w   *pipeline.EventWindowNode
	buf *cache.Cache // the cache saving data before the event arrived
}

// Create a new  WindowNode, which windows data for a period of time and emits the window.
func newEventWindowNode(et *ExecutingTask, n *pipeline.EventWindowNode, d NodeDiagnostic) (*EventWindowNode, error) {
	ewn := &EventWindowNode{
		buf:  cache.New(n.PrePeriod+n.PostPeriod, 0),
		w:    n,
		node: node{Node: n, et: et, diag: d},
	}
	//ewn.w.Event = ewn.w.EventTable
	ewn.node.runF = ewn.runEventWindow
	return ewn, nil
}

func (n *EventWindowNode) runEventWindow([]byte) (err error) {
	consumer := edge.NewConsumerWithReceiver(n.ins[0], n)
	return consumer.Consume()
}

func (w *EventWindowNode) BeginBatch(edge.BeginBatchMessage) error {
	return errors.New("event window does not support batch data")
}
func (w *EventWindowNode) BatchPoint(edge.BatchPointMessage) error {
	return errors.New("event window does not support batch data")
}
func (w *EventWindowNode) EndBatch(edge.EndBatchMessage) error {
	return errors.New("event window does not support batch data")
}
func (w *EventWindowNode) Barrier(b edge.BarrierMessage) error {
	return nil
}
func (w *EventWindowNode) DeleteGroup(d edge.DeleteGroupMessage) error {
	return nil
}
func (w *EventWindowNode) Done() {}

func (n *EventWindowNode) Point(p edge.PointMessage) error {
	switch p.Name() {
	case n.w.Source:
		n.buf.SetDefault(fmt.Sprint(p.Time().UnixNano()), p)
		n.buf.DeleteExpired()
		return nil
	default:
		n.event(p)
		return nil
	}
}

func (n *EventWindowNode) event(evt edge.PointMessage) {
	log.Printf(">>> event arriving: %+v", evt.Fields())
	if n.w.PostPeriod == 0 {
		n.emit(evt)
	} else {
		go func() {
			ticker := time.NewTicker(n.w.PostPeriod)
			<-ticker.C
			n.emit(evt)
		}()
	}

}

// emit will forward a BufferedBatchMessage to its each child
func (n *EventWindowNode) emit(evt edge.PointMessage) {
	points := n.collect(evt.Time())
	if len(points) == 0 {
		points = append(points, edge.BatchPointFromPoint(evt))
	} else if n.w.WithEventFlag {
		eTags := evt.Tags()
		eFields := evt.Fields()

		for _, p := range points {
			mergeTags := make(models.Tags)
			mergeFields := make(models.Fields)

			pTags := p.Tags()
			pFields := p.Fields()

			for k, v := range pTags {
				mergeTags[k] = v
			}
			for k, v := range pFields {
				mergeFields[k] = v
			}
			for k, v := range eTags {
				mergeTags[k] = v
			}
			for k, v := range eFields {
				mergeFields[k] = v
			}

			p.SetTags(mergeTags)
			p.SetFields(mergeFields)
		}
	}

	msg := edge.NewBufferedBatchMessage(
		edge.NewBeginBatchMessage(
			n.w.Name(),
			models.NodeMeta{},
			models.Trace{},
			models.Context{},
			points[0].Tags(),
			false,
			time.Now(),
			len(points),
		),
		points,
		edge.NewEndBatchMessage(),
	)

	log.Printf("<<< event window size: %d, first value: %+v", len(points), points[0])
	if err := edge.Forward(n.outs, msg); err != nil {
		log.Printf("send event buffered batch message failed :%v", err)
	}
}

func (n *EventWindowNode) collect(mark time.Time) []edge.BatchPointMessage {
	items := n.buf.Items()
	points := make([]edge.BatchPointMessage, 0)
	if len(items) == 0 {
		return points
	}
	pre := make([]string, 0)
	post := make([]string, 0)
	m := fmt.Sprint(mark.UnixNano())

	for t := range items {
		if t > m {
			post = append(post, t)
		} else {
			pre = append(pre, t)
		}
	}

	preCnt := int(n.w.PreCount)
	postCnt := int(n.w.PostCount)
	// trim previous items
	if preCnt > 0 && preCnt < len(pre) {
		sort.Strings(pre)
		for i := 0; i < (len(pre) - preCnt); i++ {
			delete(items, pre[i])
		}
	}

	// trim post items
	if postCnt > 0 && postCnt < len(post) {
		sort.Strings(post)
		for i := postCnt; i < len(post); i++ {
			delete(items, post[i])
		}
	}

	for _, v := range items {
		p, ok := v.Object.(edge.PointMessage)
		if !ok {
			continue
		}
		points = append(points, edge.BatchPointFromPoint(p))
	}
	return points
}
