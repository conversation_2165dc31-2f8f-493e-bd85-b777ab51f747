package kapacitor

import (
	"fmt"
	"time"

	"github.com/pkg/errors"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
	"transwarp.io/applied-ai/applet-engine/tick/ast"
	"transwarp.io/applied-ai/applet-engine/tick/stateful"
	"transwarp.io/applied-ai/applet-engine/tools/expvar"
)

const (
	statsAutoscaleIncreaseEventsCount = "increase_events"
	statsAutoscaleDecreaseEventsCount = "decrease_events"
	statsAutoscaleCooldownDropsCount  = "cooldown_drops"
)

type resourceID interface {
	ID() string
}

type autoscaler interface {
	ResourceIDFromTags(models.Tags) (resourceID, error)
	Replicas(id resourceID) (int, error)
	SetReplicas(id resourceID, replicas int) error
	SetResourceIDOnTags(id resourceID, tags models.Tags)
}

type resourceState struct {
	lastIncrease time.Time
	lastDecrease time.Time
	current      int
}

type event struct {
	ID  resourceID
	Old int
	New int
}

type AutoscaleNode struct {
	node

	a autoscaler

	replicasExpr      stateful.Expression
	replicasScopePool stateful.ScopePool

	resourceStates map[string]resourceState

	increaseCount      *expvar.Int
	decreaseCount      *expvar.Int
	cooldownDropsCount *expvar.Int

	min int
	max int

	increaseCooldown time.Duration
	decreaseCooldown time.Duration

	currentField string
}

// Create a new AutoscaleNode which can trigger autoscale events.
func newAutoscaleNode(
	et *ExecutingTask,
	d NodeDiagnostic,
	n pipeline.Node,
	a autoscaler,
	min,
	max int,
	increaseCooldown,
	decreaseCooldown time.Duration,
	currentField string,
	replicas *ast.LambdaNode,
) (*AutoscaleNode, error) {
	if min < 1 {
		return nil, fmt.Errorf("minimum count must be >= 1, got %d", min)
	}
	// Initialize the replicas lambda expression scope pool
	replicasExpr, err := stateful.NewExpression(replicas.Expression)
	if err != nil {
		return nil, errors.Wrap(err, "invalid replicas expression")
	}
	replicasScopePool := stateful.NewScopePool(ast.FindReferenceVariables(replicas.Expression))
	kn := &AutoscaleNode{
		node:              node{Node: n, et: et, diag: d},
		resourceStates:    make(map[string]resourceState),
		min:               min,
		max:               max,
		increaseCooldown:  increaseCooldown,
		decreaseCooldown:  decreaseCooldown,
		currentField:      currentField,
		a:                 a,
		replicasExpr:      replicasExpr,
		replicasScopePool: replicasScopePool,
	}
	kn.node.runF = kn.runAutoscale
	return kn, nil
}

func (n *AutoscaleNode) runAutoscale([]byte) error {
	n.increaseCount = &expvar.Int{}
	n.decreaseCount = &expvar.Int{}
	n.cooldownDropsCount = &expvar.Int{}

	n.statMap.Set(statsAutoscaleIncreaseEventsCount, n.increaseCount)
	n.statMap.Set(statsAutoscaleDecreaseEventsCount, n.decreaseCount)
	n.statMap.Set(statsAutoscaleCooldownDropsCount, n.cooldownDropsCount)

	consumer := edge.NewGroupedConsumer(
		n.ins[0],
		n,
	)
	n.statMap.Set(statCardinalityGauge, consumer.CardinalityVar())
	return consumer.Consume()
}

func (n *AutoscaleNode) NewGroup(group edge.GroupInfo, first edge.PointMeta) (edge.Receiver, error) {
	return edge.NewReceiverFromForwardReceiverWithStats(
		n.outs,
		edge.NewTimedForwardReceiver(n.timer, n.newGroup()),
	), nil
}

func (n *AutoscaleNode) newGroup() *autoscaleGroup {
	return &autoscaleGroup{
		n:    n,
		expr: n.replicasExpr.CopyReset(),
	}
}

type autoscaleGroup struct {
	n *AutoscaleNode

	expr stateful.Expression

	begin edge.BeginBatchMessage
}

func (g *autoscaleGroup) BeginBatch(begin edge.BeginBatchMessage) (edge.Message, error) {
	g.begin = begin
	return nil, nil
}

func (g *autoscaleGroup) BatchPoint(bp edge.BatchPointMessage) (edge.Message, error) {
	np, err := g.n.handlePoint(g.begin.Name(), g.begin.Dimensions(), bp, g.expr)
	if err != nil {
		g.n.diag.Error("error batch handling point", err)
	}
	return np, nil
}

func (g *autoscaleGroup) EndBatch(end edge.EndBatchMessage) (edge.Message, error) {
	return nil, nil
}

func (g *autoscaleGroup) Point(p edge.PointMessage) (edge.Message, error) {
	np, err := g.n.handlePoint(p.Name(), p.Dimensions(), p, g.expr)
	if err != nil {
		g.n.diag.Error("error handling point", err)
	}
	return np, nil
}

func (g *autoscaleGroup) Barrier(b edge.BarrierMessage) (edge.Message, error) {
	return b, nil
}
func (g *autoscaleGroup) DeleteGroup(d edge.DeleteGroupMessage) (edge.Message, error) {
	return d, nil
}
func (g *autoscaleGroup) Done() {}

func (n *AutoscaleNode) handlePoint(streamName string, dims models.Dimensions, p edge.FieldsTagsTimeGetter, expr stateful.Expression) (edge.PointMessage, error) {
	id, err := n.a.ResourceIDFromTags(p.Tags())
	if err != nil {
		return nil, err
	}
	state, ok := n.resourceStates[id.ID()]
	if !ok {
		// If we haven't seen this resource before, get its state
		replicas, err := n.a.Replicas(id)
		if err != nil {
			return nil, errors.Wrapf(err, "could not determine initial scale for %q", id)
		}
		state = resourceState{
			current: replicas,
		}
		n.resourceStates[id.ID()] = state
	}

	// Eval the replicas expression
	newReplicas, err := n.evalExpr(state.current, expr, p)
	if err != nil {
		return nil, errors.Wrap(err, "failed to evaluate the replicas expression")
	}

	// Create the event
	e := event{
		ID:  id,
		Old: state.current,
		New: newReplicas,
	}
	// Check bounds
	if n.max > 0 && e.New > n.max {
		e.New = n.max
	}
	if e.New < n.min {
		e.New = n.min
	}

	// Validate something changed
	if e.New == e.Old {
		// Nothing to do
		return nil, nil
	}

	// Update local copy of state
	change := e.New - e.Old
	state.current = e.New

	// Check last change cooldown times
	t := p.Time()
	var counter *expvar.Int
	switch {
	case change > 0:
		if t.Before(state.lastIncrease.Add(n.increaseCooldown)) {
			// Still hot, nothing to do
			n.cooldownDropsCount.Add(1)
			return nil, nil
		}
		state.lastIncrease = t
		counter = n.increaseCount
	case change < 0:
		if t.Before(state.lastDecrease.Add(n.decreaseCooldown)) {
			// Still hot, nothing to do
			n.cooldownDropsCount.Add(1)
			return nil, nil
		}
		state.lastDecrease = t
		counter = n.decreaseCount
	}

	// We have a valid event to apply
	if err := n.applyEvent(e); err != nil {
		return nil, errors.Wrap(err, "failed to apply scaling event")
	}

	// Only save the updated state if we were successful
	n.resourceStates[id.ID()] = state

	// Count event
	counter.Add(1)

	// Create new tags for the point.
	// Leave room for the namespace,kind, and resource tags.
	newTags := make(models.Tags, len(dims.TagNames)+3)

	// Copy group by tags
	for _, d := range dims.TagNames {
		newTags[d] = p.Tags()[d]
	}
	n.a.SetResourceIDOnTags(id, newTags)

	// Create point representing the event
	return edge.NewPointMessage(
		streamName, "", "",
		dims,
		models.Fields{
			"old": int64(e.Old),
			"new": int64(e.New),
		},
		newTags,
		t,
	), nil
}

func (n *AutoscaleNode) applyEvent(e event) error {
	n.diag.SettingReplicas(e.New, e.Old, e.ID.ID())
	err := n.a.SetReplicas(e.ID, e.New)
	return errors.Wrapf(err, "failed to set new replica count for %q", e.ID)
}

func (n *AutoscaleNode) evalExpr(
	current int,
	expr stateful.Expression,
	p edge.FieldsTagsTimeGetter,
) (int, error) {
	vars := n.replicasScopePool.Get()
	defer n.replicasScopePool.Put(vars)

	// Set the current replicas value on the scope if requested.
	if n.currentField != "" {
		vars.Set(n.currentField, current)
	}

	// Fill the scope with the rest of the values
	err := fillScope(vars, n.replicasScopePool.ReferenceVariables(), p)
	if err != nil {
		return 0, err
	}

	i, err := expr.EvalInt(vars)
	if err != nil {
		return 0, err
	}
	return int(i), err
}
