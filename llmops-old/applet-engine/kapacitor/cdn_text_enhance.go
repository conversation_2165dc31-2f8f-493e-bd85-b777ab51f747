package kapacitor

import (
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/kapacitor/util"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type TextEnhanceNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.TextEnhanceNode // 算子的参数定义
}

// newTextSplitNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newTextEnhanceNode(et *ExecutingTask, n *pipeline.TextEnhanceNode, d NodeDiagnostic) (*TextEnhanceNode, error) {
	hn := &TextEnhanceNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *TextEnhanceNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *TextEnhanceNode) Point(p edge.PointMessage) (err error) {
	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	modelCtx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer logError(modelCtx, err)

	ctx, err := util.GetContextFromPointMessages(p)
	if err != nil {
		return err
	}
	if n.pn == nil {
		return stderr.Internal.Error("n.pn is nil")
	}

	outChunks, err := extracChunksFromOutputField(p.Fields())
	if err != nil {
		return
	}

	params := &n.pn.WidgetParamsTextEnhance
	req := &clients.EnhanceReq{
		Num:    params.Num,
		Modes:  params.Modes,
		Prompts: params.Prompts,
		Chunks: outChunks,
	}
	if err := clients.EnhanceKnowledgeChunks(ctx, &params.ModelToolDescriber.ModelService, 0, req); err != nil {
		return err
	}
	// 结果存储在chunk的AugmentedChunks字段
	if err := setOutputIntoFields(p.Fields(), outChunks); err != nil {
		return err
	}
	return n.forwardMsg(p, true)
}
