package kapacitor

import (
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type ChunksOutputNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.ChunksOutputNode // 算子的参数定义
}

// newChunksSplitNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newChunksOutputNode(et *ExecutingTask, n *pipeline.ChunksOutputNode, d NodeDiagnostic) (*ChunksOutputNode, error) {
	hn := &ChunksOutputNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *ChunksOutputNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *ChunksOutputNode) Point(p edge.PointMessage) (err error) {
	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	modelCtx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer logError(modelCtx, err)

	outputMap, err := extracOutputFromFields[map[string]any](p.Fields())
	if err != nil {
		return stderr.Wrap(err, "extract output from fields")
	}

	chunks, err := tryCvt2Chunks(outputMap[widgets.ParamIDChunks])
	if err != nil {
		return stderr.Wrap(err, "extract chunks from outputMap")
	}

	elements, err := tryCvt2Elements(outputMap[widgets.ParamIDElements])
	if err != nil {
		return stderr.Wrap(err, "extract elements from outputMap")
	}

	outputResp := new(pb.DocSvcLoadChunkRsp)
	if temp, ok := outputMap[widgets.ParamIDLoadChunkResp]; ok {
		// 上游直接DocSvcLoadChunkRsp类型的结构
		err = stdsrv.UnmarshalMixWithProto(temp, outputResp)
		if err != nil {
			return stderr.Wrap(err, "extract LoadChunkResp from outputMap")
		}
	}
	outputResp.Chunks = append(outputResp.Chunks, chunks...)
	outputResp.Elements = append(outputResp.Elements, elements...)

	if err = setOutputIntoFields(p.Fields(), outputResp); err != nil {
		return err
	}
	return n.forwardMsg(p, true)
}
