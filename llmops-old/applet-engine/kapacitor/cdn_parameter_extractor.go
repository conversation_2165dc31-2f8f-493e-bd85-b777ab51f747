package kapacitor

import (
	"encoding/json"
	"fmt"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
	"transwarp.io/applied-ai/applet-engine/tools/model_service"
)

type ParameterExtractorNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.ParameterExtractorNode // 算子的参数定义
}

// newParameterExtractorNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newParameterExtractorNode(et *ExecutingTask, n *pipeline.ParameterExtractorNode, d NodeDiagnostic) (*ParameterExtractorNode, error) {
	hn := &ParameterExtractorNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *ParameterExtractorNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *ParameterExtractorNode) Point(p edge.PointMessage) (err error) {
	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	call := ctx.Call
	if call == nil {
		return stderr.Wrap(err, "get call from message context")
	}
	callCtx := call.Ctx()
	query, exist, err := models.GetValueOfFields[string](p.Fields(), models.PredefinedFieldOutput)
	if err != nil || !exist {
		return stderr.Wrap(err, "failed to extract output from fields")
	}
	parameterExtractorPrompt, err := n.fillPromptTemplate(query, n.pn.ParsedParams.Params)
	if err != nil {
		return stderr.Wrap(err, "failed to fill prompt template")
	}
	result, err := model_service.SyncChat(callCtx, n.pn.ParsedParams.ModelService, parameterExtractorPrompt)
	if err != nil {
		return stderr.Wrap(err, "failed to chat with model service")
	}
	var extractedParameters map[string]any
	if err := json.Unmarshal([]byte(result), &extractedParameters); err != nil {
		return stderr.Wrap(err, "failed to unmarshal llm result %s to parameters", result)
	}
	ctx.Infof("提取的参数: %+v", extractedParameters)
	if err := setOutputIntoFields(p.Fields(), extractedParameters); err != nil {
		return stderr.Wrap(err, "set output %+v into fields", extractedParameters)
	}
	return n.forwardMsg(p, false)
}

func (n *ParameterExtractorNode) cvt2FunctionParamters(params []*agent_definition.APIToolParam) *triton.FunctionParameters {
	properties := make(map[string]triton.ParameterProperty)
	required := make([]string, 0)

	for _, p := range params {
		// 不支持设置p.ModelIgnore参数，设置的参数全部对模型可见
		if p.Required {
			required = append(required, p.Name)
		}
		properties[p.Name] = triton.ParameterProperty{
			Type:        triton.ParameterType(p.ParamValueType),
			Description: p.Desc,
		}
	}

	return &triton.FunctionParameters{
		Type:       triton.ParameterTypeObject,
		Properties: properties,
		Required:   required,
	}
}

func (n *ParameterExtractorNode) fillPromptTemplate(inputText string, parameters []*agent_definition.APIToolParam) (string, error) {
	funcParams := n.cvt2FunctionParamters(parameters)
	funcParamsBytes, err := json.Marshal(funcParams)
	if err != nil {
		return "", stderr.Wrap(err, "failed to marshal function parameters")
	}
	return fmt.Sprintf(models.ParameterExtractorPromptTemplate, string(funcParamsBytes), inputText), nil
}
