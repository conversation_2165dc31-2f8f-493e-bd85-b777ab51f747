package kapacitor

import (
	"context"
	"encoding/base64"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	lpb "transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
	"transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/engine"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/kapacitor/util"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type DlieInferNode struct {
	node
	pn *pipeline.DlieInferNode
}

// Create a new  DlieInferNode which submits received items via POST to an HTTP endpoint
func newDlieInferNode(et *ExecutingTask, n *pipeline.DlieInferNode, d NodeDiagnostic) (*DlieInferNode, error) {
	hn := &DlieInferNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *DlieInferNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *DlieInferNode) BeginBatch(begin edge.BeginBatchMessage) error {
	return stderr.Internal.Error("unsupported begin batch message")
}

func (n *DlieInferNode) BatchPoint(bp edge.BatchPointMessage) error {
	return stderr.Internal.Error("unsupported batch point message")
}

func (n *DlieInferNode) EndBatch(end edge.EndBatchMessage) error {
	return stderr.Internal.Error("unsupported end batch message")
}

func (n *DlieInferNode) BufferedBatch(batch edge.BufferedBatchMessage) (edge.Message, error) {
	return nil, stderr.Internal.Error("unsupported buffered batch message")
}

func (n *DlieInferNode) Point(p edge.PointMessage) (err error) {
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	output, err := extracOutputFromFields[any](p.Fields())
	if err != nil {
		return stderr.Wrap(err, "failed to extract output from fields")
	}
	if ctx.Call == nil {
		return stderr.Wrap(err, "get call from message context")
	}
	callCtx := ctx.Call.Ctx()
	switch n.pn.WidgetParams.ModelService.SubKind {
	case lpb.ModelSubKind_MODEL_SUB_KIND_NLP_TEXT_GENERATION:
		return n.doChat(p, output, callCtx)
	case lpb.ModelSubKind_MODEL_SUB_KIND_NLP_TEXT_VECTOR:
		return n.doEmbedding(p, output)
	case lpb.ModelSubKind_MODEL_SUB_KIND_MULTI_TEXT_TO_IMAGE:
		return n.doGenerateImage(p, output, callCtx)
	case lpb.ModelSubKind_MODEL_SUB_KIND_MULTI_IMAGE_TO_TEXT:
		return n.doChatImage(p, output, callCtx)
	case lpb.ModelSubKind_MODEL_SUB_KIND_SR_STT:
		return n.doSpeechRecognition(p, output, callCtx)
	//	通用推理模型 -> 原生请求体 http 原生响应
	default:
		return n.doGeneralInfer(p, output)
	}
}

// TODO 完善side-car的通用推理接口,并通过httpClient转发
func (n *DlieInferNode) doGeneralInfer(p edge.PointMessage, v any) error {
	return stderr.Error("wait to support doGeneralInfer")
	//fields, ok := v.(map[string]any)
	//if !ok {
	//	return stderr.Error("expect the output %T, but got %T", fields, v)
	//}
	//req := make(triton.GeneralInferReq)
	//for k, v := range fields {
	//	switch tv := v.(type) {
	//	case []byte:
	//		req[k] = tv
	//	case string:
	//		req[k] = []byte(tv)
	//	default:
	//		// 数字类型会通过json.Marshal()转换为[]byte，egg: 123 -> [49 50 51](对应字符串123，不包含引号)
	//		// 其他类型如struct map slice 也会转为[]byte
	//		jsonBytes, err := json.Marshal(v)
	//		if err != nil {
	//			return stderr.Wrap(err, "marshal %T type %v to json", v, v)
	//		}
	//		req[k] = jsonBytes
	//	}
	//}
	//rsp, err := n.tc.SyncModelInfer(n.pn.ModelName, &req)
	//if err != nil {
	//	return stderr.Wrap(err, "sync general model infer")
	//}
	//
	//if err := setOutputIntoFields(p.Fields(), rsp); err != nil {
	//	return stderr.Wrap(err, "set output %+v into fields", rsp)
	//}
	//return n.handleResAsPM(p)
}

func (n *DlieInferNode) doEmbedding(p edge.PointMessage, v any) error {
	var texts []string
	if t, ok := v.(string); ok {
		texts = append(texts, t)
	}
	if ts, ok := v.([]string); ok {
		texts = ts
	}
	if len(texts) == 0 {
		return stderr.Error("there is no texts output from previous node")
	}
	req := &triton.OpenAiTextVectorReq{
		Input: texts,
	}

	ctx, err := util.GetContextFromPointMessages(p)
	if err != nil {
		return stderr.Wrap(err, "get context of point message")
	}
	resp, err := clients.Embedding(ctx, &n.pn.WidgetParams.ModelService, req)
	if err != nil {
		return stderr.Wrap(err, "sync embedding model infer")
	}
	output := &widgets.NodeOutPortText2Vector{
		FileName:             "",
		OpenAiTextVectorReq:  req,
		OpenAiTextVectorResp: resp,
	}
	if err := setOutputIntoFields(p.Fields(), output); err != nil {
		return stderr.Wrap(err, "set output %+v into fields", output)
	}
	//TODO: 暂时不处理fileName，之后可以在fileds中增加PassthroughParams，传递Output之外的参数，并且需要DeepCopy PassthroughParams
	// fileName, ok := extracValue[string](models.PredefinedFieldFileName, p)
	// if ok {
	// 	fields[models.PredefinedFieldFileName] = fileName
	// }
	return n.handleResAsPM(p)
}

func (n *DlieInferNode) doChat(p edge.PointMessage, v any, callCtx context.Context) error {
	text, ok := v.(string)
	if !ok {
		return stderr.Error("expect the output %T, but got %T", text, v)
	}
	// 默认使用流式,下游算子不能处理流式响应时，则改为非流式
	needStream := needStreamOutput(n.children)
	handler := func(textContent string) error {
		if err := setOutputIntoFields(p.Fields(), textContent); err != nil {
			return stderr.Wrap(err, "set output %+v into fields", textContent)
		}
		return n.handleResAsPM(p)
	}
	msgCtx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context of point message")
	}
	if needStream {
		if err := n.emitBegin(p); err != nil {
			return stderr.Wrap(err, "emit begin message")
		}
		handler = func(textContent string) error {
			return n.handleResAsBPM(msgCtx, textContent)
		}
	}
	chatReq, err := clients.GetBaseChatReq(&n.pn.WidgetParams.ModelService)
	if err != nil {
		return stderr.Wrap(err, "failed to get BaseChatReq")
	}
	chatReq.Messages = []triton.MultimodalMessageItem{
		{
			Role:    triton.OpenaiSystemMsg,
			Content: n.pn.System,
		},
		{
			Role:    triton.OpenaiUserMsg,
			Content: text,
		},
	}
	stdlog.Debugf("dlie openai format chat req %+v", chatReq)
	if needStream {
		err = clients.StreamChat(callCtx, &n.pn.WidgetParams.ModelService, chatReq, handler)
	} else {
		err = clients.SyncChat(callCtx, &n.pn.WidgetParams.ModelService, chatReq, handler)
	}
	if err != nil {
		return stderr.Wrap(err, "failed to do http infer ")
	}
	if needStream {
		if err := n.emitEnd(msgCtx); err != nil {
			return stderr.Wrap(err, "emit end message")
		}
	}
	return nil
}

func (n *DlieInferNode) doGenerateImage(p edge.PointMessage, v any, callCtx context.Context) error {
	text, ok := v.(string)
	if !ok {
		return stderr.Error("expect the output %T, but got %T", text, v)
	}
	imageGenReq := &triton.OpenAiImageGenReq{Prompt: text, N: 1, ResponseFormat: triton.ImageGenResponseFormatUrl}
	if n.pn.UseBase64 {
		imageGenReq.ResponseFormat = triton.ImageGenResponseFormatB64Json
	}
	resp, err := clients.ImageGen(callCtx, &n.pn.WidgetParams.ModelService, imageGenReq)
	if err != nil {
		return stderr.Wrap(err, "openai image gen model infer, req: %+v", imageGenReq)
	}
	if len(resp.Data) == 0 {
		return stderr.Error("openai image gen model infer, but got empty response")
	}
	if err := setOutputIntoFields(p.Fields(), resp); err != nil {
		return stderr.Wrap(err, "set output %+v into fields", resp)
	}
	return n.forwardMsg(p, false)
}

func (n *DlieInferNode) doChatImage(p edge.PointMessage, v any, callCtx context.Context) error {
	fields, ok := v.(map[string]any)
	if !ok {
		return stderr.Error("expect the output %T, but got %T", fields, v)
	}
	fileAny, exist, err := models.GetValueOfFields[any](fields, models.PredefinedFieldFile)
	if err != nil || !exist {
		return stderr.Wrap(err, "failed to extract file from fields")
	}
	file, err := tryCvt2SFSFile(fileAny)
	if err != nil {
		return stderr.Wrap(err, "try convert file any to SFSFile")
	}
	question, exist, err := models.GetValueOfFields[string](fields, models.PredefinedFieldQuestion)
	if err != nil || !exist {
		return stderr.Wrap(err, "failed to extract question from fields")
	}
	if question == "" {
		return stderr.Error("extracted question is empty")
	}

	needStream := needStreamOutput(n.children)
	handler := func(textContent string) error {
		if err := setOutputIntoFields(p.Fields(), textContent); err != nil {
			return stderr.Wrap(err, "set output %+v into fields", textContent)
		}
		return n.handleResAsPM(p)
	}
	msgCtx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context of point message")
	}
	if needStream {
		if err := n.emitBegin(p); err != nil {
			return stderr.Wrap(err, "emit begin message")
		}
		handler = func(textContent string) error {
			return n.handleResAsBPM(msgCtx, textContent)
		}
	}
	inferReq, err := n.buildChatImageReq(file, question)
	if err != nil {
		return stderr.Wrap(err, "failed to buildChatImageReq")
	}
	if needStream {
		err = clients.StreamChat(callCtx, &n.pn.WidgetParams.ModelService, inferReq, handler)
	} else {
		err = clients.SyncChat(callCtx, &n.pn.WidgetParams.ModelService, inferReq, handler)
	}
	if err != nil {
		return stderr.Wrap(err, "failed to do http infer ")
	}
	if needStream {
		if err := n.emitEnd(msgCtx); err != nil {
			return stderr.Wrap(err, "emit end message")
		}
	}
	return nil
}

// doSpeechRecognition
func (n *DlieInferNode) doSpeechRecognition(p edge.PointMessage, v any, callCtx context.Context) error {
	file, err := tryCvt2SFSFile(v)
	if err != nil {
		return stderr.Wrap(err, "tryCvt2SFSFile")
	}
	modelService := &n.pn.WidgetParams.ModelService
	audioTransReq, err := clients.GetBaseAudioReq(modelService)
	if err != nil {
		return stderr.Wrap(err, "get base audio req")
	}
	audioTransReq.File = file.Content
	audioTransResp, err := clients.AudioTrans(callCtx, modelService, audioTransReq)
	if err != nil {
		return stderr.Wrap(err, "openai audio trans model infer, req: %+v", audioTransReq)
	}
	if err := setOutputIntoFields(p.Fields(), audioTransResp); err != nil {
		return stderr.Wrap(err, "set output %+v into fields", audioTransResp)
	}
	return n.forwardMsg(p, false)
}

func (n *DlieInferNode) buildChatImageReq(file *engine.SFSFile, question string) (*triton.OpenAiChatReq, error) {
	if file == nil || len(file.Content) == 0 {
		return nil, stderr.Errorf("the file content is empty")
	}

	url := file.Url
	if n.pn.UseBase64 {
		ext := strings.TrimPrefix(filepath.Ext(file.Url), ".")
		url = fmt.Sprintf("data:image/%s;base64,%s", ext, base64.StdEncoding.EncodeToString(file.Content))
	}

	messages := []triton.MultimodalMessageItem{
		{
			Role: triton.OpenaiUserMsg,
			Content: []triton.MultimodalContentItem{
				{
					Type: "text",
					Text: question,
				},
				{
					Type: "image_url",
					ImageUrl: map[string]string{
						"url": url,
					},
				},
			},
		},
	}

	return &triton.OpenAiChatReq{
		Messages: messages,
	}, nil
}

func (n *DlieInferNode) Barrier(b edge.BarrierMessage) error {
	return nil
}
func (n *DlieInferNode) DeleteGroup(d edge.DeleteGroupMessage) error {
	return nil
}
func (n *DlieInferNode) Done() {}

func (n *DlieInferNode) handleResAsBPM(ctx models.Context, res any) error {
	stdlog.Infof("recv : %s", res)
	fields := models.Fields{
		models.PredefinedFieldOutput:  res,
		models.PredefinedFieldContext: ctx,
	}
	bpm := edge.NewBatchPointMessage(fields, n.inferResTags(), time.Now())
	return n.forwardMsg(bpm, true)
}

func (n *DlieInferNode) handleResAsPM(p edge.PointMessage) error {
	return n.forwardMsg(p, true)
}

func (n *DlieInferNode) emitBegin(m edge.PointMessage) error {
	trace, err := m.Trace()
	if err != nil {
		return stderr.Wrap(err, "get trace from point message")
	}
	ctx, err := m.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	bm := edge.NewBeginBatchMessage(m.Name(), n.pn.NodeMeta, trace, ctx, nil, false, m.Time(), 0)
	return n.forwardMsg(bm, true)
}

func (n *DlieInferNode) emitEnd(ctx models.Context) error {
	em := edge.NewEndBatchMessage()
	// TODO: 在NewEndBatchMessage函数中添加context参数
	em.SetContext(ctx)
	return n.forwardMsg(em, true)
}

func (n *DlieInferNode) inferResTags() models.Tags {
	model := &n.pn.WidgetParams.ModelService
	return map[string]string{
		"model_name": model.Name,
		"model_url":  model.FullUrl,
	}
}

// needStreamOutput 判断模型是否需要流式输出，当需要把结果直接返回给用户时，需要流式输出
func needStreamOutput(children []Node) bool {
	for _, child := range children {
		switch child.(type) {
		case *TextDisplayNode, *HttpRspNode, *UnionNode, *TextSensitiveFilterNode, *OutputGuardrailNode: // 可以流式输出
		default:
			return false
		}
	}
	return true
}
