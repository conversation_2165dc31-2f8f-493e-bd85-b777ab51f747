package kapacitor

import (
	"os"
	"path/filepath"
	"strings"
	"transwarp.io/applied-ai/applet-engine/kapacitor/util"

	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/engine"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdfs"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
	"transwarp.io/applied-ai/applet-engine/conf"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type FileInputNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.FileInputNode // 算子的参数定义
}

// newFileInputNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newFileInputNode(et *ExecutingTask, n *pipeline.FileInputNode, d NodeDiagnostic) (*FileInputNode, error) {
	hn := &FileInputNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *FileInputNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *FileInputNode) Point(p edge.PointMessage) (err error) {
	if p == nil || p.Fields() == nil {
		return stderr.InvalidParam.Error("recved message is nil or has no fields")
	}
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	isListenHttp, err := n.isPreNodeListenHttp()
	if err != nil {
		return stderr.Wrap(err, "failed to check if pre node is listen http")
	}

	if !isListenHttp {
		sfsFile, exist, err := models.GetValueOfFields[*engine.SFSFile](p.Fields(), models.PredefinedFieldOutput)
		if err != nil || !exist {
			return stderr.Wrap(err, "failed to extract filesBytes from fields")
		}
		return n.forwardSFSFile(p, sfsFile)
	}

	output, exist, err := models.GetValueOfFields[map[string]any](p.Fields(), models.PredefinedFieldOutput)
	if err != nil || !exist {
		return stderr.Wrap(err, "failed to extract output from fields")
	}
	// 尝试从 {nodeid}##{InputKey} key中获取文件
	inputFieldKey := n.pn.InputFieldKey(n.pn.Id)
	filesAny, exist, err := models.GetValueOfFields[[]any](output, inputFieldKey)
	if err != nil {
		return stderr.Wrap(err, "failed to extract %s from output", inputFieldKey)
	}
	if !exist {
		// 如果找不到 {nodeid}##{InputKey} key，则尝试从 {InputKey} key中获取文件
		filesAny, exist, err = models.GetValueOfFields[[]any](output, n.pn.InputKey)
		if err != nil {
			return stderr.Wrap(err, "failed to extract %s from output", n.pn.InputKey)
		}
		if !exist {
			// 如果 {nodeid}##{InputKey} 与 {InputKey} 都不存在，直接向后传递空字节数组
			ctx.Warnf("there is no %s key, forward empty value", n.pn.InputKey)
			return n.forwardSFSFile(p, new(engine.SFSFile))
		}
	}
	sfsFiles := make(SFSFiles, 0)
	if err := sfsFiles.FromAnySlice(filesAny); err != nil {
		return stderr.Wrap(err, "parse SFSFiles from any slice with type %T", filesAny)
	}
	if sfsFiles.Len() == 0 {
		ctx.Warnf("length of sfsFiles is 0, forward empty sfsfile")
		return n.forwardSFSFile(p, new(engine.SFSFile))
	}
	if sfsFiles.Len() > 1 {
		return stderr.Error("multi files not supported yet")
	}
	sfsFile := sfsFiles[0]
	// 获取租户和项目ID
	tenantId := conf.Config.Tenant.TenantId
	projectId := conf.Config.Tenant.ProjectId
	isPublished := tenantId != "" || projectId != ""
	if isPublished && (tenantId == "" || projectId == "") {
		return stderr.Errorf("service is published but tenantId: %s or projectId: %s is empty", tenantId, projectId)
	}
	if sfsFile.Url == "" {
		// 检查 Content 是否存在
		if len(sfsFile.Content) == 0 {
			// 如果 url 和 content 都为空，则直接转发空文件
			ctx.Warnf("both sfs file url and content are empty, forward empty sfsfile")
			return n.forwardSFSFile(p, new(engine.SFSFile))
		}
		// Content 的文件名不能为空
		if sfsFile.Name == "" {
			return stderr.Errorf("sfs content has no file name, can not save to file")
		}
		if !isPublished {
			// 未发布服务，从请求上下文获取租户和项目ID
			if ctx.Call == nil {
				return stderr.Errorf("call context is nil")
			}
			callCtx := ctx.Call.Ctx()
			tenantId = helper.GetTenantID(callCtx)
			projectId = helper.GetProjectID(callCtx)
			ctx.Infof("get tenantId: %s, projectId: %s from call context", tenantId, projectId)
			if tenantId == "" || projectId == "" {
				return stderr.Errorf("tenant ID or project ID is empty. tenantId: %s, projectId: %s", tenantId, projectId)
			}
		}
		// 使用 saveContentToFile 保存文件
		sfsPath, err := saveContentToFile(tenantId, projectId, sfsFile.Name, sfsFile.Content, isPublished)
		if err != nil {
			return stderr.Wrap(err, "failed to save content to file")
		}
		// 更新文件路径为 sfs 路径
		sfsFile.Url = sfsPath
		ctx.Infof("saved file content to %s", sfsPath)
	}
	// 获取文件的本地路径
	localFilePath, err := stdfs.GetSFSLocalPath(sfsFile.Url)
	if err != nil {
		return stderr.Wrap(err, "get local path of sfs file %s", sfsFile.Url)
	}
	// 设置http url
	sfsFile.HttpUrl = stdfs.SFSFilePath(sfsFile.Url).ToHttpUrlV2(util.GetSystemNamespace())
	// 已发布服务，需要将路径中的租户ID去掉
	if isPublished {
		// /sfs/tenants/llmops-assets/projs/assets/xxx  ->  /sfs/projs/assets/xxx
		localFilePath = sfsTenantRootRe.ReplaceAllString(localFilePath, "")
	}

	// 检查文件大小
	fileInfo, err := os.Stat(localFilePath)
	if err != nil {
		return stderr.Wrap(err, "get file info for %s", localFilePath)
	}
	fileSizeMB := float64(fileInfo.Size()) / script.MB
	if fileSizeMB > float64(n.pn.ParsedParams.MaxFileSizeMB) {
		return stderr.Errorf("file size %.2f MB exceeds maximum allowed size of %d MB", fileSizeMB, n.pn.ParsedParams.MaxFileSizeMB)
	}

	// 检查文件扩展名
	if len(n.pn.ParsedParams.AllowedExtensions) > 0 {
		fileExt := strings.ToLower(filepath.Ext(localFilePath))
		isAllowed := false
		for _, allowedExt := range n.pn.ParsedParams.AllowedExtensions {
			if allowedExt == "*" || allowedExt == fileExt {
				isAllowed = true
				break
			}
		}
		if !isAllowed {
			return stderr.Error("file extension %s is not allowed", fileExt)
		}
	}

	if n.pn.ParsedParams.IsFileContentRead {
		fileBytes, err := os.ReadFile(localFilePath)
		if err != nil {
			return stderr.Internal.Cause(err, "read file %s", sfsFile.Url)
		}
		sfsFile.Content = fileBytes
	}
	ctx.FileSfsUrl = sfsFile.Url
	ctx.FileHttpUrl = sfsFile.HttpUrl
	p.SetContext(ctx)
	return n.forwardSFSFile(p, sfsFile)
}

func (n *FileInputNode) forwardSFSFile(p edge.PointMessage, file *engine.SFSFile) error {
	if err := setOutputIntoFields(p.Fields(), file); err != nil {
		return stderr.Wrap(err, "set sfs file %s into output field", file.Name)
	}
	return n.forwardMsg(p, true)
}
