package kapacitor

import (
	"time"

	"github.com/patrickmn/go-cache"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

const (
	StreamCacheExpirationTime  = 300 * time.Second
	StreamCacheCleanupInterval = 10 * time.Second
)

type AggregateStreamNode struct {
	node
	unsupportedBatchNode
	pn          *pipeline.AggregateStreamNode
	streamCache *cache.Cache
}

func newAggregateStreamNode(et *ExecutingTask, n *pipeline.AggregateStreamNode, d NodeDiagnostic) (*AggregateStreamNode, error) {
	streamCache := cache.New(StreamCacheExpirationTime, StreamCacheCleanupInterval)
	hn := &AggregateStreamNode{
		node:        node{Node: n, et: et, diag: d},
		pn:          n,
		streamCache: streamCache,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *AggregateStreamNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *AggregateStreamNode) Point(p edge.PointMessage) (err error) {
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	if p == nil || p.Fields() == nil {
		return stderr.Internal.Errorf("received message is nil or has no fields")
	}
	return n.forwardMsg(p, false)
}

func (n *AggregateStreamNode) BeginBatch(begin edge.BeginBatchMessage) (err error) {
	ctx, _ := begin.Context()
	defer func() {
		logError(ctx, err)
	}()

	call := ctx.Call
	if call == nil {
		return stderr.Internal.Errorf("call is nil")
	}

	// 创建新的PointMessage并保存到缓存中
	pointMsg := edge.NewPointMessage(
		begin.Name(),
		"thinger", // database
		"autogen", // retention policy
		begin.Dimensions(),
		models.Fields{}, // 空的fields，后续在BatchPoint中填充
		begin.Tags(),
		begin.Time(),
	)

	// 设置Context和Meta
	if err := pointMsg.SetContext(ctx); err != nil {
		return stderr.Wrap(err, "failed to set context for point message")
	}
	meta, _ := begin.Meta()
	if err := pointMsg.SetMeta(meta); err != nil {
		return stderr.Wrap(err, "failed to set meta for point message")
	}
	if err := pointMsg.SetTrace(begin.Trace()); err != nil {
		return stderr.Wrap(err, "failed to set trace for point message")
	}
	// 保存到缓存
	n.streamCache.Set(call.ReqID(), pointMsg, cache.DefaultExpiration)

	return nil
}

func (n *AggregateStreamNode) BatchPoint(bp edge.BatchPointMessage) (err error) {
	if bp == nil || bp.Fields() == nil {
		return stderr.Internal.Errorf("received message is nil or has no fields")
	}
	ctx, _ := bp.Context()
	defer func() {
		logError(ctx, err)
	}()

	call := ctx.Call
	if call == nil {
		return stderr.Internal.Errorf("call is nil")
	}

	// 从缓存中获取PointMessage
	cached, exists := n.streamCache.Get(call.ReqID())
	if !exists {
		return stderr.Internal.Errorf("no cached point message found for request %s", call.ReqID())
	}

	pointMsg, ok := cached.(edge.PointMessage)
	if !ok {
		return stderr.Internal.Errorf("cached value is not a PointMessage")
	}

	// 从 bp.Fields() 获取新数据
	newValue, exist, err := models.GetValueOfFields[any](bp.Fields(), models.PredefinedFieldOutput)
	if err != nil || !exist {
		return stderr.Wrap(err, "failed to extract output from fields")
	}

	// 获取当前消息的字段
	currentFields := pointMsg.Fields()
	var currentValue any
	// 从当前字段中获取output字段的值
	currentValue, _, err = models.GetValueOfFields[any](currentFields, models.PredefinedFieldOutput)
	if err != nil {
		return stderr.Wrap(err, "failed to get current output from fields")
	}
	if err := utils.AppendData(&currentValue, newValue); err != nil {
		return stderr.Wrap(err, "failed to append new data %+v to current data %+v", newValue, currentValue)
	}

	// 更新字段中的output值
	if err := models.SetKeyValueOfFields(currentFields, models.PredefinedFieldOutput, currentValue); err != nil {
		return stderr.Wrap(err, "failed to update output in fields")
	}
	// 设置更新后的字段到消息中
	pointMsg.SetFields(currentFields)

	// 更新缓存
	n.streamCache.Set(call.ReqID(), pointMsg, cache.DefaultExpiration)

	return nil
}

func (n *AggregateStreamNode) EndBatch(end edge.EndBatchMessage) (err error) {
	ctx, _ := end.Context()
	defer func() {
		logError(ctx, err)
	}()

	call := ctx.Call
	if call == nil {
		return stderr.Internal.Errorf("call is nil")
	}

	// 从缓存中获取并删除PointMessage
	cached, exists := n.streamCache.Get(call.ReqID())
	if !exists {
		return stderr.Internal.Errorf("no cached point message found for request %s", call.ReqID())
	}
	n.streamCache.Delete(call.ReqID())

	pointMsg, ok := cached.(edge.PointMessage)
	if !ok {
		return stderr.Internal.Errorf("cached value is not a PointMessage")
	}

	// 转发最终的PointMessage
	return n.forwardMsg(pointMsg, false)
}
