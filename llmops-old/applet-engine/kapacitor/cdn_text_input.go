package kapacitor

import (
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type TextInputNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.TextInputNode // 算子的参数定义
}

// newTextInputNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newTextInputNode(et *ExecutingTask, n *pipeline.TextInputNode, d NodeDiagnostic) (*TextInputNode, error) {
	hn := &TextInputNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *TextInputNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *TextInputNode) Point(p edge.PointMessage) (err error) {
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	isListenHttp, err := n.isPreNodeListenHttp()
	if err != nil {
		return stderr.Wrap(err, "failed to check if pre node is listen http")
	}

	if !isListenHttp {
		text, exist, err := models.GetValueOfFields[string](p.Fields(), models.PredefinedFieldOutput)
		if err != nil || !exist {
			return stderr.Wrap(err, "failed to extract text from fields")
		}
		return n.forwardText(p, text)
	}

	output, exist, err := models.GetValueOfFields[map[string]any](p.Fields(), models.PredefinedFieldOutput)
	if err != nil || !exist {
		return stderr.Wrap(err, "failed to extract output from fields")
	}
	// 尝试从 {nodeid}##{InputKey} key中获取输入文本
	inputFieldKey := n.pn.InputFieldKey(n.pn.Id)
	text, exist, err := models.GetValueOfFields[string](output, inputFieldKey)
	if err != nil {
		return stderr.Wrap(err, "failed to extract %s from output", inputFieldKey)
	}
	if !exist {
		// 如果找不到 {nodeid}##{InputKey} key，则尝试从 {InputKey} key中获取输入文本
		text, exist, err = models.GetValueOfFields[string](output, n.pn.InputKey)
		if err != nil {
			return stderr.Wrap(err, "failed to extract %s from output", n.pn.InputKey)
		}
		if !exist {
			// 如果 {nodeid}##{InputKey} 与 {InputKey} 都不存在，直接向后传递空字节数组
			ctx.Warnf("there is no %s key, forward empty value", n.pn.InputKey)
			return n.forwardText(p, "")
		}
	}
	return n.forwardText(p, text)
}

func (n *TextInputNode) forwardText(p edge.PointMessage, text string) error {
	if err := setOutputIntoFields(p.Fields(), text); err != nil {
		return stderr.Wrap(err, "set text input %s into output fields", text)
	}
	return n.forwardMsg(p, true)
}
