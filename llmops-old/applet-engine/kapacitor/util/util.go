package util

import (
	"context"
	"transwarp.io/applied-ai/aiot/vision-std/boot/k8s"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-engine/conf"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/pkg/debug"
)

func GetContextFromPointMessages(p edge.PointMessage) (context.Context, error) {
	call, err := GetCallFromPointMessages(p)
	if err != nil {
		return nil, err
	}
	return call.Ctx(), nil
}
func GetCallFromPointMessages(p edge.PointMessage) (*debug.Call, error) {
	ctx, err := p.Context()
	if err != nil {
		return nil, stderr.Wrap(err, "get context from point message")
	}
	call := ctx.Call
	if call == nil {
		return nil, stderr.Wrap(err, "get call from message context")
	}
	return call, nil
}

// CastToStrSlice  显示转为[]string
func CastToStrSlice(data any) ([]string, error) {
	res := make([]string, 0)
	slice, ok := data.([]interface{})
	if !ok {
		return nil, stderr.Errorf("the type of data: %v is not []string", data)
	}
	for _, item := range slice {
		str, ok := item.(string)
		if !ok {
			return nil, stderr.Errorf("the type of data: %v is not []string", data)
		}
		res = append(res, str)
	}
	return res, nil
}

func IsStrSlice(data any) bool {
	_, err := CastToStrSlice(data)
	return err == nil
}

func GetSystemNamespace() string {
	if sysNs := conf.Config.Tenant.SystemNamespace; sysNs != "" {
		return sysNs
	}
	return k8s.CurrentNamespaceInCluster()
}
