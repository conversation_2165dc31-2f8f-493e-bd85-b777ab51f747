package kapacitor

import (
	"fmt"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-engine/clients"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type ToolCallNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.ToolCallNode // 算子的参数定义
}

func newToolCallNode(et *ExecutingTask, n *pipeline.ToolCallNode, d NodeDiagnostic) (*ToolCallNode, error) {
	hn := &ToolCallNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *ToolCallNode) Point(p edge.PointMessage) (err error) {
	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	toolInput, err := extracOutputFromFields[map[string]any](p.Fields())
	if err != nil {
		return stderr.Wrap(err, "failed to extract output from fields")
	}
	call := ctx.Call
	if call == nil {
		return stderr.Internal.Error("context call is nil")
	}
	showThought := call.IsThought()
	// ToolDescriber 经过json.Unmarshal()赋值，不为nil
	toolDescriber := n.pn.ToolDescriber
	toolDefinition := toolDescriber.Definition()
	thought := fmt.Sprintf(ToolThoughtTemplate, toolDefinition.NameForHuman)
	thoughtEvent := models.ThoughtEvent{
		Data:  thought,
		Event: stdsrv.SSEEvtThought,
	}
	if err := sendSSE(call, thoughtEvent, showThought); err != nil {
		return stderr.Wrap(err, "failed to send thought event")
	}

	actionData := models.ActionEventData{
		Tool:      toolDefinition,
		ToolInput: toolInput,
	}
	actionEvent := models.ActionEvent{
		Data:  actionData,
		Event: stdsrv.SSEEvtAction,
	}
	if err := sendSSE(call, actionEvent, showThought); err != nil {
		return stderr.Wrap(err, "failed to send action event")
	}
	observation, err := clients.ApiToolExecutor.Execute(ctx.Call.Ctx(), toolDescriber, toolInput)
	if err != nil {
		return stderr.Wrap(err, " failed to execute toolDescriber")
	}
	observationData := models.ObservationEventData{
		Tool:        toolDefinition,
		Observation: observation,
	}
	observationEvent := models.ObservationEvent{
		Data:  observationData,
		Event: stdsrv.SSEEvtObservation,
	}
	if err = sendSSE(call, observationEvent, showThought); err != nil {
		return stderr.Wrap(err, "failed to send observation event")
	}
	if err = setOutputIntoFields(p.Fields(), observation); err != nil {
		return stderr.Wrap(err, "set output %+v into fields", observation)
	}
	return n.forwardMsg(p, true)
}

func (n *ToolCallNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}
