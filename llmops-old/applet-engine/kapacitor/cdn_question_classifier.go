package kapacitor

import (
	"encoding/json"
	"fmt"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-backend/pkg/widgets/script"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
	"transwarp.io/applied-ai/applet-engine/tools/model_service"
)

type QuestionClassifierNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.QuestionClassifierNode // 算子的参数定义
}

// newQuestionClassifierNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newQuestionClassifierNode(et *ExecutingTask, n *pipeline.QuestionClassifierNode, d NodeDiagnostic) (*QuestionClassifierNode, error) {
	hn := &QuestionClassifierNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *QuestionClassifierNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *QuestionClassifierNode) Point(p edge.PointMessage) (err error) {
	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	existCategories := n.pn.ParsedParams.Categories
	defaultCategoryId := n.pn.ParsedParams.DefaultCategoryId
	ctx.Infof("所有分类:%+v, 默认分类: %s", existCategories, defaultCategoryId)

	call := ctx.Call
	if call == nil {
		return stderr.Wrap(err, "get call from message context")
	}
	callCtx := call.Ctx()
	query, exist, err := models.GetValueOfFields[string](p.Fields(), models.PredefinedFieldOutput)
	if err != nil || !exist {
		return stderr.Wrap(err, "failed to extract output from fields")
	}
	questionClassifierPrompt, err := n.fillPromptTempalte(query, n.pn.ParsedParams.Categories)
	if err != nil {
		return stderr.Wrap(err, "failed to fill prompt template")
	}
	result, err := model_service.SyncChat(callCtx, n.pn.ParsedParams.ModelService, questionClassifierPrompt)
	if err != nil {
		return stderr.Wrap(err, "failed to chat with model service")
	}
	ctx.Infof("模型分类结果: %s", result)
	var classifiedCategory script.Category
	if err := json.Unmarshal([]byte(result), &classifiedCategory); err != nil {
		if defaultCategoryId == "" {
			return stderr.Wrap(err, "failed to unmarshal llm result %s to script.Category struct and defaultCategoryId is empty", result)
		}
		ctx.Warnf("failed to unmarshal llm result %s to script.Category struct: %+v, use defaultCategoryId %s", result, err, defaultCategoryId)
		return n.forward2TargetNode(defaultCategoryId, p)
	}
	targetCategoryId := classifiedCategory.CategoryId
	existCategoryIds := make(map[string]bool)
	for _, category := range n.pn.ParsedParams.Categories {
		existCategoryIds[category.CategoryId] = true
	}
	if !existCategoryIds[targetCategoryId] {
		if defaultCategoryId == "" {
			return stderr.Error("classified category id %s is not in the categories %+v and defaultCategoryId is empty", targetCategoryId, existCategories)
		}
		ctx.Warnf("classified category id %s is not in the categories %+v, use defaultCategoryId %s", targetCategoryId, existCategories, defaultCategoryId)
		targetCategoryId = defaultCategoryId
	}
	return n.forward2TargetNode(targetCategoryId, p)
}

func (n *QuestionClassifierNode) fillPromptTempalte(input_text string, categories []script.Category) (string, error) {
	bs, err := json.Marshal(categories)
	if err != nil {
		return "", stderr.Wrap(err, "failed to marshal categories %+v", categories)
	}
	userInput := fmt.Sprintf(models.QuestionClassifierUserInputTemplate, input_text, string(bs))
	return fmt.Sprintf(models.QuestionClassifierPromptTemplate, userInput), nil
}
