package kapacitor

import (
	"encoding/json"
	"fmt"
	"strings"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/pipeline"
	"transwarp.io/applied-ai/applet-engine/tools/jsonnet"
)

type ConditionalSwitchNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.ConditionalSwitchNode // 算子的参数定义
}

// newConditionalSwitchNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newConditionalSwitchNode(et *ExecutingTask, n *pipeline.ConditionalSwitchNode, d NodeDiagnostic) (*ConditionalSwitchNode, error) {
	hn := &ConditionalSwitchNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *ConditionalSwitchNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *ConditionalSwitchNode) Point(p edge.PointMessage) (err error) {
	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	params, err := extracOutputFromFields[any](p.Fields())
	if err != nil {
		return stderr.Wrap(err, "failed to extract output from fields")
	}
	jsonParams, err := json.Marshal(params)
	if err != nil {
		return stderr.Wrap(err, "failed to marshal params %+v", params)
	}
	preVar := fmt.Sprintf("local %s = %s;", jsonnet.DEFAULT_LOCAL_VAR, string(jsonParams))
	resultString, err := jsonnet.EvaluateJsonnet(preVar, n.pn.Condition, true)
	if err != nil {
		return stderr.Wrap(err, "EvaluateJsonnett")
	}
	resultBool := strings.TrimSpace(resultString) == "true"
	if resultBool {
		return n.forward2TargetNode(n.pn.IfNode, p)
	} else {
		return n.forward2TargetNode(n.pn.ElseNode, p)
	}
}
