package kapacitor

import (
	"encoding/json"
	"fmt"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
	"transwarp.io/applied-ai/applet-engine/tools/jsonnet"
)

type JsonnetNode struct {
	node
	pn *pipeline.JsonnetNode // 算子的参数定义
}

// newJsonnetNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newJsonnetNode(et *ExecutingTask, n *pipeline.JsonnetNode, d NodeDiagnostic) (*JsonnetNode, error) {
	hn := &JsonnetNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *JsonnetNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *JsonnetNode) Point(p edge.PointMessage) (err error) {
	if p == nil || p.Fields() == nil {
		return stderr.InvalidParam.Error("recved message is nil or has no fields")
	}
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	return n.forwardJsonnetResult(p)
}

// BeginBatch 处理一批数据点开始的信号
func (n *JsonnetNode) BeginBatch(begin edge.BeginBatchMessage) (err error) {
	ctx, _ := begin.Context()
	defer func() {
		logError(ctx, err)
	}()

	return n.forwardMsg(begin, true)
}

func (n *JsonnetNode) BatchPoint(bp edge.BatchPointMessage) (err error) {
	if bp == nil || bp.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	ctx, err := bp.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	return n.forwardJsonnetResult(bp)
}

func (n *JsonnetNode) EndBatch(end edge.EndBatchMessage) (err error) {
	ctx, _ := end.Context()
	defer func() {
		logError(ctx, err)
	}()

	return n.forwardMsg(end, true)
}

func (n *JsonnetNode) forwardJsonnetResult(m edge.Message) error {
	fieldGetter, ok := m.(edge.FieldGetter)
	if !ok {
		return stderr.InvalidParam.Error("message %+v is not a FieldGetter", m)
	}
	fields := fieldGetter.Fields()
	codeAny, exist, err := models.GetValueOfFields[any](fields, models.PredefinedFieldOutput)
	if err != nil || !exist {
		return stderr.Wrap(err, "failed to extract output from fields")
	}
	codeJson, err := json.Marshal(codeAny)
	if err != nil {
		return stderr.Wrap(err, "failed to marshal code %+v", codeAny)
	}
	preVar := fmt.Sprintf("local %s = %s;", jsonnet.DEFAULT_LOCAL_VAR, string(codeJson))
	resultString, err := jsonnet.EvaluateJsonnet(preVar, n.pn.Code, false)
	if err != nil {
		return stderr.Wrap(err, "EvaluateJsonnet")
	}
	var output any
	if err := json.Unmarshal([]byte(resultString), &output); err != nil {
		return stderr.Wrap(err, "unmarshal jsonnet result %s", resultString)
	}
	if err := setOutputIntoFields(fields, output); err != nil {
		return stderr.Wrap(err, "set output %+v into fields", output)
	}
	return n.forwardMsg(m, true)
}

func (n *JsonnetNode) Barrier(b edge.BarrierMessage) error {
	return nil
}

func (n *JsonnetNode) DeleteGroup(d edge.DeleteGroupMessage) error {
	return nil
}

func (n *JsonnetNode) Done() {}
