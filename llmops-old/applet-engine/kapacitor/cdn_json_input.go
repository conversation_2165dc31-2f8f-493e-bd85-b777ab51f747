package kapacitor

import (
	"encoding/json"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type JsonInputNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.JsonInputNode // 算子的参数定义
}

// newJsonInputNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newJsonInputNode(et *ExecutingTask, n *pipeline.JsonInputNode, d NodeDiagnostic) (*JsonInputNode, error) {
	hn := &JsonInputNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *JsonInputNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

func (n *JsonInputNode) Point(p edge.PointMessage) (err error) {
	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	isListenHttp, err := n.isPreNodeListenHttp()
	if err != nil {
		return stderr.Wrap(err, "failed to check if pre node is listen http")
	}

	if !isListenHttp {
		unmarshaledJson, exist, err := models.GetValueOfFields[any](p.Fields(), models.PredefinedFieldOutput)
		if err != nil || !exist {
			return stderr.Wrap(err, "failed to extract json any from fields")
		}
		return n.forwardUnmarshaledJson(p, unmarshaledJson)
	}

	output, exist, err := models.GetValueOfFields[map[string]any](p.Fields(), models.PredefinedFieldOutput)
	if err != nil || !exist {
		return stderr.Wrap(err, "failed to extract output from fields")
	}
	// 尝试从 {nodeid}##{InputKey} key中获取Json输入
	inputFieldKey := n.pn.InputFieldKey(n.pn.Id)
	jsonStr, exist, err := models.GetValueOfFields[string](output, inputFieldKey)
	if err != nil {
		return stderr.Wrap(err, "failed to extract %s from output", inputFieldKey)
	}
	if !exist {
		// 如果找不到 {nodeid}##{InputKey} key，则尝试从 {InputKey} key中获取Json输入
		jsonStr, exist, err = models.GetValueOfFields[string](output, n.pn.InputKey)
		if err != nil {
			return stderr.Wrap(err, "failed to extract %s from output", n.pn.InputKey)
		}
		if !exist {
			// 如果 {nodeid}##{InputKey} 与 {InputKey} 都不存在，直接向后传递空值
			ctx.Warnf("there is no %s key, forward empty value", n.pn.InputKey)
			return n.forwardUnmarshaledJson(p, map[string]any{})
		}
	}
	var unmarshaledJson any
	if err := json.Unmarshal([]byte(jsonStr), &unmarshaledJson); err != nil {
		return stderr.Wrap(err, "unmarshal json string %s -> %T", jsonStr, unmarshaledJson)
	}
	return n.forwardUnmarshaledJson(p, unmarshaledJson)
}

func (n *JsonInputNode) forwardUnmarshaledJson(p edge.PointMessage, v any) error {
	if err := setOutputIntoFields(p.Fields(), v); err != nil {
		return stderr.Wrap(err, "set unmarshaled json %+v into output fields", v)
	}
	return n.forwardMsg(p, true)
}
