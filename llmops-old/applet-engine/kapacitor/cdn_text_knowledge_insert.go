package kapacitor

import (
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/applet-engine/clients"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/pipeline"
)

type TextKnowledgeInsertNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.TextKnowledgeInsertNode // 算子的参数定义
}

func newTextKnowledgeInsertNode(et *ExecutingTask, n *pipeline.TextKnowledgeInsertNode, d NodeDiagnostic) (*TextKnowledgeInsertNode, error) {
	hn := &TextKnowledgeInsertNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *TextKnowledgeInsertNode) Point(p edge.PointMessage) (err error) {
	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}

	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	call := ctx.Call
	if call == nil {
		return stderr.Internal.Error("call is nil")
	}
	if ctx.FileSfsUrl == "" {
		return stderr.Error("file sfs url is empty, only support inserting text chunks from file")
	}
	knowledgeBaseId := n.pn.KbDescriber.ID

	chunks, err := extracChunksFromOutputField(p.Fields())
	if err != nil {
		return err
	}
	insertResult, err := clients.KnowledgeToolExecutor.Insert(call.Ctx(), knowledgeBaseId, ctx.FileSfsUrl, chunks)
	if err != nil {
		return stderr.Wrap(err, "failed to insert knowledge")
	}
	if err := setOutputIntoFields(p.Fields(), insertResult); err != nil {
		return stderr.Wrap(err, "set output %+v into fields", insertResult)
	}
	return n.forwardMsg(p, true)
}

func (n *TextKnowledgeInsertNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}
