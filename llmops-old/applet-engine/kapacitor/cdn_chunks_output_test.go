package kapacitor

import (
	"testing"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
)

func TestChunks(t *testing.T) {
	temp := pb.DocSvcLoadChunkRsp{
		Chunks: []*pb.Chunk{
			{Id: "123132", SourceType: pb.ChunkSourceType_SOURCE_TYPE_CREATED},
		},
		Elements: []*pb.DocElement{{ElementId: "xxxx", Type: pb.DocElementType_DocElementType_Address}},
	}
	outputResp := new(pb.DocSvcLoadChunkRsp)
	if err := stdsrv.UnmarshalMixWithProto(temp, outputResp); err != nil {
		stdlog.Info(err)
	}
	stdlog.Info("ok")
}
