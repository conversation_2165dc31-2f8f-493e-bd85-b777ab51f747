package kapacitor

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"github.com/sashabaranov/go-openai"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
	"transwarp.io/applied-ai/applet-backend/pkg/agent_executor"
	clients2 "transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-engine/clients"
	"transwarp.io/applied-ai/applet-engine/conf"
	"transwarp.io/applied-ai/applet-engine/edge"
	"transwarp.io/applied-ai/applet-engine/models"
	"transwarp.io/applied-ai/applet-engine/pipeline"
	"transwarp.io/applied-ai/applet-engine/pkg/debug"
)

const (
	// doFunctionCallingChat函数中读取channel数据的超时时间
	ReadChannelTimeout = 20 * time.Second
)

type AgentAssistantNode struct {
	node
	unsupportedBatchNode
	pn *pipeline.AgentAssistantNode // 算子的参数定义
}

// newAgentAssistantNode 为算子的初始化方法，需要添加到 ExecutingTask.createNode 方法实现中
func newAgentAssistantNode(et *ExecutingTask, n *pipeline.AgentAssistantNode, d NodeDiagnostic) (*AgentAssistantNode, error) {
	hn := &AgentAssistantNode{
		node: node{Node: n, et: et, diag: d},
		pn:   n,
	}
	hn.node.runF = hn.run
	return hn, nil
}

func (n *AgentAssistantNode) run([]byte) error {
	return edge.NewCdnConsumerWithReceiverAndErrHandler(n.ins[0], n, HandleMsgErr).Consume()
}

// TODO: Final Answer使用流式输出，api模式下流式输出开关，应用体验页面是否展示思考过程，当后面的算子无法支持流式消息时，要发送pointmessage
func (n *AgentAssistantNode) Point(p edge.PointMessage) (err error) {
	if p == nil || p.Fields() == nil {
		return stderr.Internal.Error("received message is nil or has no fields")
	}
	ctx, err := p.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	defer func() {
		logError(ctx, err)
	}()

	call := ctx.Call
	if call == nil {
		return stderr.Wrap(err, "get call from message context")
	}
	history := ""
	citations := []*agent_executor.Citation{}
	// 先尝试获取 string 类型的 output
	question, err := extracOutputFromFields[string](p.Fields())
	if err != nil {
		// 如果获取 map 类型失败，尝试获取 map[string]any 类型
		outputMap, mapErr := extracOutputFromFields[map[string]any](p.Fields())
		if mapErr != nil {
			return stderr.Wrap(err, "extract output from fields failed for both map and string types")
		}
		// 提取问题，不允许问题不存在
		var questionErr error
		var exist bool
		question, exist, questionErr = models.GetValueOfFields[string](outputMap, models.PredefinedFieldQuestion)
		if questionErr != nil || !exist {
			return stderr.Wrap(questionErr, "extract question from fields")
		}
		// 提取历史记录，允许历史记录不存在
		var historyErr error
		history, _, historyErr = models.GetValueOfFields[string](outputMap, models.PredefinedFieldHistory)
		if historyErr != nil {
			return stderr.Wrap(historyErr, "extract history from fields")
		}
		// 提取知识库检索结果，允许知识库检索结果不存在
		knowledgeCitations, knowledgeCitationsErr := agent_executor.TryCvt2Citations(outputMap[models.PredefinedFieldKnowledge], agent_executor.CitationTypeTextKnowledge)
		if knowledgeCitationsErr != nil {
			return stderr.Wrap(knowledgeCitationsErr, "extract knowledgeCitations from fields")
		}
		citations = append(citations, knowledgeCitations...)
		// 提取互联网搜索结果，允许互联网搜索结果不存在
		internetCitations, internetCitationsErr := agent_executor.TryCvt2Citations(outputMap[models.PredefinedFieldInternet], agent_executor.CitationTypeInternetSearch)
		if internetCitationsErr != nil {
			return stderr.Wrap(internetCitationsErr, "extract internetCitations from fields")
		}
		citations = append(citations, internetCitations...)
	}
	citationTexts := agent_executor.CvtCitations2CitationTexts(citations)
	ctx.Infof("====== citation texts ======\n%s", citationTexts)
	if err := sendCitationSSE(call, citations); err != nil {
		return stderr.Wrap(err, "send citation sse")
	}

	fileUrl := fmt.Sprintf("file sfs protocol url: %s\n file http protocol url: %s", ctx.FileSfsUrl, ctx.FileHttpUrl)
	ctx.Infof("====== file url ======\n%s", fileUrl)

	agentConfig := n.pn.AgentConfig
	customInstruction := agentConfig.Prompt
	// 在遍历tool的时候保存 name_for_model 到 describer的映射，传给执行器直接执行
	name2Describer := make(map[string]agent_definition.ToolDescriber)
	// 保存api tools
	tools := make([]agent_definition.Tool, 0)
	for _, collection := range agentConfig.APICollections {
		if collection == nil {
			continue
		}
		for _, apiTool := range collection.AgentTools {
			apiToolDef := apiTool.Definition()
			tools = append(tools, apiToolDef)
			apiToolCopy := apiTool
			name2Describer[apiToolDef.NameForModel] = &apiToolCopy
		}
	}
	// 保存knowledge tools
	for _, knowTool := range agentConfig.KnowledgeBases.KnowledgeBaseDesc {
		if knowTool == nil {
			continue
		}
		knowToolDef := knowTool.Definition()
		tools = append(tools, knowToolDef)
		knowToolCopy := knowTool
		name2Describer[knowToolDef.NameForModel] = knowToolCopy
	}

	// 保存 system service tools
	for _, systemService := range agentConfig.SystemServices {
		var tool agent_definition.ToolDescriber
		if systemService.ModelService != nil {
			tool = systemService.ModelService
		} else if systemService.AppletService != nil {
			tool = systemService.AppletService
		}
		if tool != nil {
			toolDef := tool.Definition()
			tools = append(tools, toolDef)
			name2Describer[toolDef.NameForModel] = tool
		}
	}

	toolNameList := make([]string, 0)
	toolDescriptionList := make([]string, 0)
	for _, tool := range tools {
		toolNameList = append(toolNameList, tool.NameForModel)
		description, err := models.FillToolDescription(tool)
		if err != nil {
			return stderr.Wrap(err, "fill tool description")
		}
		toolDescriptionList = append(toolDescriptionList, description)
	}
	toolNames := strings.Join(toolNameList, ",")
	toolDescriptions := strings.Join(toolDescriptionList, "")

	ctx.Infof("====== toolNames ======\n%s", toolNames)
	ctx.Infof("====== toolDescriptions ======\n%s", toolDescriptions)

	var debugMessage *debug.DebugMessage
	// 获取 debugMessage 前需要判断 agent 是否在子链中
	// 暂时不支持循环智能体，循环子链，所以 parentRound 参数为 0
	//debugMessage, err = call.GetDebugMessage(call.ReqID(), n.pn.NodeMeta.SubChainID, n.pn.NodeMeta.NodeID, 0, call.GetLoopRound(n.pn.NodeMeta.Id)-1)
	//if n.pn.NodeMeta.SubChainID == "" {
	//	debugMessage, err = debug.GetDebugMessage(call.ReqID(), n.pn.NodeMeta.NodeID, call.GetLoopRound(n.pn.NodeMeta.Id)-1)
	//} else {
	//	debugMessage, err = debug.GetDebugMessage(call.ReqID(), n.pn.NodeMeta.SubChainID, call.GetLoopRound(n.pn.NodeMeta.Id)-1)
	//}
	debugMessage, err = debug.GetDebugMessage(call.ReqID(), n.pn.NodeMeta.Id, call.GetLoopRound(n.pn.NodeMeta.Id)-1)

	if err != nil {
		return stderr.Wrap(err, "get debug message")
	}
	// 填充普通提示词模板
	simplePrompt := models.FillSimplePrompt(customInstruction, history, citationTexts, fileUrl, question)
	ctx.Infof("====== simplePrompt ======\n%s", simplePrompt)
	// 如果需要调用工具，则根据不同的AgentMode选择不同的chat方法
	if toolNames != "" {
		// 根据不同的AgentMode选择不同的chat方法
		switch n.pn.AgentConfig.AgentMode {
		case agent_definition.AgentModeFunctionCalling:
			return n.doFunctionCallingChat(ctx, p, name2Describer, simplePrompt, tools, debugMessage, citations)
		case agent_definition.AgentModeReAct:
			reActPrompt := models.FillPromptWithReAct(customInstruction, history, citationTexts, fileUrl, toolNames, toolDescriptions, question)
			ctx.Infof("====== reActPrompt ======\n%s", reActPrompt)
			return n.doReActChat(ctx, p, name2Describer, reActPrompt, debugMessage, citations)
		default:
			return stderr.Errorf("unsupported agent mode: %s", n.pn.AgentConfig.AgentMode)
		}
	}
	// 如果不需要调用工具，则使用普通提示词模板
	return n.doSimpleChat(ctx, p, simplePrompt, debugMessage, citations)
}

func (n *AgentAssistantNode) doReActChat(ctx models.Context, p edge.PointMessage, name2Describer map[string]agent_definition.ToolDescriber, reActPrompt string, debugMessage *debug.DebugMessage, citations []*agent_executor.Citation) error {
	call := ctx.Call
	callCtx := call.Ctx()
	needStream := needStreamOutput(n.children)
	// 处理模型流式输出的文字
	var stringBuffer string
	var hasFinalAnswer bool
	var hasStopWordObservation bool
	var callCtxCopy context.Context
	var cancelStream context.CancelFunc
	var childDebugMessageLLM *debug.DebugMessage
	var completionTokens int
	// 初始化引用匹配相关变量
	var lastParagraph string
	var inCodeBlock bool
	var inHeading bool

	// 处理模型流式输出的文字
	streamHandler := func(textContent string) error {
		// 设置debugMessage的输出
		childDebugMessageLLM.SetOutput(textContent, true)
		completionTokens += len(textContent)

		// 模型输出Final Answer 直接流式输出后续的内容， 不再保存模型输出
		if hasFinalAnswer {
			// 累加段落
			lastParagraph += textContent
			// 检测代码块
			if strings.Contains(textContent, "```") {
				inCodeBlock = !inCodeBlock
			}
			// 检测标题
			if strings.Contains(textContent, "#") {
				inHeading = true
			}
			// 如果检测到换行符
			if strings.Contains(textContent, "\n") {
				var err error
				// 段落去匹配上下文的相似度并添加引用序号
				textContent, err = n.handleCitations(ctx, textContent, citations, &lastParagraph, &inCodeBlock, &inHeading)
				if err != nil {
					return stderr.Wrap(err, "handle citations")
				}
			}
			return n.emitBatch(ctx, textContent)
		}
		if hasStopWordObservation {
			return nil
		}
		// 累加保存模型输出
		stringBuffer += textContent
		// 检查累加的字符串中是否含有"Final Answer:"
		if strings.Contains(stringBuffer, "Final Answer:") {
			index := strings.Index(stringBuffer, "Final Answer:")
			// 找到"Final Answer:"，设置标记
			hasFinalAnswer = true
			// 发送"Final Answer:"之后的字符
			finalAnswer := stringBuffer[index+len("Final Answer:"):]
			strippedFinalAnswer := strings.TrimPrefix(finalAnswer, " ")
			return n.emitBatch(ctx, strippedFinalAnswer)
		}
		// 检查累加的字符中是否含有stop word "Observation"
		if strings.Contains(stringBuffer, models.StopWordObservation) {
			index := strings.Index(stringBuffer, models.StopWordObservation)
			hasStopWordObservation = true
			stringBuffer = stringBuffer[:index]
			cancelStream()
		}
		// if len(stringBuffer) >= len("Thought") && !strings.Contains(stringBuffer, "Thought") {
		// 	hasFinalAnswer = true
		// 	n.emitBatch(ctx, string(stringBuffer))
		// }
		return nil
	}

	// 处理模型非流式输出的文字
	blockHandler := func(textContent string) error {
		completionTokens += len(textContent)
		stringBuffer = textContent
		// 检查模型输出的字符串中是否含有"Final Answer:"
		if strings.Contains(stringBuffer, "Final Answer:") {
			index := strings.Index(stringBuffer, "Final Answer:")
			// 找到"Final Answer:"，设置标记
			hasFinalAnswer = true
			// 发送"Final Answer:"之后的字符
			finalAnswer := stringBuffer[index+len("Final Answer:"):]
			strippedFinalAnswer := strings.TrimPrefix(finalAnswer, " ")
			return n.emitPoint(p, strippedFinalAnswer)
		}
		return nil
	}

	llmSvc := n.pn.AgentConfig.LLMModelSvc
	meta := debugMessage.Meta
	debugMeta := debug.DebugMeta{
		ID:   llmSvc.Id,
		Type: debug.DEBUG_TYPE_LLM,
		Name: llmSvc.Name,
	}
	if needStream {
		if err := n.emitBegin(p); err != nil {
			return stderr.Wrap(err, "emit begin")
		}
	}

	// ReAct 循环
	var thoughtRounds int
	maxThoughtRounds := conf.Config.Agent.MaxThoughtRounds
	for thoughtRounds = 0; thoughtRounds < maxThoughtRounds; thoughtRounds++ {
		hasFinalAnswer = false
		hasStopWordObservation = false
		stringBuffer = ""
		callCtxCopy, cancelStream = context.WithCancel(callCtx)
		completionTokens = 0
		// 创建LLM的DebugMessage
		childDebugMessageLLM = &debug.DebugMessage{
			Meta:      meta,
			DebugMeta: debugMeta,
			Status:    debug.NODE_STATUS_RUNNING,
			Input:     reActPrompt,
			StartTime: time.Now().UnixMilli(),
			EndTime:   0,
			Usage: debug.TokenUsage{
				PromptTokens:     int64(len(reActPrompt)),
				CompletionTokens: 0,
				TotalTokens:      0,
			},
			Round: thoughtRounds,
		}

		debugMessage.SetDebugMsgAsChildren(childDebugMessageLLM, thoughtRounds)

		if needStream {
			// 错误处理排除因为包含stop word "Observation"导致stream中断的情况
			if err := n.doChatStream(reActPrompt, callCtxCopy, streamHandler); err != nil && !hasStopWordObservation {
				childDebugMessageLLM.SetFailedStatus()
				cancelStream()
				return stderr.Wrap(err, "do chat stream")
			}
		} else {
			if err := n.doChatBlock(reActPrompt, callCtxCopy, blockHandler); err != nil {
				childDebugMessageLLM.SetFailedStatus()
				cancelStream()
				return stderr.Wrap(err, "do chat block")
			}
		}
		cancelStream()

		// 更新LLM的DebugMessage
		childDebugMessageLLM.Usage.CompletionTokens = int64(completionTokens)
		childDebugMessageLLM.Usage.TotalTokens = childDebugMessageLLM.Usage.PromptTokens + int64(completionTokens)

		if hasFinalAnswer {
			childDebugMessageLLM.SetSuccessStatus()
			break
		}
		thought, action, actionInput, err := n.extractContent(stringBuffer)
		if err != nil {
			childDebugMessageLLM.SetFailedStatus()
			ctx.Errorf("extract content from %s error %v", stringBuffer, err)
			if needStream {
				n.emitBatch(ctx, stringBuffer)
			} else {
				n.emitPoint(p, stringBuffer)
			}
			break
		}
		ctx.Infof("thought: %s, action: %s, actionInput: %s", thought, action, actionInput)
		if err := sendThoughtSSE(call, thought); err != nil {
			childDebugMessageLLM.SetFailedStatus()
			return stderr.Wrap(err, "failed to send thought event")
		}
		var unmarshaledActionInput any
		// 防止模型输出的复杂json换行符格式错误
		actionInput = strings.Replace(actionInput, "\\n", models.TempReplaceString, -1)
		actionInput = strings.Replace(actionInput, "\n", "", -1)
		actionInput = strings.Replace(actionInput, models.TempReplaceString, "\\n", -1)
		if err := json.Unmarshal([]byte(actionInput), &unmarshaledActionInput); err != nil {
			childDebugMessageLLM.SetFailedStatus()
			return stderr.Wrap(err, "unmarshal action input: %s", actionInput)
		}
		tool, ok := name2Describer[action]
		if !ok {
			childDebugMessageLLM.SetFailedStatus()
			return stderr.Error("unknow tool %s", action)
		}
		childDebugMessageLLM.SetSuccessStatus()

		// 调用工具
		toolDef := tool.Definition()
		if err := sendActionSSE(call, toolDef, unmarshaledActionInput); err != nil {
			return stderr.Wrap(err, "failed to send action event")
		}

		// 创建Tool的DebugMessage
		childDebugMessageTool := &debug.DebugMessage{
			Meta: debugMessage.Meta,
			DebugMeta: debug.DebugMeta{
				ID:   toolDef.ID,
				Type: debug.DEBUG_TYPE_TOOL,
				Name: toolDef.NameForHuman,
			},
			Status:    debug.NODE_STATUS_RUNNING,
			Input:     unmarshaledActionInput,
			StartTime: time.Now().UnixMilli(),
			EndTime:   0,
			Round:     thoughtRounds,
		}

		debugMessage.SetDebugMsgAsChildren(childDebugMessageTool, thoughtRounds)
		// 执行对应工具
		var observation string
		var executionErr error
		switch tool.Type() {
		case agent_definition.ToolTypeAPITool:
			observation, executionErr = n.executeAPITool(ctx, tool, unmarshaledActionInput, &citations)
		case agent_definition.ToolTypeKnowledgeHub:
			observation, executionErr = n.executeKnowledgeHubTool(ctx, tool, unmarshaledActionInput, &citations)
		case agent_definition.ToolTypeModelService:
			observation, executionErr = clients.ModelToolExecutor.Execute(callCtx, tool, unmarshaledActionInput)
		case agent_definition.ToolTypeAppletService:
			observation, executionErr = clients.AppletToolExecutor.Execute(callCtx, tool, unmarshaledActionInput)
		default:
			childDebugMessageTool.SetFailedStatus()
			return stderr.Error("unsupported tool type %s", tool.Type())
		}

		if executionErr != nil {
			childDebugMessageTool.SetFailedStatus()
			observation = fmt.Sprintf("Tool execution error - %s: %v", toolDef.NameForModel, executionErr)
		} else {
			childDebugMessageTool.SetSuccessStatus()
		}
		// 更新Tool的DebugMessage
		childDebugMessageTool.SetOutput(observation, false)

		ctx.Infof("observation: %s", observation)
		if err = sendObservationSSE(call, toolDef, observation); err != nil {
			return stderr.Wrap(err, "failed to send observation event")
		}
		scratchpad := models.FillReActScratchpad(thought, action, actionInput, observation)
		reActPrompt += scratchpad
	}
	if thoughtRounds >= maxThoughtRounds {
		err_info := fmt.Sprintf("thought rounds %d reached max", thoughtRounds)
		ctx.Errorf(err_info)
		return stderr.Errorf(err_info)
	}
	// 将调用 llm, tool 等产生的 childDebugMessage 按时间顺序存入 debugMessage.ChildrenOrdered 中
	debugMessage.OrderChildDebugMsg()
	if needStream && lastParagraph != "" && len(citations) > 0 {
		// 处理最后一段文本的引用匹配
		finalCitationNumStr, err := n.handleCitations(ctx, "", citations, &lastParagraph, &inCodeBlock, &inHeading)
		if err != nil {
			return stderr.Wrap(err, "citation matching for last paragraph")
		}
		// 如果引用编号不为空，则发送引用编号
		if finalCitationNumStr != "" {
			if err := n.emitBatch(ctx, finalCitationNumStr); err != nil {
				return stderr.Wrap(err, "emit final citation numbers")
			}
		}
	}

	if needStream {
		return n.emitEnd(ctx)
	}
	return nil
}

// doFunctionCallingChat 实现function calling方式的chat
func (n *AgentAssistantNode) doFunctionCallingChat(ctx models.Context, p edge.PointMessage, name2Describer map[string]agent_definition.ToolDescriber, simplePrompt string, tools []agent_definition.Tool, debugMessage *debug.DebugMessage, citations []*agent_executor.Citation) error {
	// TODO: debugMessage设置
	call := ctx.Call
	callCtx := call.Ctx()
	needStream := needStreamOutput(n.children)
	if needStream {
		if err := n.emitBegin(p); err != nil {
			return stderr.Wrap(err, "emit begin")
		}
	}

	// 将 agent_definition.Tool 转换为 openai.Tool
	openaiTools, err := convertToOpenAITools(tools)
	if err != nil {
		return stderr.Wrap(err, "convert to openai tools")
	}

	// 初始化消息
	messages := []openai.ChatCompletionMessage{
		{
			Role:    openai.ChatMessageRoleUser,
			Content: simplePrompt,
		},
	}

	// 初始化引用匹配相关变量
	var lastParagraph string
	var inCodeBlock bool
	var inHeading bool

	// 创建OpenAI客户端
	llmModelSvc := n.pn.AgentConfig.LLMModelSvc
	openaiBaseUrl, err := getOpenaiBaseUrl(llmModelSvc.FullUrl)
	if err != nil {
		return stderr.Wrap(err, "failed to get openai base url")
	}
	openaiApiKey, err := getOpenaiApiKey(callCtx)
	if err != nil {
		return stderr.Wrap(err, "failed to get openai api key")
	}
	openaiClient := clients2.NewOpenaiClient(openaiBaseUrl, openaiApiKey)
	var thoughtRounds int
	maxThoughtRounds := conf.Config.Agent.MaxThoughtRounds
	for thoughtRounds = 0; thoughtRounds < maxThoughtRounds; thoughtRounds++ {
		ctx.Infof("开始第 %d 轮function calling", thoughtRounds+1)
		ctx.Infof("messages:\n %+v", messages)
		baseChatReq, err := clients2.GetBaseChatReq(llmModelSvc)
		if err != nil {
			return stderr.Wrap(err, "failed to get base chat req")
		}
		openaiReq := openai.ChatCompletionRequest{
			Model:       baseChatReq.Model,
			Messages:    messages,
			Tools:       openaiTools,
			ToolChoice:  "auto",
			Temperature: float32(baseChatReq.Temperature),
			TopP:        float32(baseChatReq.TopP),
			MaxTokens:   baseChatReq.MaxTokens,
			Stop:        baseChatReq.Stop,
		}

		currentRoundToolCalls := make([]openai.ToolCall, 0)
		currentRoundContent := ""

		if needStream {
			streamChan := openaiClient.ChatWithToolsStream(callCtx, openaiReq)
			timer := time.NewTimer(ReadChannelTimeout)

			// 接收模型输出
		streamLoop:
			for {
				select {
				case response, ok := <-streamChan:
					if !ok {
						ctx.Infof("chat stream channel 关闭，正常退出流式输出")
						break streamLoop
					}
					// 收到数据后重置定时器
					if !timer.Stop() {
						<-timer.C
					}
					timer.Reset(ReadChannelTimeout)

					if response.Error != nil {
						return response.Error
					}

					if response.Content != "" {
						currentRoundContent += response.Content

						// 处理引用匹配
						// 累加段落
						lastParagraph += response.Content
						// 检测代码块
						if strings.Contains(response.Content, "```") {
							inCodeBlock = !inCodeBlock
						}
						// 检测标题
						if strings.Contains(response.Content, "#") {
							inHeading = true
						}
						// 如果检测到换行符，进行引用匹配
						if strings.Contains(response.Content, "\n") {
							var citationErr error
							citatedContent, citationErr := n.handleCitations(ctx, response.Content, citations, &lastParagraph, &inCodeBlock, &inHeading)
							if citationErr != nil {
								return stderr.Wrap(citationErr, "handle citations")
							}
							// 使用添加了引用的内容进行发送
							if err := n.emitBatch(ctx, citatedContent); err != nil {
								return stderr.Wrap(err, "emit content batch")
							}
						} else {
							// 没有换行符时直接发送内容
							if err := n.emitBatch(ctx, response.Content); err != nil {
								return stderr.Wrap(err, "emit content batch")
							}
						}
					}

					if response.ToolCalls != nil {
						ctx.Infof("收到工具调用: %+v", response.ToolCalls)
						currentRoundToolCalls = response.ToolCalls
					}

				case <-timer.C:
					ctx.Infof("chat stream 读取响应超时, 抛出错误")
					return stderr.Errorf("chat stream 读取响应超时")
				}
			}

			// 处理最后一段文本的引用匹配
			if lastParagraph != "" && len(citations) > 0 {
				finalCitationNumStr, err := n.handleCitations(ctx, "", citations, &lastParagraph, &inCodeBlock, &inHeading)
				if err != nil {
					return stderr.Wrap(err, "citation matching for last paragraph")
				}
				// 如果引用编号不为空，则发送引用编号
				if finalCitationNumStr != "" {
					if err := n.emitBatch(ctx, finalCitationNumStr); err != nil {
						return stderr.Wrap(err, "emit final citation numbers")
					}
				}
			}
		} else {
			// 非流式工具调用逻辑
			content, toolCalls, err := openaiClient.ChatWithToolsBlock(callCtx, openaiReq)
			if err != nil {
				return stderr.Wrap(err, "chat with tools block")
			}
			currentRoundContent = content
			currentRoundToolCalls = toolCalls

			// 如果模型返回结果，且没有工具调用，则作为最终结果发送
			if content != "" && len(toolCalls) == 0 {
				if err := n.emitPoint(p, content); err != nil {
					return stderr.Wrap(err, "emit content point")
				}
			}
			// 如果模型返回结果，且有工具调用，则作为中间思考过程发送
			if content != "" && len(toolCalls) > 0 {
				if err := sendThoughtSSE(call, content); err != nil {
					return stderr.Wrap(err, "failed to send thought event")
				}
			}
		}

		// 如果没有工具调用，结束对话
		if len(currentRoundToolCalls) == 0 {
			break
		}
		currentRoundMessage := openai.ChatCompletionMessage{
			Role:      openai.ChatMessageRoleAssistant,
			Content:   currentRoundContent,
			ToolCalls: currentRoundToolCalls,
		}
		messages = append(messages, currentRoundMessage)
		// 处理每个工具调用
		for _, toolCall := range currentRoundToolCalls {
			toolName := toolCall.Function.Name
			toolInput := toolCall.Function.Arguments

			// 查找工具
			tool, ok := name2Describer[toolName]
			if !ok {
				return stderr.Errorf("未知工具 %s", toolName)
			}

			// 解析工具输入
			var unmarshaledToolInput any
			if err := json.Unmarshal([]byte(toolInput), &unmarshaledToolInput); err != nil {
				return stderr.Wrap(err, "unmarshal tool input: %s", toolInput)
			}

			// 发送工具调用事件
			toolDef := tool.Definition()
			if err := sendActionSSE(call, toolDef, unmarshaledToolInput); err != nil {
				return stderr.Wrap(err, "failed to send action event")
			}

			// 执行工具
			var toolResult string
			var executionErr error

			switch tool.Type() {
			case agent_definition.ToolTypeAPITool:
				toolResult, executionErr = n.executeAPITool(ctx, tool, unmarshaledToolInput, &citations)
			case agent_definition.ToolTypeKnowledgeHub:
				toolResult, executionErr = n.executeKnowledgeHubTool(ctx, tool, unmarshaledToolInput, &citations)
			case agent_definition.ToolTypeModelService:
				toolResult, executionErr = clients.ModelToolExecutor.Execute(callCtx, tool, unmarshaledToolInput)
			case agent_definition.ToolTypeAppletService:
				toolResult, executionErr = clients.AppletToolExecutor.Execute(callCtx, tool, unmarshaledToolInput)
			default:
				return stderr.Errorf("不支持的工具类型 %s", tool.Type())
			}

			if executionErr != nil {
				toolResult = fmt.Sprintf("工具执行错误 - %s: %v", toolDef.NameForModel, executionErr)
			}

			// 发送工具执行结果事件
			if err := sendObservationSSE(call, toolDef, toolResult); err != nil {
				return stderr.Wrap(err, "failed to send observation event")
			}

			// 将工具响应添加到消息中
			toolResponseMessage := openai.ChatCompletionMessage{
				Role:       openai.ChatMessageRoleTool,
				Content:    toolResult,
				ToolCallID: toolCall.ID,
			}
			messages = append(messages, toolResponseMessage)
		}
	}
	if thoughtRounds >= maxThoughtRounds {
		err_info := fmt.Sprintf("function calling rounds %d reached max", thoughtRounds)
		ctx.Errorf(err_info)
		return stderr.Errorf(err_info)
	}

	if needStream {
		return n.emitEnd(ctx)
	}

	return nil
}

func (n *AgentAssistantNode) executeAPITool(
	ctx models.Context,
	tool agent_definition.ToolDescriber,
	unmarshaledActionInput interface{},
	citations *[]*agent_executor.Citation,
) (string, error) {
	apiCitations, observation, err := clients.ApiToolExecutor.ExecuteReturnCitations(ctx.Call.Ctx(), tool, unmarshaledActionInput)
	if err != nil {
		return "", stderr.Wrap(err, "failed to execute api tool")
	}
	if len(apiCitations) == 0 {
		return observation, nil
	}
	*citations = append(*citations, apiCitations...)
	if err := sendCitationSSE(ctx.Call, *citations); err != nil {
		ctx.Errorf("failed to send citation event: %+v in bing search", err)
	}
	return observation, nil
}

func (n *AgentAssistantNode) executeKnowledgeHubTool(
	ctx models.Context,
	tool agent_definition.ToolDescriber,
	unmarshaledActionInput interface{},
	citations *[]*agent_executor.Citation,
) (string, error) {
	KnowledgeCitations, observation, err := clients.KnowledgeToolExecutor.ExecuteReturnCitations(ctx.Call.Ctx(), tool, unmarshaledActionInput)
	if err != nil {
		return "", stderr.Wrap(err, "failed to execute knowledge hub tool")
	}
	if len(KnowledgeCitations) == 0 {
		return observation, nil
	}
	*citations = append(*citations, KnowledgeCitations...)
	if err := sendCitationSSE(ctx.Call, *citations); err != nil {
		ctx.Errorf("failed to send citation event: %+v in knowledge hub", err)
	}
	return observation, nil
}

// TODO
//  debug.DebugMessage中需要挺多参数,最好提供构造方法来进行初始化。
// 当前各节点、每一次执行时,应该都会初始化各自的debugMsg,并缓存在debugMsgStack中。
// 对于调试日志的推送,只需要在 listen_http节点[链整体] 以及 cdnConsumer.consumer方法[所有单个节点都会经过] 进行处理
// listen_http + cdnConsumer 两个节点会对debugMsg进行初始化、改变、推送、缓存等操作。
// 推送方式的具体实现则取决于debugMsg.pub方法的具体实现  redis、mqtt、sse

func (n *AgentAssistantNode) doSimpleChat(ctx models.Context, p edge.PointMessage, simplePrompt string, debugMessage *debug.DebugMessage, citations []*agent_executor.Citation) (err error) {
	call := ctx.Call
	callCtx := call.Ctx()

	// 创建新的DebugMessage作为子节点
	llmSvc := n.pn.AgentConfig.LLMModelSvc
	childDebugMessageLLM := &debug.DebugMessage{
		Meta: debugMessage.Meta,
		DebugMeta: debug.DebugMeta{
			ID:   llmSvc.Id,
			Type: debug.DEBUG_TYPE_LLM,
			Name: llmSvc.Name,
		},
		Status:    debug.NODE_STATUS_RUNNING,
		Input:     simplePrompt,
		StartTime: time.Now().UnixMilli(),
		EndTime:   0,
		// FIXME: chatClient返回的结果统计token usage
		Usage: debug.TokenUsage{
			PromptTokens:     int64(len(simplePrompt)),
			CompletionTokens: 0,
			TotalTokens:      0,
		},
	}

	debugMessage.SetDebugMsgAsChildren(childDebugMessageLLM, 0)

	// 初始化引用匹配相关变量
	var lastParagraph string
	var inCodeBlock bool
	var inHeading bool

	// 处理模型流式输出的文字
	completionTokens := 0
	streamHandler := func(textContent string) error {
		completionTokens += len(textContent)
		childDebugMessageLLM.SetOutput(textContent, true)
		// 累加段落
		lastParagraph += textContent
		// 检测代码块
		if strings.Contains(textContent, "```") {
			inCodeBlock = !inCodeBlock
		}
		// 检测标题
		if strings.Contains(textContent, "#") {
			inHeading = true
		}
		// 如果检测到换行符
		if strings.Contains(textContent, "\n") {
			var err error
			// 段落去匹配上下文的相似度并添加引用序号
			textContent, err = n.handleCitations(ctx, textContent, citations, &lastParagraph, &inCodeBlock, &inHeading)
			if err != nil {
				return err
			}
		}
		return n.emitBatch(ctx, textContent)
	}

	// 处理模型非流式输出的文字
	blockHandler := func(textContent string) error {
		completionTokens += len(textContent)
		childDebugMessageLLM.SetOutput(textContent, false)
		return n.emitPoint(p, textContent)
	}

	// 报错时设置llm节点状态为failed
	defer func() {
		if err != nil {
			setFailedStatusErr := childDebugMessageLLM.SetFailedStatus()
			if setFailedStatusErr != nil {
				stdlog.Errorf("failed to set LLM node [%s] status: %v, original error: %v", llmSvc.Name, setFailedStatusErr, err)
			}
		}
	}()
	needStream := needStreamOutput(n.children)
	if needStream {
		if err := n.emitBegin(p); err != nil {
			return stderr.Wrap(err, "emit begin")
		}
		if err := n.doChatStream(simplePrompt, callCtx, streamHandler); err != nil {
			return stderr.Wrap(err, "do chat stream")
		}
		finalCitationNumStr, err := n.handleCitations(ctx, "", citations, &lastParagraph, &inCodeBlock, &inHeading)
		if err != nil {
			return stderr.Wrap(err, "citation matching for last paragraph")
		}
		if err := n.emitBatch(ctx, finalCitationNumStr); err != nil {
			return stderr.Wrap(err, "emit final citaion numbers")
		}
	} else {
		if err := n.doChatBlock(simplePrompt, callCtx, blockHandler); err != nil {
			return stderr.Wrap(err, "do chat block")
		}
	}
	debugMessage.OrderChildDebugMsg()
	childDebugMessageLLM.Usage.CompletionTokens = int64(completionTokens)
	childDebugMessageLLM.Usage.TotalTokens = childDebugMessageLLM.Usage.PromptTokens + int64(completionTokens)
	if needStream {
		if err := n.emitEnd(ctx); err != nil {
			return stderr.Wrap(err, "emit end")
		}
	}
	return childDebugMessageLLM.SetSuccessStatus()
}

func (n *AgentAssistantNode) doChatBlock(query string, callCtx context.Context, handler func(textContent string) error) error {
	model := n.pn.AgentConfig.LLMModelSvc
	chatReq, err := getAgentChatReq(model, query)
	if err != nil {
		return stderr.Wrap(err, "failed to getAgentChatReq")
	}
	if err := clients2.SyncChat(callCtx, model, chatReq, handler); err != nil {
		return stderr.Wrap(err, "do sync model infer")
	}
	return nil
}

func (n *AgentAssistantNode) doChatStream(query string, callCtx context.Context, handler func(textContent string) error) error {
	model := n.pn.AgentConfig.LLMModelSvc
	chatReq, err := getAgentChatReq(model, query)
	if err != nil {
		return stderr.Wrap(err, "failed to getAgentChatReq")
	}
	if err := clients2.StreamChat(callCtx, model, chatReq, handler); err != nil {
		return stderr.Wrap(err, "do stream model infer")
	}
	return nil
}

// TODO: 优化此函数代码 and 模型不爱输出"Thought:"
func (n *AgentAssistantNode) extractContent(text string) (thought, action, actionInput string, err error) {
	// 定义关键字
	keywords := []string{"Thought:", "Action:", "Action Input:"}
	// 检查关键字是否存在且顺序正确，且每个关键字只出现一次
	lastIndex := -1
	indexes := make([]int, len(keywords))
	for i, keyword := range keywords {
		index := strings.Index(text, keyword)
		if index == -1 || index <= lastIndex {
			return "", "", "", stderr.Error("text does not contain the keywords in the correct order or a keyword is missing")
		}
		// 检查关键字是否只出现一次
		if strings.Contains(text[index+len(keyword):], keyword) {
			return "", "", "", stderr.Error("a keyword appears more than once")
		}
		lastIndex = index
		indexes[i] = index
	}

	// 提取Thought内容
	thoughtStart := indexes[0] + len(keywords[0])
	thoughtEnd := indexes[1]
	thought = strings.TrimSpace(text[thoughtStart:thoughtEnd])

	// 提取Action内容
	actionStart := indexes[1] + len(keywords[1])
	actionEnd := indexes[2]
	action = strings.TrimSpace(text[actionStart:actionEnd])

	// 提取Action Input内容
	actionInputStart := indexes[2] + len(keywords[2])
	actionInput = strings.TrimSpace(text[actionInputStart:])

	return thought, action, actionInput, nil
}

func (n *AgentAssistantNode) emitPoint(p edge.PointMessage, output string) error {
	if err := setOutputIntoFields(p.Fields(), output); err != nil {
		return stderr.Wrap(err, "set output %s into fields", output)
	}
	return n.forwardMsg(p, false)
}

func (n *AgentAssistantNode) emitBegin(m edge.PointMessage) error {
	trace, err := m.Trace()
	if err != nil {
		return stderr.Wrap(err, "get trace from point message")
	}
	ctx, err := m.Context()
	if err != nil {
		return stderr.Wrap(err, "get context from point message")
	}
	bm := edge.NewBeginBatchMessage(m.Name(), n.pn.NodeMeta, trace, ctx, nil, false, m.Time(), 0)
	return n.forwardMsg(bm, true)
}

func (n *AgentAssistantNode) emitBatch(ctx models.Context, output string) error {
	stdlog.Infof("recv : %s", output)
	fields := models.Fields{
		models.PredefinedFieldOutput:  output,
		models.PredefinedFieldContext: ctx,
	}
	bpm := edge.NewBatchPointMessage(fields, models.Tags{}, time.Now())
	return n.forwardMsg(bpm, true)
}

func (n *AgentAssistantNode) emitEnd(ctx models.Context) error {
	em := edge.NewEndBatchMessage()
	em.SetContext(ctx)
	return n.forwardMsg(em, true)
}

func (n *AgentAssistantNode) handleCitations(ctx models.Context, textContent string, citations []*agent_executor.Citation, lastParagraph *string, inCodeBlock *bool, inHeading *bool) (string, error) {
	if len(citations) == 0 {
		return textContent, nil
	}
	// TODO: 暂时使用知识库中的rerank模型进行引用匹配，后续使用专门的rerank
	rerankModel := n.pn.AgentConfig.RerankModelSvc
	if rerankModel == nil {
		ctx.Warnf("there is no rerank model, skip citation matching")
		return textContent, nil
	}
	// 对非代码、非标题段，且长度超过一定长度，才进行引用匹配
	// TODO: 设置引用阈值0.9，设置引用topK为3
	if !*inCodeBlock && !*inHeading && utf8.RuneCountInString(*lastParagraph) > conf.Config.Citation.MinParagraphLength {
		citationNums, err := matchCitaionNums(ctx, *lastParagraph, citations, rerankModel)
		if err != nil {
			return "", stderr.Wrap(err, "match citation nums")
		}
		if len(citationNums) > 0 {
			citationNumStr := " "
			for _, num := range citationNums {
				citationNumStr += fmt.Sprintf("[[%d]]", num+1)
			}
			// 引用序号插入到段落末尾，换行符之前
			if strings.Contains(textContent, "\n") {
				textContent = strings.Replace(textContent, "\n", citationNumStr+"\n", 1)
			} else {
				textContent = textContent + citationNumStr
			}
		}
	}
	// 重置段落和标题状态
	*lastParagraph = ""
	*inHeading = false
	return textContent, nil
}

func matchCitaionNums(ctx models.Context, paragraph string, citations []*agent_executor.Citation, rerankModel *pb.ModelService) ([]int, error) {
	if len(citations) == 0 {
		return nil, nil
	}
	call := ctx.Call
	callCtx := call.Ctx()
	texts := make([]string, len(citations))
	text2citationNum := make(map[string]int)
	for i, citation := range citations {
		texts[i] = citation.Content
		text2citationNum[citation.Content] = i
	}
	rerankReq := &triton.RerankReq{
		Query: paragraph,
		Texts: texts,
	}
	rerankRsp, err := clients2.Rerank(callCtx, rerankModel, rerankReq)
	if err != nil {
		ctx.Errorf("rerank request %+v failed: %+v", rerankReq, err)
		return nil, stderr.Wrap(err, "rerank request %+v failed", rerankReq)
	}
	rspTexts := rerankRsp.Texts
	rspScores := rerankRsp.Scores
	if len(rspTexts) != len(rspScores) {
		return nil, stderr.Errorf("rerank response texts and scores length not equal")
	}
	threshold := conf.Config.Citation.RelevanceScoreThreshold
	topK := conf.Config.Citation.TopK
	ctx.Infof("rerank socres: %+v, threshold: %f, topk: %d", rspScores, threshold, topK)
	// 遍历返回的texts 和 scores，如果 socre 大于阈值，则将对应的索引添加到 citaionNums
	citationNums := make([]int, 0)
	for i, text := range rspTexts {
		citationNum, ok := text2citationNum[text]
		if !ok {
			ctx.Warnf("rerank response text %s not found in citations %+v", text, citations)
			continue
		}
		score := rspScores[i]
		if score > threshold {
			citationNums = append(citationNums, citationNum)
		}
		// rerank接口以文本相似度分数降序返回，取前 k 个
		if len(citationNums) >= topK {
			break
		}
	}
	// 升序排序引用序号
	sort.Ints(citationNums)
	return citationNums, nil
}

func getAgentChatReq(model *pb.ModelService, query string) (*triton.OpenAiChatReq, error) {
	chatReq, err := clients2.GetBaseChatReq(model)
	if err != nil {
		return nil, stderr.Wrap(err, "failed to get BaseChatReq")
	}
	chatReq.Stop = append(chatReq.Stop, models.StopWordObservation)
	chatReq.Messages = []triton.MultimodalMessageItem{
		{
			Role:    triton.OpenaiUserMsg,
			Content: query,
		},
	}
	return chatReq, nil
}
