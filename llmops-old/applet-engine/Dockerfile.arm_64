# ARG 指令有生效范围，如果在 FROM 指令之前指定，那么只能用于 FROM 指令中
# 在FROM 之后使用变量，必须在每个阶段分别指定
ARG ARCH=arm64

FROM ***********/aip/base/golang-builder:1.23-ubuntu24.04 AS build
# RUN apk add go
ARG PACKAGE=transwarp.io/applied-ai/applet-engine
ARG BUILD_VERSION=0.0.0
ARG BUILD_TIME=99999999
ARG CI_COMMIT_SHA=00000000
ARG ARCH=arm64
ARG COMP=applet-engine

WORKDIR /build

COPY ${COMP} ${COMP}
COPY vision-std vision-std
COPY llmops-common llmops-common
COPY applet-backend applet-backend
COPY hippo-go hippo-go
COPY tkh-go tkh-go

RUN cd ${COMP} && \
    GOPROXY=http://*************:1111,https://goproxy.io/,https://goproxy.cn/,https://mirrors.aliyun.com/goproxy,direct \
    GO111MODULE=on \
    GOOS=linux \
    GOARCH=${ARCH} \
    GOSUMDB=off \
    go build -ldflags "-X ${PACKAGE}/version.BuildName=${PACKAGE} -X ${PACKAGE}/version.BuildVersion=${BUILD_VERSION} -X ${PACKAGE}/version.BuildTime=${BUILD_TIME} -X ${PACKAGE}/version.CommitID=${CI_COMMIT_SHA}" \
    -o ${COMP} ./main.go


FROM ***********/aip/base/alpine-glibc228:${ARCH}-3.19.1
ARG ARCH=arm64
ARG COMP=applet-engine

WORKDIR /${COMP}

COPY --from=build /build/vision-std/license/bin/verifier /usr/bin/verifier
COPY --from=build /build/${COMP}/etc etc
COPY --from=build /build/${COMP}/${COMP} ${COMP}
COPY --from=build /build/${COMP}/bin/boot.sh /bin/boot.sh
ENV GOLANG_PROTOBUF_REGISTRATION_CONFLICT=warn

RUN chmod +x /bin/boot.sh
EXPOSE 9092

CMD ["/bin/sh", "/bin/boot.sh"]
