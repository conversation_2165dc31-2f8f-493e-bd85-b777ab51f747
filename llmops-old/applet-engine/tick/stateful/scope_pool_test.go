package stateful_test

import (
	"reflect"
	"testing"

	"transwarp.io/applied-ai/applet-engine/tick/ast"
	"transwarp.io/applied-ai/applet-engine/tick/stateful"
)

func TestScopePool_Sanity(t *testing.T) {
	n := stateful.NewScopePool([]string{"value"})

	scope := n.Get()

	if scope.Has("value") {
		t.<PERSON>("First: expected scope to not have a value set")
	}
	value := 42
	scope.Set("value", value)
	if !scope.Has("value") {
		t.<PERSON>("First: expected scope to have a value set")
	}
	if v, err := scope.Get("value"); err != nil || v != value {
		t.<PERSON>rrorf("First: unexpected scope value got %v exp %v", v, value)
	}

	n.Put(scope)

	// Scope should be empty now
	scope = n.Get()
	if scope.Has("value") {
		t.<PERSON>rf("Second: expected scope to not have a value set")
	}
	value = 24
	scope.Set("value", value)
	if !scope.Has("value") {
		t.<PERSON>("Second: expected scope to have a value set")
	}
	if v, err := scope.Get("value"); err != nil || v != value {
		t.<PERSON><PERSON>rf("Second: unexpected scope value got %v exp %v", v, value)
	}
}

func TestExpression_RefernceVariables(t *testing.T) {

	type expectation struct {
		node         ast.Node
		refVariables []string
	}

	expectations := []expectation{
		{node: &ast.NumberNode{IsFloat: true}, refVariables: make([]string, 0)},
		{node: &ast.BoolNode{}, refVariables: make([]string, 0)},

		{node: &ast.ReferenceNode{Reference: "yosi"}, refVariables: []string{"yosi"}},
		{node: &ast.BinaryNode{Left: &ast.ReferenceNode{Reference: "value"}, Right: &ast.NumberNode{IsInt: true}}, refVariables: []string{"value"}},
	}

	for i, expect := range expectations {
		refVariables := ast.FindReferenceVariables(expect.node)
		if !reflect.DeepEqual(refVariables, expect.refVariables) {
			t.Errorf("[Iteration: %v, Node: %T] Got unexpected result:\ngot: %v\nexpected: %v", i+1, expect.node, refVariables, expect.refVariables)
		}

	}
}
