machine:
    services:
        - docker

dependencies:
    pre:
      # setup ipv6
      - sudo sysctl -w net.ipv6.conf.lo.disable_ipv6=0 net.ipv6.conf.default.disable_ipv6=0 net.ipv6.conf.all.disable_ipv6=0
    cache_directories:
        - "~/docker"
    override:
      - ./test.sh save

test:
    override:
        - bash circle-test.sh:
            parallel: true

deployment:
    release:
        tag: /v[0-9]+(\.[0-9]+){2}(-(rc|beta)[0-9]+)?/
        commands:
            - ./build.sh --debug --clean --generate --package --package-udfs --upload --bucket=dl.influxdata.com/kapacitor/releases --platform=all --arch=all --release
