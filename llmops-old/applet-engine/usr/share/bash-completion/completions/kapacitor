# Bash completion for the kapacitor command.
_kapacitor()
{
    local cur prev words

    COMPREPLY=()
    cur=${COMP_WORDS[COMP_CWORD]}
    prev=${COMP_WORDS[COMP_CWORD-1]}

    case "${COMP_WORDS[1]}" in
        record)
            case "${COMP_WORDS[2]}" in
                batch)
                    case "$prev" in
                        -task)
                            words=$(_kapacitor_list tasks "$cur")
                            ;;
                        *)
                            words='-no-wait -past -recording-id -start -stop -task'
                            ;;
                    esac
                    ;;
                stream)
                    case "$prev" in
                        -task)
                            words=$(_kapacitor_list tasks "$cur")
                            ;;
                        *)
                            words='-duration -no-wait -recording-id -task'
                            ;;
                    esac
                    ;;
                query)
                    words='-query -type -cluster -no-wait -recording-id'
                    ;;
                *)
                    words='batch stream query'
                    ;;
            esac
            ;;
        define)
            if [[ -z "${COMP_WORDS[2]}" || ("$cur" = "${COMP_WORDS[2]}" && -z "${COMP_WORDS[3]}") ]]
            then
                words=$(_kapacitor_list tasks "$cur")
            else
                case "$prev" in
                    -dbrp)
                        words=$(_kapacitor_dbrps)
                        ;;
                    -tick)
                        _kapacitor_tick_files "${cur}"
                        ;;
                    -type)
                        words='batch stream'
                        ;;
                    -template)
                        words=$(_kapacitor_list templates "$cur")
                        ;;
                    -vars)
                        _kapacitor_json_files "${cur}"
                        ;;
                    *)
                        words='-dbrp -no-reload -tick -type -template -vars'
                        ;;
                esac
            fi
            ;;
        define-template)
            if [[ -z "${COMP_WORDS[2]}" || ("$cur" = "${COMP_WORDS[2]}" && -z "${COMP_WORDS[3]}") ]]
            then
                words=$(_kapacitor_list templates "$cur")
            else
                case "$prev" in
                    -tick)
                        _kapacitor_tick_files "${cur}"
                        ;;
                    -type)
                        words='batch stream'
                        ;;
                    *)
                        words='-tick -type'
                        ;;
                esac
            fi
            ;;
        define-topic-handler)
          _kapacitor_json_yaml_files "${cur}"
            ;;
        replay)
            case "$prev" in
                -recording)
                    words=$(_kapacitor_list recordings "$cur")
                    ;;
                -task)
                    words=$(_kapacitor_list tasks "$cur")
                    ;;
                *)
                    words='-no-wait -real-clock -rec-time -recording -replay-id -task'
                    ;;
            esac
            ;;
        replay-live)
            case "${COMP_WORDS[2]}" in
                batch)
                    case "$prev" in
                        -task)
                            words=$(_kapacitor_list tasks "$cur")
                            ;;
                        *)
                            words='-no-wait -past -real-clock -rec-time -replay-id -start -stop -task'
                            ;;
                    esac
                    ;;
                query)
                    case "$prev" in
                        -task)
                            words=$(_kapacitor_list tasks "$cur")
                            ;;
                        *)
                            words='-cluster -no-wait -real-clock -rec-time -replay-id -query -task'
                            ;;
                    esac
                    ;;
                *)
                    words='batch query'
                    ;;
            esac
            ;;
        show)
            case "$prev" in
                -replay)
                    words=$(_kapacitor_list replays "$cur")
                    ;;
                *)
                    words=$(_kapacitor_list tasks "$cur")
                    words+=' -replay'
                    ;;
            esac
            ;;
        reload)
            words=$(_kapacitor_list tasks "$cur")
            ;;
        enable)
            words=$(_kapacitor_list_filtered tasks "$cur" "\$3 == \"disabled\"")
            ;;
        disable)
            words=$(_kapacitor_list_filtered tasks "$cur" "\$3 == \"enabled\"")
            ;;
        service-tests)
            words=$(_kapacitor_list service-tests "$cur")
            ;;
        delete|list)
            case "${COMP_WORDS[2]}" in
                tasks|templates|recordings|replays|topics|service-tests)
                    words=$(_kapacitor_list "${COMP_WORDS[2]}" "$cur")
                    ;;
                topic-handlers)
                    if [[ -z "${COMP_WORDS[3]}" || ("$cur" = "${COMP_WORDS[3]}" && -z "${COMP_WORDS[4]}") ]]
                    then
                        words=$(_kapacitor_list topics "$cur")
                    else
                        words=$(_kapacitor_list_topic_handlers "${COMP_WORDS[3]}" "$cur")
                    fi
                    ;;
                *)
                    words='tasks templates recordings replays topic-handlers topics service-tests'
                    ;;
            esac
            ;;
        show-template)
            words=$(_kapacitor_list templates "$cur")
            ;;
        show-topic-handler)
            if [[ -z "${COMP_WORDS[2]}" || ("$cur" = "${COMP_WORDS[2]}" && -z "${COMP_WORDS[3]}") ]]
            then
                words=$(_kapacitor_list topics "$cur")
            else
                words=$(_kapacitor_list_topic_handlers "${COMP_WORDS[2]}" "$cur")
            fi
            ;;
        show-topic)
            words=$(_kapacitor_list topics "$cur")
            ;;
        backup)
            ;;
        level)
            words='debug info warn error'
            ;;
        logs)
            ;;
        watch)
            words=$(_kapacitor_list tasks)
            ;;
        stats)
            case "${COMP_WORDS[2]}" in
                ingress|general)
                    ;;
                *)
                    words='general ingress'
                    ;;
            esac
            ;;
        *)
            words='record define define-template define-topic-handler replay replay-live enable disable \
                reload delete list show show-template show-topic-handler show-topic backup level logs watch stats version vars service-tests help'
            ;;
    esac
    if [ -z "$COMPREPLY" ]
    then
        COMPREPLY=($(compgen -W "$words" -- "$cur"))
    fi

    return 0
}

_kapacitor_files_add_postfix()
{
    #Add a slash (directories) or space (files except directories) after name
    local c i=0
    for c in "${COMPREPLY[@]}"; do
        if [ -d "${c}" ]; then
            COMPREPLY[i++]="${c}/";
        else
            COMPREPLY[i++]="${c} ";
        fi
    done
    compopt -o nospace
}

_kapacitor_tick_files()
{
    local didi cur="$1"
    COMPREPLY=($(compgen -o filenames -f -X '!*.tick' -- "$cur") )
    didi=$(compgen -d -- "$cur")
    COMPREPLY=(${COMPREPLY[@]:-} $didi )
    _kapacitor_files_add_postfix
}

_kapacitor_json_files()
{
    local didi cur="$1"
    COMPREPLY=($(compgen -o filenames -f -X '!*.json' -- "$cur") )
    didi=$(compgen -d -- "$cur")
    COMPREPLY=(${COMPREPLY[@]:-} $didi )
    _kapacitor_files_add_postfix
}

_kapacitor_json_yaml_files()
{
    local didi cur="$1"
    COMPREPLY=($(compgen -o filenames -f -X '!*.json' -- "$cur") )
    COMPREPLY+=($(compgen -o filenames -f -X '!*.yaml' -- "$cur") )
    didi=$(compgen -d -- "$cur")
    COMPREPLY=(${COMPREPLY[@]:-} $didi )
    _kapacitor_files_add_postfix
}

_kapacitor_list()
{
    # List a certain kind of object and return a set of IDs
    kapacitor list "$1" "$2*" 2>/dev/null | awk 'NR>1 {print $1}' 2>/dev/null
}

_kapacitor_list_topic_handlers()
{
    # List a certain kind of object and return a set of IDs
    kapacitor list topic-handlers "$1" "$2*" 2>/dev/null | awk 'NR>1 {print $2}' 2>/dev/null
}

_kapacitor_list_filtered()
{
    # List a certain kind of object and return a filtered set of IDs ($3: AWK 'if' test)
    awkcode='NR>1 {if ('"$3"') print $1}'
    kapacitor list "$1" "$2*" 2>/dev/null | awk "$awkcode" 2>/dev/null

}

_kapacitor_dbrps()
{
    # Determine set of DBRPs from the ingress stats
    kapacitor stats ingress 2>/dev/null | awk 'NR>1 {printf "%s.%s\n", $1, $2}' 2>/dev/null
}

complete -F _kapacitor kapacitor
