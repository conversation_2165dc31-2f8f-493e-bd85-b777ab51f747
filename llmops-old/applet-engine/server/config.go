package server

import (
	"encoding"
	"fmt"
	"log"
	"os"
	"os/user"
	"path/filepath"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/BurntSushi/toml"
	"github.com/pkg/errors"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-engine/conf"
	"transwarp.io/applied-ai/applet-engine/services/config"
	"transwarp.io/applied-ai/applet-engine/services/deadman"
	"transwarp.io/applied-ai/applet-engine/services/diagnostic"
	"transwarp.io/applied-ai/applet-engine/services/httpd"
	"transwarp.io/applied-ai/applet-engine/services/httppost"
	"transwarp.io/applied-ai/applet-engine/services/influxdb"
	"transwarp.io/applied-ai/applet-engine/services/kafka"
	"transwarp.io/applied-ai/applet-engine/services/load"
	"transwarp.io/applied-ai/applet-engine/services/mqtt"
	"transwarp.io/applied-ai/applet-engine/services/replay"
	"transwarp.io/applied-ai/applet-engine/services/reporting"
	"transwarp.io/applied-ai/applet-engine/services/smtp"
	"transwarp.io/applied-ai/applet-engine/services/stats"
	"transwarp.io/applied-ai/applet-engine/services/storage"
	"transwarp.io/applied-ai/applet-engine/services/task_store"
	"transwarp.io/applied-ai/applet-engine/services/udf"
	"transwarp.io/applied-ai/applet-engine/services/udp"
	"transwarp.io/applied-ai/applet-engine/tools/command"

	"github.com/influxdata/influxdb/services/collectd"
	"github.com/influxdata/influxdb/services/graphite"
	"github.com/influxdata/influxdb/services/opentsdb"
)

// Config represents the configuration format for the kapacitord binary.
type Config struct {
	// Stream         stream.Config     `toml:"stream"`
	HTTP           httpd.Config            `toml:"http"`
	AppletEngine   conf.AppletEngineConfig `toml:"applet"`
	Replay         replay.Config           `toml:"replay"`
	Storage        storage.Config          `toml:"storage"`
	Task           task_store.Config       `toml:"task"`
	Load           load.Config             `toml:"load"`
	InfluxDB       []influxdb.Config       `toml:"influxdb" override:"influxdb,element-key=name"`
	Logging        diagnostic.Config       `toml:"logging"`
	ConfigOverride config.Config           `toml:"config-override"`

	// Input services
	Graphite []graphite.Config `toml:"graphite"`
	Collectd collectd.Config   `toml:"collectd"`
	OpenTSDB opentsdb.Config   `toml:"opentsdb"`
	UDP      []udp.Config      `toml:"udp"`

	// Alert handlers
	Kafka    kafka.Configs    `toml:"kafka" override:"kafka,element-key=id"`
	MQTT     mqtt.Configs     `toml:"mqtt" override:"mqtt,element-key=name"`
	HTTPPost httppost.Configs `toml:"httppost" override:"httppost,element-key=endpoint"`
	SMTP     smtp.Config      `toml:"smtp" override:"smtp"`

	Reporting reporting.Config `toml:"reporting"`
	Stats     stats.Config     `toml:"stats"`
	UDF       udf.Config       `toml:"udf"`
	Deadman   deadman.Config   `toml:"deadman"`

	Hostname               string `toml:"hostname"`
	DataDir                string `toml:"data_dir"`
	SkipConfigOverrides    bool   `toml:"skip-config-overrides"`
	DefaultRetentionPolicy string `toml:"default-retention-policy"`

	Commander command.Commander `toml:"-"`
}

// NewConfig returns an instance of Config with reasonable defaults.
func NewConfig() *Config {
	c := &Config{
		Hostname:  "localhost",
		Commander: command.ExecCommander,
	}

	c.HTTP = httpd.NewConfig()
	c.AppletEngine = *conf.Config
	// c.Stream = stream.NewConfig()
	c.Storage = storage.NewConfig()
	c.Replay = replay.NewConfig()
	c.Task = task_store.NewConfig()
	c.InfluxDB = []influxdb.Config{influxdb.NewConfig()}
	c.Logging = diagnostic.NewConfig()
	c.ConfigOverride = config.NewConfig()

	c.Collectd = collectd.NewConfig()
	c.OpenTSDB = opentsdb.NewConfig()

	c.Kafka = kafka.Configs{kafka.NewConfig()}
	c.MQTT = mqtt.Configs{mqtt.NewConfig()}
	c.HTTPPost = httppost.Configs{httppost.NewConfig()}
	c.SMTP = smtp.NewConfig()

	c.Reporting = reporting.NewConfig()
	c.Stats = stats.NewConfig()
	c.UDF = udf.NewConfig()
	c.Deadman = deadman.NewConfig()
	c.Load = load.NewConfig()

	return c
}

// NewDemoConfig returns the config that runs when no config is specified.
func NewDemoConfig() (*Config, error) {
	c := NewConfig()

	var homeDir string
	// By default, store meta and data files in current users home directory
	u, err := user.Current()
	if err == nil {
		homeDir = u.HomeDir
	} else if os.Getenv("HOME") != "" {
		homeDir = os.Getenv("HOME")
	} else {
		return nil, fmt.Errorf("failed to determine current user for storage")
	}

	c.Replay.Dir = filepath.Join(homeDir, ".kapacitor", c.Replay.Dir)
	c.Task.Dir = filepath.Join(homeDir, ".kapacitor", c.Task.Dir)
	c.Storage.BoltDBPath = filepath.Join(homeDir, ".kapacitor", c.Storage.BoltDBPath)
	c.DataDir = filepath.Join(homeDir, ".kapacitor", c.DataDir)
	c.Load.Dir = filepath.Join(homeDir, ".kapacitor", c.Load.Dir)

	return c, nil
}

// Validate returns an error if the config is invalid.
func (c *Config) Validate() error {
	if c.Hostname == "" {
		return fmt.Errorf("must configure valid hostname")
	}
	if c.DataDir == "" {
		return fmt.Errorf("must configure valid data dir")
	}
	if err := c.Replay.Validate(); err != nil {
		return errors.Wrap(err, "replay")
	}
	if err := c.Storage.Validate(); err != nil {
		return errors.Wrap(err, "storage")
	}
	if err := c.HTTP.Validate(); err != nil {
		return errors.Wrap(err, "http")
	}
	if err := c.Task.Validate(); err != nil {
		return errors.Wrap(err, "task")
	}
	if err := c.Load.Validate(); err != nil {
		return err
	}
	// Validate the set of InfluxDB configs.
	// All names should be unique.
	names := make(map[string]bool, len(c.InfluxDB))
	// Should be exactly one default if at least one configs is enabled.
	defaultInfluxDB := -1
	numEnabled := 0
	for i := range c.InfluxDB {
		c.InfluxDB[i].ApplyConditionalDefaults()
		config := c.InfluxDB[i]
		if names[config.Name] {
			return fmt.Errorf("duplicate name %q for influxdb configs", config.Name)
		}
		names[config.Name] = true
		if err := config.Validate(); err != nil {
			return err
		}
		if config.Enabled && config.Default {
			if defaultInfluxDB != -1 {
				return fmt.Errorf("More than one InfluxDB default was specified: %s %s", config.Name, c.InfluxDB[defaultInfluxDB].Name)
			}
			defaultInfluxDB = i
		}
		if config.Enabled {
			numEnabled++
		}
	}
	if numEnabled > 1 && defaultInfluxDB == -1 {
		return errors.New("at least one of the enabled InfluxDB clusters must be marked as default.")
	}

	// Validate inputs
	for _, g := range c.Graphite {
		if err := g.Validate(); err != nil {
			return errors.Wrap(err, "graphite")
		}
	}

	// Validate alert handlers
	if err := c.Kafka.Validate(); err != nil {
		return errors.Wrap(err, "kafka")
	}
	if err := c.MQTT.Validate(); err != nil {
		return errors.Wrap(err, "mqtt")
	}
	if err := c.HTTPPost.Validate(); err != nil {
		return errors.Wrap(err, "httppost")
	}
	if err := c.SMTP.Validate(); err != nil {
		return errors.Wrap(err, "smtp")
	}

	if err := c.UDF.Validate(); err != nil {
		return errors.Wrap(err, "udf")
	}
	return nil
}

func (c *Config) ApplyEnvOverrides() error {
	return c.applyEnvOverrides("KAPACITOR", "", reflect.ValueOf(c))
}

func (c *Config) applyEnvOverridesToMap(prefix string, fieldDesc string, mapValue, key, spec reflect.Value) error {
	// If we have a pointer, dereference it
	s := spec
	if spec.Kind() == reflect.Ptr {
		s = spec.Elem()
	}

	var value string

	if s.Kind() != reflect.Struct {
		value = os.Getenv(prefix)
		// Skip any fields we don't have a value to set
		if value == "" {
			return nil
		}

		if fieldDesc != "" {
			fieldDesc = " to " + fieldDesc
		}
	}

	switch s.Kind() {
	case reflect.String:
		mapValue.SetMapIndex(key, reflect.ValueOf(value))
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:

		var intValue int64

		// Handle toml.Duration
		if s.Type().Name() == "Duration" {
			dur, err := time.ParseDuration(value)
			if err != nil {
				return fmt.Errorf("failed to apply %v%v using type %v and value '%v'", prefix, fieldDesc, s.Type().String(), value)
			}
			intValue = dur.Nanoseconds()
		} else {
			var err error
			intValue, err = strconv.ParseInt(value, 0, s.Type().Bits())
			if err != nil {
				return fmt.Errorf("failed to apply %v%v using type %v and value '%v'", prefix, fieldDesc, s.Type().String(), value)
			}
		}

		mapValue.SetMapIndex(key, reflect.ValueOf(intValue))
	case reflect.Bool:
		boolValue, err := strconv.ParseBool(value)
		if err != nil {
			return fmt.Errorf("failed to apply %v%v using type %v and value '%v'", prefix, fieldDesc, s.Type().String(), value)

		}
		mapValue.SetMapIndex(key, reflect.ValueOf(boolValue))
	case reflect.Float32, reflect.Float64:
		floatValue, err := strconv.ParseFloat(value, s.Type().Bits())
		if err != nil {
			return fmt.Errorf("failed to apply %v%v using type %v and value '%v'", prefix, fieldDesc, s.Type().String(), value)

		}
		mapValue.SetMapIndex(key, reflect.ValueOf(floatValue))
	case reflect.Struct:
		if err := c.applyEnvOverridesToStruct(prefix, s); err != nil {
			return err
		}
	}
	return nil
}

var textUnmarshalerType = reflect.TypeOf((*encoding.TextUnmarshaler)(nil)).Elem()

func (c *Config) applyEnvOverrides(prefix string, fieldDesc string, spec reflect.Value) error {
	// If we have a pointer, dereference it
	s := spec
	if spec.Kind() == reflect.Ptr {
		s = spec.Elem()
	}
	var addrSpec reflect.Value
	if i := reflect.Indirect(s); i.CanAddr() {
		addrSpec = i.Addr()
	}

	var value string

	if s.Kind() != reflect.Struct {
		value = os.Getenv(prefix)
		// Skip any fields we don't have a value to set
		if value == "" {
			return nil
		}

		if fieldDesc != "" {
			fieldDesc = " to " + fieldDesc
		}
	}

	// Check if the type is a test.Unmarshaler
	if addrSpec.Type().Implements(textUnmarshalerType) {
		um := addrSpec.Interface().(encoding.TextUnmarshaler)
		err := um.UnmarshalText([]byte(value))
		return errors.Wrap(err, "failed to unmarshal env var")
	}

	switch s.Kind() {
	case reflect.String:
		s.SetString(value)
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		intValue, err := strconv.ParseInt(value, 0, s.Type().Bits())
		if err != nil {
			return fmt.Errorf("failed to apply %v%v using type %v and value '%v'", prefix, fieldDesc, s.Type().String(), value)
		}

		s.SetInt(intValue)
	case reflect.Bool:
		boolValue, err := strconv.ParseBool(value)
		if err != nil {
			return fmt.Errorf("failed to apply %v%v using type %v and value '%v'", prefix, fieldDesc, s.Type().String(), value)

		}
		s.SetBool(boolValue)
	case reflect.Float32, reflect.Float64:
		floatValue, err := strconv.ParseFloat(value, s.Type().Bits())
		if err != nil {
			return fmt.Errorf("failed to apply %v%v using type %v and value '%v'", prefix, fieldDesc, s.Type().String(), value)

		}
		s.SetFloat(floatValue)
	case reflect.Struct:
		if err := c.applyEnvOverridesToStruct(prefix, s); err != nil {
			return err
		}
	}
	return nil
}

const (
	// envConfigTag is a struct tag key for specifying information to the apply env overrides configuration process.
	envConfigTag = "env-config"
	// implicitIndexTag is the name of the value of an env-config tag that instructs the process to allow implicit 0 indexes.
	implicitIndexTag = "implicit-index"
)

func (c *Config) applyEnvOverridesToStruct(prefix string, s reflect.Value) error {
	typeOfSpec := s.Type()
	for i := 0; i < s.NumField(); i++ {
		f := s.Field(i)
		// Get the toml tag to determine what env var name to use
		configName := typeOfSpec.Field(i).Tag.Get("toml")
		// Replace hyphens with underscores to avoid issues with shells
		configName = strings.Replace(configName, "-", "_", -1)
		fieldType := typeOfSpec.Field(i)
		fieldName := fieldType.Name

		// Skip any fields that we cannot set
		if f.CanSet() || f.Kind() == reflect.Slice {

			// Use the upper-case prefix and toml name for the env var
			key := strings.ToUpper(configName)
			if prefix != "" {
				key = strings.ToUpper(fmt.Sprintf("%s_%s", prefix, configName))
			}

			// If the type is s slice, apply to each using the index as a suffix
			// e.g. GRAPHITE_0
			if f.Kind() == reflect.Slice || f.Kind() == reflect.Array {
				// Determine if the field supports implicit indexes.
				implicitIndex := false
				fieldEnvConfigTags := strings.Split(fieldType.Tag.Get(envConfigTag), ",")
				for _, s := range fieldEnvConfigTags {
					if s == implicitIndexTag {
						implicitIndex = true
						break
					}
				}

				l := f.Len()
				for i := 0; i < l; i++ {
					// Also support an implicit 0 index, if there is only one entry and the slice supports it.
					// e.g. KAPACITOR_KUBERNETES_ENABLED=true
					if implicitIndex && l == 1 {
						if err := c.applyEnvOverrides(key, fieldName, f.Index(i)); err != nil {
							return err
						}
					}
					if err := c.applyEnvOverrides(fmt.Sprintf("%s_%d", key, i), fieldName, f.Index(i)); err != nil {
						return err
					}
				}
			} else if f.Kind() == reflect.Map {
				for _, k := range f.MapKeys() {
					if err := c.applyEnvOverridesToMap(fmt.Sprintf("%s_%v", key, k), fieldName, f, k, f.MapIndex(k)); err != nil {
						return err
					}
				}
			} else if err := c.applyEnvOverrides(key, fieldName, f); err != nil {
				return err
			}
		}
	}
	return nil
}

// FindConfigPath returns the config path specified or searches for a valid config path.
// It will return a path by searching in this order:
//   1. The given configPath
//   2. The environment variable KAPACITOR_CONFIG_PATH
//   3. The first non empty kapacitor.conf file in the path:
//        - ~/.kapacitor/
//        - /etc/kapacitor/
func FindConfigPath(configPath string) string {
	if configPath != "" {
		if configPath == os.DevNull {
			return ""
		}
		return configPath
	} else if envVar := os.Getenv("KAPACITOR_CONFIG_PATH"); envVar != "" {
		return envVar
	}

	for _, path := range []string{
		os.ExpandEnv("${HOME}/.kapacitor/kapacitor.conf"),
		"/etc/kapacitor/kapacitor.conf",
	} {
		if fi, err := os.Stat(path); err == nil && fi.Size() != 0 {
			return path
		}
	}
	return ""
}

// ParseConfig parses the config at path.
// Returns a demo configuration if path is blank.
func ParseConfig(path string) (*Config, error) {
	cfg, err := NewDemoConfig()
	if err != nil {
		cfg = NewConfig()
	}

	if path == "" {
		return cfg, nil
	}

	log.Println("Merging with configuration at:", path)
	if _, err := toml.DecodeFile(path, &cfg); err != nil {
		return nil, err
	}
	return cfg, nil
}

var (
	c *Config
)

func InitConfig(path string) (cfg *Config, err error) {
	// Parse config from path.
	cfg, err = ParseConfig(path)
	if err != nil {
		return nil, fmt.Errorf("parse config: %s", err)
	}

	// Apply any environment variables on top of the parsed config
	if err = cfg.ApplyEnvOverrides(); err != nil {
		return nil, fmt.Errorf("apply env config: %v", err)
	}
	// Validate the configuration.
	if err = cfg.Validate(); err != nil {
		return nil, fmt.Errorf("%s. To generate a valid configuration file run `kapacitord config > kapacitor.generated.conf`", err)
	}

	c = cfg
	return
}

func GetConfig() *Config {
	if c == nil {
		stdlog.Errorf("config is nil")
	}
	return c
}
