# Kapacitor HTTP API Go client

This package provides an official Go client package for the Kapacitor HTTP API.


## API stability

> NOTE: This package will not be considered stable until the 1.0 release of Ka<PERSON><PERSON>tor.

This is version 1 of the client, after the 1.0 release no breaking changes will be made to this package.
Future versions may be added as necessary.

## Docs

* [GoDoc](https://godoc.org/github.com/influxdata/kapacitor/client/v1)
* [API Docs](https://docs.influxdata.com/kapacitor/latest/api/)

