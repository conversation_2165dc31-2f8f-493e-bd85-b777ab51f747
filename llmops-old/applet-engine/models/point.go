package models

import (
	"bytes"
	"sort"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

type GroupID string

const (
	NilGroup GroupID = ""
)

type Dimensions struct {
	ByName   bool
	TagNames []string
}

func (d Dimensions) Equal(o Dimensions) bool {
	if d.ByName != o.ByName || len(d.TagNames) != len(o.Tag<PERSON>ames) {
		return false
	}
	for i := range d.TagNames {
		if d.TagNames[i] != o.TagNames[i] {
			return false
		}
	}
	return true
}
func (d Dimensions) Copy() Dimensions {
	tags := make([]string, len(d.TagNames))
	copy(tags, d.TagNames)
	return Dimensions{ByName: d.ByName, TagNames: tags}
}

func (d Dimensions) ToSet() map[string]bool {
	set := make(map[string]bool, len(d.TagNames))
	for _, dim := range d.TagNames {
		set[dim] = true
	}
	return set
}

type Fields map[string]interface{}

func (f Fields) Context() (Context, error) {
	ctx, exist, err := GetValueOfFields[Context](f, PredefinedFieldContext)
	if err != nil {
		return Context{}, stderr.Wrap(err, "get context of fields")
	}
	if !exist {
		return Context{}, stderr.Error("context not exist in fields")
	}
	return ctx, nil
}

func (f Fields) SetContext(ctx Context) error {
	return SetKeyValueOfFields(f, PredefinedFieldContext, ctx)
}

func (f Fields) Meta() (NodeMeta, error) {
	meta, exist, err := GetValueOfFields[NodeMeta](f, PredefinedFieldMeta)
	if err != nil {
		return NodeMeta{}, stderr.Wrap(err, "get meta of fields")
	}
	if !exist {
		return NodeMeta{}, stderr.Error("meta not exist in fields")
	}
	return meta, nil
}

func (f Fields) SetMeta(meta NodeMeta) error {
	return SetKeyValueOfFields(f, PredefinedFieldMeta, meta)
}

func (f Fields) RawInput() (RawInput, error) {
	rawInput, exist, err := GetValueOfFields[RawInput](f, PredefinedFieldRawInput)
	if err != nil {
		return nil, stderr.Wrap(err, "get raw input of fields")
	}
	if !exist {
		return nil, stderr.Error("raw input not exist in fields")
	}
	return rawInput, nil
}

func (f Fields) SetRawInput(rawInput RawInput) error {
	return SetKeyValueOfFields(f, PredefinedFieldRawInput, rawInput)
}

func (f Fields) Copy() Fields {
	cf := make(Fields, len(f))
	for k, v := range f {
		cf[k] = v
	}
	return cf
}

// DeepCopyTrace 消息传递时Trace有读写操作，其他只有读，只深拷贝Trace字段
func (f Fields) DeepCopyTrace() (Fields, error) {
	copiedFields := f.Copy()
	traceAny, ok := copiedFields[PredefinedFieldTrace]
	if !ok {
		return copiedFields, nil
	}
	trace, ok := traceAny.(Trace)
	if !ok {
		return nil, stderr.Error("convert %T to map[string]*pipeline.TraceItem", traceAny)
	}
	copiedTrace := make(Trace, len(trace))
	for k, v := range trace {
		copiedTrace[k] = v
	}
	copiedFields[PredefinedFieldTrace] = copiedTrace
	return copiedFields, nil
}

func SortedFields(fields Fields) []string {
	a := make([]string, 0, len(fields))
	for k := range fields {
		a = append(a, k)
	}
	sort.Strings(a)
	return a
}

type Tags map[string]string

func (t Tags) Copy() Tags {
	ct := make(Tags, len(t))
	for k, v := range t {
		ct[k] = v
	}
	return ct
}

func SortedKeys(tags map[string]string) []string {
	a := make([]string, 0, len(tags))
	for k := range tags {
		a = append(a, k)
	}
	sort.Strings(a)
	return a
}

func ToGroupID(name string, tags map[string]string, dims Dimensions) GroupID {
	if len(dims.TagNames) == 0 {
		if dims.ByName {
			return GroupID(name)
		}
		return NilGroup
	}
	var buf bytes.Buffer
	if dims.ByName {
		buf.WriteString(name)
		// Add delimiter that is not allowed in name.
		buf.WriteRune('\n')
	}
	for i, d := range dims.TagNames {
		if i != 0 {
			buf.WriteRune(',')
		}
		buf.WriteString(d)
		buf.WriteRune('=')
		buf.WriteString(tags[d])

	}
	return GroupID(buf.Bytes())
}

// GetValueOfFields 从给定的models.Fields对象中获取指定字段的值，并返回结果
// 参数：
//   - fields: 要获取字段值的models.Fields对象
//   - key: 要获取值的字段名
//
// 返回值：
//   - res: 返回的字段值
//   - exist: 标记字段是否存在
//   - err: 错误信息，如果出现错误则返回相应的错误信息, 注意key不存在时err为nil
func GetValueOfFields[T any](fields Fields, key string) (res T, exist bool, err error) {
	if fields == nil {
		err = stderr.Error("fields of message is nil")
		return
	}
	valueAny, exist := fields[key]
	if !exist {
		return
	}
	res, ok := valueAny.(T)
	if !ok {
		err = stderr.Error("try convert %T to %T in fields", valueAny, res)
		return
	}
	return
}

// SetKeyValueOfFields 添加或更新message中fields的key-value
func SetKeyValueOfFields(fields Fields, key string, value any) error {
	if fields == nil {
		return stderr.Error("fields of message is nil")
	}
	fields[key] = value
	return nil
}
