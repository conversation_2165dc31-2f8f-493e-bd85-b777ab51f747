package models

type (
	PredefinedField = string
	DLIEModelType   = string
)

const (
	// 应用链std接口字段名
	PredefinedFieldStdQuery   = "query"
	PredefinedFieldStdParams  = "params"
	PredefinedFieldStdFiles   = "files"
	PredefinedFieldStdHistory = "history"

	// 应用链原始接口字段名
	PredefinedFieldTextInput = "TextInput"
	PredefinedFieldFileInput = "FileInput"
	PredefinedFieldJsonInput = "JsonInput"
	PredefinedFieldChatInput = "ChatInput"

	// 应用链算子字段
	PredefinedFieldFiles           = "Files"
	PredefinedFieldUrls            = "Urls"
	PredefinedFieldName            = "Name"
	PredefinedFieldBase64Content   = "Base64Content"
	PredefinedFieldRawContent      = "RawContent"
	PredefinedFieldOutput          = "Output"
	PredefinedFieldMeta            = "Meta"
	PredefinedFieldTrace           = "Trace"
	PredefinedFieldContext         = "Context"
	PredefinedFieldRawInput        = "RawInput"
	PredefinedFieldStreamOutputRes = "StreamOutputRes" // 流式输出时， field 中对应的 key, 类型为 string
	PredefinedFieldFileName        = "FileName"        // 输入文件名
	PredefinedFieldText            = "Text"            // 文本， string
	PredefinedFieldTexts           = "Texts"           // 文本数组， []string
	PredefinedFieldVector          = "Vector"          // 向量, []float
	PredefinedFieldVectors         = "Vectors"         // 向量， [][]float32
	PredefinedFieldChatResult      = "ChatResult"
	PredefinedFieldEmbeddingTexts  = "EmbeddingTexts"  // 嵌入前的原始文本, *triton.Text2VecReqV2
	PredefinedFieldEmbeddingResult = "EmbeddingResult" // 嵌入后的文本向量, *triton.TextVecResV2
	PredefinedFieldEmbeddingEntity = "EmbeddingResult" // 嵌入后的文本向量, *triton.TextVecResV2
	PredefinedFieldResponse        = "response"        // 应用链最终的输出放到response中

	// agent算子相关 Question Konwledge History File
	PredefinedFieldQuestion  = "Question"
	PredefinedFieldKnowledge = "Knowledge"
	PredefinedFieldInternet  = "Internet"
	PredefinedFieldHistory   = "History"
	PredefinedFieldFile      = "File"

	// SFSFile field constants
	SFSFieldName     = "name"
	SFSFieldUID      = "uid"
	SFSFieldURL      = "url"
	SFSFieldHTTPURL  = "http_url"
	SFSFieldContent  = "content"

	DLIEModelTypeEmbedding    DLIEModelType = "text-vec"
	DLIEModelTypeChat         DLIEModelType = "text-gen"
	DLIEModelTypeGeneralInfer DLIEModelType = "general-infer"
	DLIEProtocol                            = "dlie://"
	HTTPProtocol                            = "http://"
)

func CvtFloatSlice[S, T float32 | float64](sources []S) (targets []T) {
	targets = make([]T, len(sources))
	for i, source := range sources {
		targets[i] = (T)(source)
	}
	return targets
}

func CvtFloatSlices[S, T float32 | float64](sources [][]S) (targets [][]T) {
	targets = make([][]T, len(sources))
	for i, source := range sources {
		targets[i] = CvtFloatSlice[S, T](source)
	}
	return targets
}
