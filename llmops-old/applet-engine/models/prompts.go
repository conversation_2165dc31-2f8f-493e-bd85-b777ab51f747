package models

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
)

// agent算子提示词
const (
	TempReplaceString   = "__TEMP_REPLACE_STRING__"
	StopWordObservation = "Observation"
)

const DefaultLanguageInstruction = "如果没有特别说明，请使用和问题（任务）一致的语言进行回答用户问题"

const ChatHistoryInstructionTemplate = `
The content within the <chat-history></chat-history> XML tags is your historical chat record. Use it if necessary.
<chat-history>
%s
</chat-history>
`

const ContextInstructionTemplate = `
The content within the <context></context> XML tags is your context. Use it if necessary. Please use markdown format "![image name](image link)" to display any images you need to show.
<context>
%s
</context>
`

const FileInstructionTemplate = `
The content within the <file></file> XML tags are files user uploaded.
<file>
%s
</file>
`

const ReActInstructionTemplate = `
Answer the following questions as best you can. You have access to the following tools:
%s

Use the following format:

Question: the input question you must answer
Thought: you should alwaws OUTPUT the "Thought:" and you should always think about what to do
Action: the action to take, should be one of [%s]
Action Input: the input to the action 
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can be repeated zero or more times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Begin!
`

const ToolDescriptionTemplate = `
%s: Call this tool to interact with the %s API. What is the %s API useful for? %s. Parameters: %s. Format the arguments as a JSON object.
`

const ReActScratchpadTemplate = `
Thought: %s
Action: %s
Action Input: %s
Observation: %s
`

const QuestionTemplate = `
Question: %s
`

func FillChatHistory(chatHistory string) string {
	return fmt.Sprintf(ChatHistoryInstructionTemplate, chatHistory)
}

func FillContext(context string) string {
	return fmt.Sprintf(ContextInstructionTemplate, context)
}

func FillFile(file string) string {
	return fmt.Sprintf(FileInstructionTemplate, file)
}

func FillReAct(toolNames, toolDescriptions string) string {
	return fmt.Sprintf(ReActInstructionTemplate, toolDescriptions, toolNames)
}

func FillToolDescription(tool agent_definition.Tool) (string, error) {
	bs, err := json.Marshal(tool.Parameters)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf(ToolDescriptionTemplate, tool.NameForModel, tool.NameForHuman, tool.NameForHuman, tool.Description, string(bs)), nil
}

func FillReActScratchpad(thought, action, actionInput, observation string) string {
	return fmt.Sprintf(ReActScratchpadTemplate, thought, action, actionInput, observation)
}

func FillQuestion(question string) string {
	return fmt.Sprintf(QuestionTemplate, question)
}

func FillEssentialPrompt(customInstruction, chatHistory, context, file string) string {
	defaultTimeInstruction := "当前时间: " + GetCurrentTime()
	essentialPrompt := customInstruction + "\n" + defaultTimeInstruction + "\n" + DefaultLanguageInstruction
	if strings.TrimSpace(chatHistory) != "" {
		essentialPrompt += FillChatHistory(chatHistory)
	}
	if strings.TrimSpace(context) != "" {
		essentialPrompt += FillContext(context)
	}
	if strings.TrimSpace(file) != "" {
		essentialPrompt += FillFile(file)
	}
	return essentialPrompt
}

func FillSimplePrompt(customInstruction, chatHistory, context, file, question string) string {
	simplePrompt := FillEssentialPrompt(customInstruction, chatHistory, context, file)
	simplePrompt += FillQuestion(question)
	return simplePrompt
}

func FillPromptWithReAct(customInstruction, chatHistory, context, file, toolNames, toolDescriptions, question string) string {
	reActPrompt := FillEssentialPrompt(customInstruction, chatHistory, context, file)
	reActPrompt += FillReAct(toolNames, toolDescriptions)
	reActPrompt += FillQuestion(question)
	return reActPrompt
}

func GetCurrentTime() string {
	timeLocation, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		stdlog.Warnf("Failed to load Asia/Shanghai timezone: %v. Using system default.", err)
		timeLocation = time.Local
	}

	now := time.Now().In(timeLocation)
	return now.Format("2006-01-02 15:04")
}

// 其他算子

const ParameterExtractorPromptTemplate = `
You are a helpful assistant tasked with extracting structured information based on specific criteria provided. Follow the guidelines below to ensure consistency and accuracy.

### Task
Extract the correct parameters. Ensure that the information extraction is contextual and aligns with the provided criteria.

### Instructions:
Always adhere to these instructions as closely as possible:
Steps:
1. Extract the relevant information based on the criteria given, output multiple values if there is multiple relevant information that match the criteria in the given text.
2. Generate a well-formatted output using the defined functions and arguments.
3. Generate structured outputs with appropriate parameters.
4. Do not include any XML tags in your output.

### Example
#### User Input
Here is the structure of the JSON object, you should always follow the structure.
<structure>
{"type": "object", "properties": {"location": {"type": "string", "description": "The location to get the weather information", "required": true}}, "required": ["location"]}
</structure>
Inside <text></text> XML tags, there is a text that you should convert to a JSON object.
<text>
What is the weather today in SF?
</text>
#### Assistant Output
{"location": "San Francisco"}
#### User Input
Here is the structure of the JSON object, you should always follow the structure.
<structure>
{"type": "object", "properties": {"food": {"type": "string", "description": "The food to eat", "required": true}}, "required": ["food"]}
</structure>
Inside <text></text> XML tags, there is a text that you should convert to a JSON object.
<text>
I want to eat some apple pie.
</text>
#### Assistant Output
{"result": "apple pie"}

Begin extracting parameters.
### User Input
Here is the structure of the JSON object, you should always follow the structure.
<structure>
%s
</structure>
Inside <text></text> XML tags, there is a text that you should convert to a JSON object.
<text>
%s
</text>
### Assistant Output
`

const QuestionClassifierUserInputTemplate = `{"input_text": "%s", "categories": %s}`

const QuestionClassifierPromptTemplate = `
### Job Description
You are a text classification engine that analyzes text data and assigns categories based on user input.
### Task
Your task is to assign one category ONLY to the input text and only one category may be assigned in the output.
### Format
The input text is in the variable input_text. Categories are specified as a category list in the variable categories.
### Constraint
DO NOT include anything other than the JSON object in your response.
### Example
Here is the chat example between human and assistant, inside <example></example> XML tags.
<example>
User:{"input_text": "I recently had a great experience with your company. The service was prompt and the staff was very friendly.", "categories": [{"category_id":"f5660049-284f-41a7-b301-fd24176a711c","category_name":"Customer Service"},{"category_id":"8d007d06-f2c9-4be5-8ff6-cd4381c13c60","category_name":"Satisfaction"},{"category_id":"5fbbbb18-9843-466d-9b8e-b9bfbb9482c8","category_name":"Sales"},{"category_id":"23623c75-7184-4a2e-8226-466c2e4631e4","category_name":"Product"}]}
Assistant:{"category_id": "f5660049-284f-41a7-b301-fd24176a711c","category_name": "Customer Service"}
User:{"input_text": "bad service, slow to bring the food", "categories": [{"category_id":"80fb86a0-4454-4bf5-924c-f253fdd83c02","category_name":"Food Quality"},{"category_id":"f6ff5bc3-aca0-4e4a-8627-e760d0aca78f","category_name":"Experience"},{"category_id":"cc771f63-74e7-4c61-882e-3eda9d8ba5d7","category_name":"Price"}]}
Assistant:{"category_id": "f6ff5bc3-aca0-4e4a-8627-e760d0aca78f","category_name": "Experience"}
</example>
### User Input
%s
### Assistant Output
`
