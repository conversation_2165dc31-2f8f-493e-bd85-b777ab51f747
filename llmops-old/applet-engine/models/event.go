package models

import (
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-engine/pkg/debug"
)

type Event interface {
	GetType() stdsrv.SSEEvt
	GetData() any
}

type ThoughtEvent struct {
	Event stdsrv.SSEEvt `json:"event"`
	Data  string        `json:"data"`
}

func (e ThoughtEvent) GetType() stdsrv.SSEEvt {
	return e.Event
}

func (e ThoughtEvent) GetData() any {
	return e.Data
}

type ActionEvent struct {
	Event stdsrv.SSEEvt   `json:"event"`
	Data  ActionEventData `json:"data"`
}

func (e ActionEvent) GetType() stdsrv.SSEEvt {
	return e.Event
}

func (e ActionEvent) GetData() any {
	return e.Data
}

type ActionEventData struct {
	Tool      agent_definition.Tool `json:"tool"`
	ToolInput any                   `json:"tool_input"`
}

type ObservationEvent struct {
	Event stdsrv.SSEEvt        `json:"event"`
	Data  ObservationEventData `json:"data"`
}

func (e ObservationEvent) GetType() stdsrv.SSEEvt {
	return e.Event
}

func (e ObservationEvent) GetData() any {
	return e.Data
}

type ObservationEventData struct {
	Tool        agent_definition.Tool `json:"tool"`
	Observation any                   `json:"observation"`
}

type DebugEvent struct {
	Event stdsrv.SSEEvt              `json:"event"`
	Data  *debug.OverallDebugMessage `json:"data"`
}

func (e DebugEvent) GetType() stdsrv.SSEEvt {
	return e.Event
}

func (e DebugEvent) GetData() any {
	return e.Data
}

type CitationEvent struct {
	Event stdsrv.SSEEvt `json:"event"`
	Data  any           `json:"data"`
}

func (e CitationEvent) GetType() stdsrv.SSEEvt {
	return e.Event
}
func (e CitationEvent) GetData() any {
	return e.Data
}
