package conf

import (
	"encoding/json"
	"fmt"
	"os"
	"time"

	"github.com/mitchellh/mapstructure"
	"github.com/spf13/viper"

	"transwarp.io/applied-ai/aiot/vision-std/conf"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/applet-backend/pkg/agent_executor"
)

const (
	DefaultConfigName   = "applet-engine" // 配置文件前缀
	DefaultConfigFormat = "yaml"          // 配置文件格式
	EnvPrefix           = "engine"        // 自动读取环境变量时添加的前缀 engine -> ENGINE_

	EnvBackendTaskDefine     = "TICK_SCRIPT"
	EnvBackendTaskID         = "TASK_ID"
	EnvBackendDatabase       = "DATABASE"
	EnvBackendDBRP           = "DBRP"
	EnvBackendRunCodeUrl     = "RUN_CODE_URL"
	EnvBackendTickScriptPath = "TICK_SCRIPT_PATH"
)

var Config *AppletEngineConfig

func DecConfig(dc *mapstructure.DecoderConfig) {
	dc.TagName = "yaml" // 使用字段的yaml tag进行映射
}

func Init(configPath string) {
	// NOTICE: 此处自动读取的环境变量必须可以在配置文件中找到对应的配置项,
	// 否则不会自动加载对应的环境变量
	viper.AutomaticEnv()                     // 开启环境变量自动读取
	viper.SetEnvPrefix(EnvPrefix)            // 设置自动添加的环境变量前缀
	viper.AddConfigPath(configPath)          // 配置文件所在路径
	viper.SetConfigName(DefaultConfigName)   // 配置文件前缀
	viper.SetConfigType(DefaultConfigFormat) // 配置文件格式 e.g. json, yaml .etc
	if err := viper.ReadInConfig(); err != nil {
		panic(any(fmt.Errorf("配置读取失败: %s", err.Error())))
	}
	Config = new(AppletEngineConfig)
	if err := viper.Unmarshal(&Config, DecConfig); err != nil {
		panic(any(fmt.Errorf("配置加载失败: %s", err.Error())))
	}
	setBackendTaskConfig()
	bs, _ := json.MarshalIndent(Config, "", "  ")
	fmt.Println(string(bs))
}

type AppletEngineConfig struct {
	// 基础配置
	EdgeDebug         bool                    `yaml:"edge_debug"`
	EdgeDropError     bool                    `yaml:"edge"`
	Tenant            TenantConfig            `yaml:"tenant"`
	Transport         conf.TransportConfig    `yaml:"transport"`
	Mqtt              conf.MqttConfig         `yaml:"mqtt"`
	Redis             conf.RedisConfig        `yaml:"redis"`
	BackendTask       BackendTaskConfig       `yaml:"backend_task"`
	AgentToolExecutor AgentToolExecutorConfig `yaml:"agent_tool_executor"`
	Citation          CitationConfig          `yaml:"citation"`
	Agent             AgentConfig             `yaml:"agent"`
	// Milvus        milvus.Config     `yaml:"milvus"`
	AppletBackend AppletBackendConfig `yaml:"applet_backend"`
}

type AppletConfig struct {
}

func NewAppletEngineConfig() AppletEngineConfig {
	return AppletEngineConfig{}
}

type AppletBackendConfig struct {
	Host    string        `yaml:"host"`
	Port    string        `yaml:"port"`
	TimeOut time.Duration `yaml:"http_timeout"`
}

type BackendTaskConfig struct {
	TickScript        string `yaml:"tick_script"`      // 默认后台启动的任务的定义
	TickScriptPath    string `yaml:"tick_script_path"` // TICK脚本文件路径
	TaskID            string `yaml:"task_id"`          // 默认后台启动的任务的ID
	Database          string `yaml:"database"`
	RP                string `yaml:"rp"`
	RunCodeUrl        string `yaml:"run_code_url"`
	SecurityCensorUrl string `yaml:"security_censor_url"`
}

type AgentToolExecutorConfig struct {
	Api       agent_executor.ApiToolExecutorConfig       `yaml:"api"`
	Knowledge agent_executor.KnowledgeToolExecutorConfig `yaml:"knowledge"`
	Model     agent_executor.ModelToolExecutorConfig     `yaml:"model"`
	Applet    agent_executor.AppletToolExecutorConfig    `yaml:"applet"`
}

type CitationConfig struct {
	MinParagraphLength      int     `yaml:"min_paragraph_length"`
	RelevanceScoreThreshold float32 `yaml:"relevance_score_threshold"`
	TopK                    int     `yaml:"top_k"`
}

// Agent 相关配置
type AgentConfig struct {
	MaxThoughtRounds int `yaml:"max_thought_rounds"` // 最大思考轮次
}

type TenantConfig struct {
	TenantId        string `yaml:"tenant_id"`
	ProjectId       string `yaml:"project_id"`
	SystemNamespace string `yaml:"system_namespace"` //为各租户记录系统服务所处的命名空间
}

func setBackendTaskConfig() {
	if Config == nil {
		stdlog.Warnf("config not initialized")
		return
	}

	Config.BackendTask.TaskID = os.Getenv(EnvBackendTaskID)
	Config.BackendTask.TickScript = os.Getenv(EnvBackendTaskDefine)
	Config.BackendTask.TickScriptPath = os.Getenv(EnvBackendTickScriptPath)

	if db := os.Getenv(EnvBackendDatabase); db != "" {
		Config.BackendTask.Database = db
	} else {
		Config.BackendTask.RP = "autogen"
	}

	if dbrp := os.Getenv(EnvBackendDBRP); dbrp != "" {
		Config.BackendTask.RP = dbrp
	} else {
		Config.BackendTask.RP = "autogen"
	}

}
