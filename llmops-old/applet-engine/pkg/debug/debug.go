package debug

import (
	"fmt"
	"github.com/patrickmn/go-cache"
	"sort"
	"strconv"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
)

const EVICTION_DELAY_TIME = time.Second * 5

type DebugType string

// node, llm, tool
const (
	DEBUG_TYPE_NODE DebugType = "node"
	DEBUG_TYPE_LLM  DebugType = "llm"
	DEBUG_TYPE_TOOL DebugType = "tool"
)

type TokenUsage struct {
	PromptTokens     int64 `json:"prompt_tokens"`
	CompletionTokens int64 `json:"completion_tokens"`
	TotalTokens      int64 `json:"total_tokens"`
}

type NodeStatus string

// 运行中、成功、失败、取消
const (
	NODE_STATUS_RUNNING  NodeStatus = "running"
	NODE_STATUS_SUCCESS  NodeStatus = "success"
	NODE_STATUS_FAILED   NodeStatus = "failed"
	NODE_STATUS_CANCELED NodeStatus = "canceled"
)

type DebugScope string

const (
	DEBUG_SCOPE_NODE      DebugScope = "node"
	DEBUG_SCOPE_SUB_CHAIN DebugScope = "sub_chain"
	DEBUG_SCOPE_CHAIN     DebugScope = "chain"
)

const NODE_DEBUG_TOPIC_FORMAT = "app_debug/%s/%s"

const TOPIC_TYPE_NODE_INFO = "node_info"

// NodeMeta  debug message 的来源的node的基础信息
type NodeMeta struct {
	NodeID     string `json:"node_id"`
	SubChainID string `json:"sub_chain_id"`
	NodeName   string `json:"node_name"`
	WidgetKey  string `json:"widget_key"` //算子类别id
}

// DebugMeta  用于前端展示调试Icon/Name等
type DebugMeta struct {
	ID   string    `json:"id"`   // agent debug 新增 用于标识存储node，llm，tool id
	Type DebugType `json:"type"` // agent debug 新增 node llm tool
	Name string    `json:"name"` // agent debu g新增 node llm tool name
}

type DebugMessage struct {
	taskID        string             `json:"-"`
	reqID         string             `json:"-"`
	isDebug       bool               `json:"-"` //pub时,检查该字段决定是否对消息进行推送。构建debugMsg时请注意该字段
	call          *Call              `json:"-"` //seePub时，需要使用call中的responseWriter
	Meta          NodeMeta           `json:"meta"`
	DebugMeta     DebugMeta          `json:"debug_meta"`
	Scope         DebugScope         `json:"scope"` // node/sub_chain/chain
	Round         int                `json:"round"` // 循环的轮数, 0/1/2/.../max_round，无循环时默认为0
	Status        NodeStatus         `json:"status"`
	Input         any                `json:"input"`
	Output        any                `json:"output"`
	Log           any                `json:"log"` // 实际是string类型，设置成any类型复用appendData函数
	StartTime     int64              `json:"start_time"`
	EndTime       int64              `json:"end_time"`
	Usage         TokenUsage         `json:"usage"`
	ChildrenStack *DebugMessageStack `json:"children"`
	Children      []*DebugMessage    `json:"children_ordered"`
}

// overall messages
type OverallDebugMessage struct {
	ReqID              string          `json:"req_id"`
	Status             NodeStatus      `json:"status"`
	StartTime          int64           `json:"start_time"`
	EndTime            int64           `json:"end_time"`
	Usage              TokenUsage      `json:"usage"`
	FirstRespLatencyMs int64           `json:"first_response_latency_ms"` // 首字时延, 毫秒
	Input              any             `json:"input"`
	Output             any             `json:"output"`
	Log                string          `json:"log"`
	DebugMessages      []*DebugMessage `json:"debug_messages"`
}

func (m *OverallDebugMessage) SetOutput(output any, isBatch bool) error {
	if !isBatch {
		m.Output = output
		return nil
	}
	if err := utils.AppendData(&m.Output, output); err != nil {
		return stderr.Wrap(err, "failed to set output %+v of request %s", output, m.ReqID)
	}
	return nil
}

// key: round -> value: DebugMessage
type NodeDebugMessages map[int]*DebugMessage

// key: node id -> value: NodeDebugMessages
type DebugMessageStack map[string]NodeDebugMessages

func (s DebugMessageStack) SetDebugMessage(id string, round int, debugMessage *DebugMessage) {
	messages, ok := s[id]
	if !ok {
		messages = make(NodeDebugMessages)
		s[id] = messages
	}
	messages[round] = debugMessage
}

type DebugMessageCache struct {
	cache *cache.Cache
}

var msgCache *DebugMessageCache

const (
	DEFAULT_EXPIRATION       = time.Second * 30
	DEFAULT_CLEANUP_INTERVAL = time.Second * 10
	EXPIRATION_THRESHOLD     = time.Millisecond * 500
)

// get
// input:
//   - reqID: string
//   - secondaryID: string
//   - round: string
//
// return:
//   - debugMessage: *DebugMessage
//   - exist: bool
//   - err: error 注意exist为false时err为nil，需要调用者自己决定缓存不存在时是否报错
func (c *DebugMessageCache) get(reqID, secondaryID string, round int) (*DebugMessage, bool, error) {
	cacheKey := reqID + secondaryID + "-round-" + strconv.Itoa(round)
	v, exist := msgCache.cache.Get(cacheKey)
	if !exist {
		return nil, false, nil
	}
	debugMessage, ok := v.(*DebugMessage)
	if !ok {
		return nil, true, stderr.Error("expected type %T got %T in debugMessageCache of cacheKey %s", debugMessage, v, cacheKey)
	}
	return debugMessage, true, nil
}

func (c *DebugMessageCache) set(reqID, secondaryID string, round int, debugMessage *DebugMessage, timeout time.Duration) {
	cacheKey := reqID + secondaryID + "-round-" + strconv.Itoa(round)
	msgCache.cache.Set(cacheKey, debugMessage, timeout+EVICTION_DELAY_TIME)
}

func getDebugMessageCache() *DebugMessageCache {
	if msgCache != nil {
		return msgCache
	}
	// 初始化cache
	newCache := cache.New(DEFAULT_EXPIRATION, DEFAULT_CLEANUP_INTERVAL)
	newCache.OnEvicted(func(cacheKey string, v interface{}) {
		m, ok := v.(*DebugMessage)
		if !ok {
			return
		}
		// 请求超时驱逐缓存时，仍在运行的节点设置为失败状态
		if m.Status == NODE_STATUS_RUNNING {
			m.Status = NODE_STATUS_FAILED
			m.EndTime = time.Now().UnixMilli()
			if err := m.Pub(); err != nil {
				stdlog.Error(err)
			}
		}
	})
	msgCache = &DebugMessageCache{
		cache: newCache,
	}
	return msgCache
}

// InitDebugMessage
// input:
//   - taskID: tick脚本的id
//   - reqID: 请求的id
//   - secondaryID: 辅助ID，reqID + secondaryID可以唯一标识一个debugmessage，算子传入Id，子链传入subChainID，主链传入空字符串
//   - NodeID: 算子id，不同子链中算子的nodeID可能重复
//   - subChainID: 子链的id
//   - nodeName: 算子的名称
//   - Scope: (node/sub_chain/chain)标识debugmessage是单个节点的、子链的还是整条链的
//   - timeout: http连接超时时间，缓存超时时间会在timeout的基础上增加EVICTION_DELAY_TIME，缓存延迟删除
//   - isDebug: 是否是debug模式，debug模式下会通过mqtt实时发布debug信息
func InitDebugMessage(call *Call, taskID, reqID, secondaryID, nodeID, subChainID, nodeName, widgetKey string, scope DebugScope, timeout time.Duration, isDebug bool, round int) (*DebugMessage, error) {
	if taskID == "" {
		return nil, stderr.Errorf("taskID is empty")
	}
	if reqID == "" {
		return nil, stderr.Errorf("reqID is empty")
	}
	msgCache = getDebugMessageCache()
	debugMessage, exist, err := msgCache.get(reqID, secondaryID, round)
	if err != nil {
		return nil, stderr.Wrap(err, "get debug message from cache")
	}
	// 节点debug消息已经初始化，无需再次初始化，处理子链有多个输入情况
	if exist {
		return debugMessage, nil
	}
	// 初始化debug message
	meta := NodeMeta{
		NodeID:     nodeID,
		SubChainID: subChainID,
		NodeName:   nodeName,
		WidgetKey:  widgetKey,
	}
	debugMeta := DebugMeta{
		ID:   nodeID,
		Name: nodeName,
		Type: DEBUG_TYPE_NODE,
	}
	debugMessage = &DebugMessage{
		call:      call,
		taskID:    taskID,
		reqID:     reqID,
		Meta:      meta,
		DebugMeta: debugMeta,
		Scope:     scope,
		isDebug:   isDebug,
	}
	msgCache.set(reqID, secondaryID, round, debugMessage, timeout)
	return debugMessage, nil
}

// GetDebugMessage reqID + secondaryID 可以唯一标识一个debugmessage
func GetDebugMessage(reqID, secondaryID string, round int) (*DebugMessage, error) {
	debugMessage, exist, err := getDebugMessageCache().get(reqID, secondaryID, round)
	if err != nil {
		return nil, stderr.Wrap(err, "get debug message from cache")
	}
	if !exist {
		return nil, stderr.Error("debug message of reqID %s secondaryID %s in round %d not exist", reqID, secondaryID, round)
	}
	return debugMessage, nil
}

func DebugMessageExist(reqID, secondaryID string, round int) bool {
	_, exist, _ := getDebugMessageCache().get(reqID, secondaryID, round)
	return exist
}

func (m *DebugMessage) SetRunningStatus(round int) error {
	if m.Round != round {
		// 设置当前算子的循环轮数
		m.Round = round
		// 重置debug message
		m.Input = nil
		m.Output = nil
		m.Log = nil
		m.StartTime = 0
		m.EndTime = 0
	}
	return m.setStatus(NODE_STATUS_RUNNING)
}

func (m *DebugMessage) SetSuccessStatus() error {
	return m.setStatus(NODE_STATUS_SUCCESS)
}

func (m *DebugMessage) SetFailedStatus() error {
	return m.setStatus(NODE_STATUS_FAILED)
}

func (m *DebugMessage) SetCanceledStatus() error {
	// 如果当前节点状态已经是成功，则无需设置取消状态
	if m.Status == NODE_STATUS_SUCCESS {
		return nil
	}
	return m.setStatus(NODE_STATUS_CANCELED)
}

// MergeInput 专门用于处理子链有多个输入情况
//   - input: 上游算子的输出数据，当前子链的输入数据
//   - nodeName: 当前算子的名称，用于区分当前输入数据对应子链中哪个算子
//   - nodeID: 当前算子的名称，用于区分当前输入数据对应子链中哪个算子
func (m *DebugMessage) MergeInput(input any, nodeName, nodeID string) error {
	if m.Input == nil {
		m.Input = make(map[string]any)
	}
	inputMap, ok := m.Input.(map[string]any)
	if !ok {
		return stderr.Error("failed to convert %T type input to %T", m.Input, inputMap)
	}
	inputMap[nodeName+nodeID] = input
	if err := m.Pub(); err != nil {
		return stderr.Wrap(err, "failed to publish subchain input of node %s", nodeName)
	}
	return nil
}

func (m *DebugMessage) SetInput(input any, isBatch bool) error {
	var err error
	if isBatch {
		err = m.appendData(&m.Input, input)
	} else {
		err = m.setData(&m.Input, input)
	}
	if err != nil {
		return stderr.Wrap(err, "failed to set input %s of %s", input, m.Meta.NodeName)
	}
	return nil
}

func (m *DebugMessage) SetOutput(output any, isBatch bool) error {
	var err error
	if isBatch {
		err = m.appendData(&m.Output, output)
	} else {
		err = m.setData(&m.Output, output)
	}
	if err != nil {
		return stderr.Wrap(err, "failed to set output %+v of %s", output, m.Meta.NodeName)
	}
	return nil
}

func (m *DebugMessage) SetLog(log string) error {
	err := m.appendData(&m.Log, log) // 日志只有append模式
	if err != nil {
		return stderr.Wrap(err, "failed to append log %s of %s", log, m.Meta.NodeName)
	}
	return nil
}

func (m *DebugMessage) setData(data *any, newData any) error {
	*data = newData
	return m.Pub()
}

func (m *DebugMessage) appendData(data *any, newData any) error {
	if err := utils.AppendData(data, newData); err != nil {
		return stderr.Wrap(err, "failed to do _appendData")
	}
	return m.Pub()
}

func (m *DebugMessage) setStatus(status NodeStatus) error {
	// 状态已经设置，无需再次设置
	if m.Status == status {
		return nil
	}
	m.Status = status
	switch status {
	case NODE_STATUS_RUNNING:
		m.StartTime = time.Now().UnixMilli()
	case NODE_STATUS_SUCCESS, NODE_STATUS_FAILED, NODE_STATUS_CANCELED:
		m.EndTime = time.Now().UnixMilli()
	default:
		return stderr.Error("unknown status %s", status)
	}
	return m.Pub()
}

func (m *DebugMessage) Pub() error {
	if !m.shouldPub() {
		return nil
	}
	call := m.call
	return stdsrv.SSESendDataWithEventName(call.W(), call.reqID, stdsrv.SSEEvtLog, stdsrv.AnyToString(m))
}

// shouldPub FIXME
// 移除mqtt后,原本的调试信息<log>与工作流信息<message>等均通过see往外传输
// 原先的日志方案,同一节点、同一个轮次、相同状态的日志信息可能写多次。
// 因为涉及到子链、循环、嵌套等,直接修改比较复杂。在pub之前统一拦截
// 只允许发送第一个running消息以及end消息
func (m *DebugMessage) shouldPub() bool {
	call := m.call
	if call == nil || call.Done() {
		stderr.Errorf("call is nil or done for pub log")
		return false
	}

	if !(m.isDebug && m.IsStreamMode()) {
		stdlog.Info("only debug mod and streaming response mode should pub log")
		return false
	}

	if m.IsEndMessage() {
		stdlog.Info("do not restriction for end log")
		return true
	}

	IdentifyId := m.getIdentifyId()
	if exist, _ := call.logsHelper.Get(IdentifyId); exist {
		stdlog.Info("only one log msg should be pub with IdentifyId[%s]", IdentifyId)
		return false
	}
	call.logsHelper.Set(IdentifyId, true)
	return true
}

func (m *DebugMessage) getIdentifyId() string {
	return fmt.Sprintf("%s-%s-%s-%s-%s-%s-%d", m.taskID, m.reqID, m.Meta.NodeID, m.Meta.SubChainID, m.Scope, m.Status, m.Round)
}

// IsEndMessage 是否为单个节点、本轮次的最后消息
func (m *DebugMessage) IsEndMessage() bool {
	switch m.Status {
	case NODE_STATUS_SUCCESS, NODE_STATUS_FAILED, NODE_STATUS_CANCELED:
		return true
	}
	return false
}

func (m *DebugMessage) IsRunning() bool {
	return m.Status == NODE_STATUS_RUNNING
}

func (m *DebugMessage) IsStreamMode() bool {
	return m.call.GetResponseModel() == ResponseModeStreaming
}

// IsChainEndMessage 是否为本次调试的最后一条消息
func (m *DebugMessage) IsChainEndMessage() bool {
	return m.Scope == DEBUG_SCOPE_CHAIN && m.IsEndMessage()
}

// GetTopic  script_id、task_id 、chat_id 、run_id 等价
func GetTopic(taskID string) string {
	return fmt.Sprintf(NODE_DEBUG_TOPIC_FORMAT, taskID, TOPIC_TYPE_NODE_INFO)
}

// SetChildDebugMessage
// input:
//   - parentKey: “虚构父节点”在 debugMessageStack 中的 key，当前默认不能循环执行子链，所以“虚构父节点”的round硬编码为0
//   - round: 子链node的执行轮次
//   - subChainDebugMsg: 子链node产生的 debugMessage
func (s DebugMessageStack) SetChildDebugMessage(parentKey string, round int, subChainDebugMsg *DebugMessage) error {
	pNodeDebugMsg, exists := s[parentKey]

	if !exists {
		return stderr.Errorf("parent debugmessage with key %s does not exist", parentKey)
	}

	// 将 debugMessage 存入 DebugMessageStack，
	// 存入 虚拟父节点 的 debugMessageStack[nodeID][0].Children 中
	// 当前不支持循环执行"应用链嵌入"（但是支持子链里有循环），所以 虚拟父节点 只取第0轮，赋值给 pDebugMsg
	pDebugMsg := pNodeDebugMsg[0]
	pDebugMsg.SetDebugMsgAsChildren(subChainDebugMsg, round)

	return nil
}

// 给 DebugMessag 设置类型为 DebugMessageStack 的 ChildrenStack 字段
func (m *DebugMessage) SetDebugMsgAsChildren(childDebugMsg *DebugMessage, round int) {
	var pDebugMsgChildren DebugMessageStack
	if m.ChildrenStack == nil {
		newDebugMessageStackAsChild := make(DebugMessageStack)
		m.ChildrenStack = &newDebugMessageStackAsChild
		pDebugMsgChildren = *m.ChildrenStack
	} else {
		pDebugMsgChildren = *m.ChildrenStack
	}

	//childKey := childDebugMsg.Meta.NodeID
	childKey := childDebugMsg.DebugMeta.ID
	if targetNodeDebugMsgs, exists := pDebugMsgChildren[childKey]; exists {
		targetNodeDebugMsgs[round] = childDebugMsg
	} else {
		newNodeDebugMsgs := make(NodeDebugMessages) // 初始化 map
		newNodeDebugMsgs[0] = childDebugMsg         // 作为第 0 轮的 debugMessage
		pDebugMsgChildren[childKey] = newNodeDebugMsgs
	}
}

// 将 ChildrenStack 字段中的 DebugMessage 按时间顺序存入 debugMessage.Children 中
func (m *DebugMessage) OrderChildDebugMsg() {
	if m.ChildrenStack == nil {
		return
	}

	childDSM := make([]*DebugMessage, 0)

	for _, childNodeDebugMessages := range *m.ChildrenStack { // 遍历所有 node
		if len(childNodeDebugMessages) == 0 {
			continue
		}
		for _, childDebugMessage := range childNodeDebugMessages { // 遍历 node 的所有轮次
			if childDebugMessage == nil {
				continue
			}
			childDebugMessage.Meta.NodeID = childDebugMessage.Meta.SubChainID + "_" + childDebugMessage.Meta.NodeID
			childDSM = append(childDSM, childDebugMessage)
		}
	}

	// 将子链的 DebugMessage 按照StartTime字段升序排序
	sort.Slice(childDSM, func(i, j int) bool {
		return childDSM[i].StartTime < childDSM[j].StartTime
	})

	m.Children = childDSM
	m.ChildrenStack = nil
}
