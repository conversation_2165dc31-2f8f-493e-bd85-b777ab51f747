package debug

import (
	"context"
	"errors"
	"github.com/sirupsen/logrus"
	"net/http"
	"strings"
	"sync"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stdctn"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
)

const (
	QueryNameDebug        = "debug"
	QueryNamePretty       = "pretty"
	QueryNameThought      = "thought"
	QueryNameText         = "text"
	QueryNameTrace        = "trace"
	QueryNameStack        = "stack"
	QueryValueTrue        = "true"
	QueryNameResponseMode = "response_mode"
)

type ResponseMode string

const (
	ResponseModeBlocking  ResponseMode = "blocking"
	ResponseModeStreaming ResponseMode = "streaming"
)

type Call struct {
	*logrus.Entry
	reqID               string
	req                 *http.Request
	w                   http.ResponseWriter
	ctx                 context.Context
	cancel              context.CancelFunc
	completed           bool                                // 标识退出时是否已完成
	isDebug             bool                                // 请求url中是否带有query参数?debug=true，控制是否发送每个算子的调试信息
	isPretty            bool                                // 请求url中是否带有query参数?pretty=true, 控制最终的json等数据是否需要pretty marshal 与 markdown wrap
	isThought           bool                                // 请求url中是否带有query参数?thought=true，控制是否发送agent算子的思考与执行过程
	isText              bool                                // 请求url中是否带有query参数?text=true，控制返回的结果是纯文本形式还是{response: xxx}形式
	isTrace             bool                                // 请求url中是否带有query参数?trace=true，控制是否发送trace event
	isStack             bool                                // 请求url中是否带有query参数?stack=true，控制是否发送stack event
	responseModel       ResponseMode                        // 请求url中query参数?response_mode=blocking|streaming, 默认为streaming
	mus                 map[string]*sync.Mutex              // 为每个请求的每个算子创建一个互斥锁，保证并发时正确的消息顺序，限制单个请求在单个算子中的并发
	overallDebugMessage *OverallDebugMessage                // 返回给前端的单次请求所有debug message
	loopRounds          *stdctn.ConcurrentMap[string, int]  // 记录单个请求中，算子的循环轮数
	debugMessageStack   DebugMessageStack                   // 单次请求的所有debug message
	logsHelper          *stdctn.ConcurrentMap[string, bool] // 调试中、单个节点-每一轮次-每一个转态的消息，只需要发送一次
}

func NewCall(entry *logrus.Entry, reqID string, req *http.Request, w http.ResponseWriter, ctx context.Context, cancel context.CancelFunc, mus map[string]*sync.Mutex, overallDebugMessage *OverallDebugMessage) *Call {
	return &Call{
		Entry:               entry,
		reqID:               reqID,
		req:                 req,
		w:                   w,
		ctx:                 ctx,
		cancel:              cancel,
		isDebug:             ParseBoolQuery(req, QueryNameDebug),
		isPretty:            ParseBoolQuery(req, QueryNamePretty),
		isThought:           ParseBoolQuery(req, QueryNameThought),
		isText:              ParseBoolQuery(req, QueryNameThought),
		isTrace:             ParseBoolQuery(req, QueryNameTrace),
		isStack:             ParseBoolQuery(req, QueryNameStack),
		responseModel:       ParseResponseModeQuery(req, QueryNameResponseMode),
		mus:                 mus,
		overallDebugMessage: overallDebugMessage,
		loopRounds:          stdctn.NewConcurrentMap[string, int](),
		debugMessageStack:   make(DebugMessageStack),
		logsHelper:          stdctn.NewConcurrentMap[string, bool](),
	}
}

func (c *Call) ReqID() string {
	return c.reqID
}

// Cancel 取消本次调用
func (c *Call) Cancel() {
	c.cancel()
}

func (c *Call) W() http.ResponseWriter {
	return c.w
}

func (c *Call) Ctx() context.Context {
	return c.ctx
}

// IsPretty 请求url中是否带有query参数?pretty=true，控制最终的json等数据是否需要pretty marshal 与 markdown wrap
func (c *Call) IsPretty() bool {
	return c.isPretty
}

// IsDebug 请求url中是否带有query参数?debug=true，控制是否发送每个算子的调试信息
func (c *Call) IsDebug() bool {
	return c.isDebug
}

// NeedShowThought 请求url中是否带有query参数?thought=true，控制是否发送agent算子的思考与执行过程
func (c *Call) IsThought() bool {
	return c.isThought
}

// IsText 请求url中是否带有query参数?text=true，控制返回的结果是纯文本形式还是{response: xxx}形式
func (c *Call) IsText() bool {
	return c.isText
}

// IsTrace 请求url中是否带有query参数?trace=true，控制是否发送trace event
func (c *Call) IsTrace() bool {
	return c.isTrace
}

// IsStack
func (c *Call) IsStack() bool {
	return c.isStack
}

// GetResponseModel
func (c *Call) GetResponseModel() ResponseMode {
	return c.responseModel
}

func (c *Call) Lock(nodeKey string) error {
	mu, ok := c.mus[nodeKey]
	if !ok {
		return stderr.Error("no mutex found for node %s", nodeKey)
	}
	mu.Lock()
	return nil
}

func (c *Call) Unlock(nodeKey string) error {
	mu, ok := c.mus[nodeKey]
	if !ok {
		return stderr.Error("no mutex found for node %s", nodeKey)
	}
	mu.Unlock()
	return nil
}

func (c *Call) Deadline() (deadline time.Time, ok bool) {
	return c.ctx.Deadline()
}

// Complete 将本次请求调用标记为已完成
func (c *Call) Complete() {
	c.completed = true
	c.cancel()
}

// Done 返回本次请求调用是否已完成
func (c *Call) Done() bool {
	if c == nil {
		return true
	}
	select {
	case <-c.ctx.Done():
		return true
	default:
		return false
	}
}

// Wait 将会阻塞直至本次请求超时或者完成
func (c *Call) Wait() {
	<-c.ctx.Done()
	if c.ctx.Err() == context.DeadlineExceeded {
		stdlog.Warnf("call %s timeout", c.reqID)
		// 设置主链错误状态
	}
	if c.ctx.Err() == context.Canceled && !c.completed {
		// TODO: 发送主链取消状态，避免重复发送
		stdlog.Warnf("call %s canceled", c.reqID)
	}
}

// TODO: 此函数永远不会执行select中default部分代码，因为call总是先超时，call缓存后过期，执行此函数时c.ctx.Done()通道已经关闭
// EndWithTimeout 当前请求超时未接收到响应
func (c *Call) EndWithTimeout() {
	if c == nil || c.completed {
		return
	}
	select {
	case <-c.ctx.Done():
		// already completed
	default:
		err := stderr.Internal.Error("waiting response for request %s timeout", c.reqID)
		if nErr := c.EndWithError(err); nErr != nil {
			stdlog.WithError(nErr).Errorf("error ocurred while handling anothor error: %s", err.Error())
		}
		return
	}
}

// endWithError 处理请求过程中发生的错误并返回
func (c *Call) EndWithError(err error) error {
	if c == nil {
		return errors.New("handle msg with nil call")
	}
	if c.Done() {
		return errors.New("handle msg with a completed call")
	}

	defer c.Complete()
	if err = stdsrv.SSESendError(c.w, c.reqID, err); err != nil {
		return stderr.Wrap(err, "sending sse")
	}
	return nil
}

// 通过唯一id获取算子或子链的循环轮数
func (c *Call) GetLoopRound(id string) int {
	round, _ := c.loopRounds.Get(id)
	return round
}

// 算子的循环轮数加一
func (c *Call) IncrementLoopRound(id string) {
	c.loopRounds.Set(id, c.GetLoopRound(id)+1)
}

func (c *Call) SetDebugMessage(id string, round int, debugMessage *DebugMessage) {
	c.debugMessageStack.SetDebugMessage(id, round, debugMessage)
}

func (c *Call) GetDebugMessageStack() DebugMessageStack {
	return c.debugMessageStack
}

// GetOverallDebugMessage
func (c *Call) GetOverallDebugMessage() *OverallDebugMessage {
	return c.overallDebugMessage
}

func ParseResponseModeQuery(r *http.Request, query string) ResponseMode {
	if GetUrlQuery(r, QueryNameResponseMode) == string(ResponseModeBlocking) {
		return ResponseModeBlocking
	}
	return ResponseModeStreaming
}

func ParseBoolQuery(r *http.Request, query string) bool {
	return GetUrlQuery(r, query) == QueryValueTrue // 判断是否为true
}

func GetUrlQuery(r *http.Request, query string) string {
	queryValues := r.URL.Query()
	return queryValues.Get(query)
}

// 提取 TaskID
func (c *Call) ExtractTaskIDFromPath() (string, error) {
	// path 示例: “/api/v1/ebce9208-399a-4327-9adf-755c7697690e”
	path := c.req.URL.Path

	// 检查路径是否为空
	if path == "" {
		return "", errors.New("path is empty")
	}

	// 按 "/" 分割路径
	parts := strings.Split(path, "/")
	if len(parts) < 4 {
		return "", errors.New("invalid path format")
	}

	// 提取 taskId
	taskID := parts[len(parts)-1]

	return taskID, nil
}

func (c *Call) HasDebugMsgStaskKey(key string) bool {
	_, exists := c.debugMessageStack[key]

	if !exists {
		return false
	} else {
		return true
	}
}

func (c *Call) SetChildDebugMessageIntoStack(parentKey string, round int, childDebugMessage *DebugMessage) error {
	err := c.debugMessageStack.SetChildDebugMessage(parentKey, round, childDebugMessage)

	if err != nil {
		return err
	}

	return nil
}
