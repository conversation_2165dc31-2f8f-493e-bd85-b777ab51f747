Dependencies
============

* github.com/BurntSushi/toml [MIT](https://github.com/BurntSushi/toml/blob/master/COPYING)
* github.com/boltdb/bolt [MIT](https://github.com/boltdb/bolt/blob/master/LICENSE)
* github.com/cenkalti/backoff [MIT](https://github.com/cenkalti/backoff/blob/master/LICENSE)
* github.com/dgrijalva/jwt-go [MIT](https://github.com/dgrijalva/jwt-go/blob/master/LICENSE)
* github.com/dustin/go-humanize [MIT](https://github.com/dustin/go-humanize/blob/master/LICENSE)
* github.com/golang/protobuf [BSD](https://github.com/golang/protobuf/blob/master/LICENSE)
* github.com/google/uuid [BSD](https://github.com/google/uuid/blob/master/LICENSE)
* github.com/gorhill/cronexpr [APLv2](https://github.com/gorhill/cronexpr/blob/master/APLv2)
* github.com/k-sone/snmpgo [MIT](https://github.com/k-sone/snmpgo/blob/master/LICENSE)
* github.com/kimor79/gollectd [BSD](https://github.com/kimor79/gollectd/blob/master/LICENSE)
* github.com/mattn/go-runewidth [MIT](https://github.com/mattn/go-runewidth/blob/master/README.mkd)
* github.com/mitchellh/copystructure[MIT](https://github.com/mitchellh/copystructure/blob/master/LICENSE)
* github.com/mitchellh/reflectwalk [MIT](https://github.com/mitchellh/reflectwalk/blob/master/LICENSE)
* github.com/pkg/errors [BSD](https://github.com/pkg/errors/blob/master/LICENSE)
* github.com/russross/blackfriday [BSD](https://github.com/russross/blackfriday/blob/master/LICENSE.txt)
* github.com/segmentio/kafka-go [MIT](https://github.com/segmentio/kafka-go/blob/master/LICENSE)
* github.com/serenize/snaker [MIT](https://github.com/serenize/snaker/blob/master/LICENSE.txt)
* github.com/shurcooL/go [MIT](https://github.com/shurcooL/go/blob/master/README.md)
* github.com/shurcooL/markdownfmt [MIT](https://github.com/shurcooL/markdownfmt/blob/master/README.md)
* github.com/shurcooL/sanitized\_anchor\_name [MIT](https://github.com/shurcooL/sanitized_anchor_name/blob/master/LICENSE)
* github.com/stretchr/testify [MIT](https://github.com/stretchr/testify/blob/master/LICENSE)
* gopkg.in/gomail.v2 [MIT](https://github.com/go-gomail/gomail/blob/v2/LICENSE)
* github.com/mailru/easyjson [MIT](https://github.com/mailru/easyjson/blob/3fdea8d05856a0c8df22ed4bc71b3219245e4485/LICENSE)
